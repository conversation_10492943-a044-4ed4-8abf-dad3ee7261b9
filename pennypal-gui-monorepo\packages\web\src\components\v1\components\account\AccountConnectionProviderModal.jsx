import React, { useEffect } from 'react'
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faLink,
  faSync,
  faPlus,
  faRedo,
  faTimes,
  faClock,
  faUniversity,
  faExclamationTriangle, 
  faArrowUp, 
  faArrowDown, 
  faEquals,  
  faSpinner,
  faChartLine,
  faCreditCard,
  faCoins,
  faLandmark,
  faSackDollar,
  faRobot,
  faExchangeAlt
} from "@fortawesome/free-solid-svg-icons";
import { useDispatch, useSelector } from "react-redux";
import { usePlaidLink } from 'react-plaid-link'; // Add this import
import plaidLogo  from "../../../../assets/plaid.jpeg";
import mxLogo     from "../../../../assets/mx.jpeg";
import stripeLogo from "../../../../assets/stripe.jpeg";  
import finicityLogo from "../../../../assets/finicity.jpeg";

import { formatLastSyncTime } from './AccountUtil';
import {
  fetchLinkToken,
  fetchAccountDetails,
  exchangePublicToken,
  refreshAllAccounts,
  syncAccount,
  connectMx,
  connectStripe,
  connectFinicity,
  resetError
} from "../../../../../../logic/redux/accountsDashboardSlice";

// Add logging function if not available elsewhere
const logEvent = (category, action, properties = {}) => {
  console.log(`Event: ${category}.${action}`, properties);
  // Replace with your actual analytics logging
};

const AccountConnectionProviderModal = ({darkMode, currentTheme, setShowConnectionModal}) => {
  const colors = currentTheme.colors;
  const dispatch = useDispatch();
  const { 
    linkToken, 
    accounts, 
    isLinked, 
    isLoading,
    balanceHistory,
    error,
    syncingAccounts = [],
    refreshAllStatus
  } = useSelector((state) => state.accounts);

  // Get userId from your auth state or props
  const { user } = useSelector((state) => state.auth); // Adjust based on your auth structure
  const userId = user?.id; // Adjust based on your user object structure

  // Fetch link token when modal opens
  useEffect(() => {
    if (!linkToken) {
      dispatch(fetchLinkToken());
    }
  }, [dispatch, linkToken]);

  const onSuccess = async (publicToken, metadata) => {
    logEvent('AccountsDashboard', 'PlaidConnectionSuccess', {
      institutionId: metadata?.institution?.institution_id,
      institutionName: metadata?.institution?.name,
      accountsCount: metadata?.accounts?.length
    });
    try {
      await dispatch(exchangePublicToken(publicToken)).unwrap();
      await dispatch(fetchAccountDetails(1)).unwrap();
      setShowConnectionModal(false);
    } catch (err) {
      logEvent('AccountsDashboard', 'PlaidConnectionError', {
        error: err.message || 'Unknown error'
      });
      console.error("Error linking accounts:", err);
    }
  };

  const onExit = (err, metadata) => {
    if (err) {
      logEvent('AccountsDashboard', 'PlaidConnectionExit', {
        error: err.error_message || 'User exited',
        errorCode: err.error_code
      });
    }
  };

  // Configure Plaid Link
  const config = {
    token: linkToken,
    onSuccess,
    onExit,
  };

  const { open, ready } = usePlaidLink(config);

  const handleMxConnect = () => {
    dispatch(connectMx());
  };

  const handleStripeConnect = () => {
    dispatch(connectStripe());
  };

  const handleFinicityConnect = () => {
    if (userId) {
      dispatch(connectFinicity({ userId: userId }));
    } else {
      console.error('User ID not available for Finicity connection');
    }
  };

  const handlePlaidConnect = () => {
    if (ready && linkToken) {
      open();
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black/60 backdrop-blur-sm p-5 z-50 animate-fade-in">
      <div className={`border p-8 rounded-2xl shadow-2xl max-w-lg w-full animate-slide-up`}
        style={{ 
          backgroundColor: colors.cardBg,
          borderColor: colors.border
        }}>
        <div className="flex justify-between items-center border-b border-slate-200/20 pb-6 mb-6">
          <div>
            <h2 className={`text-2xl font-bold`} style={{ color: colors.text }}>Connect Account</h2>
            <p className={`${colors.neutral} mt-1`}>Choose your financial institution</p>
          </div>
          <button
            onClick={() => setShowConnectionModal(false)}
            className={`p-2 rounded-xl transition-all duration-200 ${
              darkMode 
                ? 'text-slate-400 hover:text-slate-300' 
                : 'text-slate-500 hover:text-slate-700'
            }`}
            onMouseEnter={(e) => e.target.style.backgroundColor = `${currentTheme.colors.primary}20`}
            onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
          >
            <FontAwesomeIcon icon={faTimes} className="text-xl" />
          </button>
        </div>
        
        <div className="space-y-4">
          {/* Plaid Connection */}
          {linkToken && ready ? (
            <button
              className={`flex items-center space-x-4 hover:shadow-lg text-black py-4 px-6 rounded-xl transition-all duration-200 w-full font-semibold`}
              style={{ backgroundColor: colors.primary }}
              onClick={handlePlaidConnect}
              disabled={isLoading}
            >
              <img src={plaidLogo} alt="Plaid Icon" className="w-8 h-8" />
              <span>{isLoading ? 'Connecting...' : 'Connect with Plaid'}</span>
            </button>
          ) : (
            <div className={`flex items-center space-x-4 py-4 px-6 rounded-xl opacity-75`}
              style={{ backgroundColor: colors.secondary }}>
              <FontAwesomeIcon icon={faSpinner} className="w-8 h-8 animate-spin" />
              <div>
                <span className="text-black font-semibold">Plaid</span>
                <p className="text-black/70 text-sm">Loading connection...</p>
              </div>
            </div>
          )}
          
          {/* Error Display */}
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
              <strong className="font-bold">Error: </strong>
              <span className="block sm:inline">{error}</span>
              <button
                onClick={() => dispatch(resetError())}
                className="absolute top-0 bottom-0 right-0 px-4 py-3"
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>
          )}
          
          <div className={`text-center py-4 ${colors.neutral} font-medium`}>
            ━━━━━━━━━━━ OR ━━━━━━━━━━━
          </div>
          
          {/* Other Connection Options */}
          <button
            className={`flex items-center space-x-4 hover:shadow-lg text-black py-4 px-6 rounded-xl transition-all duration-200 w-full font-semibold`}
            style={{ backgroundColor: colors.accent }}
            onClick={handleMxConnect}
          >
          <img src={mxLogo} alt="MX Icon" className="w-8 h-8" />
            <span>Connect MX Financial</span>
          </button>
          
          <button
            className={`flex items-center space-x-4 hover:shadow-lg text-black py-4 px-6 rounded-xl transition-all duration-200 w-full font-semibold`}
            style={{ backgroundColor: colors.secondary }}
            onClick={handleStripeConnect}
          >
                     <img src={stripeLogo} alt="MX Icon" className="w-8 h-8" />

            <span>Connect Stripe</span>
          </button>
          
          <button
            className={`flex items-center space-x-4 hover:shadow-lg text-black py-4 px-6 rounded-xl transition-all duration-200 w-full font-semibold`}
            style={{ backgroundColor: colors.success }}
            onClick={handleFinicityConnect}
            disabled={!userId}
          >
            {isLoading ? (
              <FontAwesomeIcon icon={faSpinner} className="w-8 h-8 animate-spin" />
            ) : (
          <img src={finicityLogo} alt="MX Icon" className="w-8 h-8" />

            )}
            <span>{isLoading ? "Connecting..." : "Connect with Finicity"}</span>
          </button>
        </div>
      </div>
    </div>
  )
}

export default AccountConnectionProviderModal