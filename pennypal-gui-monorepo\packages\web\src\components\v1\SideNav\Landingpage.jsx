import React, { useState, useEffect } from 'react';
import { Eye, CreditCard, Repeat, Wallet, Target, BarChart3, Zap, DollarSign, ChevronDown, Linkedin, Twitter, Instagram, Facebook } from 'lucide-react'; // Added social media icons
const LandingPage = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
 const [currentFeature, setCurrentFeature] = useState(0); // Changed from currentTestimonial to currentFeature
  const [openDropdown, setOpenDropdown] = useState(null); // State for dropdown

  // Auto-rotate testimonials
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 4000);
    return () => clearInterval(timer);
  }, []);

  // Auto-rotate features
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % keyFeatures.length);
    }, 4000);
    return () => clearInterval(timer);
  }, []);

  // Dropdown handlers
  const handleMouseEnter = (dropdown) => {
    setOpenDropdown(dropdown);
  };

  const handleMouseLeave = () => {
    setOpenDropdown(null);
  };

  // Placeholder button handlers
  const handleLoginClick = () => {
    console.log('Login clicked');
    // Add your login logic here
  };

  const handleGetStartedClick = () => {
    console.log('Get Started clicked');
    // Add your get started logic here
  };

  // Dropdown items
  const dropdownItems = {
    features: [
      { name: 'Net Worth Tracking', href: '#net-worth' },
      { name: 'Transaction Management', href: '#transactions' },
      { name: 'Subscription Tracking', href: '#subscriptions' },
      { name: 'Custom Reports', href: '#reports' },
      { name: 'Goal Setting', href: '#goals' },
      { name: 'Multi-Platform', href: '#multi-platform' },
    ],
    pricing: [
      { name: 'Pricing Plans', href: '#pricing-plans' },
      { name: 'Free Trial', href: '#free-trial' },
      { name: 'Compare Plans', href: '#compare-plans' },
    ],
    blog: [
      { name: 'Latest Posts', href: '#latest-posts' },
      { name: 'Financial Tips', href: '#financial-tips' },
      { name: 'User Stories', href: '#user-stories' },
    ],
    support: [
      { name: 'Help Center', href: '#help-center' },
      { name: 'Contact Us', href: '#contact' },
      { name: 'Community', href: '#community' },
      { name: 'FAQs', href: '#faqs' },
    ],
  };

  // Key Features array
  const keyFeatures = [
    { 
      name: "Net Worth", 
      icon: <Eye className="w-5 h-5 text-[#8bc34a]" />, 
      description: "See your entire financial picture in one place by connecting bank accounts, credit cards, loans, and investments for a unified view. Monitor your assets and liabilities effortlessly with a clear overview. Stay updated with real-time syncing to ensure your net worth is always accurate. This feature helps you make informed financial decisions with confidence."
    },
    { 
      name: "Transactions", 
      icon: <CreditCard className="w-5 h-5 text-[#8bc34a]" />, 
      description: "Track every transaction in a clean, searchable list, mark them as reviewed, and easily spot unexpected expenses to stay in control. Review your spending habits with detailed categorization. Get updated instantly with new transactions as they occur, ensuring you never miss a beat. Take control of your finances with ease and precision."
    },
    { 
      name: "Recurring", 
      icon: <Repeat className="w-5 h-5 text-[#8bc34a]" />, 
      description: "Never miss a subscription payment again with automatic detection of recurring charges like streaming services, gyms, or app memberships. Keep track of all your regular expenses in one place. Receive updated alerts to manage your subscriptions effectively and avoid unnecessary costs. Save money by identifying and canceling unused subscriptions."
    },
    { 
      name: "Budget", 
      icon: <Wallet className="w-5 h-5 text-[#8bc34a]" />, 
      description: "Create and manage budgets tailored to your needs, ensuring you stay on track with spending and savings goals effortlessly. Set limits for different categories to control your expenses. Stay updated with budget progress notifications to meet your targets without stress. Achieve financial discipline with a clear plan for your money."
    },
    { 
      name: "Goals", 
      icon: <Target className="w-5 h-5 text-[#8bc34a]" />, 
      description: "Set savings targets, track your progress, and stay motivated with clear plans to achieve what matters most to you. Whether it’s a vacation or a new car, plan with purpose. Get updated regularly on your goal milestones to keep pushing forward toward success. Celebrate every step as you get closer to your dreams."
    },
    { 
      name: "Reports", 
      icon: <BarChart3 className="w-5 h-5 text-[#8bc34a]" />, 
      description: "Visualize your finances with custom charts to track spending trends, income, or net worth over time with actionable insights. Analyze your financial patterns to make better decisions. Charts are updated automatically to reflect your latest financial data for accuracy. Gain a deeper understanding of your money with powerful reporting tools."
    },
    { 
      name: "AI Insights", 
      icon: <Zap className="w-5 h-5 text-[#8bc34a]" />, 
      description: "Get personalized financial recommendations powered by AI to optimize your spending, saving, and investment habits effectively. Discover new ways to improve your financial health. Insights are updated daily to reflect your financial behavior and goals accurately. Make smarter decisions with cutting-edge technology at your fingertips."
    }
  ];

  // Enhanced features with financial content
  const features = [
    {
      icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>,
      title: "Instant Verification",
      description: "Every transaction is verified in real-time with AI-powered fraud detection and account validation",
      color: "from-blue-500 to-cyan-500",
      highlight: "⚡ Real-time"
    },
    {
      icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>,
      title: "Zero Wait Time",
      description: "Transfer funds instantly with no delays. Payments arrive in seconds, not days",
      color: "from-purple-500 to-pink-500",
      highlight: "🚀 Instant"
    },
    {
      icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>,
      title: "Smart Budgeting",
      description: "Automated spending analysis and personalized savings recommendations",
      color: "from-green-500 to-emerald-500",
      highlight: "💳 Intelligent"
    }
  ];

  // Financial testimonials
  const testimonials = [
    {
      text: "PennyPal saved my small business during a cash crunch. Got approved for a line of credit in 15 minutes when traditional banks turned me down!",
      source: "Sarah M.",
      location: "Small Business Owner",
      rating: 5,
      avatar: "https://randomuser.me/api/portraits/women/32.jpg",
      service: "Business Banking"
    },
    {
      text: "As a freelancer, PennyPal is a lifesaver. Instant invoicing, same-day payments, and automatic tax savings - all in one app!",
      source: "Mike Rodriguez",
      location: "Freelance Designer", 
      rating: 5,
      avatar: "https://randomuser.me/api/portraits/men/45.jpg",
      service: "Freelancer Tools"
    },
    {
      text: "Finally found a financial app that actually understands my needs. The AI savings assistant has helped me put away $5,000 this year without thinking about it.",
      source: "Jennifer Wu",
      location: "College Student",
      rating: 5,
      avatar: "https://randomuser.me/api/portraits/women/68.jpg",
      service: "Personal Finance"
    }
  ];

  // Updated stats for financial services
  const stats = [
    { number: "500K+", label: "Happy Users", icon: "👥" },
    { number: "98%", label: "Satisfaction Rate", icon: "⭐" },
    { number: "15sec", label: "Average Transfer Time", icon: "⏱️" },
    { number: "24/7", label: "Customer Support", icon: "🔄" }
  ];

  // Financial services
  const services = [
    {
      name: "Personal Banking",
      description: "Complete financial management for individuals",
      price: "No monthly fees",
      popular: true,
      features: ["2% APY savings", "No-fee checking", "Budget tools", "Credit monitoring"]
    },
    {
      name: "Business Accounts", 
      description: "Banking solutions for businesses",
      price: "Starting at $9/month",
      popular: false,
      features: ["Merchant services", "Payroll tools", "Business credit", "Expense tracking"]
    },
    {
      name: "Investments",
      description: "Automated investing and wealth management",
      price: "1% of AUM",
      popular: false,
      features: ["Robo-advisor", "Tax harvesting", "Retirement planning", "Portfolio tracking"]
    },
    {
      name: "Loans & Credit",
      description: "Competitive lending products",
      price: "Starting at 5.9% APR",
      popular: false,
      features: ["Personal loans", "Business lines", "Credit builder", "Debt consolidation"]
    }
  ];

  return (
    <div className="font-['Inter'] bg-white text-black overflow-x-hidden">
      {/* Enhanced Header with animated elements */}
      {/* <header className="flex justify-between items-center p-4 border-b sticky top-0 bg-white/90 backdrop-blur-md z-50 transition-all duration-300">
        <div className="text-2xl font-bold">
          <span className="bg-gradient-to-r from-blue-500 to-teal-500 text-white px-2 py-1 rounded-lg">PENNY</span>
          <span className="text-orange-600 ml-1">PAL</span>
        </div>
        
        <button 
          className="md:hidden text-gray-600 hover:text-orange-600 transition-colors"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={isMenuOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"} />
          </svg>
        </button>
        
        <nav className={`${isMenuOpen ? 'block' : 'hidden'} md:flex absolute top-full left-0 w-full bg-white/95 backdrop-blur-md shadow-lg md:shadow-none md:static md:bg-transparent md:w-auto p-4 md:p-0`}>
          <div className="flex flex-col md:flex-row gap-6 text-sm font-semibold">
            <a href="#services" className="hover:text-orange-600 transition-colors">Services</a>
            <a href="#features" className="hover:text-orange-600 transition-colors">Features</a>
            <a href="#how-it-works" className="hover:text-orange-600 transition-colors">How it Works</a>
            <a href="#pricing" className="hover:text-orange-600 transition-colors">Pricing</a>
            <a href="#about" className="hover:text-orange-600 transition-colors">About</a>
          </div>
        </nav>
        
        <button className="hidden md:block bg-gradient-to-r from-blue-500 to-teal-500 text-white px-6 py-2 rounded-full text-sm font-semibold hover:shadow-lg hover:scale-105 transition-all">
          Get Started 
        </button>
      </header> */}
          {/* Updated Header with Dropdowns */}
            <header className="bg-white/95 backdrop-blur-lg border-b sticky top-0 bg-white/90 backdrop-blur-md z-50 transition-all duration-300">
              <div className=" mx-auto px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold">
          <span className="bg-gradient-to-r from-blue-500 to-teal-500 text-white px-2 py-1 rounded-lg">PENNY</span>
          <span className="text-orange-600 ml-1">PAL</span>
        </div>
                  <nav className="hidden md:flex items-center space-x-5 text-sm font-semibold justify-end">
                    {/* Features Dropdown */}
                    <div 
                      className="relative"
                      onMouseEnter={() => handleMouseEnter('features')}
                      onMouseLeave={handleMouseLeave}
                    >
                      <a 
                        href="#features" 
                        className="text-gray-800 hover:text-teal-500 transition-colors flex items-center space-x-1"
                      >
                        <span>Features</span>
                        <ChevronDown 
                          className={`w-4 h-4 transition-transform duration-200 ${openDropdown === 'features' ? 'rotate-180' : ''}`} 
                        />
                      </a>
                      {openDropdown === 'features' && (
                        <div className="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 z-50">
                          {dropdownItems.features.map((item, index) => (
                            <a
                              key={index}
                              href={item.href}
                              className="block px-4 py-2 text-gray-700 hover:bg-green-50 hover:text-teal-500 transition-colors"
                            >
                              {item.name}
                            </a>
                          ))}
                        </div>
                      )}
                    </div>
      
                    {/* Pricing Dropdown */}
                    <div 
                      className="relative"
                      onMouseEnter={() => handleMouseEnter('pricing')}
                      onMouseLeave={handleMouseLeave}
                    >
                      <a 
                        href="#pricing" 
                        className="text-gray-800 hover:text-teal-500 transition-colors flex items-center space-x-1"
                      >
                        <span>Pricing</span>
                        <ChevronDown 
                          className={`w-4 h-4 transition-transform duration-200 ${openDropdown === 'pricing' ? 'rotate-180' : ''}`} 
                        />
                      </a>
                      {openDropdown === 'pricing' && (
                        <div className="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 z-50">
                          {dropdownItems.pricing.map((item, index) => (
                            <a
                              key={index}
                              href={item.href}
                              className="block px-4 py-2 text-gray-700 hover:bg-green-50 hover:text-teal-500 transition-colors"
                            >
                              {item.name}
                            </a>
                          ))}
                        </div>
                      )}
                    </div>
      
                    {/* Blog Dropdown */}
                    <div 
                      className="relative"
                      onMouseEnter={() => handleMouseEnter('blog')}
                      onMouseLeave={handleMouseLeave}
                    >
                      <a 
                        href="#blog" 
                        className="text-gray-800 hover:text-teal-500 transition-colors flex items-center space-x-1"
                      >
                        <span>Blog</span>
                        <ChevronDown 
                          className={`w-4 h-4 transition-transform duration-200 ${openDropdown === 'blog' ? 'rotate-180' : ''}`} 
                        />
                      </a>
                      {openDropdown === 'blog' && (
                        <div className="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 z-50">
                          {dropdownItems.blog.map((item, index) => (
                            <a
                              key={index}
                              href={item.href}
                              className="block px-4 py-2 text-gray-700 hover:bg-green-50 hover:text-teal-500 transition-colors"
                            >
                              {item.name}
                            </a>
                          ))}
                        </div>
                      )}
                    </div>
      
                    {/* Support Dropdown */}
                    <div 
                      className="relative"
                      onMouseEnter={() => handleMouseEnter('support')}
                      onMouseLeave={handleMouseLeave}
                    >
                      <a 
                        href="#support" 
                        className="text-gray-800 hover:text-teal-500 transition-colors flex items-center space-x-1"
                      >
                        <span>Support</span>
                        <ChevronDown 
                          className={`w-4 h-4 transition-transform duration-200 ${openDropdown === 'support' ? 'rotate-180' : ''}`} 
                        />
                      </a>
                      {openDropdown === 'support' && (
                        <div className="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 z-50">
                          {dropdownItems.support.map((item, index) => (
                            <a
                              key={index}
                              href={item.href}
                              className="block px-4 py-2 text-gray-700 hover:bg-green-50 hover:text-teal-500 transition-colors"
                            >
                              {item.name}
                            </a>
                          ))}
                        </div>
                      )}
                    </div>
      
                    <button 
                      onClick={handleLoginClick}
                      className="border-2 border-gray-300 text-gray-700 px-6 py-2 rounded-full font-semibold hover:border-teal-500 hover:text-blue-500 transition-all duration-300"
                    >
                      Login
                    </button>
                    <button
                      onClick={handleGetStartedClick}
                      className="hidden md:block bg-gradient-to-r from-blue-500 to-teal-500 text-white px-6 py-2 rounded-full text-sm font-semibold hover:shadow-lg hover:scale-105 transition-all"
                    >
                      Get Started
                    </button>
                  </nav>
                  <button 
                    className="md:hidden text-gray-600 hover:text-[#8bc34a] transition-colors"
                    onClick={() => setIsMenuOpen(!isMenuOpen)}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={isMenuOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"} />
                    </svg>
                  </button>
                  <nav className={`${isMenuOpen ? 'block' : 'hidden'} md:hidden absolute top-full left-0 w-full bg-white/95 backdrop-blur-md shadow-lg p-4`}>
                    <div className="flex flex-col gap-4">
                      {['features', 'pricing', 'blog', 'support'].map((dropdown) => (
                        <div key={dropdown}>
                          <button 
                            className="text-gray-600 hover:text-[#8bc34a] transition-colors font-medium flex items-center justify-between w-full"
                            onClick={() => setOpenDropdown(openDropdown === dropdown ? null : dropdown)}
                          >
                            <span>{dropdown.charAt(0).toUpperCase() + dropdown.slice(1)}</span>
                            <ChevronDown 
                              className={`w-4 h-4 transition-transform duration-200 ${openDropdown === dropdown ? 'rotate-180' : ''}`} 
                            />
                          </button>
                          {openDropdown === dropdown && (
                            <div className="mt-2 flex flex-col gap-2">
                              {dropdownItems[dropdown].map((item, index) => (
                                <a
                                  key={index}
                                  href={item.href}
                                  className="block px-4 py-2 text-gray-700 hover:bg-green-50 hover:text-[#8bc34a] transition-colors"
                                >
                                  {item.name}
                                </a>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                      <button 
                      onClick={handleLoginClick}
                      className="border-2 border-gray-300 text-gray-700 px-6 py-2 rounded-full font-semibold hover:border-teal-500 hover:text-blue-500 transition-all duration-300"
                    >
                      Login
                    </button>
                    <button
                      onClick={handleGetStartedClick}
                      className="hidden md:block bg-gradient-to-r from-blue-500 to-teal-500 text-white px-6 py-2 rounded-full text-sm font-semibold hover:shadow-lg hover:scale-105 transition-all"
                    >
                      Get Started
                    </button>
                    </div>
                  </nav>
                </div>
              </div>
            </header>

      {/* Enhanced Hero Section */}
      <section className="grid grid-cols-1 lg:grid-cols-2 border-b bg-gradient-to-br from-blue-50 via-cyan-50 to-purple-50 relative overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-20 w-72 h-72 bg-blue-200/30 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-96 h-96 bg-purple-200/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        <div className="p-10 flex flex-col justify-center relative z-10">
          <div className="text-xs font-bold mb-6 px-4 py-2 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-full w-max animate-bounce">
            🔥 JOIN 500,000+ USERS REVOLUTIONIZING FINANCE
          </div>
          
          <h1 className="text-5xl md:text-6xl font-black leading-tight mb-6">
            <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600 bg-clip-text text-transparent">
              Banking That
            </span>
            <br />
            <span className="text-gray-900">Actually Works For You</span>
          </h1>
          
          <p className="text-gray-700 text-xl mb-8 max-w-lg leading-relaxed">
            Skip the bank queues, hidden fees, and paperwork. Get instant access to modern banking, smart budgeting, and lightning-fast transfers — all in one revolutionary app.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 mb-8">
            <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-full text-lg font-semibold hover:shadow-xl hover:scale-105 transition-all duration-300 flex items-center gap-3">
              <span>Start Free Trial</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </button>
            <button className="bg-white text-gray-700 px-8 py-4 rounded-full text-lg font-semibold border-2 border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-300">
              Watch Demo (2min)
            </button>
          </div>

          {/* Trust indicators */}
          <div className="flex items-center gap-6 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="flex -space-x-2">
                {[1,2,3,4].map(i => (
                  <img key={i} src={`https://randomuser.me/api/portraits/women/${20+i}.jpg`} className="w-8 h-8 rounded-full border-2 border-white" alt="" />
                ))}
              </div>
              <span>500K+ users trust us</span>
            </div>
            <div className="flex items-center gap-1">
              <span>⭐ 4.9/5</span>
              <span>(3,847 reviews)</span>
            </div>
          </div>
        </div>

        {/* Enhanced floating card */}
        <div className="relative flex items-center justify-center p-10">
          <div className="relative">
            <img 
              src="https://images.unsplash.com/photo-**********-6726b3ff858f?auto=format&fit=crop&w=800&q=80" 
              className="rounded-3xl shadow-2xl w-full max-w-md h-96 object-cover" 
              alt="Modern banking" 
            />
            
            {/* Floating account card */}
            <div className="absolute top-8 right-8 bg-white rounded-2xl shadow-xl p-6 w-80 animate-float">
              <div className="flex gap-4 items-center mb-4">
                <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-teal-500 flex items-center justify-center text-white text-2xl font-bold">
                  PP
                </div>
                <div>
                  <div className="font-bold text-lg">Smart Account</div>
                  <p className="text-sm text-gray-600">PennyPal Premium</p>
                  <div className="flex items-center gap-2 text-sm">
                    <span className="text-green-500">● Active</span>
                    <span>2.5% APY</span>
                  </div>
                </div>
              </div>
              
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-semibold text-gray-700">Available Balance:</span>
                  <span className="text-green-600 font-bold">$8,742.50</span>
                </div>
                <button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-xl font-semibold hover:shadow-lg transition-all">
                  Send Money
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white border-b">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            {stats.map((stat, index) => (
              <div key={index} className="group">
                <div className="text-4xl mb-2">{stat.icon}</div>
                <div className="text-3xl md:text-4xl font-black text-gray-900 mb-1 group-hover:text-blue-600 transition-colors">
                  {stat.number}
                </div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Enhanced Features Section */}
      <section className="py-20 border-b bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-black mb-6 text-gray-900">
              Why PennyPal is Different
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We've rebuilt banking from the ground up to work for you, not financial institutions
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {features.map((feature, i) => (
              <div 
                key={i} 
                className="group bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-blue-200 hover:-translate-y-2"
              >
                <div className="mb-4">
                  <span className="text-xs font-bold bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 px-3 py-1 rounded-full">
                    {feature.highlight}
                  </span>
                </div>
                
                <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${feature.color} flex items-center justify-center mb-6 text-white group-hover:scale-110 transition-transform duration-300`}>
                  {feature.icon}
                </div>
                
                <h3 className="text-2xl font-bold mb-4 group-hover:text-blue-600 transition-colors">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

 <section className="py-20 border-b bg-gradient-to-br from-blue-100 to-purple-50">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-black mb-6 text-gray-900">
              Discover Our Key Features
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Explore the powerful tools PennyPal offers to transform your financial management
            </p>
          </div>
          
          <div className="relative max-w-4xl mx-auto">
            <div className="bg-white rounded-3xl p-8 md:p-12 shadow-2xl">
              <div className="flex items-center justify-center mb-6 gap-4">
                <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-blue-500 to-teal-500 flex items-center justify-center text-white">
                  {keyFeatures[currentFeature].icon}
                </div>
                <h3 className="text-2xl font-bold text-gray-900">
                  {keyFeatures[currentFeature].name}
                </h3>
              </div>
              
              <p className="text-lg text-gray-800 leading-relaxed text-center">
                {keyFeatures[currentFeature].description}
              </p>
            </div>
            
            {/* Dots indicator */}
            <div className="flex justify-center mt-8 space-x-2">
              {keyFeatures.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentFeature(index)}
                  className={`w-3 h-3 rounded-full transition-all ${index === currentFeature ? 'bg-blue-500 w-8' : 'bg-gray-300'}`}
                />
              ))}
            </div>
          </div>
        </div>
      </section>
      
      {/* Services Section */}
      <section id="services" className="py-20 border-b bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-black mb-6 text-gray-900">
              Complete Financial Solutions
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              From personal banking to business accounts, investments to loans — we've got you covered
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {services.map((service, index) => (
              <div 
                key={index}
                className={`relative bg-white rounded-2xl p-6 border-2 hover:border-blue-300 transition-all duration-300 hover:shadow-xl ${service.popular ? 'border-blue-500 ring-4 ring-blue-100' : 'border-gray-200'}`}
              >
                {service.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-1 rounded-full text-sm font-bold">
                    Most Popular
                  </div>
                )}
                
                <h3 className="text-xl font-bold mb-2">{service.name}</h3>
                <p className="text-gray-600 mb-4">{service.description}</p>
                <div className="text-2xl font-black text-blue-600 mb-4">{service.price}</div>
                
                <ul className="space-y-2 mb-6">
                  {service.features.map((feature, i) => (
                    <li key={i} className="flex items-center text-sm text-gray-600">
                      <svg className="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      {feature}
                    </li>
                  ))}
                </ul>
                
                <button className={`w-full py-3 rounded-xl font-semibold transition-all ${service.popular ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white hover:shadow-lg' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}>
                  Get Started
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>


      {/* Enhanced Testimonials with carousel */}
      <section className="py-20 border-b bg-gradient-to-br from-blue-50 to-purple-50">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-black mb-6 text-gray-900">
              Real Stories, Real Results
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              See how PennyPal is transforming financial experiences across the country
            </p>
          </div>
          
          <div className="relative max-w-4xl mx-auto">
            <div className="bg-white rounded-3xl p-8 md:p-12 shadow-2xl">
              <div className="flex items-center space-x-1 mb-6 justify-center">
                {[...Array(testimonials[currentTestimonial].rating)].map((_, i) => (
                  <svg key={i} xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              
              <p className="text-2xl text-gray-800 leading-relaxed mb-8 text-center italic">
                "{testimonials[currentTestimonial].text}"
              </p>
              
              <div className="flex items-center justify-center">
                <img 
                  src={testimonials[currentTestimonial].avatar} 
                  className="w-16 h-16 rounded-full mr-4 ring-4 ring-blue-100" 
                  alt={testimonials[currentTestimonial].source}
                />
                <div>
                  <div className="font-bold text-lg text-gray-900">
                    {testimonials[currentTestimonial].source}
                  </div>
                  <div className="text-gray-600">
                    {testimonials[currentTestimonial].location} • {testimonials[currentTestimonial].service}
                  </div>
                </div>
              </div>
            </div>
            
            {/* Dots indicator */}
            <div className="flex justify-center mt-8 space-x-2">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentTestimonial(index)}
                  className={`w-3 h-3 rounded-full transition-all ${index === currentTestimonial ? 'bg-blue-500 w-8' : 'bg-gray-300'}`}
                />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced CTA Section */}
      <section className="py-20 border-b bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-4xl md:text-6xl font-black mb-6">
            Ready to Experience Banking Without Limits?
          </h2>
          <p className="text-xl md:text-2xl mb-10 max-w-3xl mx-auto opacity-90">
            Join 500,000+ users who've already discovered a better way to manage money
          </p>
          
          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-8">
            <button className="bg-white text-blue-600 px-10 py-4 rounded-full text-xl font-bold hover:bg-gray-100 hover:scale-105 transition-all duration-300 shadow-xl">
              Start Your Free Trial
            </button>
            <button className="bg-transparent border-2 border-white text-white px-10 py-4 rounded-full text-xl font-bold hover:bg-white/10 transition-all duration-300">
              Schedule a Demo
            </button>
          </div>
          
          <p className="text-sm opacity-75">✨ No credit card required • Cancel anytime • 30-day money-back guarantee</p>
        </div>
      </section>

      {/* Enhanced Footer */}
      <footer className="bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white px-6 py-16">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-12 mb-12">
            <div className="md:col-span-2">
              <div className="text-3xl font-bold mb-4">
                <span className="bg-gradient-to-r from-blue-500 to-teal-500 text-white px-2 py-1 rounded-lg">PENNY</span>
                <span className="text-orange-600 ml-1">PAL</span>
              </div>
              <p className="text-gray-400 mb-6 max-w-md">
                Revolutionizing banking by putting customers first. Experience the future of financial services today.
              </p>
              <div className="flex gap-4">
                {[
  {
    platform: 'linkedin',
    icon: <Linkedin className="w-5 h-5 text-white" />,
    url: 'https://www.linkedin.com/company/pennypal',
    hoverColor: 'hover:bg-blue-700',
    backgroundColor: 'bg-blue-800'
  },
  {
    platform: 'twitter',
    icon: <Twitter className="w-5 h-5 text-white" />,
    url: 'https://twitter.com/pennypal',
    hoverColor: 'hover:bg-sky-500'
  },
  {
    platform: 'instagram',
    icon: <Instagram className="w-5 h-5 text-white" />,
    url: 'https://www.instagram.com/pennypal',
    hoverColor: 'hover:bg-pink-500'
  },
  {
    platform: 'facebook',
    icon: <Facebook className="w-5 h-5 text-white" />,
    url: 'https://www.facebook.com/pennypal',
    hoverColor: 'hover:bg-blue-800'
  }
].map(({ platform, icon, url, hoverColor }, i) => (
  <a
    key={i}
    href={url}
    target="_blank"
    rel="noopener noreferrer"
    className={`bg-gray-800 p-3 rounded-full transition-all cursor-pointer hover:scale-110 ${hoverColor}`}
    aria-label={`Visit our ${platform} page`}
  >
    {icon}
  </a>
))}

                    </div>
            </div>
            
            {[
              {
                title: "Services",
                links: ["Personal Banking", "Business Accounts", "Investments", "Loans", "Credit Cards", "Budgeting"]
              },
              {
                title: "Company", 
                links: ["About Us", "Careers", "Press", "Blog", "Investors", "Partners"]
              },
              {
                title: "Support",
                links: ["Help Center", "Contact Us", "FAQs", "Community", "Security", "FDIC Insurance"]
              }
            ].map((section, i) => (
              <div key={i}>
                <h3 className="font-bold mb-6 text-lg">{section.title}</h3>
                <ul className="space-y-3">
                  {section.links.map((link, j) => (
                    <li key={j}>
                      <a href="#" className="text-gray-400 hover:text-white transition-colors hover:underline">
                        {link}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
          
          <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-500 text-sm mb-4 md:mb-0">
              © 2025 PennyPal. All rights reserved. | Privacy Policy | Terms of Service
            </div>
            <div className="text-gray-500 text-sm">
              Member FDIC • Equal Housing Lender
            </div>
          </div>
        </div>
      </footer>
      
      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-10px); }
        }
        .animate-float {
          animation: float 3s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
};

export default LandingPage;