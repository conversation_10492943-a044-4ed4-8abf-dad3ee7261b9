import React from "react";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Button,
  IconButton,
  InputAdornment,
  FormControl,
  InputLabel,
  LinearProgress,
  CircularProgress,
  TextField,
  Table,
  TableHead,
  Tooltip,
  Select,
  Grid2,
  Paper,
  ListSubheader,
  TableBody,
  TableRow,
  TableCell,
  Tabs,
  Tab,
  MenuItem,
  Radio,
  Box,
  Typography,
  FormControlLabel,
  Checkbox,
} from "@mui/material";

import {
  setCurrentTab,
  setReceiptUploadModal,
  // setSelectedFile,
  setErrorMessage,
  setShowError,
  // setFileMetadata,
  setBlinkError,
  setIsReceiptModalOpen,
  setSelectedReceipt,
  // setSelectedTransaction,
  setEditingField,
  setEditedValue,
  setEditedItemIndex,
  setSelectedDate,
  setUploadProgress,
  setIsUploading,
  setIsProcessing,
  // setIsPopupVisible,
  // setReceiptNewTransaction,
  uploadReceiptRequest,
  saveReceiptRequest,
  addNewTransaction,
  fetchReceiptTransactionIdsRequest,
  fetchReceiptDetailsRequest,
  // addTransactionRequest,
  updateReceiptField,
  setJsonResponse,
  setIsMatchingTransactionAdd,
} from "@pp-logic/redux/receiptSlice";

const CurrentReceiptTabView = () => {
  /*
        
        */
  const {
    errorMessage,
    jsonResponse,
    currentTab,
    receiptUploadModal,
    showError,
    blinkError,
    isReceiptModalOpen,
    selectedReceipt,
    // selectedFile,
    fileMetadata,
    // selectedTransaction,
    uploadPopupWidth,
    editingField,
    editedValue,
    editedItemIndex,
    selectedDate,
    uploadProgress,
    isUploading,
    isProcessing,
    isPopupVisible,

    //  receiptNewTransaction,
    receiptTransactionIds,
  } = useSelector((state) => state.receipts);
  // Render editable summary field
  const renderEditableSummaryField = (field) => {
    if (editingField === field) {
      return (
        <input
          type="text"
          value={editedValue || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          autoFocus
          style={{
            width: "100%",
            padding: "5px",
            border: "1px solid #ccc",
            borderRadius: "4px",
          }}
        />
      );
    }
    const value = jsonResponse?.[field] || "-";
    if (typeof value === "number") {
      return value.toFixed(2);
    }
    return value.replace(/[()$]/g, "");
  };

  // Handle field change
  const handleChange = (e) => {
    dispatch(setEditedValue(e.target.value));
  };
  // Handle field blur
  const handleBlur = () => {
    if (editedItemIndex !== null) {
      dispatch(
        updateReceiptField({
          field: editingField,
          value: editedValue,
          itemIndex: editedItemIndex,
        })
      );
    } else {
      dispatch(
        updateReceiptField({
          field: editingField,
          value: editedValue,
          itemIndex: null,
        })
      );
    }
    dispatch(setEditingField(null));
    dispatch(setEditedItemIndex(null));
  };
  // Render editable field
  const renderEditableField = (field, index) => {
    if (editingField === field && editedItemIndex === index) {
      return (
        <input
          type="text"
          value={editedValue || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          autoFocus
          style={{
            width: "100%",
            padding: "5px",
            border: "1px solid #ccc",
            borderRadius: "4px",
          }}
        />
      );
    }
    const value = jsonResponse?.Items?.[index]?.[field] || "-";
    if (typeof value === "number") {
      return value.toFixed(2);
    }
    return value.replace(/[()$]/g, "");
  };

  return (
    <DialogContent
      sx={{
        padding: "16px",
        height: "calc(80vh - 150px)",
        overflowY: "auto",
        display: "flex",
        flexDirection: "column",
        width: "100%",
      }}
    >
      <Box
        sx={{
          textAlign: "center",
          marginBottom: "16px",
          padding: "16px",
          borderBottom: "1px solid #8BC34A",
          borderTop: "1px solid #8BC34A",
          backgroundColor: "#f6fbf3",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          width: "90%",
        }}
      >
        <Typography
          variant="h5"
          sx={{ fontWeight: "bold", textAlign: "center", width: "100%" }}
        >
          {jsonResponse?.MerchantName || " "}
        </Typography>
        <Typography variant="body1" sx={{ textAlign: "center", width: "100%" }}>
          {jsonResponse?.MerchantAddress || " "}
        </Typography>
        <Typography variant="body2" sx={{ textAlign: "center", width: "100%" }}>
          {jsonResponse?.MerchantPhoneNumber || " "}
        </Typography>
      </Box>

      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <Grid2
          container
          spacing={2}
          sx={{ marginBottom: "24px", justifyContent: "center", width: "100%" }}
        >
          <Grid2
            item
            xs={6}
            sx={{
              textAlign: "center",
              display: "flex",
              justifyContent: "center",
            }}
          >
            <Paper
              sx={{
                p: 2,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: "#f0f7e6",
                width: "100%",
                textAlign: "center",
              }}
            >
              <CalendarTodayIcon sx={{ mr: 1, color: "#8BC34A" }} />
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                }}
                onDoubleClick={() => handleFieldDoubleClick("TransactionDate")}
              >
                <Typography variant="caption" color="text.secondary">
                  Date
                </Typography>
                {editingField === "TransactionDate" ? (
                  <DatePicker
                    value={selectedDate || jsonResponse?.TransactionDate}
                    onChange={handleDateChange}
                    renderInput={(params) => (
                      <input
                        {...params}
                        style={{
                          width: "100%",
                          padding: "5px",
                          border: "1px solid #ccc",
                          borderRadius: "4px",
                          textAlign: "center",
                        }}
                      />
                    )}
                    onBlur={handleBlur}
                    autoFocus
                  />
                ) : (
                  <Typography variant="body1">
                    {selectedDate
                      ? selectedDate.toISOString().split("T")[0]
                      : jsonResponse?.TransactionDate || "-"}
                  </Typography>
                )}
              </Box>
            </Paper>
          </Grid2>
          <Grid2
            item
            xs={6}
            sx={{
              textAlign: "center",
              display: "flex",
              justifyContent: "center",
            }}
          >
            <Paper
              sx={{
                p: 2,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: "#f0f7e6",
                width: "100%",
                textAlign: "center",
              }}
            >
              <AccessTimeIcon sx={{ mr: 1, color: "#8BC34A" }} />
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                }}
              >
                <Typography variant="caption" color="text.secondary">
                  Time
                </Typography>
                <Typography variant="body1">
                  {jsonResponse?.TransactionTime || "-"}
                </Typography>
              </Box>
            </Paper>
          </Grid2>
        </Grid2>
      </LocalizationProvider>

      <Box sx={{ flex: 1, width: "100%" }}>
        <Paper
          elevation={3}
          sx={{
            mb: 3,
            border: "1px solid #e0e0e0",
            borderRadius: "8px",
            overflow: "hidden",
          }}
        >
          <Table sx={{ minWidth: "100%" }}>
            <TableHead>
              <TableRow sx={{ backgroundColor: "#8BC34A" }}>
                <TableCell sx={{ color: "#333", fontWeight: "bold" }}>
                  Item
                </TableCell>
                <TableCell sx={{ color: "#333", fontWeight: "bold" }}>
                  Qty
                </TableCell>
                <TableCell sx={{ color: "#333", fontWeight: "bold" }}>
                  Price
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {jsonResponse?.Items && jsonResponse.Items.length > 0 ? (
                jsonResponse.Items.map((item, index) => (
                  <TableRow
                    key={index}
                    sx={{
                      "&:hover": { backgroundColor: "#f5f5f5" },
                    }}
                  >
                    <TableCell
                      sx={{ cursor: "pointer" }}
                      onDoubleClick={() => handleDoubleClick("Name", index)}
                    >
                      {renderEditableField("Name", index)}
                    </TableCell>
                    <TableCell
                      sx={{ cursor: "pointer" }}
                      onDoubleClick={() => handleDoubleClick("Quantity", index)}
                    >
                      {renderEditableField("Quantity", index)}
                    </TableCell>
                    <TableCell
                      sx={{ cursor: "pointer" }}
                      onDoubleClick={() =>
                        handleDoubleClick("TotalPrice", index)
                      }
                    >
                      {renderEditableField("TotalPrice", index)}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} sx={{ textAlign: "center", py: 2 }}>
                    No items found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </Paper>

        <Paper sx={{ p: 2, backgroundColor: "#f9f9f9" }}>
          <Grid2 container>
            <Grid2 item xs={6}></Grid2>
            <Grid2 item xs={6}>
              <Box
                sx={{ display: "flex", justifyContent: "space-between", mb: 1 }}
              >
                <Typography variant="body1">Subtotal:</Typography>
                <Typography
                  variant="body1"
                  sx={{
                    cursor: "pointer",
                    textAlign: "right",
                    minWidth: "100px",
                  }}
                  onDoubleClick={() => handleFieldDoubleClick("Subtotal")}
                >
                  {renderEditableSummaryField("Subtotal")}
                </Typography>
              </Box>
              <Box
                sx={{ display: "flex", justifyContent: "space-between", mb: 1 }}
              >
                <Typography variant="body1">Tax:</Typography>
                <Typography
                  variant="body1"
                  sx={{
                    cursor: "pointer",
                    textAlign: "right",
                    minWidth: "100px",
                  }}
                  onDoubleClick={() => handleFieldDoubleClick("Tax")}
                >
                  {renderEditableSummaryField("Tax")}
                </Typography>
              </Box>
              <Divider sx={{ my: 1 }} />
              <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                <Typography variant="body1" sx={{ fontWeight: "bold" }}>
                  Total:
                </Typography>
                <Typography
                  variant="body1"
                  sx={{
                    fontWeight: "bold",
                    cursor: "pointer",
                    textAlign: "right",
                    minWidth: "450px",
                  }}
                  onDoubleClick={() => handleFieldDoubleClick("Total")}
                >
                  {renderEditableSummaryField("Total")}
                </Typography>
              </Box>
            </Grid2>
          </Grid2>
        </Paper>
      </Box>
      {jsonResponse?.matchingTransactions &&
      Array.isArray(jsonResponse.matchingTransactions) &&
      jsonResponse.matchingTransactions.length > 0 ? (
        <Box
          sx={{
            color: "green",
            marginTop: "16px",
            padding: "8px",
            borderRadius: "4px",
            backgroundColor: "#f0f7f0",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            cursor: "pointer",
          }}
          onClick={() => dispatch(setCurrentTab("matchingTransaction"))}
        ></Box>
      ) : (
        <Box
          sx={{
            color: "red",
            marginTop: "16px",
            padding: "8px",
            borderRadius: "4px",
            backgroundColor: "#fff0f0",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        ></Box>
      )}
      <Box
        sx={{
          mt: "auto",
          marginBottom: "40px",
          display: "flex",
          justifyContent: "center",
          width: "100%",
        }}
      >
        <Button
          onClick={handleSave}
          color="primary"
          variant="contained"
          disabled={isUploading || isProcessing} // Enable button unless uploading or processing
          sx={{
            fontFamily: "Roboto, sans-serif",
            mt: 2,
            backgroundColor: "#8BC34A",
            "&:hover": {
              backgroundColor: "#7CB342",
            },
            width: "100%",
            maxWidth: "100%",
            borderRadius: "4px",
            textAlign: "center",
          }}
          fullWidth
        >
          Submit
        </Button>
      </Box>
    </DialogContent>
  );
};

export default CurrentReceiptTabView;
