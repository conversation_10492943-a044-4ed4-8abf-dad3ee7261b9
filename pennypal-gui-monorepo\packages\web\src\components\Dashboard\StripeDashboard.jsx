import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { loadStripe } from '@stripe/stripe-js';
import { axiosInstance } from  'logic/api/axiosConfig';
import { getCurrentUserId } from '../../utils/AuthUtil';
import {
  fetchPaymentData,
  startSubscription,
  updateSubscription,
  setDefaultPaymentMethod
} from 'logic/epics/paymentEpic';
import {
  Elements,
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { FiChevronRight, FiChevronDown, FiCheck, FiCreditCard, FiCalendar, FiDollarSign } from 'react-icons/fi';
import PaymentLoader from '../load/PaymentLoader';

const stripePromise = loadStripe('pk_test_51RClAOQ8d4Ag6P7HC740kc7XEwbKiuKx5K3O2S8wmppDbxMz22C3ABjGFLhROwHz6UcOF0gpYdip3NLni2GzEho800D0rMU61T');

const CardSection = ({ darkMode }) => (
  <div className="p-4 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
    <label className={`block text-sm font-semibold mb-3 ${darkMode ? 'text-gray-200' : 'text-gray-700'} flex items-center`}>
      <FiCreditCard className="mr-2" />
      Enter Card Information
    </label>
    <div className="bg-white dark:bg-gray-900 p-3 rounded-lg border border-gray-300 dark:border-gray-600 focus-within:ring-2 focus-within:ring-[#8bc34a] dark:focus-within:ring-gray-500 transition-all duration-200">
      <CardElement 
        className="w-full"
        options={{
          style: {
            base: {
              fontSize: '16px',
              color: darkMode ? '#e5e7eb' : '#374151',
              '::placeholder': {
                color: darkMode ? '#9ca3af' : '#6b7280',
              },
            },
          },
        }}
      />
    </div>
  </div>
);

const StripeDashboardInner = ({ darkMode }) => {
  const dispatch = useDispatch();
  const cache = useSelector(state => state.cache);
  const userId = getCurrentUserId();

  const elements = useElements();
  const stripe = useStripe();

  const [clientSecret, setClientSecret] = useState('');
  const [message, setMessage] = useState('');
  const [priceId, setPriceId] = useState('');
  const [frequency, setFrequency] = useState('');

  // Get data from cache
  const subscriptionInfo = cache?.paymentSubscription;
  const savedCards = cache?.paymentMethods || [];
  const products = cache?.paymentProducts || [];
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('');
  const [invoicePreview, setInvoicePreview] = useState(null);
  const [isInvoiceOpen, setIsInvoiceOpen] = useState(true);
  const [isLoadingInvoice, setIsLoadingInvoice] = useState(false);
  const [isDisabled, setIsDisabled] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  // States for Start Subscription button
  const [startSubscriptionLoading, setStartSubscriptionLoading] = useState(false);
  const [startSubscriptionSuccess, setStartSubscriptionSuccess] = useState('');
  const [startSubscriptionError, setStartSubscriptionError] = useState('');
  // States for Save (combined Update Subscription and Set Default Payment)
  const [saveLoading, setSaveLoading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState('');
  const [saveError, setSaveError] = useState('');
  // States for Cancel/Resume Subscription
  const [cancelResumeSubscriptionLoading, setCancelResumeSubscriptionLoading] = useState(false);
  const [cancelResumeSubscriptionSuccess, setCancelResumeSubscriptionSuccess] = useState('');
  const [cancelResumeSubscriptionError, setCancelResumeSubscriptionError] = useState('');

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch setup intent (still direct API call as it's not cached)
        try {
          const setupIntentRes = await axiosInstance.post('/pennypal/api/v1/payment/create-setup-intent', {
            userId,
          });

          const setupIntentData = setupIntentRes.data;
          setClientSecret(setupIntentData.clientSecret);
        } catch (error) {
          throw new Error('Failed to fetch setup intent');
        }

        // Dispatch action to fetch all payment data using cache
        dispatch(fetchPaymentData(userId));

      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchData();
    }
  }, [userId, dispatch]);

  const fetchAllData = async () => {
    setLoading(true);
    setError(null);
    try {
      const setupIntentRes = await axiosInstance.post('/pennypal/api/v1/payment/create-setup-intent', { userId });
      setClientSecret(setupIntentRes.data.clientSecret);

      // Dispatch action to fetch all payment data using cache
      dispatch(fetchPaymentData(userId));
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (userId) {
      fetchAllData();
    }
  }, [userId]);

  useEffect(() => {
    if (subscriptionInfo?.cancelAt !== "null") {
      setIsDisabled(true);
    } else {
      setIsDisabled(false);
    }
  }, [subscriptionInfo?.cancelAt]);

  const handleSubmit = async (e) => {
    setStartSubscriptionLoading(true);
    e.preventDefault();
    setMessage('');
    setStartSubscriptionSuccess('');
    setStartSubscriptionError('');

    let paymentMethodId = selectedPaymentMethod;

    if (!paymentMethodId) {
      const cardElement = elements.getElement(CardElement);
      const { setupIntent, error } = await stripe.confirmCardSetup(clientSecret, {
        payment_method: { card: cardElement }
      });

      if (error) {
        setStartSubscriptionError(error.message);
        setStartSubscriptionLoading(false);
        return;
      }

      paymentMethodId = setupIntent.payment_method;
    }

    try {
      // Dispatch action to start subscription using epic
      dispatch(startSubscription({
        userId,
        paymentMethodId,
        priceId,
        frequency
      }));
      setStartSubscriptionSuccess('Subscription started successfully.');
    } catch (err) {
      setStartSubscriptionError('Subscription failed.');
    } finally {
      setStartSubscriptionLoading(false);
    }
  };

  const handleSave = async () => {
    setSaveLoading(true);
    setSaveSuccess('');
    setSaveError('');

    try {
      let hasChanges = false;
      let paymentMethodId = selectedPaymentMethod;
      
      const originalPriceId = subscriptionInfo?.priceId;
      
      // Pick from paymentMethods with isDefault === true
      const originalDefaultPaymentMethod = savedCards.find((card) => card.isDefault === 'true')?.id;
      
      const priceChanged = priceId && priceId !== originalPriceId;
      
      if (!paymentMethodId && selectedPaymentMethod === '') {
        const cardElement = elements.getElement(CardElement);
        const { setupIntent, error } = await stripe.confirmCardSetup(clientSecret, {
          payment_method: { card: cardElement }
        });
        
        if (error) {
          setSaveError(error.message);
          setSaveLoading(false);
          return;
        }
        
        paymentMethodId = setupIntent.payment_method;
        hasChanges = true;
      }
      
      if (priceChanged) {
        // Dispatch action to update subscription using epic
        dispatch(updateSubscription({
          userId,
          subscriptionId: subscriptionInfo.subscriptionId,
          priceId
        }));
        hasChanges = true;
      }

      if (paymentMethodId && paymentMethodId !== originalDefaultPaymentMethod && paymentMethodId !== '') {
        // Dispatch action to set default payment method using epic
        dispatch(setDefaultPaymentMethod({
          userId,
          paymentMethodId
        }));
        hasChanges = true;
      }
      
      if (!hasChanges) {
        setSaveError('No changes to save.');
      } else {
        setSaveSuccess('Saved successfully!');
        setTimeout(() => {
          fetchAllData();
          setSaveSuccess('');
        }, 2000);
      }
    } catch (err) {
      setSaveError(err.message || 'Failed to save changes.');
    } finally {
      setSaveLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    setCancelResumeSubscriptionLoading(true);
    setCancelResumeSubscriptionSuccess('');
    setCancelResumeSubscriptionError('');
    try {
      const res = await axiosInstance.post('/pennypal/api/v1/payment/cancel-subscription', {
        userId,
        subscriptionId: subscriptionInfo.subscriptionId,
        priceId
      });
      const msg = await res.data;
      setCancelResumeSubscriptionSuccess(msg || 'Subscription canceled successfully.');
    } catch (err) {
      setCancelResumeSubscriptionError(err.message || 'Failed to cancel subscription.');
    } finally {
      setCancelResumeSubscriptionLoading(false);
    }
  };

  const handleResumeSubscription = async () => {
    setCancelResumeSubscriptionLoading(true);
    setCancelResumeSubscriptionSuccess('');
    setCancelResumeSubscriptionError('');
    try {
      const res = await axiosInstance.post('/pennypal/api/v1/payment/resume-subscription', {
        userId,
        subscriptionId: subscriptionInfo.subscriptionId,
        priceId
      });
      const msg = await res.data;
      setCancelResumeSubscriptionSuccess(msg || 'Subscription resumed successfully.');
    } catch (err) {
      setCancelResumeSubscriptionError(err.message || 'Failed to resume subscription.');
    } finally {
      setCancelResumeSubscriptionLoading(false);
    }
  };

  useEffect(() => {
    const fetchInvoicePreview = async () => {
      if (!priceId || !subscriptionInfo?.subscriptionId || !subscriptionInfo?.customerId) {
        setInvoicePreview(null);
        return;
      }

      setIsLoadingInvoice(true);
      try {
        const res = await axiosInstance.post('/pennypal/api/v1/payment/generate-invoice-preview', {
          userId,
          customerId: subscriptionInfo.customerId,
          subscriptionId: subscriptionInfo.subscriptionId,
          priceId
        });

        const previewData = await res.data;
        setInvoicePreview(previewData);
      } catch (err) {
        console.error('Failed to fetch invoice preview');
        setInvoicePreview(null);
      } finally {
        setIsLoadingInvoice(false);
      }
    };

    fetchInvoicePreview();
  }, [priceId]);

  const matchedProduct = products.find(
    product => product.priceId === subscriptionInfo.priceId
  );

  useEffect(() => {
    const defaultCard = savedCards.find((card) => card.isDefault === 'true');
    if (defaultCard) {
      setSelectedPaymentMethod(defaultCard.id);
    }
  }, [savedCards]);

  const handlePlanSelect = (plan) => {
    setPriceId(plan.priceId);
    setFrequency(plan.frequencyCharge || 'monthly');
  };

  if (loading) {
    return (
      <div className={`min-h-screen w-full p-8 flex flex-col justify-center items-center ${darkMode ? 'bg-gray-900' : 'bg-white'}`}>
        <PaymentLoader darkMode={darkMode} />
      </div>
    );
  }

  if (error) {
    return (
      <div className={`w-full max-w-4xl mx-auto p-6 ${darkMode ? 'bg-gray-900' : 'bg-white'}`}>
        <div className={`border px-6 py-4 rounded-xl relative shadow-lg ${darkMode ? 'bg-red-900/20 border-red-800 text-red-200' : 'bg-red-50 border-red-200 text-red-800'}`} role="alert">
          <strong className="font-bold">Error:</strong>
          <span className="block sm:inline"> {error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-gray-900' : 'bg-white'} transition-colors duration-300`}>
      <div className="max-w-6xl mx-auto p-4 space-y-6">
        {['active', 'trialing'].includes(subscriptionInfo?.status) ? (
          <>

            {/* Subscription Status Card */}
            <div className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-xl border shadow-lg p-6 transition-all duration-300 hover:shadow-xl`}>
              <h2 className={`text-xl font-bold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-800'} flex items-center`}>
                <FiCheck className="mr-2 text-green-500" />
                Current Subscription
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700/50' : 'bg-gray-50'} border ${darkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                  <div className="flex items-center justify-between">
                    <span className={`font-medium text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Plan</span>
                  </div>
                  <p className={`text-lg font-bold mt-1 ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                    {matchedProduct?.productName || 'Unknown'}
                  </p>
                </div>

                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700/50' : 'bg-gray-50'} border ${darkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                  <div className="flex items-center justify-between">
                    <span className={`font-medium text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Status</span>
                  </div>
                  <div className="mt-1">
                    {subscriptionInfo?.status === 'active' && !isDisabled && (
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-green-100 text-green-800 dark:bg-green-800/20 dark:text-green-400">
                        Active
                      </span>
                    )}
                    {subscriptionInfo?.status === 'trialing' && (
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-purple-100 text-purple-800 dark:bg-purple-800/20 dark:text-purple-400">
                        Trialing
                      </span>
                    )}
                    {isDisabled && (
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-red-100 text-red-800 dark:bg-red-800/20 dark:text-red-400">
                        To Be Canceled
                      </span>
                    )}
                  </div>
                </div>

                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700/50' : 'bg-gray-50'} border ${darkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                  <div className="flex items-center justify-between">
                    <span className={`font-medium text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Amount</span>
                    <FiDollarSign className={`w-4 h-4 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                  </div>
                  <p className={`text-lg font-bold mt-1 ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                    ${(+subscriptionInfo.amount / 100).toFixed(2)}
                  </p>
                </div>

                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700/50' : 'bg-gray-50'} border ${darkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                  <div className="flex items-center justify-between">
                    <span className={`font-medium text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                      {isDisabled ? 'Cancels On' : 'Next Billing'}
                    </span>
                    <FiCalendar className={`w-4 h-4 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                  </div>
                  <p className={`text-lg font-bold mt-1 ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                    {isDisabled
                      ? subscriptionInfo.cancelAt.substring(0, 10)
                      : subscriptionInfo.currentPeriodEnd.substring(0, 10)}
                  </p>
                </div>
              </div>

              <div className={`mt-4 p-3 rounded-lg ${darkMode ? 'bg-blue-900/20 border-blue-800' : 'bg-blue-50 border-blue-200'} border`}>
                <div className="flex items-center justify-between">
                  <span className={`font-medium text-sm ${darkMode ? 'text-blue-300' : 'text-blue-700'}`}>Stripe Balance</span>
                  <span className={`text-lg font-bold ${darkMode ? 'text-blue-200' : 'text-blue-800'}`}>
                    ${(+subscriptionInfo.stripeBalance / 100).toFixed(2)}
                  </span>
                </div>
              </div>
            </div>

            {/* Change Plan Section */}
            <div className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-xl border shadow-lg p-6 transition-all duration-300`}>
              <h2 className={`text-xl font-bold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                Change Plan
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {products
                  .filter((product) => product.priceId !== subscriptionInfo?.priceId)
                  .slice(0, 3)
                  .map((plan) => (
                    <div
                      key={plan.priceId}
                      className={`relative p-4 rounded-xl cursor-pointer transition-all duration-300 transform hover:scale-105 border-2 ${
                        priceId === plan.priceId 
                          ? `${darkMode ? 'border-gray-500 bg-gray-700/50 shadow-lg' : 'border-[#8bc34a] bg-green-50 shadow-lg'}` 
                          : `${darkMode ? 'border-gray-600 bg-gray-700/30 hover:border-gray-500' : 'border-gray-200 bg-gray-50 hover:border-gray-300'}`
                      }`}
                      onClick={() => handlePlanSelect(plan)}
                    >
                      {priceId === plan.priceId && (
                        <div className={`absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center ${darkMode ? 'bg-gray-600' : 'bg-[#8bc34a]'}`}>
                          <FiCheck className="w-4 h-4 text-white" />
                        </div>
                      )}
                      
                      <h3 className={`text-lg font-bold mb-2 ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                        {plan.productName}
                      </h3>
                      
                      <div className="mb-4">
                        <span className={`text-3xl font-bold ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                          ${plan.unitPrice}
                        </span>
                        <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          /{plan.frequencyCharge === 'monthly' ? 'month' : 'year'}
                        </span>
                      </div>
                      
                      <ul className="space-y-2 mb-4">
                        <li className="flex items-center">
                          <FiCheck className="w-4 h-4 text-green-500 mr-2" />
                          <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Core features</span>
                        </li>
                        <li className="flex items-center">
                          <FiCheck className="w-4 h-4 text-green-500 mr-2" />
                          <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                            {plan.frequencyCharge === 'monthly' ? 'Monthly' : 'Yearly'} billing
                          </span>
                        </li>
                        <li className="flex items-center">
                          <FiCheck className="w-4 h-4 text-green-500 mr-2" />
                          <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>24/7 support</span>
                        </li>
                      </ul>
                      
                      <button
                        className={`w-full py-2 px-3 rounded-lg font-semibold transition-all duration-200 ${
                          priceId === plan.priceId
                            ? `${darkMode ? 'bg-gray-600 text-white' : 'bg-[#8bc34a] text-white'}`
                            : `${darkMode ? 'bg-gray-700 text-gray-200 hover:bg-gray-600' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`
                        }`}
                      >
                        {priceId === plan.priceId ? 'Selected' : 'Select Plan'}
                      </button>
                    </div>
                  ))}
              </div>
            </div>

            {/* Invoice Preview */}
            {invoicePreview && (
              <div className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-xl border shadow-lg p-6 transition-all duration-300`}>
                <button
                  className={`flex items-center cursor-pointer select-none text-lg font-bold mb-3 ${darkMode ? 'text-gray-200' : 'text-gray-800'} hover:opacity-80 transition-opacity`}
                  onClick={() => setIsInvoiceOpen((prev) => !prev)}
                >
                  <span className="mr-2">
                    {isInvoiceOpen ? <FiChevronDown className="w-5 h-5" /> : <FiChevronRight className="w-5 h-5" />}
                  </span>
                  <span>Invoice Preview</span>
                </button>

                {isInvoiceOpen && (
                  <div className="transition-all duration-300 ease-in-out">
                    {isLoadingInvoice ? (
                      <div className="flex justify-center py-8">
                        <div className={`w-8 h-8 border-4 border-t-4 rounded-full animate-spin ${darkMode ? 'border-gray-600 border-t-gray-400' : 'border-gray-300 border-t-[#8bc34a]'}`}></div>
                      </div>
                    ) : (
                      <div className={`overflow-hidden rounded-xl border ${darkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                        <table className="w-full">
                          <thead className={`${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                            <tr>
                              <th className={`px-6 py-4 text-left font-semibold ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>Description</th>
                              <th className={`px-6 py-4 text-right font-semibold ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>Amount</th>
                            </tr>
                          </thead>
                          <tbody>
                            {invoicePreview.items.map((item, idx) => (
                              <tr key={idx} className={`border-t ${darkMode ? 'border-gray-600 bg-gray-800' : 'border-gray-200 bg-white'}`}>
                                <td className={`px-6 py-4 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>{item.description}</td>
                                <td className={`px-6 py-4 text-right font-medium ${item.credit ? 'text-red-500' : 'text-green-500'}`}>
                                  {item.credit ? '-' : '+'}${Math.abs(parseFloat(item.amount)).toFixed(2)}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                          <tfoot className={`${darkMode ? 'bg-gray-700' : 'bg-gray-50'} border-t ${darkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                            <tr>
                              <td className={`px-6 py-4 font-bold ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>Total</td>
                              <td className={`px-6 py-4 text-right font-bold text-lg ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                                ${parseFloat(invoicePreview.totalAmount).toFixed(2)} {invoicePreview.currency}
                              </td>
                            </tr>
                          </tfoot>
                        </table>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Payment Method Section */}
            <div className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-xl border shadow-lg p-6 transition-all duration-300`}>
              <h2 className={`text-xl font-bold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-800'} flex items-center`}>
                <FiCreditCard className="mr-2" />
                Payment Method
              </h2>
              
              <div className="max-w-2xl">
                <div className={`space-y-3 border rounded-xl p-4 ${darkMode ? 'border-gray-600 bg-gray-700/30' : 'border-gray-200 bg-gray-50'}`}>
                  {savedCards.length === 0 ? (
                    <p className={`${darkMode ? 'text-gray-400' : 'text-gray-500'} text-center py-4`}>No saved cards</p>
                  ) : (
                    savedCards.map((card) => (
                      <label
                        key={card.id}
                        className={`flex items-center p-4 cursor-pointer rounded-xl transition-all duration-200 ${
                          selectedPaymentMethod === card.id 
                            ? `${darkMode ? 'bg-gray-600 border-gray-500' : 'bg-blue-50 border-blue-200'} border-2`
                            : `${darkMode ? 'hover:bg-gray-600/50' : 'hover:bg-gray-100'} border border-transparent`
                        }`}
                      >
                        <input
                          type="radio"
                          name="savedCard"
                          value={card.id}
                          checked={selectedPaymentMethod === card.id}
                          onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                          className="mr-4 w-5 h-5 text-[#8bc34a] focus:ring-[#8bc34a]"
                        />
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span className={`font-semibold text-lg ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                              {card.brand.toUpperCase()} •••• {card.last4}
                            </span>
                            {card.isDefault === 'true' && (
                              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-green-100 text-green-800 dark:bg-green-800/20 dark:text-green-400">
                                Default
                              </span>
                            )}
                          </div>
                          <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            Expires {card.expMonth}/{card.expYear}
                          </span>
                        </div>
                      </label>
                    ))
                  )}
                  
                  <label
                    className={`flex items-center p-4 cursor-pointer rounded-xl transition-all duration-200 ${
                      selectedPaymentMethod === '' 
                        ? `${darkMode ? 'bg-gray-600 border-gray-500' : 'bg-blue-50 border-blue-200'} border-2`
                        : `${darkMode ? 'hover:bg-gray-600/50' : 'hover:bg-gray-100'} border border-transparent`
                    }`}
                  >
                    <input
                      type="radio"
                      name="savedCard"
                      value=""
                      checked={selectedPaymentMethod === ''}
                      onChange={() => setSelectedPaymentMethod('')}
                      className="mr-4 w-5 h-5 text-[#8bc34a] focus:ring-[#8bc34a]"
                    />
                    <div className="flex-1">
                      <span className={`font-semibold text-lg ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>Use a New Card</span>
                    </div>
                  </label>
                </div>
                
                {!selectedPaymentMethod && (
                  <div className="mt-6">
                    <CardSection darkMode={darkMode} />
                  </div>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row justify-center gap-3">
              <button
                onClick={handleSave}
                disabled={isDisabled || saveLoading}
                className={`px-6 py-3 rounded-lg font-semibold text-white transition-all duration-200 transform hover:scale-105 flex items-center justify-center min-w-[180px] ${
                  isDisabled || saveLoading
                    ? 'bg-gray-400 cursor-not-allowed'
                    : `${darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-[#8bc34a] hover:bg-[#6ec122]'} shadow-lg hover:shadow-xl`
                }`}
              >
                {saveLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Saving...
                  </>
                ) : saveSuccess ? (
                  <>
                    <FiCheck className="mr-2 text-white" />
                    Saved
                  </>
                ) : (
                  'Save Changes'
                )}
              </button>              
              {!isDisabled ? (
                <button
                  onClick={handleCancelSubscription}
                  disabled={cancelResumeSubscriptionLoading}
                  className={`px-6 py-3 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 flex items-center justify-center min-w-[180px] ${
                    cancelResumeSubscriptionLoading
                      ? 'bg-gray-400 text-white cursor-not-allowed'
                      : `${darkMode ? 'bg-red-800 hover:bg-red-700 text-white' : 'bg-red-600 hover:bg-red-700 text-white'} shadow-lg hover:shadow-xl`
                  }`}
                >
                  {cancelResumeSubscriptionLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Canceling...
                    </>
                  ) : (
                    'Cancel Subscription'
                  )}
                </button>
              ) : (
                <button
                  onClick={handleResumeSubscription}
                  disabled={cancelResumeSubscriptionLoading}
                  className={`px-6 py-3 rounded-lg font-semibold text-white transition-all duration-200 transform hover:scale-105 flex items-center justify-center min-w-[180px] ${
                    cancelResumeSubscriptionLoading
                      ? 'bg-gray-400 cursor-not-allowed'
                      : `${darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-[#8bc34a] hover:bg-[#6ec122]'} shadow-lg hover:shadow-xl`
                  }`}
                >
                  {cancelResumeSubscriptionLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Resuming...
                    </>
                  ) : (
                    'Resume Subscription'
                  )}
                </button>
              )}
            </div>

            {/* Status Messages */}
            <div className="text-center space-y-2">
              {isDisabled && (
                <p className={`text-sm ${darkMode ? 'text-red-400' : 'text-red-600'}`}>
                  You can't make changes as you have requested for cancellation.
                </p>
              )}
              {saveSuccess && (
                <p className={`text-sm font-medium ${darkMode ? 'text-green-400' : 'text-green-600'}`}>{saveSuccess}</p>
              )}
              {saveError && (
                <p className={`text-sm font-medium ${darkMode ? 'text-red-400' : 'text-red-600'}`}>{saveError}</p>
              )}
              {cancelResumeSubscriptionSuccess && (
                <p className={`text-sm font-medium ${darkMode ? 'text-green-400' : 'text-green-600'}`}>{cancelResumeSubscriptionSuccess}</p>
              )}
              {cancelResumeSubscriptionError && (
                <p className={`text-sm font-medium ${darkMode ? 'text-red-400' : 'text-red-600'}`}>{cancelResumeSubscriptionError}</p>
              )}
            </div>
          </>
        ) : (
          <>
            {/* No Subscription - Start Subscription Form */}
            <form
              onSubmit={handleSubmit}
              className={`max-w-6xl mx-auto space-y-6 p-6 shadow-xl rounded-2xl ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border`}
            >
              <h2 className={`text-2xl font-bold mb-4 text-center ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                Start Your Subscription
              </h2>

              {/* Plan Selection */}
              <div>
                <h3 className={`text-lg font-bold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                  Select Your Plan
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {products.slice(0, 3).map((plan) => (
                    <div
                      key={plan.priceId}
                      className={`relative p-4 rounded-xl cursor-pointer transition-all duration-300 transform hover:scale-105 border-2 ${
                        priceId === plan.priceId 
                          ? `${darkMode ? 'border-gray-500 bg-gray-700/50 shadow-lg' : 'border-[#8bc34a] bg-green-50 shadow-lg'}` 
                          : `${darkMode ? 'border-gray-600 bg-gray-700/30 hover:border-gray-500' : 'border-gray-200 bg-gray-50 hover:border-gray-300'}`
                      }`}
                      onClick={() => handlePlanSelect(plan)}
                    >
                      {priceId === plan.priceId && (
                        <div className={`absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center ${darkMode ? 'bg-gray-600' : 'bg-[#8bc34a]'}`}>
                          <FiCheck className="w-4 h-4 text-white" />
                        </div>
                      )}
                      
                      <h3 className={`text-lg font-bold mb-2 ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                        {plan.productName}
                      </h3>
                      
                      <div className="mb-4">
                        <span className={`text-3xl font-bold ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                          ${plan.unitPrice}
                        </span>
                        <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          /{plan.frequencyCharge === 'monthly' ? 'month' : 'year'}
                        </span>
                      </div>
                      
                      <ul className="space-y-2 mb-4">
                        <li className="flex items-center">
                          <FiCheck className="w-4 h-4 text-green-500 mr-2" />
                          <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Core features</span>
                        </li>
                        <li className="flex items-center">
                          <FiCheck className="w-4 h-4 text-green-500 mr-2" />
                          <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                            {plan.frequencyCharge === 'monthly' ? 'Monthly' : 'Yearly'} billing
                          </span>
                        </li>
                        <li className="flex items-center">
                          <FiCheck className="w-4 h-4 text-green-500 mr-2" />
                          <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>24/7 support</span>
                        </li>
                      </ul>
                      
                      <button
                        type="button"
                        className={`w-full py-2 px-3 rounded-lg font-semibold transition-all duration-200 ${
                          priceId === plan.priceId
                            ? `${darkMode ? 'bg-gray-600 text-white' : 'bg-[#8bc34a] text-white'}`
                            : `${darkMode ? 'bg-gray-700 text-gray-200 hover:bg-gray-600' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`
                        }`}
                      >
                        {priceId === plan.priceId ? 'Selected' : 'Select Plan'}
                      </button>
                    </div>
                  ))}
                </div>
              </div>

              {/* Payment Method Selection */}
              <div>
                <h3 className={`text-lg font-bold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-800'} flex items-center`}>
                  <FiCreditCard className="mr-2" />
                  Payment Method
                </h3>
                
                <div className="max-w-2xl">
                  <div className={`space-y-3 border rounded-xl p-4 ${darkMode ? 'border-gray-600 bg-gray-700/30' : 'border-gray-200 bg-gray-50'}`}>
                    {savedCards.length === 0 ? (
                      <p className={`${darkMode ? 'text-gray-400' : 'text-gray-500'} text-center py-3`}>No saved cards</p>
                    ) : (
                      savedCards.map((card) => (
                        <label
                          key={card.id}
                          className={`flex items-center p-3 cursor-pointer rounded-lg transition-all duration-200 ${
                            selectedPaymentMethod === card.id 
                              ? `${darkMode ? 'bg-gray-600 border-gray-500' : 'bg-blue-50 border-blue-200'} border-2`
                              : `${darkMode ? 'hover:bg-gray-600/50' : 'hover:bg-gray-100'} border border-transparent`
                          }`}
                        >
                          <input
                            type="radio"
                            name="savedCard"
                            value={card.id}
                            checked={selectedPaymentMethod === card.id}
                            onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                            className="mr-3 w-4 h-4 text-[#8bc34a] focus:ring-[#8bc34a]"
                          />
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <span className={`font-semibold ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                                {card.brand.toUpperCase()} •••• {card.last4}
                              </span>
                              {card.isDefault === 'true' && (
                                <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-semibold bg-green-100 text-green-800 dark:bg-green-800/20 dark:text-green-400">
                                  Default
                                </span>
                              )}
                            </div>
                            <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                              Expires {card.expMonth}/{card.expYear}
                            </span>
                          </div>
                        </label>
                      ))
                    )}
                    
                    <label
                      className={`flex items-center p-3 cursor-pointer rounded-lg transition-all duration-200 ${
                        selectedPaymentMethod === '' 
                          ? `${darkMode ? 'bg-gray-600 border-gray-500' : 'bg-blue-50 border-blue-200'} border-2`
                          : `${darkMode ? 'hover:bg-gray-600/50' : 'hover:bg-gray-100'} border border-transparent`
                      }`}
                    >
                      <input
                        type="radio"
                        name="savedCard"
                        value=""
                        checked={selectedPaymentMethod === ''}
                        onChange={() => setSelectedPaymentMethod('')}
                        className="mr-3 w-4 h-4 text-[#8bc34a] focus:ring-[#8bc34a]"
                      />
                      <div className="flex-1">
                        <span className={`font-semibold ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>Use a New Card</span>
                      </div>
                    </label>
                  </div>
                  
                  {!selectedPaymentMethod && (
                    <div className="mt-4">
                      <CardSection darkMode={darkMode} />
                    </div>
                  )}
                </div>
              </div>

              {/* Submit Button */}
              <div className="text-center">
                <button
                  type="submit"
                  disabled={startSubscriptionLoading}
                  className={`px-8 py-3 rounded-lg font-semibold text-white transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl ${
                    startSubscriptionLoading
                      ? 'bg-gray-400 cursor-not-allowed'
                      : `${darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-[#8bc34a] hover:bg-[#6ec122]'}`
                  }`}
                >
                  {startSubscriptionLoading ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 inline-block"></div>
                      Starting Subscription...
                    </>
                  ) : (
                    'Start Subscription'
                  )}
                </button>
              </div>

              {/* Status Messages */}
              <div className="text-center space-y-2">
                {startSubscriptionSuccess && (
                  <p className={`text-sm font-medium ${darkMode ? 'text-green-400' : 'text-green-600'}`}>{startSubscriptionSuccess}</p>
                )}
                {startSubscriptionError && (
                  <p className={`text-sm font-medium ${darkMode ? 'text-red-400' : 'text-red-600'}`}>{startSubscriptionError}</p>
                )}
              </div>
            </form>
          </>
        )}

        {message && <p className={`text-center text-sm font-medium ${darkMode ? 'text-green-400' : 'text-green-600'}`}>{message}</p>}
      </div>
    </div>
  );
};

const StripeDashboard = ({ darkMode }) => (
  <Elements stripe={stripePromise}>
    <StripeDashboardInner darkMode={darkMode} />
  </Elements>
);

export default StripeDashboard;