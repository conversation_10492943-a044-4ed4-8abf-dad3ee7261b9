// cacheDebugUtils.js - Debugging utilities for cache issues

/**
 * Debug cache state for a specific component
 * Call this in component useEffect to see cache status
 */
export const debugCacheState = (componentName, cacheKey, cache, userId) => {
  console.group(`🔍 Cache Debug: ${componentName}`);
  console.log('Cache Key:', cacheKey);
  console.log('User ID:', userId);
  console.log('Cache State:', {
    loaded: cache?.[`${cacheKey}Loaded`],
    loading: cache?.[`${cacheKey}Loading`],
    error: cache?.[`${cacheKey}Error`],
    data: cache?.[cacheKey],
    params: cache?.[`${cacheKey}Params`]
  });
  
  const isValid = cache?.[`${cacheKey}Loaded`] && 
                  cache?.[`${cacheKey}Params`]?.userId == userId;
  
  console.log('Cache Valid:', isValid);
  console.log('Should Fetch:', !isValid);
  console.groupEnd();
  
  return isValid;
};

/**
 * Debug all cache states
 */
export const debugAllCacheStates = (cache, userId) => {
  console.group('🔍 All Cache States Debug');
  console.log('User ID:', userId);
  
  const cacheKeys = [
    'transactions',
    'recurringTransactions', 
    'budgetSummary',
    'budgetData',
    'userAccounts',
    'accountIds',
    'distinctSubcategories',
    'paymentSubscription',
    'paymentMethods',
    'paymentProducts',
    'paymentInvoices',
    'upcomingInvoice'
  ];
  
  cacheKeys.forEach(key => {
    const loaded = cache?.[`${key}Loaded`];
    const loading = cache?.[`${key}Loading`];
    const error = cache?.[`${key}Error`];
    const params = cache?.[`${key}Params`];
    const dataLength = Array.isArray(cache?.[key]) ? cache[key].length : 
                      cache?.[key] ? 'object' : 'null';
    
    console.log(`${key}:`, {
      loaded,
      loading,
      error: error ? 'ERROR' : null,
      dataLength,
      userMatch: params?.userId == userId
    });
  });
  
  console.groupEnd();
};

/**
 * Monitor cache initialization progress
 */
export const monitorCacheInitialization = (cache) => {
  console.group('🚀 Cache Initialization Progress');
  
  const initializationKeys = [
    'transactions',
    'recurringTransactions',
    'budgetSummary', 
    'budgetData',
    'userAccounts',
    'accountIds',
    'distinctSubcategories',
    'accountBalancesInvestment',
    'accountBalancesDepository',
    'accountBalancesLoan',
    'accountBalancesCredit',
    'paymentSubscription',
    'paymentMethods',
    'paymentProducts',
    'paymentInvoices'
  ];
  
  const progress = initializationKeys.map(key => ({
    key,
    loaded: cache?.[`${key}Loaded`] || false,
    loading: cache?.[`${key}Loading`] || false,
    error: cache?.[`${key}Error`] || null
  }));
  
  const totalKeys = progress.length;
  const loadedKeys = progress.filter(p => p.loaded).length;
  const loadingKeys = progress.filter(p => p.loading).length;
  const errorKeys = progress.filter(p => p.error).length;
  
  console.log(`Progress: ${loadedKeys}/${totalKeys} loaded`);
  console.log(`Loading: ${loadingKeys} keys`);
  console.log(`Errors: ${errorKeys} keys`);
  
  console.table(progress);
  console.groupEnd();
  
  return {
    total: totalKeys,
    loaded: loadedKeys,
    loading: loadingKeys,
    errors: errorKeys,
    isComplete: loadedKeys === totalKeys && loadingKeys === 0
  };
};

/**
 * React hook for cache debugging
 */
export const useCacheDebug = (componentName, cacheKey) => {
  const cache = useSelector(state => state.cache);
  const userId = getCurrentUserId();
  
  useEffect(() => {
    debugCacheState(componentName, cacheKey, cache, userId);
  }, [cache?.[`${cacheKey}Loaded`], cache?.[`${cacheKey}Loading`], cache?.[`${cacheKey}Error`]]);
  
  return {
    debugCache: () => debugCacheState(componentName, cacheKey, cache, userId),
    debugAll: () => debugAllCacheStates(cache, userId),
    monitorInit: () => monitorCacheInitialization(cache)
  };
};

/**
 * Component wrapper for cache debugging
 */
export const withCacheDebug = (WrappedComponent, componentName) => {
  return function CacheDebugWrapper(props) {
    const cache = useSelector(state => state.cache);
    const userId = getCurrentUserId();
    
    useEffect(() => {
      console.log(`🔍 ${componentName} mounted, cache state:`, {
        cacheInitialized: !!cache,
        userId,
        timestamp: new Date().toISOString()
      });
      
      debugAllCacheStates(cache, userId);
    }, []);
    
    return <WrappedComponent {...props} />;
  };
};

// Import dependencies for hooks
import { useSelector } from 'react-redux';
import { useEffect } from 'react';
import { getCurrentUserId } from '../../web/src/utils/AuthUtil';
