import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { 
  <PERSON><PERSON><PERSON>, BarChart, AreaChart, 
  Line, Bar, Area, 
  XAxis, YAxis, CartesianGrid, 
  Tooltip, ResponsiveContainer, LabelList 
} from 'recharts';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faChartBar, faChartLine, faChartArea, faExclamationTriangle, faSync
} from '@fortawesome/free-solid-svg-icons';
// import './InvestmentChart.css'; // Make sure to create this CSS file
import { 
  fetchAccountData,
  setChartView,
  setTimePeriod
} from '../../../../logic/redux/accountChartSlice';

const InvestmentChart = ({ userId }) => {
  const dispatch = useDispatch();
  const { 
    chartData,
    selectedChartView,
    selectedTimePeriod,
    loading,
    error
  } = useSelector(state => state.accountChart);

  // Theme colors - Investment specific theming
  const MAIN_COLOR = '#6200ea'; // Deep purple for investments
  const SECONDARY_COLOR = '#9d46ff';
  const HOVER_COLOR = '#4a148c';

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div 
          className="custom-tooltip" 
          style={{ 
            backgroundColor: 'white', 
            padding: '10px', 
            border: '1px solid #ddd', 
            fontSize: '10px'
          }}
        >
          <p className="label">{label}</p>
          <p className="value">
            {`Investment Value: $${Number(payload[0].value).toLocaleString()}`}
          </p>
        </div>
      );
    }
    return null;
  };

  // Initial and dependent data fetching
  useEffect(() => {
    dispatch(fetchAccountData({
      userId,
      chartType: 'investment', // Always fetch investment data
      timePeriod: selectedTimePeriod
    }));
  }, [dispatch, userId, selectedTimePeriod]);

  // Error Rendering Component
  const ErrorDisplay = () => (
    <div className="chart-error-container p-6 text-center">
      <FontAwesomeIcon 
        icon={faExclamationTriangle} 
        className="text-red-500 text-4xl mb-4"
      />
      <h4 className="text-xl font-semibold mb-2">Unable to Load Investment Chart</h4>
      
      <p className="text-gray-600 mb-4">
        {error?.message || 'An unexpected error occurred while fetching investment data'}
      </p>
      
      {error?.status && (
        <div className="error-details mb-4 text-sm text-gray-500">
          Error Code: {error.status}
        </div>
      )}
      
      <button 
        onClick={() => dispatch(fetchAccountData({
          userId,
          chartType: 'investment',
          timePeriod: selectedTimePeriod
        }))}
        className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 inline-flex items-center"
      >
        <FontAwesomeIcon icon={faSync} className="mr-2" />
        Retry Fetch
      </button>
    </div>
  );

  // Custom Label Renderer
  const CustomLabel = (props) => {
    const { x, y, width, value } = props;
    
    // Format the balance with dollar sign and commas
    const formattedValue = `$${Number(value).toLocaleString(undefined, {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    })}`;

    return (
      <text 
        x={x + (width ? width / 2 : 0)} 
        y={y - 10} 
        fill="#333" 
        textAnchor="middle" 
        fontSize={10}
        fontWeight="bold"
      >
        {formattedValue}
      </text>
    );
  };

  // Find the maximum value in the data for dynamic domain calculation
  const getMaxValue = () => {
    if (!chartData || chartData.length === 0) return 0;
    
    // Find maximum balance value and add 15% padding
    const maxValue = Math.max(...chartData.map(item => item.balance));
    return Math.ceil(maxValue * 1.15); // Add 15% padding to ensure values aren't cut off
  };

  const calculateTickCount = () => {
    if (!chartData || chartData.length === 0) return 5; // Default to 5 ticks if no data
    
    const dataLength = chartData.length;
    const chartHeight = 400; // Height of the chart in pixels
  
    // Adjust the number of ticks depending on data length and chart height
    const tickCount = Math.min(Math.ceil(chartHeight / 50), dataLength, 10); // Limit ticks to 10 for readability
    return tickCount;
  };

  // Chart rendering logic
  const renderChart = () => {
    if (error) return <ErrorDisplay />;
    
    if (loading) {
      return (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-600"></div>
        </div>
      );
    }
    
    // Additional data validation
    if (!chartData || chartData.length === 0) {
      return (
        <div className="text-center p-6 text-gray-500">
          <FontAwesomeIcon 
            icon={faExclamationTriangle} 
            className="text-yellow-500 text-4xl mb-4"
          />
          <p>No investment data available for the selected time period</p>
          <button 
            onClick={() => dispatch(fetchAccountData({
              userId,
              chartType: 'investment',
              timePeriod: selectedTimePeriod
            }))}
            className="mt-4 bg-purple-600 text-white px-4 py-2 rounded"
          >
            Refresh Data
          </button>
        </div>
      );
    }

    // Calculate appropriate height based on data
    const chartHeight = 400; // Increased default height from 300 to 400
    
    const chartProps = {
      width: '100%',
      height: chartHeight,
      data: chartData,
      margin: { top: 40, right: 30, left: 20, bottom: 50 } // Increased top margin from 30 to 40
    };

    const axisFontStyle = {
      fontSize: '12px',
      fill: '#666'
    };

    const gradient = (
      <defs>
        <linearGradient id="colorBalance" x1="0" y1="0" x2="0" y2="1">
          <stop offset="5%" stopColor={MAIN_COLOR} stopOpacity={0.5}/>
          <stop offset="95%" stopColor={SECONDARY_COLOR} stopOpacity={0.6}/>
        </linearGradient>
      </defs>
    );

    // Calculate the maximum domain value to ensure chart isn't cut off
    const maxDomain = getMaxValue();
    const tickCount = calculateTickCount(); 
    const commonElements = (
      <>
        {gradient}
        <CartesianGrid 
          strokeDasharray="3 3" 
          stroke="#E0E0E0"
          strokeWidth={1.5}
        />
        <XAxis
          dataKey="name"
          tick={{ ...axisFontStyle }}
          axisLine={{ stroke: '#ddd' }}
          tickCount={tickCount}
        />
        <YAxis
          tick={{ ...axisFontStyle }}
          axisLine={{ stroke: '#ddd' }}
          domain={[0, maxDomain]} // Set dynamic domain to ensure values aren't cut off
          tickFormatter={(value) => {
            const absValue = Math.abs(value);
            const prefix = value < 0 ? '-$' : '$';
            
            if (absValue >= 1000000) {
              return `${prefix}${(absValue / 1000000).toFixed(1)}M`;
            } else if (absValue >= 1000) {
              return `${prefix}${(absValue / 1000).toFixed(1)}K`;
            } else {
              return `${prefix}${absValue.toFixed(0)}`;
            }
          }}
          tickCount={tickCount} 
        />
      </>
    );

    switch (selectedChartView) {
      case 'line':
        return (
          <ResponsiveContainer width="100%" height={chartHeight}>
            <LineChart {...chartProps}>
              {commonElements}
              <Tooltip 
                content={<CustomTooltip />}
                formatter={(value) => [`$${Number(value).toLocaleString()}`, 'Investment Value']}
              />
              <Line
                type="monotone"
                dataKey="balance"
                stroke={MAIN_COLOR}
                fillOpacity={0.3}
                strokeWidth={2.5}
                fill="url(#colorBalance)"
                dot={{
                  stroke: MAIN_COLOR,
                  strokeWidth: 2,
                  r: 4,
                  fill: 'white',
                  fillOpacity: 1
                }}
              >
                <LabelList 
                  dataKey="balance" 
                  content={CustomLabel}
                  position="top"
                />
              </Line>
            </LineChart>
          </ResponsiveContainer>
        );
        
      case 'area':
        return (
          <ResponsiveContainer width="100%" height={chartHeight}>
            <AreaChart {...chartProps}>
              {commonElements}
              <Tooltip 
                content={<CustomTooltip />}
                formatter={(value) => [`$${Number(value).toLocaleString()}`, 'Investment Value']}
              />
              <Area
                type="monotone"
                dataKey="balance"
                stroke={MAIN_COLOR}
                strokeWidth={2.5}
                fill="url(#colorBalance)"
                fillOpacity={0.3}
                dot={{
                  stroke: MAIN_COLOR,
                  strokeWidth: 2,
                  r: 4,
                  fill: 'white',
                  fillOpacity: 1
                }}
              >
                <LabelList 
                  dataKey="balance" 
                  content={CustomLabel}
                  position="top"
                />
              </Area>
            </AreaChart>
          </ResponsiveContainer>
        );
        
      case 'bar':
      default:
        return (
          <ResponsiveContainer width="100%" height={chartHeight}>
            <BarChart {...chartProps}>
              {commonElements}
              <Tooltip 
                content={<CustomTooltip />}
                formatter={(value) => [`$${Number(value).toLocaleString()}`, 'Investment Value']}
              />
              <Bar
                dataKey="balance"
                stroke="url(#colorBalance)"
                strokeWidth={1.5}
                fill="url(#colorBalance)"
                fillOpacity={0.7}
                activeBar={{
                  fill: HOVER_COLOR
                }}
              >
                <LabelList 
                  dataKey="balance" 
                  content={CustomLabel}
                  position="top"
                />
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        );
    }
  };

  return (
    <div className="investment-chart-container"
    style={{ 
      fontFamily: "Architects Daughter" 
    }}>
      <div className="chart-header">
        <h3 className="chart-title">
          Investment Portfolio History
        </h3>
        
        {/* Chart type selection icons */}
        <div className="chart-type-icons">
          {[
            { type: 'bar', icon: faChartBar, label: 'Bar Chart' },
            { type: 'line', icon: faChartLine, label: 'Line Chart' },
            { type: 'area', icon: faChartArea, label: 'Area Chart' }
          ].map((chartType) => (
            <div
              key={chartType.type}
              onClick={() => dispatch(setChartView(chartType.type))}
              className={`chart-type-icon ${selectedChartView === chartType.type ? 'active' : ''}`}
              title={chartType.label}
            >
              <FontAwesomeIcon
                icon={chartType.icon}
                className="icon"
              />
            </div>
          ))}
        </div>
        
        {/* Filters - Only time period is needed for investment chart */}
        <div className="chart-filters">
          <div className="time-period-filter">
            <select
              value={selectedTimePeriod}
              onChange={(e) => dispatch(setTimePeriod(e.target.value))}
              className="filter-select"
              style={{ 
                fontFamily: "Architects Daughter" 
              }}
            >
              <option value="one-month">One Month</option>
              <option value="three-month">Three Months</option>
              <option value="ytd">Year to Date</option>
              <option value="half-year">Half-Year</option>
              <option value="yearly">Yearly</option>
              <option value="quarterly-aggregate">Quarterly</option>
            </select>
          </div>
        </div>
      </div>
      
      {/* Chart Content */}
      <div className="chart-content">
        <div className="chart-container">
          {renderChart()}
        </div>
      </div>
    </div>
  );
};

export default InvestmentChart;