import { createSlice } from '@reduxjs/toolkit';
import Cookies from 'js-cookie';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { clearCache } from './cacheSlice';

const TOKEN_COOKIE_NAME = 'jwt_token';
const TOKEN_STORAGE_KEY = 'pennypal_jwt_token';
const REFRESH_TOKEN_STORAGE_KEY = 'pennypal_refresh_token';
const USER_DATA_STORAGE_KEY = 'pennypal_user_data';

const initialState = {
  isUserAuthenticated: false, // Will be updated after checking AsyncStorage
  sessionId: null,
  loading: false,
  error: null,
  data: null,
  showLogin: false,
  user: {
    id: null,   
    firstName: null,
    lastName: null,
    mobileNumber: null,
    phoneNumber: null,
    emailId: null,
    password: null,
    confirmPassword: null,
    jwtToken: null,
    refreshToken: null,
    // Add OTP and remember me
    otp: null,
    rememberMe: false,
    // Add isPrimary field
    isPrimary: null
  }
};

// Helper function to update user state properly
const updateUserState = (state, userData, tokens = {}) => {
  const { jwtToken, refreshToken } = tokens;
  
  state.user = {
    ...state.user,
    ...userData,
    id: userData.id || userData.userId || state.user.id,
    isPrimary: userData.isPrimary !== undefined ? userData.isPrimary : state.user.isPrimary,
    jwtToken: jwtToken || state.user.jwtToken,
    refreshToken: refreshToken || state.user.refreshToken
  };
  
  console.log('User state updated:', {
    id: state.user.id,
    email: state.user.emailId,
    isPrimary: state.user.isPrimary,
    hasToken: !!state.user.jwtToken
  });
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // Sign In Actions
    signInRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    signInSuccess: (state, action) => {
      state.isUserAuthenticated = true;
      state.loading = false;
      state.error = null;
      
      const { user, jwtToken, refreshToken } = action.payload;
      updateUserState(state, user, { jwtToken, refreshToken });
      
      console.log('Sign-in successful for user:', state.user.id, 'isPrimary:', state.user.isPrimary);
    },
    signInFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
      state.isUserAuthenticated = false;
    },

    // Google Sign In Actions
    googleSignInRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    googleSignInSuccess: (state, action) => {
      state.loading = false;
      state.error = null;
      state.isUserAuthenticated = true;
      
      const { user, jwtToken, refreshToken } = action.payload;
      updateUserState(state, user, { jwtToken, refreshToken });
      
      console.log('Google sign-in successful for user:', state.user.id, 'isPrimary:', state.user.isPrimary);
    },
    googleSignInFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
      state.isUserAuthenticated = false;
    },

    // OTP Verification Actions
    otpVerifyRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    otpVerifySuccess: (state, action) => {
      state.loading = false;
      state.error = null;
      state.isUserAuthenticated = true;
      
      const { user, jwtToken, refreshToken } = action.payload;
      updateUserState(state, user, { jwtToken, refreshToken });
      
      console.log('OTP verification successful for user:', state.user.id, 'isPrimary:', state.user.isPrimary);
    },
    otpVerifyFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
      state.isUserAuthenticated = false;
    },

    // Registration Actions
    registrationRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    registrationSuccess: (state, action) => {
      state.loading = false;
      state.error = null;
      
      const { user, jwtToken, refreshToken, message } = action.payload;
      
      // If we have tokens, user is fully authenticated
      if (jwtToken) {
        state.isUserAuthenticated = true;
        updateUserState(state, user, { jwtToken, refreshToken });
      } else {
        // Registration initiated but not completed (email verification pending)
        updateUserState(state, user);
      }
      
      state.data = { message: message || 'Registration successful' };
      console.log('Registration processed for user:', state.user.id, 'isPrimary:', state.user.isPrimary);
    },
    registrationFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Set Password Actions (for email verification flow)
    setPasswordRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    setPasswordSuccess: (state, action) => {
      state.loading = false;
      state.error = null;
      
      const { user, message } = action.payload;
      updateUserState(state, user);
      
      state.data = { message: message || 'Password set successfully' };
      console.log('Password set for user:', state.user.id, 'isPrimary:', state.user.isPrimary);
    },
    setPasswordFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },

    // User Management Actions
    setUser: (state, action) => {
      const userData = action.payload;
      updateUserState(state, userData);
      console.log('User data set in Redux state:', state.user.id, 'isPrimary:', state.user.isPrimary);
    },
    
    bindFormValueToUser: (state, action) => {
      const { name, value } = action.payload;
      state.user = {
        ...state.user,
        [name]: value,
      };
    },

    // Auth Status Management
    updateAuthFromStorage: (state, action) => {
      const { token, refreshToken, userData } = action.payload;
      
      if (token) {
        state.isUserAuthenticated = true;
        state.user.jwtToken = token;
        
        if (refreshToken) {
          state.user.refreshToken = refreshToken;
        }
        
        if (userData) {
          updateUserState(state, userData);
        }
        
        console.log('Auth state restored from storage for user:', state.user.id, 'isPrimary:', state.user.isPrimary);
      }
    },

    // Sign Out Action
    signOut: (state) => {
      console.log('Signing out user:', state.user.id);
      
      state.isUserAuthenticated = false;
      state.user = initialState.user;
      state.sessionId = null;
      state.loading = false;
      state.error = null;
      state.data = null;
      
      // Remove cookies
      Cookies.remove(TOKEN_COOKIE_NAME);
      
      // Remove from AsyncStorage (async operation, handled outside reducer)
      Promise.all([
        AsyncStorage.removeItem(TOKEN_STORAGE_KEY),
        AsyncStorage.removeItem(REFRESH_TOKEN_STORAGE_KEY),
        AsyncStorage.removeItem(USER_DATA_STORAGE_KEY)
      ]).catch(error => console.error('Error removing data during sign out:', error));
      
      console.log('User signed out successfully');
    },

    // Clear Error Action
    clearAuthError: (state) => {
      state.error = null;
    },

    // Update User Profile Action (for profile updates)
    updateUserProfile: (state, action) => {
      const updatedData = action.payload;
      updateUserState(state, updatedData);
      
      // Update AsyncStorage
      AsyncStorage.setItem(USER_DATA_STORAGE_KEY, JSON.stringify(state.user))
        .catch(error => console.error('Error updating user profile in storage:', error));
      
      console.log('User profile updated:', state.user.id, 'isPrimary:', state.user.isPrimary);
    }
  }
});

export const {
  signInRequest,
  signInSuccess,
  signInFailure,
  googleSignInRequest,
  googleSignInSuccess,
  googleSignInFailure,
  otpVerifyRequest,
  otpVerifySuccess,
  otpVerifyFailure,
  registrationRequest,
  registrationSuccess,
  registrationFailure,
  setPasswordRequest,
  setPasswordSuccess,
  setPasswordFailure,
  setUser,
  bindFormValueToUser,
  updateAuthFromStorage,
  signOut,
  clearAuthError,
  updateUserProfile
} = authSlice.actions;

export default authSlice.reducer;