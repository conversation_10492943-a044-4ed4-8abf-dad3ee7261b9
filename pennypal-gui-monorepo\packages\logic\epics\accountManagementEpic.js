import { ofType } from 'redux-observable';
import { from, of } from 'rxjs';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  changePasswordStart,
  changePasswordSuccess,
  changePasswordFailure,
  deleteAccountStart,
  deleteAccountSuccess,
  deleteAccountFailure,
  checkDeletionStatusStart,
  checkDeletionStatusSuccess,
  checkDeletionStatusFailure
} from '../redux/accountManagementSlice';
import { getCurrentUserId } from '../../web/src/utils/AuthUtil';

// **UPDATED ENDPOINTS** - Make sure these match your backend
const API_ENDPOINTS = {
  CHANGE_PASSWORD: '/pennypal/api/v1/accounts/change-password', // ⚠️ Update this URL
  DELETE_ACCOUNT: '/pennypal/api/v1/accounts/delete-permanently', // ⚠️ Update this URL
  DELETION_STATUS: '/pennypal/api/v1/accounts/deletion-status' // ⚠️ Update this URL
};

// Epic for changing password
export const changePasswordEpic = (action$) =>
  action$.pipe(
    ofType(changePasswordStart.type),
    mergeMap((action) => {
      const userId = getCurrentUserId();
      if (!userId) return of(changePasswordFailure('User ID not found'));

      // Ensure userId is included in the DTO if not already present
      const changePasswordDto = {
        ...action.payload.changePasswordDto,
        userId: action.payload.changePasswordDto.userId || userId
      };

      console.log('Epic making change password request to:', API_ENDPOINTS.CHANGE_PASSWORD);
      console.log('Epic request payload:', changePasswordDto);

      return from(
        axiosInstance.post(API_ENDPOINTS.CHANGE_PASSWORD, changePasswordDto)
      ).pipe(
        map((res) => {
          console.log("Change password epic response:", res.data);
          return changePasswordSuccess(res.data);
        }),
        catchError((err) => {
          console.error("Change password epic error:", err);
          console.error("Epic error details:", {
            status: err.response?.status,
            statusText: err.response?.statusText,
            data: err.response?.data,
            url: err.config?.url
          });
          
          const errorMessage = err.response?.data?.message || err.message;
          return of(changePasswordFailure(errorMessage));
        })
      );
    })
  );

// Epic for deleting account permanently
export const deleteAccountEpic = (action$) =>
  action$.pipe(
    ofType(deleteAccountStart.type),
    mergeMap((action) => {
      const userId = getCurrentUserId();
      if (!userId) return of(deleteAccountFailure('User ID not found'));

      // Ensure userId is included in the DTO if not already present
      const deleteAccountDto = {
        ...action.payload.deleteAccountDto,
        userId: action.payload.deleteAccountDto.userId || userId
      };

      console.log('Epic making delete account request to:', API_ENDPOINTS.DELETE_ACCOUNT);
      console.log('Epic request payload:', deleteAccountDto);

      return from(
        axiosInstance.post(API_ENDPOINTS.DELETE_ACCOUNT, deleteAccountDto)
      ).pipe(
        map((res) => {
          console.log("Delete account epic response:", res.data);
          return deleteAccountSuccess(res.data);
        }),
        catchError((err) => {
          console.error("Delete account epic error:", err);
          console.error("Epic error details:", {
            status: err.response?.status,
            statusText: err.response?.statusText,
            data: err.response?.data,
            url: err.config?.url
          });
          
          const errorMessage = err.response?.data?.message || err.message;
          return of(deleteAccountFailure(errorMessage));
        })
      );
    })
  );

// Epic for checking deletion status
export const checkDeletionStatusEpic = (action$) =>
  action$.pipe(
    ofType(checkDeletionStatusStart.type),
    mergeMap((action) => {
      const { emailId } = action.payload;
      
      if (!emailId) return of(checkDeletionStatusFailure('Email ID is required'));

      const url = `${API_ENDPOINTS.DELETION_STATUS}?emailId=${encodeURIComponent(emailId)}`;
      console.log('Epic making deletion status request to:', url);

      return from(
        axiosInstance.get(url)
      ).pipe(
        map((res) => {
          console.log("Check deletion status epic response:", res.data);
          // Assuming the actual status boolean is in res.data.data
          return checkDeletionStatusSuccess(res.data.data);
        }),
        catchError((err) => {
          console.error("Check deletion status epic error:", err);
          console.error("Epic error details:", {
            status: err.response?.status,
            statusText: err.response?.statusText,
            data: err.response?.data,
            url: err.config?.url
          });
          
          const errorMessage = err.response?.data?.message || err.message;
          return of(checkDeletionStatusFailure(errorMessage));
        })
      );
    })
  );

export default [changePasswordEpic, deleteAccountEpic, checkDeletionStatusEpic];