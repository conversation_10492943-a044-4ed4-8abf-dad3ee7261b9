// utils/tailwindUtils.js
export const getThemeClasses = (darkMode, lightClasses, darkClasses) => {
  return darkMode ? darkClasses : lightClasses;
};

export const themeClasses = {
  container: (darkMode) => getThemeClasses(darkMode, 'bg-white text-gray-900', 'bg-gray-900 text-gray-100'),
  border: (darkMode) => getThemeClasses(darkMode, 'border-gray-300', 'border-gray-700'),
  hover: (darkMode) => getThemeClasses(darkMode, 'hover:bg-gray-100', 'hover:bg-gray-800'),
  button: (darkMode) => getThemeClasses(darkMode, 'bg-gray-200 text-gray-900', 'bg-gray-700 text-gray-100'),
  tableHeader: (darkMode) => getThemeClasses(darkMode, 'bg-[#c5e1a5] text-gray-900', 'bg-gray-800 text-gray-100'),
  error: (darkMode) => getThemeClasses(darkMode, 'bg-red-100 text-red-700 border-red-400', 'bg-red-900/30 text-red-300 border-red-700'),
  // Classes for Summary Cards (from Budget5)
  cardContainer: (darkMode) => getThemeClasses(darkMode, 'bg-white text-gray-900', 'bg-gray-800 text-gray-100'),
  cardBorder: (darkMode) => getThemeClasses(darkMode, 'border-gray-300', 'border-gray-800'),
  cardIconIncome: (darkMode) => getThemeClasses(darkMode, 'bg-red-500', 'bg-red-600'),
  cardIconBudget: (darkMode) => getThemeClasses(darkMode, 'bg-purple-500', 'bg-purple-600'),
  cardIconActual: (darkMode) => getThemeClasses(darkMode, 'bg-teal-500', 'bg-teal-600'),
  cardIconRemaining: (darkMode) => getThemeClasses(darkMode, 'bg-yellow-500', 'bg-yellow-600'),
  // Classes for Table Rows (from Budget5)
  tableRowContainer: (darkMode) => getThemeClasses(darkMode, 'bg-white text-gray-900', 'bg-gray-800 text-gray-100'),
  tableRowBorder: (darkMode) => getThemeClasses(darkMode, 'border-gray-300', 'border-gray-800'),
  tableRowHover: (darkMode) => getThemeClasses(darkMode, 'hover:bg-cyan-100', 'hover:bg-gray-700'),
  tableProgressContainer: (darkMode) => getThemeClasses(darkMode, 'bg-white', 'bg-gray-800'),
  tableProgressBorder: (darkMode) => getThemeClasses(darkMode, 'border-gray-300 border-x-gray-300 border-y-white', 'border-gray-800 border-x-gray-800 border-y-gray-800'),
  toggleText: (darkMode) => getThemeClasses(darkMode, 'text-blue-500', 'text-blue-400'),
  // Classes for InvoicesTransactions
  tabActive: (darkMode) => getThemeClasses(darkMode, 'bg-[#8bc34a] text-white', 'bg-lime-600 text-white'),
  tabInactive: (darkMode) => getThemeClasses(darkMode, 'hover:bg-gray-200 text-gray-900', 'hover:bg-gray-700 text-gray-300'),
  link: (darkMode) => getThemeClasses(darkMode, 'text-blue-600 hover:text-blue-800', 'text-blue-400 hover:text-blue-200'),
  linkSecondary: (darkMode) => getThemeClasses(darkMode, 'text-gray-700 hover:text-gray-900', 'text-gray-400 hover:text-white'),
  amountCredit: (darkMode) => getThemeClasses(darkMode, 'text-red-600', 'text-red-500'),
  amountDebit: (darkMode) => getThemeClasses(darkMode, 'text-green-600', 'text-green-500'),
  // New classes for StripeDashboard
  subscriptionBadgeActive: (darkMode) => getThemeClasses(darkMode, 'bg-green-100 text-green-700', 'bg-green-900/30 text-green-300'),
  subscriptionBadgeTrialing: (darkMode) => getThemeClasses(darkMode, 'bg-purple-100 text-purple-700', 'bg-purple-900/30 text-purple-300'),
  subscriptionBadgeCanceled: (darkMode) => getThemeClasses(darkMode, 'bg-red-100 text-red-700', 'bg-red-900/30 text-red-300'),
  planCard: (darkMode) => getThemeClasses(darkMode, 'border-gray-300 hover:bg-gray-50', 'border-gray-700 hover:bg-gray-800'),
  planCardSelected: (darkMode) => getThemeClasses(darkMode, 'border-[#8bc34a] bg-gray-100', 'border-lime-600 bg-gray-800'),
  planButton: (darkMode) => getThemeClasses(darkMode, 'bg-gray-200 text-gray-900', 'bg-gray-700 text-gray-300'),
  planButtonSelected: (darkMode) => getThemeClasses(darkMode, 'bg-[#8bc34a] text-white', 'bg-lime-600 text-white'),
  successText: (darkMode) => getThemeClasses(darkMode, 'bg-green-100 text-green-700 border-green-400', 'bg-green-900/30 text-green-300 border-green-700'),
  errorText: (darkMode) => getThemeClasses(darkMode, 'text-red-600', 'text-red-500'),
  spinner: (darkMode) => getThemeClasses(darkMode, 'border-gray-200 border-t-white', 'border-gray-600 border-t-gray-100'),
  cardInput: (darkMode) => getThemeClasses(darkMode, 'border-gray-300 bg-white text-gray-900', 'border-gray-700 bg-gray-800 text-gray-100'),
  // New classes for Reconciliation
  loadingText: (darkMode) => getThemeClasses(darkMode, 'text-gray-600', 'text-gray-300'),
  badgeDate: (darkMode) => getThemeClasses(darkMode, 'bg-lime-100 text-lime-800', 'bg-lime-900 text-lime-300'),
  badgeGroup: (darkMode) => getThemeClasses(darkMode, 'bg-blue-100 text-blue-800', 'bg-blue-900 text-blue-300'),
  badgeSelected: (darkMode) => getThemeClasses(darkMode, 'bg-lime-50 text-lime-700 border-lime-100', 'bg-lime-900 text-lime-300 border-lime-800'),
  badgeCategory: (darkMode) => getThemeClasses(darkMode, 'bg-purple-50 text-purple-700', 'bg-purple-900 text-purple-300'),
  badgeTransaction: (darkMode) => getThemeClasses(darkMode, 'bg-blue-50 text-blue-700', 'bg-blue-900 text-blue-300'),
  badgeDateTransaction: (darkMode) => getThemeClasses(darkMode, 'bg-green-50 text-green-700', 'bg-green-900 text-green-300'),
  filterButton: (darkMode) => getThemeClasses(darkMode, 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100', 'bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600'),
  actionButton: (darkMode) => getThemeClasses(darkMode, 'bg-gray-100 text-gray-700 hover:bg-gray-200', 'bg-gray-700 text-gray-300 hover:bg-gray-600'),
  actionButtonDisabled: (darkMode) => getThemeClasses(darkMode, 'bg-gray-100 text-gray-400 cursor-not-allowed', 'bg-gray-700 text-gray-400 cursor-not-allowed'),
  deleteButton: (darkMode) => getThemeClasses(darkMode, 'bg-rose-50 text-rose-700 hover:bg-rose-100 border-rose-100', 'bg-rose-900 text-rose-300 hover:bg-rose-800 border-rose-800'),
  reconcileButton: (darkMode) => getThemeClasses(darkMode, 'bg-[#8bc34a] text-white hover:bg-lime-600', 'bg-lime-600 text-white hover:bg-lime-700'),
  reconcileButtonDisabled: (darkMode) => getThemeClasses(darkMode, 'bg-gray-100 text-gray-500', 'bg-gray-700 text-gray-400'),
  groupHeader: (darkMode) => getThemeClasses(darkMode, 'bg-white border-gray-100 hover:bg-gray-50', 'bg-gray-800 border-gray-700 hover:bg-gray-700'),
  groupContent: (darkMode) => getThemeClasses(darkMode, 'bg-gray-50 border-gray-100', 'bg-gray-700 border-gray-600'),
  transactionRow: (darkMode) => getThemeClasses(darkMode, 'border-gray-100 hover:bg-white', 'border-gray-600 hover:bg-gray-600'),
  icon: (darkMode) => getThemeClasses(darkMode, 'text-gray-400', 'text-gray-500'),
  iconHover: (darkMode) => getThemeClasses(darkMode, 'hover:text-lime-600', 'hover:text-lime-400'),
  iconSelected: (darkMode) => getThemeClasses(darkMode, 'text-lime-600', 'text-lime-400'),
  // New classes for RecurringTransactionsView
  calendarHeader: (darkMode) => getThemeClasses(darkMode, 'bg-[#c5e1a5] border-gray-300 text-gray-900', 'bg-gray-800 border-gray-700 text-gray-100'),
  calendarCell: (darkMode) => getThemeClasses(darkMode, 'bg-white border-gray-300 hover:bg-gray-50', 'bg-gray-800 border-gray-700 hover:bg-gray-700'),
  calendarCellEmpty: (darkMode) => getThemeClasses(darkMode, 'bg-gray-50', 'bg-gray-900'),
  calendarCellToday: (darkMode) => getThemeClasses(darkMode, 'border-green-600 bg-green-50 text-green-600', 'border-green-500 bg-gray-700 text-green-400'),
  calendarGrid: (darkMode) => getThemeClasses(darkMode, 'bg-gray-100', 'bg-gray-700'),
  tableCell: (darkMode) => getThemeClasses(darkMode, 'text-gray-900', 'text-gray-100'),
  tableBody: (darkMode) => getThemeClasses(darkMode, 'bg-white divide-gray-200', 'bg-gray-800 divide-gray-700'),
  modal: (darkMode) => getThemeClasses(darkMode, 'bg-white text-gray-900', 'bg-gray-800 text-gray-100'),
  modalClose: (darkMode) => getThemeClasses(darkMode, 'bg-gray-700 text-white hover:bg-gray-900', 'bg-gray-600 text-gray-100 hover:bg-gray-500'),
  controlContainer: (darkMode) => getThemeClasses(darkMode, 'bg-green-50 border-green-200', 'bg-gray-800 border-gray-700'),
  navButton: (darkMode) => getThemeClasses(darkMode, 'border-green-600 text-gray-700 hover:bg-gray-100', 'border-green-500 text-gray-300 hover:bg-gray-600'),
  selectInput: (darkMode) => getThemeClasses(darkMode, 'bg-transparent text-gray-900', 'bg-transparent text-gray-100'),
  viewToggle: (darkMode) => getThemeClasses(darkMode, 'text-gray-900 hover:bg-gray-200', 'text-gray-300 hover:bg-gray-700'),
  viewToggleActive: (darkMode) => getThemeClasses(darkMode, 'bg-[#8bc34a] text-white', 'bg-lime-600 text-white'),
  totalText: (darkMode) => getThemeClasses(darkMode, 'text-green-600', 'text-green-400'),
  merchantCount: (darkMode) => getThemeClasses(darkMode, 'text-blue-600', 'text-blue-300'),
  badgeCategoryHousing: (darkMode) => getThemeClasses(darkMode, 'bg-red-100 text-red-800', 'bg-red-900/20 text-red-400'),
  badgeCategoryEntertainment: (darkMode) => getThemeClasses(darkMode, 'bg-yellow-100 text-yellow-800', 'bg-yellow-900/20 text-yellow-400'),
  badgeCategoryHealth: (darkMode) => getThemeClasses(darkMode, 'bg-green-100 text-green-800', 'bg-green-900/20 text-green-400'),
  badgeCategoryUtilities: (darkMode) => getThemeClasses(darkMode, 'bg-blue-100 text-blue-800', 'bg-blue-900/20 text-blue-400'),
  badgeCategoryDefault: (darkMode) => getThemeClasses(darkMode, 'bg-purple-100 text-purple-800', 'bg-purple-900/20 text-purple-400'),
  calendarTransaction: (darkMode) => getThemeClasses(darkMode, 'bg-gray-100 text-gray-900', 'bg-gray-700 text-gray-100'),
  tooltip: (darkMode) => getThemeClasses(darkMode, 'bg-gray-100 text-gray-900', 'bg-gray-700 text-gray-100'),
  // New classes for Cashflow
  headerText: (darkMode) => getThemeClasses(darkMode, 'text-gray-800', 'text-gray-200'),
  navText: (darkMode) => getThemeClasses(darkMode, 'text-gray-900', 'text-gray-100'),
  checkboxLabel: (darkMode) => getThemeClasses(darkMode, 'text-gray-700', 'text-gray-300'),
  checkboxInput: (darkMode) => getThemeClasses(darkMode, 'text-indigo-600 focus:ring-indigo-500 bg-white border-gray-300', 'text-indigo-400 focus:ring-indigo-400 bg-gray-600 border-gray-500'),
  loadingOverlay: (darkMode) => getThemeClasses(darkMode, 'bg-white', 'bg-gray-900'),
  svgText: (darkMode) => getThemeClasses(darkMode, 'fill-gray-900', 'fill-gray-100'),
  svgStroke: (darkMode) => getThemeClasses(darkMode, 'stroke-gray-600', 'stroke-gray-400'),
  svgStrokeHover: (darkMode) => getThemeClasses(darkMode, 'stroke-black', 'stroke-white'),
  svgLegendBackground: (darkMode) => getThemeClasses(darkMode, 'fill-white stroke-gray-300', 'fill-gray-700 stroke-gray-600'),
  categoryIncome: (darkMode) => getThemeClasses(darkMode, 'fill-teal-500', 'fill-teal-400'),
  categoryExpenses: (darkMode) => getThemeClasses(darkMode, 'fill-orange-400', 'fill-orange-300'),
  categoryBuffer: (darkMode) => getThemeClasses(darkMode, 'fill-green-400', 'fill-green-300'),
  categoryTax: (darkMode) => getThemeClasses(darkMode, 'fill-red-400', 'fill-red-300'),
  categoryDefault: (darkMode) => getThemeClasses(darkMode, 'fill-gray-400', 'fill-gray-500'),
  // New classes for Chatbot
  chatUserMessage: (darkMode) => getThemeClasses(darkMode, 'bg-green-200 text-black', 'bg-green-700 text-white'),
  chatAIMessage: (darkMode) => getThemeClasses(darkMode, 'bg-blue-200 text-black', 'bg-blue-700 text-white'),
  inputField: (darkMode) => getThemeClasses(darkMode, 'bg-white text-black border-black', 'bg-gray-700 text-white border-gray-600'),
  inputFocusRing: (darkMode) => getThemeClasses(darkMode, 'focus:ring-blue-500', 'focus:ring-blue-400'),
  scrollButton: (darkMode) => getThemeClasses(darkMode, 'bg-gray-200 text-black hover:bg-gray-300', 'bg-gray-600 text-white hover:bg-gray-500'),
  visualizationPanel: (darkMode) => getThemeClasses(darkMode, 'bg-white text-gray-900', 'bg-gray-800 text-gray-100'),
  closeButton: (darkMode) => getThemeClasses(darkMode, 'text-black hover:bg-gray-200', 'text-white hover:bg-gray-600'),
  emptyVisualization: (darkMode) => getThemeClasses(darkMode, 'text-gray-500', 'text-gray-400'),
  iconThumbsUp: (darkMode) => getThemeClasses(darkMode, 'text-gray-600 hover:text-green-600', 'text-gray-300 hover:text-green-400'),
  iconThumbsDown: (darkMode) => getThemeClasses(darkMode, 'text-gray-600 hover:text-red-600', 'text-gray-300 hover:text-red-400'),
  iconCopy: (darkMode) => getThemeClasses(darkMode, 'text-gray-600 hover:text-blue-600', 'text-gray-300 hover:text-blue-400'),
  copiedTooltip: (darkMode) => getThemeClasses(darkMode, 'bg-black text-white', 'bg-gray-700 text-white'),
  // New classes for ChatbotVisualizer
  chartContainer: (darkMode) => getThemeClasses(darkMode, 'bg-white text-gray-900', 'bg-gray-900 text-gray-100'),
  chartTitle: (darkMode) => getThemeClasses(darkMode, 'text-gray-800', 'text-gray-200'),
  saveButton: (darkMode) => getThemeClasses(darkMode, 'bg-white text-gray-800 border-gray-300 hover:bg-gray-100', 'bg-gray-800 text-gray-100 border-gray-600 hover:bg-gray-700'),
  saveButtonSaved: (darkMode) => getThemeClasses(darkMode, 'bg-green-100 text-green-600 border-green-300', 'bg-green-900/30 text-green-300 border-green-700'),
  saveButtonSaving: (darkMode) => getThemeClasses(darkMode, 'bg-gray-100 text-gray-600 border-gray-300', 'bg-gray-700 text-gray-400 border-gray-600'),
  downloadButton: (darkMode) => getThemeClasses(darkMode, 'bg-white text-gray-600 border-gray-300 hover:bg-gray-100', 'bg-gray-800 text-gray-300 border-gray-600 hover:bg-gray-700'),
  tableBorder: (darkMode) => getThemeClasses(darkMode, 'border-gray-300', 'border-gray-600'),
  paginationButton: (darkMode) => getThemeClasses(darkMode, 'bg-gray-200 text-black hover:bg-gray-300', 'bg-gray-700 text-gray-100 hover:bg-gray-600'),
  paginationButtonActive: (darkMode) => getThemeClasses(darkMode, 'bg-green-600 text-white', 'bg-green-500 text-white'),
  chartColors: (darkMode) => darkMode
    ? ['#93c5fd', '#86efac', '#fef08a', '#fdba74', '#a5f3fc', '#bef264', '#d9f99d', '#fed7aa', '#f87171', '#d8b4fe']
    : ['#3b82f6', '#22c55e', '#eab308', '#f97316', '#06b6d4', '#84cc16', '#a3e635', '#fb923c', '#ef4444', '#a855f7'],
  // New classes for CustomChartsSection
  draggableChart: (darkMode) => getThemeClasses(darkMode, 'bg-white text-black border-[#7fe029]', 'bg-gray-800 text-gray-100 border-gray-600'),
  chartShadow: (darkMode) => getThemeClasses(darkMode, 'shadow-[0_4px_12px_rgba(127,224,41,0.15)]', 'shadow-[0_4px_12px_rgba(0,0,0,0.2)]'),
  sectionHeader: (darkMode) => getThemeClasses(darkMode, 'text-gray-900', 'text-gray-100'),
  resetButton: (darkMode) => getThemeClasses(darkMode, 'bg-[#8bc34a] text-white hover:bg-[#6ec122]', 'bg-lime-600 text-white hover:bg-lime-700'),
  editButton: (darkMode) => getThemeClasses(darkMode, 'text-gray-600 hover:text-blue-500', 'text-gray-300 hover:text-blue-400'),
  noChartsText: (darkMode) => getThemeClasses(darkMode, 'text-gray-600', 'text-gray-300'),
  // New classes for BudgetRulePage
  pageContainer: (darkMode) => getThemeClasses(darkMode, 'bg-white text-gray-900', 'bg-gray-900 text-gray-100'),
  tableContainer: (darkMode) => getThemeClasses(darkMode, 'bg-white shadow-lg', 'bg-gray-800 shadow-lg'),
  tableHeaderRow: (darkMode) => getThemeClasses(darkMode, 'bg-[#c5e1a5] text-gray-700', 'bg-gray-700 text-gray-300'),
  tableRow: (darkMode) => getThemeClasses(darkMode, 'border-gray-200', 'border-gray-600'),
  modalOverlay: (darkMode) => getThemeClasses(darkMode, 'bg-black bg-opacity-50', 'bg-black bg-opacity-50'),
  modalContent: (darkMode) => getThemeClasses(darkMode, 'bg-white text-gray-900', 'bg-gray-800 text-gray-100'),
  modalHeader: (darkMode) => getThemeClasses(darkMode, 'bg-gray-100 text-gray-900', 'bg-gray-700 text-gray-100'),
  modalPanelLeft: (darkMode) => getThemeClasses(darkMode, 'bg-gray-50 text-gray-900', 'bg-gray-700 text-gray-100'),
  modalPanelRight: (darkMode) => getThemeClasses(darkMode, 'bg-white text-gray-900', 'bg-gray-800 text-gray-100'),
  modalFooter: (darkMode) => getThemeClasses(darkMode, 'border-gray-300', 'border-gray-600'),
  tooltipContainer: (darkMode) => getThemeClasses(darkMode, 'bg-white text-gray-900 shadow-md', 'bg-gray-700 text-gray-100 shadow-md'),
  tooltipTable: (darkMode) => getThemeClasses(darkMode, 'border-gray-300', 'border-gray-600'),
  tooltipTableCell: (darkMode) => getThemeClasses(darkMode, 'text-gray-900', 'text-gray-100'),
  tooltipTableHeader: (darkMode) => getThemeClasses(darkMode, 'text-gray-700', 'text-gray-300'),
  infoButton: (darkMode) => getThemeClasses(darkMode, 'text-gray-600 hover:bg-gray-100 hover:text-gray-800', 'text-gray-400 hover:bg-gray-700 hover:text-gray-200'),
  selectField: (darkMode) => getThemeClasses(darkMode, 'bg-white text-gray-900 border-gray-300', 'bg-gray-700 text-gray-100 border-gray-600'),
  // New classes for SettingsPage
  tabContainer: (darkMode) => getThemeClasses(darkMode, 'border-gray-300', 'border-gray-700'),
  tabButton: (darkMode) => getThemeClasses(darkMode, 'text-gray-900', 'text-gray-300'),
  tabIcon: (darkMode) => getThemeClasses(darkMode, 'text-gray-600', 'text-gray-400'),
  // New classes for NotificationSettingsModal
  tableCellBorder: (darkMode) => getThemeClasses(darkMode, 'border-r-gray-200', 'border-r-gray-600'),
  checkboxInput: (darkMode) => getThemeClasses(darkMode, 'text-green-600 focus:ring-green-500 bg-white border-gray-300', 'text-green-500 focus:ring-green-400 bg-gray-700 border-gray-600'),
  descriptionText: (darkMode) => getThemeClasses(darkMode, 'text-gray-500', 'text-gray-400'),
  spinner: (darkMode) => getThemeClasses(darkMode, 'border-2 border-t-2 border-white', 'border-2 border-t-2 border-gray-100'),
  // New classes for Notifications
  notificationItem: (darkMode) => getThemeClasses(darkMode, 'hover:bg-gray-50', 'hover:bg-gray-700'),
  severityBackgroundLow: (darkMode) => getThemeClasses(darkMode, 'bg-green-100', 'bg-green-900'),
  severityBackgroundMedium: (darkMode) => getThemeClasses(darkMode, 'bg-yellow-100', 'bg-yellow-900'),
  severityBackgroundHigh: (darkMode) => getThemeClasses(darkMode, 'bg-red-100', 'bg-red-900'),
  severityBackgroundCritical: (darkMode) => getThemeClasses(darkMode, 'bg-purple-100', 'bg-purple-900'),
  severityIconLow: (darkMode) => getThemeClasses(darkMode, 'text-green-500', 'text-green-400'),
  severityIconMedium: (darkMode) => getThemeClasses(darkMode, 'text-yellow-500', 'text-yellow-400'),
  severityIconHigh: (darkMode) => getThemeClasses(darkMode, 'text-orange-500', 'text-orange-400'),
  severityIconCritical: (darkMode) => getThemeClasses(darkMode, 'text-red-500', 'text-red-400'),
  severityIconDefault: (darkMode) => getThemeClasses(darkMode, 'text-gray-600', 'text-gray-300'),
  unreadNotification: (darkMode) => getThemeClasses(darkMode, '', ''),
  // New classes for FamilySection
  warning: (darkMode) => getThemeClasses(darkMode, 'bg-yellow-100 text-yellow-800 border-yellow-400', 'bg-yellow-900/30 text-yellow-300 border-yellow-700'),
  badgeStatusPending: (darkMode) => getThemeClasses(darkMode, 'bg-yellow-100 text-yellow-800', 'bg-yellow-900 text-yellow-300'),
  badgeStatusActive: (darkMode) => getThemeClasses(darkMode, 'bg-green-100 text-green-800', 'bg-green-900 text-green-300'),
  descriptionLabel: (darkMode) => getThemeClasses(darkMode, 'text-gray-700', 'text-gray-300'),
  // New classes for AccountsDashboard
  loadingMessage: (darkMode) => getThemeClasses(darkMode, 'bg-blue-100 text-blue-700 border-blue-400', 'bg-blue-900/30 text-blue-300 border-blue-700'),
  addAccountButton: (darkMode) => getThemeClasses(darkMode, 'bg-blue-600 text-white hover:bg-blue-700', 'bg-blue-700 text-white hover:bg-blue-600'),
  connectButton: (darkMode) => getThemeClasses(darkMode, 'bg-gray-800 text-white hover:bg-gray-700', 'bg-gray-700 text-white hover:bg-gray-600'),
  tableRowMock: (darkMode) => getThemeClasses(darkMode, 'bg-gray-50 text-gray-400', 'bg-gray-800/50 text-gray-500'),
  amountNegative: (darkMode) => getThemeClasses(darkMode, 'text-red-500', 'text-red-500'),
  syncIcon: (darkMode) => getThemeClasses(darkMode, 'text-gray-500 hover:text-blue-500', 'text-gray-400 hover:text-blue-400'),
  syncIconActive: (darkMode) => getThemeClasses(darkMode, 'text-blue-500', 'text-blue-400'),
  draggableChartActive: (darkMode) => getThemeClasses(darkMode, 'bg-white shadow-lg z-10', 'bg-gray-800 shadow-lg z-10'),
};