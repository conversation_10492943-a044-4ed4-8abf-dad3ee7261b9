import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>hart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import { formatCurrency, formatPercentage } from '../../../../logic/utils/formatters';

const PerformanceChart = ({ performanceData }) => {
  const [timeRange, setTimeRange] = useState('1M');
  const [chartData, setChartData] = useState([]);
  
  // Process chart data based on selected time range
  useEffect(() => {
    if (performanceData && performanceData.history) {
      const { history } = performanceData;
      
      // Filter data based on time range
      const today = new Date();
      let startDate = new Date();
      
      switch(timeRange) {
        case '1W':
          startDate.setDate(today.getDate() - 7);
          break;
        case '1M':
          startDate.setMonth(today.getMonth() - 1);
          break;
        case '3M':
          startDate.setMonth(today.getMonth() - 3);
          break;
        case '6M':
          startDate.setMonth(today.getMonth() - 6);
          break;
        case '1Y':
          startDate.setFullYear(today.getFullYear() - 1);
          break;
        case 'ALL':
          // Use all available data
          break;
        default:
          startDate.setMonth(today.getMonth() - 1);
      }
      
      // Filter data by date range
      const filteredData = timeRange === 'ALL' 
        ? history 
        : history.filter(item => new Date(item.date) >= startDate);
      
      setChartData(filteredData);
    }
  }, [performanceData, timeRange]);
  
  // If no data is available
  if (!performanceData || !performanceData.history || performanceData.history.length === 0) {
    return <div>No performance history available</div>;
  }
  
  const handleTimeRangeChange = (range) => {
    setTimeRange(range);
  };
  
  return (
    <div>
      <div className="btn-group mb-3" role="group">
        <button 
          type="button" 
          className={`btn btn-outline-primary ${timeRange === '1W' ? 'active' : ''}`}
          onClick={() => handleTimeRangeChange('1W')}
        >
          1W
        </button>
        <button 
          type="button" 
          className={`btn btn-outline-primary ${timeRange === '1M' ? 'active' : ''}`}
          onClick={() => handleTimeRangeChange('1M')}
        >
          1M
        </button>
        <button 
          type="button" 
          className={`btn btn-outline-primary ${timeRange === '3M' ? 'active' : ''}`}
          onClick={() => handleTimeRangeChange('3M')}
        >
          3M
        </button>
        <button 
          type="button" 
          className={`btn btn-outline-primary ${timeRange === '6M' ? 'active' : ''}`}
          onClick={() => handleTimeRangeChange('6M')}
        >
          6M
        </button>
        <button 
          type="button" 
          className={`btn btn-outline-primary ${timeRange === '1Y' ? 'active' : ''}`}
          onClick={() => handleTimeRangeChange('1Y')}
        >
          1Y
        </button>
        <button 
          type="button" 
          className={`btn btn-outline-primary ${timeRange === 'ALL' ? 'active' : ''}`}
          onClick={() => handleTimeRangeChange('ALL')}
        >
          ALL
        </button>
      </div>
      
      <ResponsiveContainer width="100%" height={300}>
        <LineChart
          data={chartData}
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="date" 
            tickFormatter={(tick) => {
              const date = new Date(tick);
              return date.toLocaleDateString();
            }}
          />
          <YAxis 
            yAxisId="left"
            domain={['auto', 'auto']}
            tickFormatter={(value) => formatCurrency(value, 'USD', 0)} 
          />
          <YAxis 
            yAxisId="right"
            orientation="right"
            domain={[0, 'auto']}
            tickFormatter={(value) => formatPercentage(value / 100)} 
          />
          <Tooltip 
            formatter={(value, name) => {
              if (name === 'totalValue') {
                return [formatCurrency(value, 'USD'), 'Portfolio Value'];
              } else if (name === 'gainPercent') {
                return [formatPercentage(value / 100), 'Total Return'];
              }
              return [value, name];
            }}
            labelFormatter={(label) => {
              const date = new Date(label);
              return date.toLocaleDateString();
            }}
          />
          <Legend />
          <Line 
            yAxisId="left"
            type="monotone" 
            dataKey="totalValue" 
            stroke="#8884d8" 
            name="Portfolio Value"
          />
          <Line 
            yAxisId="right"
            type="monotone" 
            dataKey="gainPercent" 
            stroke="#82ca9d" 
            name="Total Return (%)"
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default PerformanceChart;