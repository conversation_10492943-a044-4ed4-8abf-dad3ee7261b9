import React, { useState,useEffect } from 'react';
import { Link } from 'react-router-dom';
import './Sidebar.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {  faClipboardList, faDollarSign, faFileAlt, faWallet, faReceipt, faHome, faRepeat,faCog,faBullseye   } from '@fortawesome/free-solid-svg-icons';

const Sidebar = ({ isMinimized,toggleSidebar,isToggledByIcon }) => {
  const [activePage, setActivePage] = useState('dashboard1');
  const [mouseNear, setMouseNear] = useState(false);

  const handlePageSelect = (page) => {
    setActivePage(page);
  };

  useEffect(() => {
    const sidebarElement = document.querySelector('.sidebar');

    const handleMouseEnter = () => {
      if (!isToggledByIcon && isMinimized) {
        setMouseNear(true);
        toggleSidebar(); // Maximize the sidebar on hover
      }
    };

    const handleMouseLeave = () => {
      if (!isToggledByIcon && !isMinimized) {
        setMouseNear(false);
        toggleSidebar(); // Minimize the sidebar on hover
      }
    };

    if (!isToggledByIcon) {
      sidebarElement.addEventListener('mouseenter', handleMouseEnter);
      sidebarElement.addEventListener('mouseleave', handleMouseLeave);
    }

    return () => {
      sidebarElement.removeEventListener('mouseenter', handleMouseEnter);
      sidebarElement.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [isMinimized, isToggledByIcon, toggleSidebar]);


  return (
    <div className={`sidebar ${isMinimized ? 'minimized' : ''}`}>
      <div className="logo-section">
        <img src="/images/rocket.png" alt="Logo" className="logo" />
        {!isMinimized && <div className="sidebar-title">Pennypal</div>}
      </div>
      {/* <div className="menu-icon" onClick={toggleSidebar}>
        <img src="/images/menubar.png" alt="Menu" />
      </div> */}
      <div className="menu-section">
        <p className="menu-title">HOME</p>
        <ul>
          <li>
            <Link
              to='dashboard'
              className={activePage === 'dashboard' ? 'active' : ''}
              onClick={() => handlePageSelect('dashboard')}
              >
                <FontAwesomeIcon icon={faHome} />
                {!isMinimized && 'Dashboard'}
              </Link>
          </li>
          <li>
            <Link
              to="dashboard/accounts"
              className={activePage === 'accounts' ? 'active' : ''}
              onClick={() => handlePageSelect('accounts')}
            >
              <FontAwesomeIcon icon={faClipboardList} />
              {!isMinimized && 'Accounts'}
            </Link>
          </li>
          <li>
            <Link
              to="dashboard/transactions"
              className={activePage === 'transactions' ? 'active' : ''}
              onClick={() => handlePageSelect('transactions')}
            >
              <FontAwesomeIcon icon={faDollarSign} />
              {!isMinimized && 'Transactions'}
            </Link>
          </li>
          <li>
            <Link
              to="dashboard/recurring-transactions"
              className={activePage === "recurring-transactions" ? "active" : ""}
              onClick={() => handlePageSelect("recurring-transactions")}
            >
              <FontAwesomeIcon icon={faRepeat} />
                {!isMinimized && "Recurring Transactions"}
              </Link>
          </li>
          <li>
            <Link
              to="dashboard/reports"
              className={activePage === 'reports' ? 'active' : ''}
              onClick={() => handlePageSelect('reports')}
            >
              <FontAwesomeIcon icon={faFileAlt} />
              {!isMinimized && 'Reports'}
            </Link>
          </li>
	  <li>
            <Link
              to="dashboard/investment"
              className={activePage === 'investment' ? 'active' : ''}
              onClick={() => handlePageSelect('investment')}
            >
              <FontAwesomeIcon icon={faWallet} />
              {!isMinimized && 'Investment'}
            </Link>
          </li>
          <li>
            <Link
              to="dashboard/budget"
              className={activePage === 'budget' ? 'active' : ''}
              onClick={() => handlePageSelect('budget')}
            >
              <FontAwesomeIcon icon={faWallet} />
              {!isMinimized && 'Budget'}
            </Link>
          </li>
          <li>
            <Link
              to="dashboard/budget-rules"
              className={activePage === 'budget-rules' ? 'active' : ''}
              onClick={() => handlePageSelect('budget-rules')}
            >
              <FontAwesomeIcon icon={faWallet} />
              {!isMinimized && 'Budget Rules'}
            </Link>
          </li>
          <li>
            <Link
              to="dashboard/receipts"  // The new path to the Receipt page
              className={activePage === 'receipts' ? 'active' : ''}
              onClick={() => handlePageSelect('receipts')}
            >
              <FontAwesomeIcon icon={faReceipt} /> {/* Add Receipt icon */}
              {!isMinimized && 'Receipts'} {/* The text will be hidden in minimized mode */}
            </Link>
          </li>
          
          <li>
  <Link
    to="dashboard/goals"
    className={activePage === 'goals' ? 'active' : ''}
    onClick={() => handlePageSelect('goals')}
  >
    <FontAwesomeIcon icon={faBullseye} />
    {!isMinimized && 'Goals'}
  </Link>
</li>

        </ul>

        {/* <p className="menu-title">BUDGET</p>
        <ul>
         
        </ul> */}
      </div>
      <div className="settings-section">
  <Link
    to="dashboard/settings"
    className={activePage === 'settings' ? 'active' : ''}
    onClick={() => handlePageSelect('settings')}
  >
    <FontAwesomeIcon icon={faCog} />
    {!isMinimized && 'Settings'}
  </Link>
</div>
      <div className="profile-section">
        {!isMinimized && (
          <>
            <img src="/images/user.png" alt="User" className="profile-pic" />
            <div className="profile-info">
              <p>Mike</p>
              <span>Admin</span>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default Sidebar;
