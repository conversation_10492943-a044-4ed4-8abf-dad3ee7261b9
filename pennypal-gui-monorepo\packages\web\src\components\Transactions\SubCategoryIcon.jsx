// SubCategoryIcon.jsx
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchAllIconsStart } from '../../../../logic/redux/transactionSlice';
import { FaQuestionCircle } from 'react-icons/fa';
import { CircularProgress } from '@mui/material';
import { logEvent } from '../../utils/EventLogger';

const SubCategoryIcon = ({ subCategoryId, size = 25, className = '' }) => {
  const dispatch = useDispatch();
const { iconMap, loaded, loading } = useSelector(state => state.transactions);
  
  // Load all icons on mount if not already loaded
 useEffect(() => {
  // console.log('Current iconMap:', iconMap); // Add this line
  if (!loaded && !loading) {
  logEvent('SubCategoryIcon', 'IconFetchInitiated', { subCategoryId });
    dispatch(fetchAllIconsStart());
  }
}, [dispatch, loaded, loading, iconMap]);

  if (!subCategoryId) {
    logEvent('SubCategoryIcon', 'NoSubCategoryId');
    return null;
  }


  if (loading || !loaded) {
    return <CircularProgress size={size} />;
  }

  const iconUrl = iconMap[subCategoryId];
   if (!iconUrl) {
    logEvent('SubCategoryIcon', 'DefaultIconShown', { subCategoryId });
  }

  return (
    <span className={`inline-flex items-center ${className}`}>
      {iconUrl ? (
        <img 
          src={iconUrl}
          alt="Subcategory icon"
          style={{ 
            width: 25,
            height: 25,
            objectFit: 'contain'
          }}
          onClick={() => {
            logEvent('SubCategoryIcon', 'IconClicked', {
              subCategoryId,
              iconUrl
            });
          }}
        />
      ) : (
        <FaQuestionCircle 
          style={{ 
            color: '#607d8b',
            fontSize: size,
              width: size,  // Add this
          height: size 
          }}
           onClick={() => {
            logEvent('SubCategoryIcon', 'DefaultIconClicked', {
              subCategoryId
            });
          }}
        />
      )}
    </span>
  );
};

export default SubCategoryIcon;