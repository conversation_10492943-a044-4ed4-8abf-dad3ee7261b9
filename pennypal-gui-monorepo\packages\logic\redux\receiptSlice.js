import { createSlice,createAction } from '@reduxjs/toolkit';

export const addNewTransaction = createAction('receipts/addNewTransaction');
const initialState = {
  errorMessage: '',
  jsonResponse: {},
  currentTab: 'receipt',
  receiptUploadModal: false,
  showError: false,
  blinkError: false,
  isReceiptModalOpen: false,
  selectedReceipt: null,
  isMatchingTransactionAdd: false, // New flag
  // fileMetadata: [], // Replaced selectedFile
  selectedTransactions: [],
  selectedTransaction: null,
  isModalOpen: false,
  uploadPopupWidth: '700px',
  editingField: null,
  editedValue: '',
  editedItemIndex: null,
  selectedDate: null,
  uploadProgress: 0,
  isUploading: false,
  isProcessing: false,
  isPopupVisible: false,
  // receiptNewTransaction: {
  //   date: '',
  //   description: '',
  //   category: '',
  //   account: '',
  //   amount: '',
  // },
  receiptTransactionIds: [],
  loading: false,
  error: null,
};

const receiptSlice = createSlice({
  name: 'receipts',
  initialState,
  reducers: {
    // UI state actions
    setErrorMessage: (state, action) => {
      state.errorMessage = action.payload;
    },
    setJsonResponse: (state, action) => {
      state.jsonResponse = action.payload;
    },
    setCurrentTab: (state, action) => {
      state.currentTab = action.payload;
    },
    setReceiptUploadModal: (state, action) => {
      state.receiptUploadModal = action.payload;
    },
    setShowError: (state, action) => {
      state.showError = action.payload;
    },
    setBlinkError: (state, action) => {
      state.blinkError = action.payload;
    },
    setIsReceiptModalOpen: (state, action) => {
      state.isReceiptModalOpen = action.payload;
    },
    setSelectedReceipt: (state, action) => {
      state.selectedReceipt = action.payload;
    },
    setFileMetadata: (state, action) => { // Replaced setSelectedFile
      state.fileMetadata = action.payload;
    },
    setSelectedTransactions: (state, action) => {
      state.selectedTransactions = action.payload;
    },
    setIsMatchingTransactionAdd: (state, action) => {
      state.isMatchingTransactionAdd = action.payload;
    },
    setSelectedTransaction: (state, action) => {
      state.selectedTransaction = action.payload;
    },
    setIsModalOpen: (state, action) => {
      state.isModalOpen = action.payload;
    },
    setUploadPopupWidth: (state, action) => {
      state.uploadPopupWidth = action.payload;
    },
    setEditingField: (state, action) => {
      state.editingField = action.payload;
    },
    setEditedValue: (state, action) => {
      state.editedValue = action.payload;
    },
    setEditedItemIndex: (state, action) => {
      state.editedItemIndex = action.payload;
    },
    setSelectedDate: (state, action) => {
      state.selectedDate = action.payload;
    },
    setUploadProgress: (state, action) => {
      state.uploadProgress = action.payload;
    },
    setIsUploading: (state, action) => {
      state.isUploading = action.payload;
    },
    setIsProcessing: (state, action) => {
      state.isProcessing = action.payload;
    },
    setIsPopupVisible: (state, action) => {
      state.isPopupVisible = action.payload;
    },
    // setReceiptNewTransaction: (state, action) => {
    //   state.receiptNewTransaction  = action.payload;
    // },
    setReceiptTransactionIds: (state, action) => {
      state.receiptTransactionIds = action.payload;
    },
    // API actions
    uploadReceiptRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
   uploadReceiptSuccess: (state, action) => {
  state.loading = false;
  state.jsonResponse = {
    ...action.payload,
    // Ensure all required fields are properly set
    Items: action.payload.Items || [],
    matchingTransactions: action.payload.matchingTransactions || [],
  };
  state.receiptUploadModal = true;
  state.isProcessing = false;
  state.uploadProgress = 100;
  state.currentTab = 'receipt';
  state.errorMessage = '';
  state.showError = false;
},
    uploadReceiptFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
      state.isProcessing = false;
      state.uploadProgress = 0;
    },
    saveReceiptRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    saveReceiptSuccess: (state, action) => {
      state.loading = false;
      state.receiptUploadModal = false;
      state.selectedTransaction = null;
      state.fileMetadata = []; // Clear metadata
      state.editingField = null;
      state.editedItemIndex = null;
      state.editedValue = null;
      state.errorMessage = '';
  state.showError = false;
    console.log('Receipt saved successfully:', action.payload);

    },
    saveReceiptFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },
    fetchReceiptTransactionIdsRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchReceiptTransactionIdsSuccess: (state, action) => {
      state.loading = false;
      state.receiptTransactionIds = action.payload;
    },
    fetchReceiptTransactionIdsFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },
    fetchReceiptDetailsRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchReceiptDetailsSuccess: (state, action) => {
      state.loading = false;
      state.selectedReceipt = action.payload;
      state.isReceiptModalOpen = true;
    },
    fetchReceiptDetailsFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },
    addTransactionRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
 // In your receiptSlice reducers
// addNewTransaction: (state, action) => {
//   const newTransaction = action.payload;
  
//   // Ensure matchingTransactions exists and is an array
//   if (!state.jsonResponse.matchingTransactions) {
//     state.jsonResponse.matchingTransactions = [];
//   }
  
//   // Add the new transaction
//   state.jsonResponse.matchingTransactions = [
//     ...state.jsonResponse.matchingTransactions,
//     newTransaction
//   ];
  
//   // Clear the popup and reset form
//   state.isPopupVisible = false;
//   state.newTransaction = initialState.newTransaction;
// },
  
  //   addTransactionSuccess: (state, action) => {
  //     state.loading = false;
  //     state.jsonResponse.matchingTransactions = [
  //       ...(state.jsonResponse.matchingTransactions || []),
  //       action.payload,
  //     ];
  //     state.isPopupVisible = false;
  //     state.newTransaction = initialState.newTransaction;
  //     state.addTransactionSuccess = true; // Set success flag
  // state.addTransactionError = null;
  //   },
  //   addTransactionFailure: (state, action) => {
  //     state.loading = false;
  //     state.error = action.payload;
  //     state.addTransactionSuccess = false;        // Clear success
  //   },
    updateReceiptField: (state, action) => {
      const { field, value, itemIndex } = action.payload;
      if (itemIndex !== null) {
        state.jsonResponse = {
          ...state.jsonResponse,
          Items: state.jsonResponse.Items.map((item, index) =>
            index === itemIndex ? { ...item, [field]: value } : item
          ),
        };
      } else {
        state.jsonResponse = {
          ...state.jsonResponse,
          [field]: value,
        };
      }
    },
  },
  // In your receiptSlice reducers
extraReducers: (builder) => {
  builder.addCase(addNewTransaction, (state, action) => {
    const newTransaction = action.payload;
    
    // Initialize matchingTransactions if it doesn't exist
    if (!state.jsonResponse.matchingTransactions) {
      state.jsonResponse.matchingTransactions = [];
    }
    
    // Add the new transaction
    state.jsonResponse.matchingTransactions = [
      ...state.jsonResponse.matchingTransactions,
      newTransaction
    ];
    
    // Automatically select the new transaction
    state.selectedTransaction = newTransaction.transactionId;
    
   state.isMatchingTransactionAdd = false;
    state.receiptUploadModal = true;
    state.currentTab = 'matchingTransaction';
  });
}
});

export const {
  setErrorMessage,
  setJsonResponse,
  setCurrentTab,
  setReceiptUploadModal,
  setShowError,
  setBlinkError,
  setIsReceiptModalOpen,
  setSelectedReceipt,
  // setFileMetadata, // Replaced setSelectedFile
  setSelectedTransactions,
  setSelectedTransaction,
  setIsModalOpen,
  setUploadPopupWidth,
  setEditingField,
  setEditedValue,
  setEditedItemIndex,
  setSelectedDate,
  setUploadProgress,
  setIsUploading,
  setIsProcessing,
  setIsPopupVisible,
  // setReceiptNewTransaction,
  setReceiptTransactionIds,
  uploadReceiptRequest,
  uploadReceiptSuccess,
  uploadReceiptFailure,
  saveReceiptRequest,
  saveReceiptSuccess,
  saveReceiptFailure,
  fetchReceiptTransactionIdsRequest,
  fetchReceiptTransactionIdsSuccess,
  fetchReceiptTransactionIdsFailure,
  fetchReceiptDetailsRequest,
  fetchReceiptDetailsSuccess,
  fetchReceiptDetailsFailure,
  addTransactionRequest,

  // addTransactionSuccess,
  // addTransactionFailure,
  updateReceiptField,
  setIsMatchingTransactionAdd,
} = receiptSlice.actions;

export default receiptSlice.reducer;