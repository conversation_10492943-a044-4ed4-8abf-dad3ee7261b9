// src/redux/slices/goalSlice.js
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  goals: [],
  selectedGoal: null,
  loading: false,
  error: null,
  monthlyGoals: [],
  goalSummary: null,
  successMessage: '',
  isAddingGoal: false,
  isAddingContribution: false
};

const goalSlice = createSlice({
  name: 'goals',
  initialState,
  reducers: {
    // Get all goals
    fetchGoalsRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchGoalsSuccess: (state, action) => {
      state.goals = action.payload;
      state.loading = false;
    },
    fetchGoalsFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Get single goal
    fetchGoalRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchGoalSuccess: (state, action) => {
      state.selectedGoal = action.payload;
      state.loading = false;
    },
    fetchGoalFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Create goal
    createGoalRequest: (state) => {
      state.isAddingGoal = true;
      state.error = null;
    },
    createGoalSuccess: (state, action) => {
      state.goals = [...state.goals, action.payload];
      state.isAddingGoal = false;
      state.successMessage = 'Goal created successfully!';
    },
    createGoalFailure: (state, action) => {
      state.isAddingGoal = false;
      state.error = action.payload;
    },

    // Update goal
    updateGoalRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    updateGoalSuccess: (state, action) => {
      state.goals = state.goals.map(goal => 
        goal.id === action.payload.id ? action.payload : goal
      );
      if (state.selectedGoal && state.selectedGoal.id === action.payload.id) {
        state.selectedGoal = action.payload;
      }
      state.loading = false;
      state.successMessage = 'Goal updated successfully!';
    },
    updateGoalFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Contribute to goal
    contributeToGoalRequest: (state) => {
      state.isAddingContribution = true;
      state.error = null;
    },
    contributeToGoalSuccess: (state, action) => {
      // Update goal in goals array
      state.goals = state.goals.map(goal => 
        goal.id === action.payload.id ? action.payload : goal
      );
      // Update selected goal if it's the current one
      if (state.selectedGoal && state.selectedGoal.id === action.payload.id) {
        state.selectedGoal = action.payload;
      }
      state.isAddingContribution = false;
      state.successMessage = 'Contribution added successfully!';
    },
    contributeToGoalFailure: (state, action) => {
      state.isAddingContribution = false;
      state.error = action.payload;
    },

    // Get goals for month
    fetchMonthlyGoalsRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchMonthlyGoalsSuccess: (state, action) => {
      state.monthlyGoals = action.payload;
      state.loading = false;
    },
    fetchMonthlyGoalsFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Get goals summary
    fetchGoalSummaryRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchGoalSummarySuccess: (state, action) => {
      state.goalSummary = action.payload;
      state.loading = false;
    },
    fetchGoalSummaryFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Create next goal based on previous one
    createNextGoalRequest: (state) => {
      state.isAddingGoal = true;
      state.error = null;
    },
    createNextGoalSuccess: (state, action) => {
      state.goals = [...state.goals, action.payload];
      state.isAddingGoal = false;
      state.successMessage = 'Next goal created successfully!';
    },
    createNextGoalFailure: (state, action) => {
      state.isAddingGoal = false;
      state.error = action.payload;
    },

    // Clear messages and errors
    clearMessages: (state) => {
      state.successMessage = '';
      state.error = null;
    },
    
    // Set selected goal
    setSelectedGoal: (state, action) => {
      state.selectedGoal = action.payload;
    },
    clearSelectedGoal: (state) => {
      state.selectedGoal = null;
    }
  }
});

export const {
  fetchGoalsRequest,
  fetchGoalsSuccess,
  fetchGoalsFailure,
  fetchGoalRequest,
  fetchGoalSuccess,
  fetchGoalFailure,
  createGoalRequest,
  createGoalSuccess,
  createGoalFailure,
  updateGoalRequest,
  updateGoalSuccess,
  updateGoalFailure,
  contributeToGoalRequest,
  contributeToGoalSuccess,
  contributeToGoalFailure,
  fetchMonthlyGoalsRequest,
  fetchMonthlyGoalsSuccess,
  fetchMonthlyGoalsFailure,
  fetchGoalSummaryRequest,
  fetchGoalSummarySuccess,
  fetchGoalSummaryFailure,
  createNextGoalRequest,
  createNextGoalSuccess,
  createNextGoalFailure,
  clearMessages,
  setSelectedGoal,
  clearSelectedGoal
} = goalSlice.actions;

export default goalSlice.reducer;