import { ofType, combineEpics } from 'redux-observable';
import { of, from } from 'rxjs';
import { mergeMap } from 'rxjs/operators';
import {
  invalidateTransactionCache,
  invalidateRecurringTransactionCache,
  invalidateBudgetCache,
  invalidateAllTransactionRelatedCache,
  invalidateRec<PERSON>ptCache,
  invalidateC<PERSON>botCache,
  invalidateSubcategoryCache,
  invalidateAccountCache,
  fetchTransactionsStart,
  fetchRecurringTransactionsStart,
  fetchFutureRecurringTransactionsStart,
  fetchBudgetSummaryStart,
  fetchBudgetDataStart,
  fetchTransactionSummaryStart,
  fetchHiddenTransactionsStart,
  fetchReconcileDataStart,
  fetchReceiptTransactionIdsStart,
  fetchReceiptItemsStart,
  fetchReceiptSummaryStart,
  fetchUserReceiptsStart,
  fetchChatbotHistoryStart,
  fetchDistinctSubcategoriesStart,
  fetchAccountIdsStart,
  fetchUserAccountsStart,
  fetchAccountBalancesInvestmentStart,
  fetchAccountBalancesDepositoryStart,
  fetchAccountBalancesLoanStart,
  fetchAccountBalancesCreditStart,
  invalidatePaymentSubscriptionCache,
  invalidatePaymentMethodsCache,
  fetchPaymentSubscriptionStart,
  fetchPaymentMethodsStart,
  fetchPaymentInvoicesStart
} from '../redux/cacheSlice';

// Epic to invalidate cache when transactions are added/modified/deleted
export const invalidateOnTransactionChangeEpic = (action$) =>
  action$.pipe(
    ofType(
      'transactions/addTransactionSuccess',
      'transactions/updateTransactionSuccess',
      'transactions/deleteTransactionSuccess',
      'transactions/hideFromBudgetSuccess'
    ),
    mergeMap((action) => {
      console.log('🔄 Transaction data changed, invalidating related cache:', action.type);

      // Invalidate transaction, budget, and subcategory caches
      return from([
        invalidateTransactionCache(),
        invalidateBudgetCache(),
        invalidateSubcategoryCache()
      ]);
    })
  );

// Epic to invalidate cache when accounts are synced/added
export const invalidateOnAccountSyncEpic = (action$) =>
  action$.pipe(
    ofType(
      'accounts/refreshAllAccounts/fulfilled',
      'accounts/syncAccount/fulfilled',
      'accounts/exchangePublicToken/fulfilled'
    ),
    mergeMap((action) => {
      console.log('🔄 Account sync completed, invalidating all transaction and account-related cache:', action.type);

      // Invalidate account, transaction, budget, and subcategory caches
      return from([
        invalidateAccountCache(),
        invalidateAllTransactionRelatedCache(),
        invalidateBudgetCache(),
        invalidateSubcategoryCache()
      ]);
    })
  );

// Epic to invalidate cache when budget is added/modified/deleted
export const invalidateOnBudgetChangeEpic = (action$) =>
  action$.pipe(
    ofType(
      'budget/addBudget',
      'budget/updateBudget',
      'budget/deleteSubcategory',
      'budget/saveBudget',
      'budget/addBudgetItem'
    ),
    mergeMap((action) => {
      console.log('🔄 Budget data changed, invalidating budget and subcategory cache:', action.type);

      // Invalidate budget and subcategory cache
      return from([
        invalidateBudgetCache(),
        invalidateSubcategoryCache()
      ]);
    })
  );

// Epic to invalidate cache when receipts are uploaded/saved/modified
export const invalidateOnReceiptChangeEpic = (action$) =>
  action$.pipe(
    ofType(
      // Receipt actions that exist in the codebase
      'receipts/uploadReceiptSuccess',
      'receipts/saveReceiptSuccess',
      'receipts/addNewTransaction'
    ),
    mergeMap((action) => {
      console.log('🔄 Receipt data changed, invalidating receipt cache:', action.type);

      // Invalidate receipt-related cache
      return of(invalidateReceiptCache());
    })
  );

// Epic to invalidate cache when new chatbot queries are made
export const invalidateOnChatbotQueryEpic = (action$) =>
  action$.pipe(
    ofType(
      // Listen for successful chatbot queries
      'chatbot/querySuccess'
    ),
    mergeMap((action) => {
      console.log('🔄 New chatbot query, invalidating chatbot cache:', action.type);

      // Invalidate chatbot history cache
      return of(invalidateChatbotCache());
    })
  );

// Epic to invalidate payment subscription cache when subscription is added/updated
export const invalidatePaymentSubscriptionEpic = (action$) =>
  action$.pipe(
    ofType(
      'payment/startSubscription',
      'payment/updateSubscription',
      'payment/cancelSubscription'
    ),
    mergeMap((action) => {
      console.log('🔄 Subscription changed, invalidating payment subscription cache:', action.type);

      // Invalidate subscription, invoices, and upcoming invoice cache
      return of(invalidatePaymentSubscriptionCache());
    })
  );

// Epic to invalidate payment methods cache when payment method is added or default changed
export const invalidatePaymentMethodsEpic = (action$) =>
  action$.pipe(
    ofType(
      'payment/setDefaultPaymentMethod'
    ),
    mergeMap((action) => {
      console.log('🔄 Payment method changed, invalidating payment methods cache:', action.type);

      // Invalidate payment methods cache
      return of(invalidatePaymentMethodsCache());
    })
  );

// Epic to automatically refetch data after cache invalidation
export const refetchAfterInvalidationEpic = (action$, state$) =>
  action$.pipe(
    ofType(
      invalidateAllTransactionRelatedCache.type,
      invalidateTransactionCache.type,
      invalidateRecurringTransactionCache.type,
      invalidateBudgetCache.type,
      invalidateReceiptCache.type,
      invalidateChatbotCache.type,
      invalidateSubcategoryCache.type,
      invalidateAccountCache.type,
      invalidatePaymentSubscriptionCache.type,
      invalidatePaymentMethodsCache.type
    ),
    mergeMap((action) => {
      console.log('🔄 Cache invalidated, triggering refetch:', action.type);
      
      const userId = state$.value?.auth?.user?.id;
      if (!userId) {
        console.warn('⚠️ User ID not found, skipping refetch');
        return of({ type: 'cache/refetchSkipped' });
      }
      
      const actions = [];
      
      // Determine what to refetch based on what was invalidated
      if (action.type === invalidateAllTransactionRelatedCache.type) {
        // Refetch everything transaction-related
        actions.push(
          fetchTransactionsStart(),
          fetchRecurringTransactionsStart(),
          fetchFutureRecurringTransactionsStart(),
          fetchTransactionSummaryStart({ userId }),
          fetchHiddenTransactionsStart({ userId }),
          fetchReconcileDataStart()
        );

      } else if (action.type === invalidateTransactionCache.type) {
        actions.push(fetchTransactionsStart());

      } else if (action.type === invalidateRecurringTransactionCache.type) {
        actions.push(
          fetchRecurringTransactionsStart(),
          fetchFutureRecurringTransactionsStart()
        );

      } else if (action.type === invalidateBudgetCache.type) {
        actions.push(fetchBudgetDataStart({ userId }));

        // Also refetch budget summary for current month
        const now = new Date();
        const currentYear = now.getFullYear();
        const currentMonth = now.getMonth() + 1;
        actions.push(fetchBudgetSummaryStart({ userId, year: currentYear, month: currentMonth }));

      } else if (action.type === invalidateReceiptCache.type) {
        // Refetch all receipt-related data
        actions.push(
          fetchReceiptTransactionIdsStart(),
          fetchReceiptItemsStart(),
          fetchReceiptSummaryStart(),
          fetchUserReceiptsStart({ userId })
        );

      } else if (action.type === invalidateChatbotCache.type) {
        // Refetch chatbot history
        actions.push(fetchChatbotHistoryStart({ userId }));

      } else if (action.type === invalidateSubcategoryCache.type) {
        // Refetch distinct subcategories
        actions.push(fetchDistinctSubcategoriesStart({ userId }));

      } else if (action.type === invalidateAccountCache.type) {
        // Refetch all account-related data with default x=12, y=30 parameters
        actions.push(
          fetchAccountIdsStart({ userId }),
          fetchUserAccountsStart({ userId }),
          fetchAccountBalancesInvestmentStart({ userId, x: 12, y: 30 }),
          fetchAccountBalancesDepositoryStart({ userId, x: 12, y: 30 }),
          fetchAccountBalancesLoanStart({ userId, x: 12, y: 30 }),
          fetchAccountBalancesCreditStart({ userId, x: 12, y: 30 })
        );

      } else if (action.type === invalidatePaymentSubscriptionCache.type) {
        // Refetch payment subscription and invoices
        // Note: upcoming invoice will be automatically fetched after subscription is loaded
        // via fetchUpcomingInvoiceAfterSubscriptionEpic, so we don't need to fetch it here
        actions.push(
          fetchPaymentSubscriptionStart({ userId }),
          fetchPaymentInvoicesStart({ userId }),
          fetchPaymentMethodsStart({ userId })
        );

      } else if (action.type === invalidatePaymentMethodsCache.type) {
        // Refetch payment methods
        actions.push(fetchPaymentMethodsStart({ userId }));
      }

      // Return all refetch actions
      return actions.length > 0 ? of(...actions) : of({ type: 'cache/noRefetchNeeded' });
    })
  );

// Combined cache invalidation epic
export const cacheInvalidationEpic = combineEpics(
  invalidateOnTransactionChangeEpic,
  invalidateOnAccountSyncEpic,
  invalidateOnBudgetChangeEpic,
  invalidateOnReceiptChangeEpic,
  invalidateOnChatbotQueryEpic,
  invalidatePaymentSubscriptionEpic,
  invalidatePaymentMethodsEpic,
  refetchAfterInvalidationEpic
);

export default cacheInvalidationEpic;