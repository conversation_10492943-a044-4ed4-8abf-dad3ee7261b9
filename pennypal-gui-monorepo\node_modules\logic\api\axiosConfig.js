import axios from 'axios';
import { jwtDecode } from "jwt-decode";
// Use conditional import to handle both web and React Native environments
const Platform = typeof navigator !== 'undefined' && navigator.product === 'ReactNative' 
  ? require('react-native').Platform 
  : { OS: 'web' };

import AsyncStorage from '@react-native-async-storage/async-storage';
import Cookies from 'js-cookie';

// Import cache clearing functionality
import { clearCache } from '../redux/cacheSlice';

const TOKEN_STORAGE_KEY = 'pennypal_jwt_token';
const TOKEN_COOKIE_NAME = TOKEN_STORAGE_KEY;
const USER_ID_COOKIE_NAME = 'pennypal_user_id';
const USER_PERMISSION_COOKIE_NAME = 'pennypal_user_permissions';

let isMobile = true;

// Configure base URL based on platform
const getBaseUrl = () => {
  if (Platform.OS === 'android') {
    return 'http://********:8080';
  } else if (Platform.OS === 'ios') {
    return 'http://************:8080';
  } else {
    isMobile = false;
    return 'http://localhost:8080'; // Default for web
  }
};

export const axiosInstance = axios.create({
  baseURL: getBaseUrl(),
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000,
});

export const getTokenFromCookies = () => {
  return Cookies.get(TOKEN_COOKIE_NAME);
};

export const plainAxios = axios.create({
  baseURL: getBaseUrl(),
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000
});

// Request interceptor
axiosInstance.interceptors.request.use(
  async (config) => {
    console.log('Request interceptor called');
    let storage = isMobile ? "AsyncStorage" : "Cookies";
    try {
      let token = null;
         let userId = null;
      if(!isMobile) {
       token=  Cookies.get(TOKEN_COOKIE_NAME);
        userId = Cookies.get(USER_ID_COOKIE_NAME);
      } else {
        token = await AsyncStorage.getItem(TOKEN_STORAGE_KEY) ;
      }
      console.log('Token from cookies:', token);
      console.log(`Request to ${config.url}`);
      console.log('Token found in cookies:', token ? 'Yes' : 'No');
      console.log('User ID found in cookies:', userId ? userId : 'No');
      if (token) {
        config.headers['Authorization'] = `Bearer ${token}`; // Make sure this format matches your backend expectation
        console.log('Added token to request headers');
      } else {
        console.warn('No token found in '+storage);
        console.warn(`No auth token found for request to: ${config.url}`);
      }
       if (userId) {
          config.headers['User-Id'] = userId;
          console.log('Added User-Id to request headers');
        } else {
          console.warn(`No User-Id found for request to: ${config.url}`);
        }
      console.log('Request URL:', config.baseURL + config.url);
    } catch (error) {
      console.error('Error retrieving token from AsyncStorage:', error);
    }
    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
axiosInstance.interceptors.response.use(
  async (response) => {
    // Handle token in response if present
    let token = response.data?.token || response.data?.jwtToken;
    if (token) {
     try {
    // TARGETED FIX: Only skip user ID update for goal endpoints
        const isGoalEndpoint = response.config?.url?.includes('/goals') || 
                          response.config?.url?.includes('/goal');
        if(!isMobile){
          if (!isGoalEndpoint) {
            // Get user ID from response (either from user object or directly)
            const userId = response.data?.user?.id || response.data?.id;
      
          if (userId) {
            Cookies.set(TOKEN_COOKIE_NAME, token, { 
              expires: 7,
              secure: process.env.NODE_ENV === 'production',
              sameSite: 'strict'
            });
          }
          console.log('Token saved to cookies');
        } else {
           await AsyncStorage.setItem(TOKEN_STORAGE_KEY, token);
          console.log('Token saved to AsyncStorage');
        }
      }
      } catch (error) {
        console.error('Error saving token to Cookie/AsyncStorage:', error);
      }
    }
    return response;
  },
  // (error) => {
  async (error) => {
    // Save original request
    const originalRequest = error.config;

    if (error.response?.status === 401 && localStorage.getItem("refreshToken") && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        const res = await plainAxios.post(
          'http://localhost:8080/pennypal/api/v1/auth/refresh-token',
          { token: localStorage.getItem("refreshToken") },
          { headers: {} }
        );

        if (res.status === 200) {
          setAuthToken(res.data.token);

          // Retry the original request with the new token
          originalRequest.headers['Authorization'] = `Bearer ${res.data.token}`;
          return axiosInstance(originalRequest);
        } else {
          removeToken();
         
        }
      } catch (err) {
        removeToken();
       
      };
    }

    if (error.response?.status === 401) {
      console.log('Unauthorized request - removing token');
      removeToken();
    }
    return Promise.reject(error);
  }
);

const removeToken = () => {

  if(isMobile){
    AsyncStorage.removeItem(TOKEN_STORAGE_KEY)
    .catch(error => console.error('Error removing token from AsyncStorage:', error));
  } else {
    Cookies.remove(TOKEN_COOKIE_NAME);
    window.location.href = '/login';
  }

}

export const setAuthToken = async (token) => {
  if (token) {
  try {
      if(isMobile){
        await AsyncStorage.setItem(TOKEN_STORAGE_KEY, token);
      } else {
      
        Cookies.set(TOKEN_COOKIE_NAME, token, { 
          expires: 7,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict'
        });

        // Decode and store userId
        try {
          const decodedToken = jwtDecode(token);
          const userId = decodedToken.userId;
          if (userId) {
            Cookies.set(USER_ID_COOKIE_NAME, userId.toString(), { 
              expires: 7,
              secure: process.env.NODE_ENV === 'production',
              sameSite: 'strict'
            });
          }
          const permissions = decodedToken.permissions;
          if (permissions) {
            Cookies.set(USER_PERMISSION_COOKIE_NAME, JSON.stringify(permissions), { 
              expires: 7,
              secure: process.env.NODE_ENV === 'production',
              sameSite: 'strict'
            });
          }
        } catch (e) {
          console.error('Error decoding token to extract userId:', e);
        }
      }
      // Update axios default headers
      axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      console.log('Token set in axios headers');
    } catch (error) {
      console.error('Error setting auth token in AsyncStorage:', error);
    }
  } else {
  
    try {
      if(isMobile){
        await AsyncStorage.removeItem(TOKEN_STORAGE_KEY);
      } else {
        Cookies.remove(TOKEN_COOKIE_NAME);
      }
      delete axiosInstance.defaults.headers.common['Authorization'];
      console.log('Token removed from axios headers');
    } catch (error) {
      console.error('Error removing auth token from AsyncStorage:', error);
    }
    
    //console.log('Token removed from axios headers');
  }
};

export const getAuthToken = async () => {
  try {
    return await AsyncStorage.getItem(TOKEN_STORAGE_KEY);
  } catch (error) {
    console.error('Error getting auth token from AsyncStorage:', error);
    return null;
  }
};

export const removeAuthToken = async () => {
  try {
    await AsyncStorage.removeItem(TOKEN_STORAGE_KEY);
    console.log('Token removed from AsyncStorage');
    return true;
  } catch (error) {
    console.error('Error removing auth token from AsyncStorage:', error);
    return false;
  }
};


export const setRefreshToken = (token) => {
  console.log('Setting refresh token:', token);
  if (token) {
    localStorage.setItem('refreshToken', token, { 
      expires: 30,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    });
    console.log('Refresh token set in cookies');
  } else {
    localStorage.removeItem('refreshToken');
    console.log('Refresh token removed from cookies');
  }
};

export const setUserId = (userId) => {
  if (userId) {
    try {
      Cookies.set(USER_ID_COOKIE_NAME, userId.toString(), {
        expires: 7,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict'
      });
      console.log('User ID successfully set in cookies');
    } catch (e) {
      console.error('Error setting user ID:', e);
    }
  } else {
    Cookies.remove(USER_ID_COOKIE_NAME);
    Cookies.remove(USER_PERMISSION_COOKIE_NAME);
    console.log('User ID removed from cookies');
  }
};
// Add a method to check if user is logged in
export const isAuthenticated = () => {
  const token = Cookies.get(TOKEN_COOKIE_NAME);
  const userId = Cookies.get(USER_ID_COOKIE_NAME);
  return !!token && !!userId;
};

// Get current auth token (useful for debugging)
export const getCurrentToken = () => {
  return Cookies.get(TOKEN_COOKIE_NAME);
};

// Get current user ID (useful for debugging)
export const getCurrentUserId = () => {
  return Cookies.get(USER_ID_COOKIE_NAME);
};

// Clear all auth data (for logout)
export const clearAuth = () => {
  Cookies.remove(TOKEN_COOKIE_NAME);
  Cookies.remove(USER_ID_COOKIE_NAME);
  Cookies.remove(USER_PERMISSION_COOKIE_NAME);
  delete axiosInstance.defaults.headers.common['Authorization'];
  delete axiosInstance.defaults.headers.common['User-Id'];
  console.log('All authentication data cleared');
};