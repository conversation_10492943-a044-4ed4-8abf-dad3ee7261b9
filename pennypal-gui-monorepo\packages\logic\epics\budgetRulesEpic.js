import { combineEpics, ofType } from 'redux-observable';
import { mergeMap, map, catchError, startWith } from 'rxjs/operators';
import { from, of, concat as rxConcat } from 'rxjs';
import { axiosInstance } from '../api/axiosConfig';
import {
  createBudgetRuleStart,
  createBudgetRuleSuccess,
  createBudgetRuleFailure,
  fetchSubcategoriesSuccess,
  fetchSubcategoriesFailure,
  fetchBudgetRulesSuccess,
  fetchBudgetRulesFailure,
  deleteBudgetRuleSuccess,
  deleteBudgetRuleFailure,
  setAccounts,
  setError
} from '../redux/budgetRulesSlice';

// Epic for creating budget rules
export const createBudgetRuleEpic = (action$) => action$.pipe(
  ofType('budgetRule/createBudgetRule'),
  mergeMap((action) => {
    console.log('Creating budget rule with payload:', action.payload);
    console.log(action.payload.id);
    const isEdit = !!action.payload.id;
    const endpoint = isEdit 
      ? `/pennypal/api/v1/budget-rules/update`
      : '/pennypal/api/v1/budget-rules/create';
    console.log(endpoint);
    const method = isEdit ? 'put' : 'post';
    console.log(method);

    // const dataToSend = {
    //     ...action.payload,
    //     renamedMerchant: action.payload.renameMerchant ? action.payload.newMerchantName : null,
    //     accountId: action.payload.accountId || null
    // };
    const payload = action.payload;
    const userId = payload.userId;
    const dataToSend = {
      ...payload,
      merchantNamePattern: payload.merchantNamePattern || null,
      merchantMatchRegex: payload.merchantMatchRegex || false,
      fromCategoryId: payload.fromCategoryId || payload.toCategoryId,
      fromSubCategoryId: payload.fromSubCategoryId || null,
      toCategoryId: payload.toCategoryId || null,
      toSubCategoryId: payload.toSubCategoryId || null,
      toCustomSubCategoryId: payload.toCustomSubCategoryId || null,
      accountId: payload.accountId || null,
      renamedMerchant: payload.renamedMerchant ? payload.renamedMerchant : null,
      thresholdAmount: payload.thresholdAmount || 0,
      amountType: payload.amountType || null,
      amountMatch: payload.amountMatch || null,
      cascadeFlag: payload.cascadeFlag || false,
      hideTransactionsFlag: payload.hideTransactionsFlag || false,
      tags: payload.tags || '',
      goal: payload.goal || ''
    };
    console.log("dataToSend", dataToSend);

    return rxConcat(
      of(createBudgetRuleStart()),
      from(axiosInstance[method](endpoint, dataToSend)).pipe(
        mergeMap((response) => {
          if (!response.data) {
            throw new Error('No data received from server');
          }
          return rxConcat(
            of(createBudgetRuleSuccess(response.data)),
            of({ type: 'budgetRule/fetchBudgetRules', payload: userId }) // Refresh the rules list after creation
          );
        }),
        catchError((error) => {
          const errorMessage = error.response?.data?.message || error.message || 'Failed to save budget rule';
          return of(createBudgetRuleFailure(errorMessage));
        })
      )
    );
  })
);

// Epic for fetching subcategories
export const fetchSubcategoriesEpic = (action$, state$) => action$.pipe(
  ofType('budgetRule/fetchSubcategories'),
  mergeMap((action) => {
    const userId = action.payload;
    const cache = state$.value.cache;

    // Check if data is available in cache first
    if (cache?.distinctSubcategoriesLoaded && cache?.distinctSubcategories?.length >= 0 &&
        cache?.distinctSubcategoriesParams?.userId == userId) {
      console.log('✅ Using cached distinct subcategories data in budget rules');
      return of(fetchSubcategoriesSuccess(cache.distinctSubcategories));
    }

    // If not cached, dispatch cache action instead of direct API call
    console.log('🔄 Distinct subcategories not cached, dispatching cache action');
    return from([
      { type: 'cache/fetchDistinctSubcategoriesStart', payload: { userId } },
      // Return empty for now, the cache epic will handle the actual fetch
      // and the component should listen to cache state changes
      fetchSubcategoriesSuccess([])
    ]);
  })
);

// Epic for fetching budget rules
export const fetchBudgetRulesEpic = (action$) => action$.pipe(
  ofType('budgetRule/fetchBudgetRules'),
  mergeMap((action) => {
    return from(axiosInstance.get(`/pennypal/api/v1/budget-rules/user/${action.payload}`)).pipe(
      map((response) => fetchBudgetRulesSuccess(response.data)),
      catchError((error) => {
        const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch budget rules';
        return of(fetchBudgetRulesFailure(errorMessage));
      })
    );
  })
);

// Epic for deleting budget rule
export const deleteBudgetRuleEpic = (action$) => action$.pipe(
  ofType('budgetRule/deleteBudgetRule'),
  mergeMap((action) => {
    const userId = action.payload.userId;
    
    return from(axiosInstance.put(`/pennypal/api/v1/budget-rules/delete/${action.payload}`)).pipe(
      mergeMap(() => {
        return of(
          deleteBudgetRuleSuccess(action.payload),
          // Refresh the rules list after successful deletion
          // APT-175 fix - commenting below line as refresh will be done in redux state (in budgetRulesSlice)
          // { type: 'budgetRule/fetchBudgetRules', payload: userId }
        );
      }),
      catchError((error) => {
        const errorMessage = error.response?.data?.message || error.message || 'Failed to delete budget rule';
        return of(deleteBudgetRuleFailure(errorMessage));
      })
    );
  })
);

// Epic for fetching accounts
export const fetchAccountsEpic = (action$, state$) => action$.pipe(
  ofType('budgetRule/fetchAccounts'),
  mergeMap((action) => {
    const userId = action.payload;
    const cache = state$.value.cache;

    // Check if data is available in cache first
    if (cache?.accountIdsLoaded && cache?.accountIds?.length >= 0 &&
        cache?.accountIdsParams?.userId == userId) {
      console.log('✅ Using cached account IDs data in budget rules');
      return of(setAccounts(cache.accountIds));
    }

    // If not cached, dispatch cache action instead of direct API call
    console.log('🔄 Account IDs not cached, dispatching cache action');
    return from([
      { type: 'cache/fetchAccountIdsStart', payload: { userId } },
      // Return empty for now, the cache epic will handle the actual fetch
      setAccounts([])
    ]);
  })
);

export const rootBudgetRulesEpic = combineEpics(
  createBudgetRuleEpic,
  fetchSubcategoriesEpic,
  fetchBudgetRulesEpic,
  deleteBudgetRuleEpic,
  fetchAccountsEpic
);