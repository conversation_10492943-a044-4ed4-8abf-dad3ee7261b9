import React from "react";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import DeleteIcon from "@mui/icons-material/Delete";
import CloseIcon from "@mui/icons-material/Close";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import ReceiptIcon from "@mui/icons-material/Receipt";
import CategoryIcon from "@mui/icons-material/Category";
import AttachMoneyIcon from "@mui/icons-material/AttachMoney";
import ReceiptLongIcon from "@mui/icons-material/ReceiptLong";
import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import LocalOfferIcon from "@mui/icons-material/LocalOffer";
import NotesIcon from "@mui/icons-material/Notes";
import SaveIcon from "@mui/icons-material/Save";

import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Button,
  IconButton,
  InputAdornment,
  FormControl,
  InputLabel,
  LinearProgress,
  CircularProgress,
  TextField,
  Table,
  TableHead,
  Tooltip,
  Select,
  Grid2,
  Paper,
  ListSubheader,
  TableBody,
  TableRow,
  TableCell,
  Tabs,
  Tab,
  MenuItem,
  Radio,
  Box,
  Typography,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import { useDispatch } from "react-redux";

import {
  setCurrentTab,
  setReceiptUploadModal,
  // setSelectedFile,
  setErrorMessage,
  setShowError,
  // setFileMetadata,
  setBlinkError,
  setIsReceiptModalOpen,
  setSelectedReceipt,
  // setSelectedTransaction,
  setEditingField,
  setEditedValue,
  setEditedItemIndex,
  setSelectedDate,
  setUploadProgress,
  setIsUploading,
  setIsProcessing,
  // setIsPopupVisible,
  // setReceiptNewTransaction,
  uploadReceiptRequest,
  saveReceiptRequest,
  addNewTransaction,
  fetchReceiptTransactionIdsRequest,
  fetchReceiptDetailsRequest,
  // addTransactionRequest,
  updateReceiptField,
  setJsonResponse,
  setIsMatchingTransactionAdd,
} from "@pp-logic/redux/receiptSlice";

const TransactionDetailsModal = ({
  closeTransactionModal,
  selectedTransaction,
}) => {
  // Receipts state
  const {
    errorMessage,
    jsonResponse,
    currentTab,
    receiptUploadModal,
    showError,
    blinkError,
    isReceiptModalOpen,
    selectedReceipt,
    // selectedFile,
    fileMetadata,
    // selectedTransaction,
    uploadPopupWidth,
    editingField,
    editedValue,
    editedItemIndex,
    selectedDate,
    uploadProgress,
    isUploading,
    isProcessing,
    isPopupVisible,

    //  receiptNewTransaction,
    receiptTransactionIds,
  } = useSelector((state) => state.receipts);
  const dispatch = useDispatch();
  //transaction detail model
  const handleFieldChange = (field, value) => {
    dispatch(
      setSelectedTransaction({
        ...selectedTransaction,
        [field]: value,
      })
    );
  };

  // In your TransactionPage1 component
  const handleSaveChanges = () => {
    if (!selectedTransaction) return;

    // Validation for required fields
    if (
      !selectedTransaction.date ||
      !selectedTransaction.name ||
      !selectedTransaction.subcategory ||
      !selectedTransaction.bank ||
      !selectedTransaction.amount
    ) {
      alert(
        "Please fill in all required fields: Date, Description, Category, Account, Amount."
      );
      return;
    }

    const formattedTransaction = {
      transactionId: selectedTransaction.transaction_id || `temp-${Date.now()}`,
      transactionDate: new Date(selectedTransaction.date).toISOString(),
      description: selectedTransaction.name,
      category: selectedTransaction.subcategory,
      categoryId: 5, // Adjust based on your category mapping
      account: selectedTransaction.bank,
      accountId: Number(selectedTransaction.bank),
      transactionAmount: parseFloat(selectedTransaction.amount) || 0,
      tax: parseFloat(selectedTransaction.tax) || 0,
      notes: selectedTransaction.notes || "",
      tag: selectedTransaction.tag || "",
      hideFromBudget: selectedTransaction.hideFromBudget || false,
      hidden: selectedTransaction.hidden || false,
      userId: getCurrentUserId(), // Adjust based on your auth logic
    };

    if (isMatchingTransactionAdd) {
      // Add to matchingTransactions in receiptSlice
      dispatch(addNewTransaction(formattedTransaction));
    } else if (selectedTransaction.transaction_id) {
      // Update existing transaction
      dispatch({
        type: "transactions/updateTransaction",
        payload: {
          id: selectedTransaction.transaction_id,
          data: formattedTransaction,
        },
      });
    } else {
      // Add new transaction to database
      dispatch(
        addTransactionRequest({ transactionData: formattedTransaction })
      );
    }

    dispatch(setOpenModal(false));
    dispatch(setSelectedTransaction(null));
    dispatch(setIsMatchingTransactionAdd(false));
  };

  const openModal = useSelector((state) => state.transactions.openModal);
  //fetch category , subcategory

  const categories = useSelector((state) => state.budget.categories);
  const subcategories = useSelector((state) => state.budget.subcategories);

  const accounts = useSelector((state) => state.transactions.accounts); // same slice

  const loadingAccounts = useSelector(
    (state) => state.transactions.loadingAccounts
  );

  // Fetch categories/subcategories on mount
  useEffect(() => {
    dispatch({ type: "budget/fetchCategories" });
    dispatch({ type: "budget/fetchSubcategories" });
  }, []);

  useEffect(() => {
    if (isPopupVisible) {
      dispatch({ type: "budget/fetchCategories" });
      dispatch({ type: "budget/fetchSubcategories" });
    }
  }, [isPopupVisible]);

  return (
    <Dialog
      open={openModal}
      onClose={closeTransactionModal}
      fullWidth={true}
      maxWidth="sm"
      PaperProps={{
        style: {
          borderRadius: "20px",
          overflow: "hidden",
          boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
          border: "none",
        },
      }}
    >
      <DialogTitle
        sx={{
          background: "linear-gradient(135deg, #8bc34a 0%, #7cb342 100%)",
          color: "white",
          padding: "24px",
          fontWeight: "700",
          fontSize: "22px",
          textAlign: "center",
          position: "relative",
          boxShadow: "0 8px 16px -4px rgba(139, 195, 74, 0.3)",
        }}
      >
        Transaction Detail
        {/* Trash Icon - Top Left */}
        <IconButton
          aria-label="delete"
          onClick={() => {
            if (
              window.confirm(
                "Are you sure you want to delete this transaction?"
              )
            ) {
              dispatch({
                type: "transactions/deleteTransaction",
                payload: selectedTransaction.transaction_id,
              });
              closeTransactionModal();
            }
          }}
          sx={{
            position: "absolute",
            left: 16,
            top: 16,
            color: "white",
            backgroundColor: "rgba(255, 255, 255, 0.15)",
            borderRadius: "50%",
            padding: "8px",
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.25)",
              transform: "scale(1.1)",
            },
            transition: "all 0.3s ease-in-out",
          }}
        >
          <DeleteIcon fontSize="small" />
        </IconButton>
        {/* Close Icon - Top Right */}
        <IconButton
          aria-label="close"
          onClick={closeTransactionModal}
          sx={{
            position: "absolute",
            right: 16,
            top: 16,
            color: "white",
            backgroundColor: "rgba(255, 255, 255, 0.15)",
            borderRadius: "50%",
            padding: "8px",
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.25)",
              transform: "scale(1.1) rotate(90deg)",
            },
            transition: "all 0.3s ease-in-out",
          }}
        >
          <CloseIcon fontSize="small" />
        </IconButton>
      </DialogTitle>

      <DialogContent
        sx={{
          padding: "32px 24px",
          backgroundColor: "#f8fafc",
        }}
      >
        <div className="w-full space-y-4">
          {/* Date Field */}
          <div className="grid grid-cols-5 gap-4 items-center">
            <div className="flex items-center col-span-2">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <CalendarTodayIcon sx={{ fontSize: 18, color: "#1976d2" }} />
              </div>
              <span className="font-semibold text-gray-700 text-sm">Date</span>
            </div>
            <div className="col-span-3">
              <TextField
                type="date"
                value={selectedTransaction?.date || ""}
                onChange={(e) => handleFieldChange("date", e.target.value)}
                variant="outlined"
                size="small"
                fullWidth
                InputLabelProps={{
                  shrink: true,
                }}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "8px",
                    backgroundColor: "#f8fafc",
                    borderColor: "#e2e8f0",
                    height: "36px",
                    "&:hover": {
                      borderColor: "#8bc34a",
                    },
                    "&.Mui-focused": {
                      borderColor: "#8bc34a",
                    },
                  },
                  "& .MuiInputBase-input": {
                    fontWeight: 500,
                    color: "#1e293b",
                    fontSize: "14px",
                  },
                }}
              />
            </div>
          </div>

          {/* Description Field */}
          <div className="grid grid-cols-5 gap-4 items-center">
            <div className="flex items-center col-span-2">
              <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <ReceiptIcon sx={{ fontSize: 18, color: "#7b1fa2" }} />
              </div>
              <span className="font-semibold text-gray-700 text-sm">
                Description
              </span>
            </div>
            <div className="col-span-3">
              <TextField
                value={selectedTransaction?.name || ""}
                onChange={(e) => handleFieldChange("name", e.target.value)}
                variant="outlined"
                size="small"
                placeholder="Enter description"
                fullWidth
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "8px",
                    backgroundColor: "#f8fafc",
                    borderColor: "#e2e8f0",
                    height: "36px",
                    "&:hover": {
                      borderColor: "#7b1fa2",
                    },
                    "&.Mui-focused": {
                      borderColor: "#7b1fa2",
                    },
                  },
                  "& .MuiInputBase-input": {
                    fontWeight: 500,
                    color: "#1e293b",
                    fontSize: "14px",
                  },
                }}
              />
            </div>
          </div>

          {/* Category Field */}
          <div className="grid grid-cols-5 gap-4 items-center">
            <div className="flex items-center col-span-2">
              <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <CategoryIcon sx={{ fontSize: 18, color: "#f57c00" }} />
              </div>
              <span className="font-semibold text-gray-700 text-sm">
                Category
              </span>
            </div>
            <div className="col-span-3">
              <select
                value={selectedTransaction?.subcategory || ""}
                onChange={(e) =>
                  handleFieldChange("subcategory", e.target.value)
                }
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#f57c00] focus:border-[#f57c00]"
              >
                <option value="">Select a category & subcategory</option>
                {categories.map((cat) => (
                  <optgroup key={cat.id} label={cat.category}>
                    {subcategories
                      .filter((sub) => sub.categoryId === cat.id)
                      .map((sub) => (
                        <option key={sub.id} value={sub.subCategory}>
                          {sub.subCategory}
                        </option>
                      ))}
                  </optgroup>
                ))}
              </select>
            </div>
          </div>

          {/* Amount Field - Highlighted */}
          <div className="grid grid-cols-5 gap-4 items-center">
            <div className="flex items-center col-span-2">
              <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-sm">
                <AttachMoneyIcon sx={{ fontSize: 18, color: "white" }} />
              </div>
              <span className="font-bold text-gray-800 text-sm">Amount</span>
            </div>
            <div className="col-span-3">
              <TextField
                type="number"
                value={selectedTransaction?.amount || ""}
                onChange={(e) => handleFieldChange("amount", e.target.value)}
                variant="outlined"
                size="small"
                fullWidth
                InputProps={{
                  startAdornment: (
                    <InputAdornment
                      position="start"
                      sx={{ color: "#8bc34a", fontWeight: "bold" }}
                    >
                      $
                    </InputAdornment>
                  ),
                }}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "8px",
                    backgroundColor: "white",
                    borderColor: "#8bc34a",
                    height: "36px",
                    "&:hover": {
                      borderColor: "#7cb342",
                    },
                    "&.Mui-focused": {
                      borderColor: "#7cb342",
                    },
                  },
                  "& .MuiInputBase-input": {
                    fontWeight: "bold",
                    fontSize: "16px",
                    color: "#7cb342",
                  },
                }}
              />
            </div>
          </div>

          {/* Tax Field */}
          <div className="grid grid-cols-5 gap-4 items-center">
            <div className="flex items-center col-span-2">
              <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <ReceiptLongIcon sx={{ fontSize: 18, color: "#f59e0b" }} />
              </div>
              <span className="font-semibold text-gray-700 text-sm">Tax</span>
            </div>
            <div className="col-span-3">
              <TextField
                type="number"
                value={selectedTransaction?.tax || ""}
                onChange={(e) => handleFieldChange("tax", e.target.value)}
                variant="outlined"
                size="small"
                placeholder="0.00"
                fullWidth
                InputProps={{
                  startAdornment: (
                    <InputAdornment
                      position="start"
                      sx={{ color: "#f59e0b", fontWeight: "bold" }}
                    >
                      $
                    </InputAdornment>
                  ),
                }}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "8px",
                    backgroundColor: "#f8fafc",
                    borderColor: "#e2e8f0",
                    height: "36px",
                    "&:hover": {
                      borderColor: "#f59e0b",
                    },
                    "&.Mui-focused": {
                      borderColor: "#f59e0b",
                    },
                  },
                  "& .MuiInputBase-input": {
                    fontWeight: 500,
                    color: "#1e293b",
                    fontSize: "14px",
                  },
                }}
              />
            </div>
          </div>

          {/* Account Field */}
          <div className="grid grid-cols-5 gap-4 items-center">
            <div className="flex items-center col-span-2">
              <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <AccountBalanceIcon sx={{ fontSize: 18, color: "#3f51b5" }} />
              </div>
              <span className="font-semibold text-gray-700 text-sm">
                Account
              </span>
            </div>

            <div className="col-span-3">
              <select
                value={selectedTransaction?.bank}
                onChange={(e) => handleFieldChange("bank", e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#3f51b5] focus:border-[#3f51b5]"
                required
                disabled={loadingAccounts}
              >
                <option value="">Select an account</option>
                {accounts.map((account) => (
                  <option key={account.accountId} value={account.accountId}>
                    {`${account.accountName} (${account.accountMask})`}
                  </option>
                ))}
              </select>
              {loadingAccounts && (
                <p className="text-sm text-gray-500">Loading accounts...</p>
              )}
            </div>
          </div>

          {/* Tag Field - NEW */}
          <div className="grid grid-cols-5 gap-4 items-center">
            <div className="flex items-center col-span-2">
              <div className="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <LocalOfferIcon sx={{ fontSize: 18, color: "#e91e63" }} />
              </div>
              <span className="font-semibold text-gray-700 text-sm">Tag</span>
            </div>
            <div className="col-span-3">
              <TextField
                value={selectedTransaction?.tag || ""}
                onChange={(e) => handleFieldChange("tag", e.target.value)}
                variant="outlined"
                size="small"
                placeholder="Add tag"
                fullWidth
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "8px",
                    backgroundColor: "#f8fafc",
                    borderColor: "#e2e8f0",
                    height: "36px",
                    "&:hover": {
                      borderColor: "#e91e63",
                    },
                    "&.Mui-focused": {
                      borderColor: "#e91e63",
                    },
                  },
                  "& .MuiInputBase-input": {
                    fontWeight: 500,
                    color: "#1e293b",
                    fontSize: "14px",
                  },
                }}
              />
            </div>
          </div>

          {/* Notes Field */}
          <div className="grid grid-cols-5 gap-4 items-start">
            <div className="flex items-center col-span-2">
              <div className="w-10 h-10 bg-teal-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <NotesIcon sx={{ fontSize: 18, color: "#0d9488" }} />
              </div>
              <span className="font-semibold text-gray-700 text-sm">Notes</span>
            </div>
            <div className="col-span-3">
              <TextField
                value={selectedTransaction?.notes || ""}
                onChange={(e) => handleFieldChange("notes", e.target.value)}
                variant="outlined"
                size="small"
                placeholder="Add notes..."
                multiline
                rows={3}
                fullWidth
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "8px",
                    backgroundColor: "#f8fafc",
                    borderColor: "#e2e8f0",
                    "&:hover": {
                      borderColor: "#0d9488",
                    },
                    "&.Mui-focused": {
                      borderColor: "#0d9488",
                    },
                  },
                  "& .MuiInputBase-input": {
                    fontWeight: 500,
                    color: "#1e293b",
                    fontSize: "14px",
                  },
                }}
              />
            </div>
          </div>

          {/* Hide from Budget Checkbox - Moved to left */}
          <div className="flex items-center">
            <FormControlLabel
              control={
                <Checkbox
                  checked={selectedTransaction?.hideFromBudget || false}
                  onChange={(e) =>
                    handleFieldChange("hideFromBudget", e.target.checked)
                  }
                  sx={{
                    color: "#dc2626",
                    "&.Mui-checked": {
                      color: "#dc2626",
                    },
                    "& .MuiSvgIcon-root": {
                      fontSize: 20,
                    },
                  }}
                />
              }
              label={
                <span className="text-sm text-gray-600 font-medium">
                  Exclude from budget calculations
                </span>
              }
              sx={{
                margin: 0,
                "& .MuiFormControlLabel-label": {
                  fontSize: "14px",
                },
              }}
            />
          </div>

          {/* Hide Transaction Checkbox - Moved to left */}
          <div className="flex items-center">
            <FormControlLabel
              control={
                <Checkbox
                  checked={selectedTransaction?.hidden || false}
                  onChange={(e) =>
                    handleFieldChange("hidden", e.target.checked)
                  }
                  sx={{
                    color: "#6b7280",
                    "&.Mui-checked": {
                      color: "#6b7280",
                    },
                    "& .MuiSvgIcon-root": {
                      fontSize: 20,
                    },
                  }}
                />
              }
              label={
                <span className="text-sm text-gray-600 font-medium">
                  Hide this transaction from view
                </span>
              }
              sx={{
                margin: 0,
                "& .MuiFormControlLabel-label": {
                  fontSize: "14px",
                },
              }}
            />
          </div>
        </div>

        {/* Save Button - Centered */}
        <div className="pt-6 flex justify-center">
          <Button
            variant="contained"
            onClick={handleSaveChanges}
            startIcon={<SaveIcon />}
            sx={{
              textTransform: "none",
              fontWeight: "600",
              borderRadius: "10px",
              padding: "12px 28px",
              fontSize: "15px",
              background: "linear-gradient(135deg, #8bc34a 0%, #7cb342 100%)",
              boxShadow: "0 4px 14px 0 rgba(139, 195, 74, 0.3)",
              "&:hover": {
                boxShadow: "0 6px 20px 0 rgba(139, 195, 74, 0.4)",
                transform: "translateY(-1px)",
                background: "linear-gradient(135deg, #7cb342 0%, #689f38 100%)",
              },
              transition: "all 0.2s ease-in-out",
            }}
          >
            Save Changes
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TransactionDetailsModal;
