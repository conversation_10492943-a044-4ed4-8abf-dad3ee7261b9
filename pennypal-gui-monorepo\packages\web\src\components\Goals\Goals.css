/* GoalDisplay.css */

.goal-display-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: Arial, sans-serif;
  }
  
  .loading, .error-container {
    text-align: center;
    padding: 40px;
  }
  
  .success-message {
    background-color: #dff0d8;
    color: #3c763d;
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    animation: fadeOut 3s forwards;
    animation-delay: 2s;
  }
  
  @keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
  }
  
  .goal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .goal-header h1 {
    margin: 0;
    color: #333;
  }
  
  .goal-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 30px;
  }
  
  .goal-details {
    margin-bottom: 20px;
  }
  
  .goal-description {
    font-size: 16px;
    line-height: 1.5;
    color: #555;
  }
  
  .goal-dates {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    color: #666;
  }
  
  .goal-progress-section {
    margin-top: 25px;
  }
  
  .progress-container {
    margin: 15px 0;
  }
  
  .progress-bar {
    height: 20px;
    background-color: #f5f5f5;
    border-radius: 10px;
    overflow: hidden;
  }
  
  .progress-fill {
    height: 100%;
    background-color: #4caf50;
    transition: width 0.5s ease-in-out;
  }
  
  .progress-text {
    text-align: right;
    font-size: 14px;
    margin-top: 5px;
    color: #666;
  }
  
  .goal-amounts {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-top: 15px;
  }
  
  .amount-item {
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
    text-align: center;
  }
  
  .amount-value {
    display: block;
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-top: 5px;
  }
  
  .contribution-form-container {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
  }
  
  .contribution-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
  }
  
  .form-group {
    margin-bottom: 15px;
  }
  
  .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #555;
  }
  
  .form-group input,
  .form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
  }
  
  .form-group textarea {
    resize: vertical;
  }
  
  .contribute-button {
    grid-column: 1 / -1;
    padding: 10px 15px;
    background-color: #4caf50;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
  }
  
  .contribute-button:hover {
    background-color: #45a049;
  }
  
  .contribute-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
  }
  
  .goal-completed {
    text-align: center;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 4px;
    margin-top: 20px;
  }
  
  .goal-completed h3 {
    color: #4caf50;
    margin-bottom: 10px;
  }
  
  .contributions-history {
    margin-top: 30px;
  }
  
  .contributions-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
  }
  
  .contributions-table th,
  .contributions-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
  }
  
  .contributions-table th {
    background-color: #f5f5f5;
    font-weight: bold;
    color: #333;
  }
  
  .contributions-table tr:hover {
    background-color: #f9f9f9;
  }
  
  .goal-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
  }
  
  .goal-actions button {
    padding: 10px 15px;
    background-color: #f0f0f0;
    color: #333;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
  }
  
  .goal-actions button:hover {
    background-color: #e0e0e0;
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .goal-amounts {
      grid-template-columns: 1fr;
    }
    
    .goal-dates {
      flex-direction: column;
      gap: 5px;
    }
    
    .contribution-form {
      grid-template-columns: 1fr;
    }
  }