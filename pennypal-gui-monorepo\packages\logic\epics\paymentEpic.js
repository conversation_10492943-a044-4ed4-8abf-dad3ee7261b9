import { ofType, combineEpics } from 'redux-observable';
import { from, of } from 'rxjs';
import { mergeMap, map, catchError } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchPaymentSubscriptionStart,
  fetchPaymentMethodsStart,
  fetchPaymentProductsStart,
  fetchPaymentInvoicesStart,
  fetchUpcomingInvoiceStart,
  invalidatePaymentSubscriptionCache,
  invalidatePaymentMethodsCache
} from '../redux/cacheSlice';

// Action types for payment operations
export const FETCH_PAYMENT_DATA = 'payment/fetchPaymentData';
export const START_SUBSCRIPTION = 'payment/startSubscription';
export const UPDATE_SUBSCRIPTION = 'payment/updateSubscription';
export const SET_DEFAULT_PAYMENT_METHOD = 'payment/setDefaultPaymentMethod';

// Action creators
export const fetchPaymentData = (userId) => ({
  type: FETCH_PAYMENT_DATA,
  payload: { userId }
});

export const startSubscription = (subscriptionData) => ({
  type: START_SUBSCRIPTION,
  payload: subscriptionData
});

export const updateSubscription = (subscriptionData) => ({
  type: UPDATE_SUBSCRIPTION,
  payload: subscriptionData
});

export const setDefaultPaymentMethod = (paymentMethodData) => ({
  type: SET_DEFAULT_PAYMENT_METHOD,
  payload: paymentMethodData
});

// Epic to fetch all payment data using cache
export const fetchPaymentDataEpic = (action$, state$) =>
  action$.pipe(
    ofType(FETCH_PAYMENT_DATA),
    mergeMap((action) => {
      const { userId } = action.payload;
      const cache = state$.value.cache;

      console.log('🔄 Fetching payment data for user:', userId);

      const actions = [];

      // Check subscription cache
      if (!cache?.paymentSubscriptionLoaded || 
          cache?.paymentSubscriptionParams?.userId != userId) {
        console.log('🔄 Payment subscription not cached, dispatching cache action');
        actions.push(fetchPaymentSubscriptionStart({ userId }));
      } else {
        console.log('✅ Using cached payment subscription data');
      }

      // Check payment methods cache
      if (!cache?.paymentMethodsLoaded || 
          cache?.paymentMethodsParams?.userId != userId) {
        console.log('🔄 Payment methods not cached, dispatching cache action');
        actions.push(fetchPaymentMethodsStart({ userId }));
      } else {
        console.log('✅ Using cached payment methods data');
      }

      // Check products cache (no user-specific params)
      if (!cache?.paymentProductsLoaded) {
        console.log('🔄 Payment products not cached, dispatching cache action');
        actions.push(fetchPaymentProductsStart({}));
      } else {
        console.log('✅ Using cached payment products data');
      }

      // Check invoices cache
      if (!cache?.paymentInvoicesLoaded ||
          cache?.paymentInvoicesParams?.userId != userId) {
        console.log('🔄 Payment invoices not cached, dispatching cache action');
        actions.push(fetchPaymentInvoicesStart({ userId }));
      } else {
        console.log('✅ Using cached payment invoices data');
      }

      // Note: Upcoming invoice is fetched automatically after subscription is loaded
      // via fetchUpcomingInvoiceAfterSubscriptionEpic, so we don't need to dispatch it here

      return from(actions.length > 0 ? actions : [{ type: 'payment/allDataCached' }]);
    })
  );

// Epic to handle subscription start and invalidate cache
export const startSubscriptionEpic = (action$) =>
  action$.pipe(
    ofType(START_SUBSCRIPTION),
    mergeMap((action) => {
      const { userId, paymentMethodId, priceId, frequency } = action.payload;

      console.log('🚀 Starting subscription for user:', userId);
      return from(axiosInstance.post('/pennypal/api/v1/payment/start-subscription', {
        userId,
        paymentMethodId,
        priceId,
        frequency
      })).pipe(
        mergeMap((response) => {
          console.log('✅ Subscription started successfully');
          // Invalidate subscription cache to trigger refetch
          return of(invalidatePaymentSubscriptionCache());
        }),
        catchError((error) => {
          console.error('❌ Failed to start subscription:', error);
          return of({ type: 'payment/startSubscriptionFailure', payload: error.message });
        })
      );
    })
  );

// Epic to handle subscription update and invalidate cache
export const updateSubscriptionEpic = (action$) =>
  action$.pipe(
    ofType(UPDATE_SUBSCRIPTION),
    mergeMap((action) => {
      const { userId, subscriptionId, priceId } = action.payload;

      console.log('🚀 Updating subscription for user:', userId);
      return from(axiosInstance.post('/pennypal/api/v1/payment/update-subscription', {
        userId,
        subscriptionId,
        priceId
      })).pipe(
        mergeMap((response) => {
          console.log('✅ Subscription updated successfully');
          // Invalidate subscription cache to trigger refetch
          return of(invalidatePaymentSubscriptionCache());
        }),
        catchError((error) => {
          console.error('❌ Failed to update subscription:', error);
          return of({ type: 'payment/updateSubscriptionFailure', payload: error.message });
        })
      );
    })
  );

// Epic to handle setting default payment method and invalidate cache
export const setDefaultPaymentMethodEpic = (action$) =>
  action$.pipe(
    ofType(SET_DEFAULT_PAYMENT_METHOD),
    mergeMap((action) => {
      const { userId, paymentMethodId } = action.payload;

      console.log('🚀 Setting default payment method for user:', userId);
      return from(axiosInstance.post('/pennypal/api/v1/payment/set-default-payment-method', {
        userId,
        paymentMethodId
      })).pipe(
        mergeMap((response) => {
          console.log('✅ Default payment method set successfully');
          // Invalidate payment methods cache to trigger refetch
          return of(invalidatePaymentMethodsCache());
        }),
        catchError((error) => {
          console.error('❌ Failed to set default payment method:', error);
          return of({ type: 'payment/setDefaultPaymentMethodFailure', payload: error.message });
        })
      );
    })
  );

// Combined payment epic
export const paymentEpic = combineEpics(
  fetchPaymentDataEpic,
  startSubscriptionEpic,
  updateSubscriptionEpic,
  setDefaultPaymentMethodEpic
);

export default paymentEpic;
