import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  isCreateRuleModalOpen: false,
  budgetRules: [],
  newBudgetRule: {
    // userId: 1,
    ruleType: 'Custom',
    conditionType: 'MERCHANT',
    isActive: true,
    fromCategoryId: '',
    fromSubCategoryId: '',
    toCategoryId: '',
    toSubCategoryId: '',
    toCustomSubCategoryId: '',
    merchantMatchRegex: true,
    merchantNamePattern: '',
    //amountType: 'credit',
    //amountMatch: 'greater',
    amountType: '',
    amountMatch: '',
    thresholdAmount: 0,
    accountId: null,
    renamedMerchant: "",
    cascadeFlag: false,
    hideTransactionsFlag: false,
    tags: '',
    goal: ''
  },
  subcategories: [],
  accounts: [],
  error: null,
  loading: false,
  successMessage: null
};

const budgetRuleSlice = createSlice({
  name: 'budgetRule',
  initialState,
  reducers: {
    toggleCreateRuleModal: (state, action) => {
      state.isCreateRuleModalOpen = action.payload;
      if (!action.payload) {
        state.error = null;
        state.successMessage = null;
      }
    },
    updateNewRule: (state, action) => {
      const payload = { ...action.payload };
      
      // Handle merchant pattern matching
      if (payload.hasOwnProperty('merchantCondition')) {
        state.newBudgetRule.merchantMatchRegex = payload.merchantCondition === 'contains';
        delete payload.merchantCondition;
      }

      // Handle category mappings
      if (payload.hasOwnProperty('toCategoryId') && !state.newBudgetRule.fromCategoryId) {
        state.newBudgetRule.fromCategoryId = payload.toCategoryId;
      }

      state.newBudgetRule = {
        ...state.newBudgetRule,
        ...payload
      };
    },
    resetNewRule: (state) => {
      state.newBudgetRule = initialState.newBudgetRule;
      state.error = null;
      state.successMessage = null;
      state.editingRule = null;
    },
    setSubcategories: (state, action) => {
      state.subcategories = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
      state.loading = false;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setSuccessMessage: (state, action) => {
      state.successMessage = action.payload;
    },
    clearMessages: (state) => {
      state.error = null;
      state.successMessage = null;
    },
    // Reducers for creating budget rule
    createBudgetRule: (state, action) => {
      state.loading = true;
      state.error = null;
    },
    createBudgetRuleStart: (state, action) => {
      state.loading = true;
      state.error = null;
    },
    createBudgetRuleSuccess: (state, action) => {
      state.loading = false;
      state.error = null;
      state.successMessage = state.editingRule ? 'Rule updated successfully' : 'Rule created successfully';
      // if (action.payload.id) {
      //   state.budgetRules = state.budgetRules.map(rule => 
      //     rule.id === action.payload.id ? action.payload : rule
      //   );
      //   state.editingRule = action.payload;
      // } else {
      //   state.budgetRules.push(action.payload);
      //   state.newBudgetRule = initialState.newBudgetRule;
      //   state.editingRule = null;
      // }
      if (state.editingRule) {
        state.budgetRules = state.budgetRules.map(rule => 
          rule.id === state.editingRule.id ? action.payload : rule
        );
        state.editingRule = action.payload;
      } else {
        state.budgetRules.push(action.payload);
        state.newBudgetRule = initialState.newBudgetRule;
        state.editingRule = null;
      }
      state.editingRule = null;
    },
    createBudgetRuleFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
      state.successMessage = null;
    },
    // Reducers for fetching subcategories
    fetchSubcategoriesSuccess: (state, action) => {
      state.subcategories = action.payload;
      state.loading = false;
      state.error = null;
    },
    fetchSubcategoriesFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
      state.subcategories = [];
    },
    // Reducers for fetching budget rules
    fetchBudgetRulesSuccess: (state, action) => {
      state.budgetRules = action.payload;
      state.loading = false;
      state.error = null;
    },
    fetchBudgetRulesFailure: (state, action) => {
      state.error = action.payload;
      state.loading = false;
    },
    // Reducers for deleting budget rule
    deleteBudgetRuleSuccess: (state, action) => {
      state.budgetRules = state.budgetRules.filter(rule => rule.id !== action.payload);
      state.loading = false;
      state.error = null;
      state.successMessage = 'Rule deleted successfully';
    },
    deleteBudgetRuleFailure: (state, action) => {
      state.error = action.payload;
      state.loading = false;
      state.successMessage = null;
    },
    setEditingRule: (state, action) => {
      state.editingRule = action.payload;
    },
    setAccounts: (state, action) => {
      state.accounts = action.payload;
      state.loading = false;
      state.error = null;
    }
  }
});

export const { 
  toggleCreateRuleModal, 
  updateNewRule, 
  resetNewRule, 
  setError, 
  setLoading,
  setSuccessMessage,
  setSubcategories,
  createBudgetRule,
  createBudgetRuleStart,
  createBudgetRuleSuccess,
  createBudgetRuleFailure,
  fetchSubcategoriesSuccess,
  fetchSubcategoriesFailure,
  clearMessages,
  fetchBudgetRulesSuccess,
  fetchBudgetRulesFailure,
  deleteBudgetRuleSuccess,
  deleteBudgetRuleFailure,
  setEditingRule,
  setAccounts
} = budgetRuleSlice.actions;

export default budgetRuleSlice.reducer;