import { createSlice,createSelector ,} from '@reduxjs/toolkit';


const initialState = {
  documents: [],
  loading: false,
  error: null,
  searchTerm: '',
  filterType: 'all',
  selectedDocument: null,
  showUploadModal: false,
  newDocumentName: '',
  newDocumentType: 'receipt',
  viewMode: 'list',
  activeFolder: 'All Documents',
  selectedFile: null,
  qrData: '',
  isGeneratingQr: false,
  maxStorage: 10, // Maximum storage in MB
  // New state for preview
  previewData: null,
  previewStep: 'upload', // 'upload', 'preview', 'confirm'
  activePreviewTab: 'original', // 'original', 'scanned', 'data'
  isProcessing: false,
  currentDocument: null,
  loadingDetails: false,
  errorDetails: null,
  showAddTransactionModal: false,
  transactionLoading: false,
  transactionError: null,
};

const documentSlice = createSlice({
  name: 'documents',
  initialState,
  reducers: {
    // Document actions
    fetchDocumentsRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchDocumentsSuccess: (state, action) => {
      state.loading = false;
      state.documents = action.payload;
    },
    fetchDocumentsFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },
    fetchDocumentDetailsRequest: (state, action) => {
      state.loadingDetails = true;
      state.errorDetails = null;
    },
    fetchDocumentDetailsSuccess: (state, action) => {
      state.loadingDetails = false;
      state.currentDocument = action.payload;
    },
    fetchDocumentDetailsFailure: (state, action) => {
      state.loadingDetails = false;
      state.errorDetails = action.payload;
    },
    // UI state actions
    setSearchTerm: (state, action) => {
      state.searchTerm = action.payload;
    },
    setFilterType: (state, action) => {
      state.filterType = action.payload;
    },
    setSelectedDocument: (state, action) => {
      state.selectedDocument = action.payload;
    },
    setShowUploadModal: (state, action) => {
      state.showUploadModal = action.payload;
    },
    setNewDocumentName: (state, action) => {
      state.newDocumentName = action.payload;
    },
    setNewDocumentType: (state, action) => {
      state.newDocumentType = action.payload;
    },
    setViewMode: (state, action) => {
      state.viewMode = action.payload;
    },
    setActiveFolder: (state, action) => {
      state.activeFolder = action.payload;
    },
    setSelectedFile: (state, action) => {
      if (!action.payload) {
        state.selectedFile = null;
        return;
      }
      // Only store the metadata, not the File object
      state.selectedFile = {
        name: action.payload.name,
        size: action.payload.size,
        type: action.payload.type,
        lastModified: action.payload.lastModified || Date.now()
      };
    },
    updatePreviewDataField: (state, action) => {
      const { path, value } = action.payload;
      
      // Helper function to handle array paths like 'items[0].name'
      const parsePath = (path) => {
        return path.split(/[\.\[\]]/).filter(x => x !== '');
      };
    
      // Deeply update the previewData object
      const updateNestedField = (obj, pathArray, newValue) => {
        const [current, ...rest] = pathArray;
        
        // Handle array indices (e.g., 'items[0]' becomes 'items.0')
        const arrayMatch = current.match(/(\w+)\[(\d+)\]/);
        const key = arrayMatch ? arrayMatch[1] : current;
        const arrayIndex = arrayMatch ? parseInt(arrayMatch[2]) : null;
    
        if (rest.length === 0) {
          // If we're at the final key in the path
          if (arrayIndex !== null) {
            obj[key][arrayIndex] = newValue;
          } else {
            obj[key] = newValue;
          }
        } else {
          // If we need to go deeper
          if (arrayIndex !== null) {
            if (!obj[key]) obj[key] = [];
            if (!obj[key][arrayIndex]) obj[key][arrayIndex] = {};
            updateNestedField(obj[key][arrayIndex], rest, newValue);
          } else {
            if (!obj[key]) obj[key] = {};
            updateNestedField(obj[key], rest, newValue);
          }
        }
      };
    
      if (state.previewData) {
        const pathArray = parsePath(path);
        updateNestedField(state.previewData, pathArray, value);
      }
    },
    setQrData: (state, action) => {
      state.qrData = action.payload;
    },
    setIsGeneratingQr: (state, action) => {
      state.isGeneratingQr = action.payload;
    },
    setPreviewData: (state, action) => {
      state.previewData = action.payload;
    },
    setPreviewStep: (state, action) => {
      state.previewStep = action.payload;
    },
    setActivePreviewTab: (state, action) => {
      state.activePreviewTab = action.payload;
    },
    setIsProcessing: (state, action) => {
      state.isProcessing = action.payload;
    },

    addTransactionRequest: (state) => {
      state.transactionLoading = true;
      state.transactionError = null;
    },
    addTransactionSuccess: (state, action) => {
      state.transactionLoading = false;
      // Update matching transactions in previewData
      if (state.previewData?.azureResponse) {
        state.previewData.azureResponse.matchingTransactions = [
          ...(state.previewData.azureResponse.matchingTransactions || []),
          action.payload,
        ];
      }
    },
    addTransactionFailure: (state, action) => {
      state.transactionLoading = false;
      state.transactionError = action.payload;
    },
    setShowAddTransactionModal: (state, action) => {
      state.showAddTransactionModal = action.payload;
    },
 
  
    // Document operations
    uploadDocumentRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
   // In uploadDocumentSuccess reducer
uploadDocumentSuccess: (state, action) => {
  state.loading = false;
  state.isProcessing = false;
  state.previewData = null;
  state.previewStep = 'upload';
  state.documents = [...state.documents, action.payload];
  state.newDocumentName = '';
  state.newDocumentType = 'receipt';
  state.selectedFile = null;
  state.qrData = '';
  state.showUploadModal = false;
},
    uploadDocumentFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },
    
    deleteDocumentRequest: (state, action) => {
      state.loading = true;
      state.error = null; 
    },
    deleteDocumentSuccess: (state, action) => {
      state.loading = false;
      state.documents = state.documents.filter(doc => String(doc.id) !== String(action.payload)); 
     },
    deleteDocumentFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
      console.error('Delete failed:', action.payload);

    },
    
    generateQrDataRequest: (state) => {
      state.isGeneratingQr = true;
    },
    generateQrDataSuccess: (state, action) => {
      state.isGeneratingQr = false;
      state.qrData = action.payload;
    },
    generateQrDataFailure: (state, action) => {
      state.isGeneratingQr = false;
      state.error = action.payload;
    },
    
    resetDocumentState: () => initialState,
  },
});

export const {
  fetchDocumentsRequest,
  fetchDocumentsSuccess,
  fetchDocumentsFailure,
  setSearchTerm,
  setFilterType,
  setSelectedDocument,
  setShowUploadModal,
  setNewDocumentName,
  setNewDocumentType,
  setViewMode,
  setActiveFolder,
  setSelectedFile,
  setQrData,
  setPreviewData,
  setPreviewStep,
  setActivePreviewTab,
  setIsProcessing,
  setIsGeneratingQr,
  uploadDocumentRequest,
  uploadDocumentSuccess,
  uploadDocumentFailure,
  deleteDocumentRequest,
  deleteDocumentSuccess,
  deleteDocumentFailure,
  generateQrDataRequest,
  generateQrDataSuccess,
  generateQrDataFailure,
  resetDocumentState,
  fetchDocumentDetailsRequest,
  fetchDocumentDetailsSuccess,
  fetchDocumentDetailsFailure,
  addTransactionRequest,
  addTransactionSuccess,
  addTransactionFailure,
  setShowAddTransactionModal,
  updatePreviewDataField,
} = documentSlice.actions;

// Selectors
export const selectDocuments = (state) => state.documents.documents;
export const selectFilteredDocuments = createSelector(
  [selectDocuments, (state) => state.documents.searchTerm, (state) => state.documents.filterType],
  (documents, searchTerm, filterType) => {
    return documents.filter(doc => {
      const docName = doc.name || doc.docName || '';
      const docCategory = doc.category || '';
      const docType = (doc.type || doc.docType || '').toLowerCase();
      const docDate = doc.uploadDate || doc.date || '';
      
      const matchesSearch = searchTerm 
        ? docName.toLowerCase().includes(searchTerm.toLowerCase()) || 
          docCategory.toLowerCase().includes(searchTerm.toLowerCase()) ||
          docType.includes(searchTerm.toLowerCase()) ||
          docDate.toString().toLowerCase().includes(searchTerm.toLowerCase())
        : true;
      
      const matchesType = filterType === 'all' 
        ? true 
        : docType === filterType.toLowerCase();
      
      return matchesSearch && matchesType;
    });
  }
);
export const selectDocumentCounts = createSelector(
  [selectDocuments],
  (documents) => {
    return {
      total: documents.length,
      invoice: documents.filter(doc => {
        const type = doc.type || doc.docType || '';
        return type.toLowerCase() === 'invoice';
      }).length,
      receipt: documents.filter(doc => {
        const type = doc.type || doc.docType || '';
        return type.toLowerCase() === 'receipt';
      }).length,
      id: documents.filter(doc => {
        const type = doc.type || doc.docType || '';
        return type.toLowerCase() === 'id';
      }).length,
      other: documents.filter(doc => {
        const type = doc.type || doc.docType || '';
        return !['invoice', 'receipt', 'id'].includes(type.toLowerCase());
      }).length
    };
  }
);
export const selectStorageUsage = createSelector(
  [selectDocuments, (state) => state.documents.maxStorage],
  (documents, maxStorage) => {
    // Convert bytes to MB (divide by 1024*1024)
    const usedStorageMB = documents.reduce((total, doc) => total + (doc.size || 0), 0) / (1024 * 1024);
    const remainingStorageMB = Math.max(0, maxStorage - usedStorageMB);
    return { 
      usedStorage: usedStorageMB, 
      remainingStorage: remainingStorageMB, 
      maxStorage 
    };
  }
);
export const selectAllFolders = createSelector(
  [selectDocuments],
  (documents) => {
    const types = documents
      .map(doc => doc.docType || doc.type) // Use docType or type
      .filter(type => type) // Remove undefined/null types
      .map(type => type.charAt(0).toUpperCase() + type.slice(1).toLowerCase()); // Capitalize first letter for consistency

    return ['All Documents', ...new Set(types)];
  }
);


// Add these selectors to your documentSlice.js
export const selectDocumentsByFolder = createSelector(
  [selectFilteredDocuments],
  (filteredDocuments) => {
    const documentsByFolder = {};
    documentsByFolder['All Documents'] = filteredDocuments;

    // Get all unique document types from filtered documents
    const types = [...new Set(filteredDocuments.map(doc => 
      (doc.docType || doc.type)?.charAt(0).toUpperCase() + (doc.docType || doc.type)?.slice(1).toLowerCase()
    ))].filter(type => type); // Remove undefined/null types

    types.forEach(type => {
      if (type !== 'All Documents') {
        documentsByFolder[type] = filteredDocuments.filter(doc => 
          (doc.docType || doc.type)?.toLowerCase() === type.toLowerCase()
        );
      }
    });
    
    return documentsByFolder;
  }
);
export const selectCurrentFolderDocuments = createSelector(
  [selectDocumentsByFolder, (state) => state.documents.activeFolder],
  (documentsByFolder, activeFolder) => {
    return activeFolder === 'All Documents' 
      ? documentsByFolder['All Documents'] 
      : documentsByFolder[activeFolder] || [];
  }
);


export default documentSlice.reducer;