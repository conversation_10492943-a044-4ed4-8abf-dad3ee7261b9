import { createSlice } from '@reduxjs/toolkit';

// Initial state for recurring contributions
const initialState = {
  recurringContributions: [],
  goalRecurringContributions: {},
  loading: false,
  error: null,
  lastCreated: null,
  lastUpdated: null,
  lastDeleted: null,
  lastTriggered: null
};

// Create the recurring contributions slice
const recurringSlice = createSlice({
  name: 'recurring',
  initialState,
  reducers: {
    // Fetch all recurring contributions
    fetchRecurringContributionsRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchRecurringContributionsSuccess: (state, action) => {
      state.loading = false;
      state.recurringContributions = action.payload;
    },
    fetchRecurringContributionsFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Fetch recurring contributions for a specific goal
    fetchGoalRecurringContributionsRequest: (state, action) => {
      state.loading = true;
      state.error = null;
    },
    fetchGoalRecurringContributionsSuccess: (state, action) => {
      state.loading = false;
      state.goalRecurringContributions[action.payload.goalId] = action.payload.contributions;
    },
    fetchGoalRecurringContributionsFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Setup a new recurring contribution
    setupRecurringContributionRequest: (state, action) => {
      state.loading = true;
      state.error = null;
    },
    setupRecurringContributionSuccess: (state, action) => {
      state.loading = false;
      state.lastCreated = action.payload;
      state.recurringContributions = [...state.recurringContributions, action.payload];
      
      // Update goal-specific contributions if we have them for this goal
      const goalId = action.payload.goalId;
      if (state.goalRecurringContributions[goalId]) {
        state.goalRecurringContributions[goalId] = [
          ...state.goalRecurringContributions[goalId],
          action.payload
        ];
      }
    },
    setupRecurringContributionFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Update an existing recurring contribution
    updateRecurringContributionRequest: (state, action) => {
      state.loading = true;
      state.error = null;
    },
    updateRecurringContributionSuccess: (state, action) => {
      state.loading = false;
      state.lastUpdated = action.payload;
      
      // Update in main list
      state.recurringContributions = state.recurringContributions.map(contrib => 
        contrib.id === action.payload.id ? action.payload : contrib
      );
      
      // Update in goal-specific list if it exists
      const goalId = action.payload.goalId;
      if (state.goalRecurringContributions[goalId]) {
        state.goalRecurringContributions[goalId] = state.goalRecurringContributions[goalId].map(contrib => 
          contrib.id === action.payload.id ? action.payload : contrib
        );
      }
    },
    updateRecurringContributionFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Delete a recurring contribution
    deleteRecurringContributionRequest: (state, action) => {
      state.loading = true;
      state.error = null;
    },
    deleteRecurringContributionSuccess: (state, action) => {
      state.loading = false;
      state.lastDeleted = action.payload;
      
      // Remove from main list
      state.recurringContributions = state.recurringContributions.filter(
        contrib => contrib.id !== action.payload.contributionId
      );
      
      // Remove from goal-specific list if it exists
      if (action.payload.goalId && state.goalRecurringContributions[action.payload.goalId]) {
        state.goalRecurringContributions[action.payload.goalId] = 
          state.goalRecurringContributions[action.payload.goalId].filter(
            contrib => contrib.id !== action.payload.contributionId
          );
      }
    },
    deleteRecurringContributionFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Manually trigger a contribution
    triggerContributionRequest: (state, action) => {
      state.loading = true;
      state.error = null;
    },
    triggerContributionSuccess: (state, action) => {
      state.loading = false;
      state.lastTriggered = action.payload;
      
      // Update contribution in main list
      state.recurringContributions = state.recurringContributions.map(contrib => 
        contrib.id === action.payload.id ? action.payload : contrib
      );
      
      // Update in goal-specific list if it exists
      const goalId = action.payload.goalId;
      if (state.goalRecurringContributions[goalId]) {
        state.goalRecurringContributions[goalId] = state.goalRecurringContributions[goalId].map(contrib => 
          contrib.id === action.payload.id ? action.payload : contrib
        );
      }
    },
    triggerContributionFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },
    
    // Clear error state
    clearRecurringError: (state) => {
      state.error = null;
    }
  }
});

// Export actions
export const {
  fetchRecurringContributionsRequest,
  fetchRecurringContributionsSuccess,
  fetchRecurringContributionsFailure,
  fetchGoalRecurringContributionsRequest,
  fetchGoalRecurringContributionsSuccess,
  fetchGoalRecurringContributionsFailure,
  setupRecurringContributionRequest,
  setupRecurringContributionSuccess,
  setupRecurringContributionFailure,
  updateRecurringContributionRequest,
  updateRecurringContributionSuccess,
  updateRecurringContributionFailure,
  deleteRecurringContributionRequest,
  deleteRecurringContributionSuccess,
  deleteRecurringContributionFailure,
  triggerContributionRequest,
  triggerContributionSuccess,
  triggerContributionFailure,
  clearRecurringError
} = recurringSlice.actions;

// Export reducer
export default recurringSlice.reducer;