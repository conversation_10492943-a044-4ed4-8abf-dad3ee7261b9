import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  transactions: [],
  loading: false,
  error: null,
  reconciling: false,
};

const reconcileSlice = createSlice({
  name: 'reconcile',
  initialState,
  reducers: {
    fetchReconcileRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchReconcileSuccess: (state, action) => {
      state.loading = false;
      state.transactions = action.payload;
      state.error = null;
    },
    fetchReconcileFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
      state.transactions = [];
    },
    reconcileTransactionsRequest: (state) => {
      state.reconciling = true;
      state.error = null;
    },
    reconcileTransactionsSuccess: (state, action) => {
      state.reconciling = false;
      state.error = null;
      // Optionally update transactions if the response contains updated data
      if (action.payload) {
        state.transactions = action.payload;
      }
    },
    reconcileTransactionsFailure: (state, action) => {
      state.reconciling = false;
      state.error = action.payload;
    },
  },
});

export const {
  fetchReconcileRequest,
  fetchReconcileSuccess,
  fetchReconcileFailure,
  reconcileTransactionsRequest,
  reconcileTransactionsSuccess,
  reconcileTransactionsFailure,
} = reconcileSlice.actions;

export default reconcileSlice.reducer;