import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Grid,
  Divider,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Paper,
  Alert
} from '@mui/material';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { 
  selectSelectedInvestment, 
  selectInvestmentsLoading,
  selectInvestmentsError,
  fetchInvestmentByIdStart
} from '../../../../logic/redux/investmentSlice';
import { axiosInstance } from '../../../../logic/api/axiosConfig';

const InvestmentDetailModal = ({ open, onClose, investmentId }) => {
  const dispatch = useDispatch();
  const investment = useSelector(selectSelectedInvestment);
  const loading = useSelector(selectInvestmentsLoading);
  const error = useSelector(selectInvestmentsError);
  const [historicalData, setHistoricalData] = useState([]);
  const [historicalLoading, setHistoricalLoading] = useState(false);
  const [historicalError, setHistoricalError] = useState(null);

  useEffect(() => {
    if (investmentId && open) {
      dispatch(fetchInvestmentByIdStart(investmentId));
      fetchHistoricalData(investmentId);
    }
  }, [dispatch, investmentId, open]);

  const fetchHistoricalData = async (id) => {
    setHistoricalLoading(true);
    setHistoricalError(null);
    
    try {
      // Assuming your API has an endpoint for historical data
      const response = await axiosInstance.get(`/api/investments/${id}/history`);
      setHistoricalData(response.data);
    } catch (err) {
      console.error('Error fetching historical data:', err);
      setHistoricalError(err.response?.data?.message || 'Failed to load historical data');
    } finally {
      setHistoricalLoading(false);
    }
  };

  const formatCurrency = (value) => {
    if (value === null || value === undefined) return '$0.00';
    return new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(value);
  };

  const formatPercentage = (value) => {
    if (value === null || value === undefined) return '0.00%';
    return value.toFixed(2) + '%';
  };

  if (loading) {
    return (
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogContent>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
            <CircularProgress />
          </Box>
        </DialogContent>
      </Dialog>
    );
  }

  if (error) {
    return (
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogContent>
          <Alert severity="error">{error}</Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose} color="primary">Close</Button>
        </DialogActions>
      </Dialog>
    );
  }

  if (!investment) {
    return null;
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Typography variant="h5">{investment.securityName}</Typography>
        <Typography variant="subtitle1" color="textSecondary">
          {investment.ticker} • {investment.securityType}
        </Typography>
      </DialogTitle>
      
      <DialogContent>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Box mb={2}>
              <Typography variant="h6">Current Price</Typography>
              <Box display="flex" alignItems="center">
                <Typography variant="h4">{formatCurrency(investment.currentPrice)}</Typography>
                <Box ml={2} display="flex" alignItems="center">
                  {investment.todayChangePercent >= 0 ? (
                    <>
                      <TrendingUpIcon color="success" />
                      <Typography variant="body1" color="success.main">
                        {formatPercentage(investment.todayChangePercent)}
                      </Typography>
                    </>
                  ) : (
                    <>
                      <TrendingDownIcon color="error" />
                      <Typography variant="body1" color="error.main">
                        {formatPercentage(investment.todayChangePercent)}
                      </Typography>
                    </>
                  )}
                </Box>
              </Box>
            </Box>
          </Grid>

          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>Price History</Typography>
            <Paper variant="outlined" sx={{ p: 2 }}>
              {historicalLoading ? (
                <Box display="flex" justifyContent="center" alignItems="center" height={200}>
                  <CircularProgress size={30} />
                </Box>
              ) : historicalError ? (
                <Box display="flex" justifyContent="center" alignItems="center" height={200}>
                  <Alert severity="warning">
                    {historicalError}
                  </Alert>
                </Box>
              ) : historicalData.length > 0 ? (
                <ResponsiveContainer width="100%" height={200}>
                  <LineChart
                    data={historicalData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" tickFormatter={(timestamp) => {
                      const date = new Date(timestamp);
                      return `${date.getMonth() + 1}/${date.getDate()}`;
                    }} />
                    <YAxis 
                      domain={['dataMin - 5', 'dataMax + 5']} 
                      tickFormatter={(value) => '$' + value.toFixed(2)}
                    />
                    <Tooltip 
                      formatter={(value) => ['$' + value.toFixed(2), 'Price']}
                      labelFormatter={(timestamp) => `Date: ${new Date(timestamp).toLocaleDateString()}`}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="price" 
                      stroke="#8884d8" 
                      activeDot={{ r: 8 }} 
                    />
                  </LineChart>
                </ResponsiveContainer>
              ) : (
                <Box display="flex" justifyContent="center" alignItems="center" height={200}>
                  <Typography variant="body1" color="textSecondary">
                    No historical data available
                  </Typography>
                </Box>
              )}
            </Paper>
          </Grid>

          <Grid item xs={12} md={6}>
            <TableContainer component={Paper} variant="outlined">
              <Table size="small">
                <TableBody>
                  <TableRow>
                    <TableCell><strong>Shares Owned</strong></TableCell>
                    <TableCell align="right">{investment.quantity?.toFixed(4) || '0.0000'}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell><strong>Total Value</strong></TableCell>
                    <TableCell align="right">{formatCurrency(investment.value)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell><strong>Cost Basis</strong></TableCell>
                    <TableCell align="right">{formatCurrency(investment.costBasis)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell><strong>Average Purchase Price</strong></TableCell>
                    <TableCell align="right">
                      {investment.quantity > 0 ? 
                        formatCurrency(investment.costBasis / investment.quantity) : 
                        '$0.00'}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          </Grid>

          <Grid item xs={12} md={6}>
            <TableContainer component={Paper} variant="outlined">
              <Table size="small">
                <TableBody>
                  <TableRow>
                    <TableCell><strong>Total Gain/Loss</strong></TableCell>
                    <TableCell 
                      align="right"
                      sx={{ color: investment.totalGain >= 0 ? 'success.main' : 'error.main' }}
                    >
                      {formatCurrency(investment.totalGain)}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell><strong>Total Gain/Loss %</strong></TableCell>
                    <TableCell 
                      align="right"
                      sx={{ color: investment.totalGainPercent >= 0 ? 'success.main' : 'error.main' }}
                    >
                      {formatPercentage(investment.totalGainPercent)}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell><strong>Today's Change</strong></TableCell>
                    <TableCell 
                      align="right"
                      sx={{ color: investment.todayChange >= 0 ? 'success.main' : 'error.main' }}
                    >
                      {formatCurrency(investment.todayChange)}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell><strong>Today's Change %</strong></TableCell>
                    <TableCell 
                      align="right"
                      sx={{ color: investment.todayChangePercent >= 0 ? 'success.main' : 'error.main' }}
                    >
                      {formatPercentage(investment.todayChangePercent)}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          </Grid>

          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
            <Typography variant="body2" color="textSecondary">
              Last Updated: {new Date(investment.lastUpdated).toLocaleString()}
            </Typography>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} color="primary" variant="contained">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default InvestmentDetailModal;