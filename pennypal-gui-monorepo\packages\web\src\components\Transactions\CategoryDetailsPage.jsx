import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Typography,
  CircularProgress,
  IconButton,
  Container,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
} from '@mui/material';
import { logEvent } from '../../utils/EventLogger';
import { getCurrentUserId } from '../../utils/AuthUtil';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import BarChartIcon from '@mui/icons-material/BarChart';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';
import {
  fetchCategoryMonthlyExpensesStart,
  setOpenCategoryModal,
  setSelectedSubCategoryId,
} from '../../../../logic/redux/transactionSlice';

ChartJS.register(LineElement, PointElement, LinearScale, CategoryScale, Title, Tooltip, Legend, Filler);

const CategoryDetailsPage = ({ darkMode }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { categoryId, subCategoryId } = useParams();
  const categories = useSelector(state => state.budget.categories);
  const subcategories = useSelector(state => state.budget.subcategories);
  const subcategoriesLoading = useSelector(state => state.budget.loadingSubcategories);

  const data = useSelector((state) => state.transactions.categoryMonthlyExpenses);
  const loading = useSelector((state) => state.transactions.loadingCategoryExpenses);

  const currentCategory = categories.find(cat => cat.id === parseInt(categoryId));
  const currentSubcategory = subcategories.find(sub => sub.id === parseInt(subCategoryId));

  // Color palette for different categories/subcategories
  const colorPalette = [
    {
      name: 'Emerald',
      primary: '#10b981',
      secondary: '#059669',
      gradient: ['#6ee7b7', '#10b981'],
      bg: darkMode ? 'rgba(16, 185, 129, 0.1)' : 'rgba(16, 185, 129, 0.05)',
      border: darkMode ? 'rgba(16, 185, 129, 0.3)' : 'rgba(16, 185, 129, 0.2)'
    },
    {
      name: 'Blue',
      primary: '#3b82f6',
      secondary: '#2563eb',
      gradient: ['#93c5fd', '#3b82f6'],
      bg: darkMode ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)',
      border: darkMode ? 'rgba(59, 130, 246, 0.3)' : 'rgba(59, 130, 246, 0.2)'
    },
    {
      name: 'Purple',
      primary: '#8b5cf6',
      secondary: '#7c3aed',
      gradient: ['#c4b5fd', '#8b5cf6'],
      bg: darkMode ? 'rgba(139, 92, 246, 0.1)' : 'rgba(139, 92, 246, 0.05)',
      border: darkMode ? 'rgba(139, 92, 246, 0.3)' : 'rgba(139, 92, 246, 0.2)'
    },
    {
      name: 'Pink',
      primary: '#ec4899',
      secondary: '#db2777',
      gradient: ['#f9a8d4', '#ec4899'],
      bg: darkMode ? 'rgba(236, 72, 153, 0.1)' : 'rgba(236, 72, 153, 0.05)',
      border: darkMode ? 'rgba(236, 72, 153, 0.3)' : 'rgba(236, 72, 153, 0.2)'
    },
    {
      name: 'Orange',
      primary: '#f97316',
      secondary: '#ea580c',
      gradient: ['#fdba74', '#f97316'],
      bg: darkMode ? 'rgba(249, 115, 22, 0.1)' : 'rgba(249, 115, 22, 0.05)',
      border: darkMode ? 'rgba(249, 115, 22, 0.3)' : 'rgba(249, 115, 22, 0.2)'
    },
    {
      name: 'Teal',
      primary: '#14b8a6',
      secondary: '#0f766e',
      gradient: ['#5eead4', '#14b8a6'],
      bg: darkMode ? 'rgba(20, 184, 166, 0.1)' : 'rgba(20, 184, 166, 0.05)',
      border: darkMode ? 'rgba(20, 184, 166, 0.3)' : 'rgba(20, 184, 166, 0.2)'
    },
    {
      name: 'Red',
      primary: '#ef4444',
      secondary: '#dc2626',
      gradient: ['#fca5a5', '#ef4444'],
      bg: darkMode ? 'rgba(239, 68, 68, 0.1)' : 'rgba(239, 68, 68, 0.05)',
      border: darkMode ? 'rgba(239, 68, 68, 0.3)' : 'rgba(239, 68, 68, 0.2)'
    },
    {
      name: 'Indigo',
      primary: '#6366f1',
      secondary: '#4f46e5',
      gradient: ['#a5b4fc', '#6366f1'],
      bg: darkMode ? 'rgba(99, 102, 241, 0.1)' : 'rgba(99, 102, 241, 0.05)',
      border: darkMode ? 'rgba(99, 102, 241, 0.3)' : 'rgba(99, 102, 241, 0.2)'
    }
  ];

  // Create the dropdown value based on current selection
  const getDropdownValue = () => {
    if (subCategoryId) {
      return `sub_${subCategoryId}`;
    } else if (categoryId) {
      return `cat_${categoryId}`;
    }
    return '';
  };

  // State for the selected option
  const [selectedOption, setSelectedOption] = React.useState(getDropdownValue);

  useEffect(() => {
    setSelectedOption(getDropdownValue());
  }, [categoryId, subCategoryId]);

  // Handle selection change
  const handleSelectionChange = (event) => {
    const value = event.target.value;
    setSelectedOption(value);

    const userId = getCurrentUserId();

    if (value.startsWith('cat_')) {
      const newCategoryId = value.replace('cat_', '');
      const category = categories.find(cat => cat.id === parseInt(newCategoryId));
    
    logEvent('CategoryDetailsPage', 'CategorySelected', {
      previousCategoryId: categoryId,
      newCategoryId,
      categoryName: category?.name,
      fromSubcategory: subCategoryId ? true : false
    });
      navigate(`/dashboard/category-details/${newCategoryId}`);
      
      dispatch(
        fetchCategoryMonthlyExpensesStart({
          categoryId: parseInt(newCategoryId),
          subCategoryId: null,
          userId,
          months: 6,
        })
      );
    } else if (value.startsWith('sub_')) {
      const newSubCategoryId = value.replace('sub_', '');
      const subcategory = subcategories.find(sub => sub.id === parseInt(newSubCategoryId));
      
      if (subcategory) {
        logEvent('CategoryDetailsPage', 'SubCategorySelected', {
        categoryId: subcategory.categoryId,
        previousSubCategoryId: subCategoryId,
        newSubCategoryId,
        subCategoryName: subcategory.subCategory
      });

        navigate(`/dashboard/category-details/${subcategory.categoryId}/${newSubCategoryId}`);
        
        dispatch(
          fetchCategoryMonthlyExpensesStart({
            categoryId: subcategory.categoryId,
            subCategoryId: parseInt(newSubCategoryId),
            userId,
            months: 6,
          })
        );
      }
    }
  };

  // Initial data fetch
  useEffect(() => {
      const userId = getCurrentUserId(); //  Add this line

    if (subcategories.length === 0) {
      dispatch({ type: 'budget/fetchSubcategories' });
    }

    if (categoryId) {
      dispatch(
        fetchCategoryMonthlyExpensesStart({
          categoryId: parseInt(categoryId),
          subCategoryId: subCategoryId ? parseInt(subCategoryId) : null,
          userId,
          months: 6,
        })
      );
    }
  }, [dispatch, categoryId, subCategoryId, subcategories.length]);

  // Calculate statistics
  const calculateStats = () => {
    if (!data || data.length === 0) return null;
    const amounts = data.map((entry) => entry.total);
    const total = amounts.reduce((sum, amount) => sum + amount, 0);
    const average = total / amounts.length;
    const highest = Math.max(...amounts);
    const lowest = Math.min(...amounts);
    const firstMonth = amounts[0];
    const lastMonth = amounts[amounts.length - 1];
    const trend = firstMonth !== 0 ? ((lastMonth - firstMonth) / firstMonth) * 100 : 0;
    return { total, average, highest, lowest, trend };
  };

  const stats = calculateStats();

  // Get current color based on selection
  const getCurrentColor = () => {
    if (subCategoryId) {
      const subIndex = subcategories.findIndex(sub => sub.id === parseInt(subCategoryId));
      return colorPalette[subIndex % colorPalette.length] || colorPalette[0];
    } else if (categoryId) {
      const catIndex = categories.findIndex(cat => cat.id === parseInt(categoryId));
      return colorPalette[catIndex % colorPalette.length] || colorPalette[0];
    }
    return colorPalette[0];
  };

  const currentColor = getCurrentColor();

  // Get display name for current selection
  const getDisplayName = () => {
    if (currentSubcategory) {
      return `${currentCategory?.name || 'Category'} - ${currentSubcategory.subCategory}`;
    } else if (currentCategory) {
      return currentCategory.name || 'Category';
    }
    return 'Category';
  };

  // Get render value for dropdown
  const getRenderValue = (selected) => {
    if (selected.startsWith('cat_')) {
      const catId = selected.replace('cat_', '');
      const category = categories.find(cat => cat.id === parseInt(catId));
      const catIndex = categories.findIndex(cat => cat.id === parseInt(catId));
      const catColor = colorPalette[catIndex % colorPalette.length];
      return (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box
            sx={{
              width: 8,
              height: 8,
              borderRadius: '50%',
              backgroundColor: catColor.primary,
            }}
          />
          <Typography variant="body2" sx={{ fontWeight: 500 }}>
            {category?.name || 'Category'}
          </Typography>
        </Box>
      );
    } else if (selected.startsWith('sub_')) {
      const subId = selected.replace('sub_', '');
      const subcategory = subcategories.find(sub => sub.id === parseInt(subId));
      const subIndex = subcategories.findIndex(sub => sub.id === parseInt(subId));
    const subColor = colorPalette[(subIndex === -1 ? 0 : subIndex) % colorPalette.length]; // fix here
      return (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box
            sx={{
              width: 8,
              height: 8,
              borderRadius: '50%',
              backgroundColor: subColor.primary,
            }}
          />
          <Typography variant="body2" sx={{ fontWeight: 500 }}>
            {subcategory?.subCategory || 'Subcategory'}
          </Typography>
        </Box>
      );
    }
    return 'Select';
  };

  // Card data for stats with dynamic colors
  const cardData = stats
    ? [
        {
          label: 'Total Spent',
          value: `$${stats.total.toLocaleString()}`,
          icon: AttachMoneyIcon,
          color: currentColor,
        },
        {
          label: 'Monthly Average',
          value: `$${Math.round(stats.average).toLocaleString()}`,
          icon: BarChartIcon,
          color: colorPalette[1],
        },
        {
          label: 'Highest Month',
          value: `$${stats.highest.toLocaleString()}`,
          icon: TrendingUpIcon,
          color: colorPalette[2],
        },
        {
          label: 'Growth Trend',
          value: `${stats.trend > 0 ? '+' : ''}${stats.trend.toFixed(1)}%`,
          icon: stats.trend >= 0 ? TrendingUpIcon : TrendingDownIcon,
          color: stats.trend >= 0 ? colorPalette[4] : colorPalette[0],
        },
      ]
    : [];

  // Chart data with dynamic colors
  const chartData = {
    labels: data.map((entry) => entry.month),
    datasets: [
      {
        label: getDisplayName(),
        data: data.map((entry) => entry.total),
        fill: true,
        backgroundColor: (context) => {
          const ctx = context.chart.ctx;
          const gradient = ctx.createLinearGradient(0, 0, 0, 300);
          gradient.addColorStop(0, `${currentColor.primary}40`);
          gradient.addColorStop(1, `${currentColor.primary}00`);
          return gradient;
        },
        borderColor: currentColor.primary,
        borderWidth: 3,
        pointBackgroundColor: darkMode ? '#1f2937' : '#ffffff',
        pointBorderColor: currentColor.primary,
        pointBorderWidth: 3,
        pointRadius: 5,
        pointHoverRadius: 7,
        pointHoverBackgroundColor: currentColor.gradient[0],
        pointHoverBorderColor: currentColor.secondary,
        tension: 0.4,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false },
      tooltip: {
        backgroundColor: darkMode ? 'rgba(31, 41, 55, 0.95)' : 'rgba(255, 255, 255, 0.95)',
        titleColor: darkMode ? '#f9fafb' : '#1f2937',
        bodyColor: darkMode ? '#d1d5db' : '#6b7280',
        borderColor: currentColor.primary,
        borderWidth: 2,
        cornerRadius: 8,
        displayColors: false,
        padding: 12,
        titleFont: { size: 14, weight: 'bold' },
        bodyFont: { size: 13 },
        callbacks: {
          label: function (context) {
            return `$${context.parsed.y.toLocaleString()}`;
          },
        },
      },
    },
    scales: {
      x: {
        grid: { display: false },
        border: { display: false },
        ticks: {
          color: darkMode ? '#9ca3af' : '#6b7280',
          font: { size: 12 },
          padding: 8,
        },
      },
      y: {
        grid: { display: false },
        border: { display: false },
        ticks: { display: false },
      },
    },
    interaction: {
      intersect: false,
      mode: 'index',
    },
    layout: {
      padding: { top: 20, right: 20, bottom: 20, left: 20 },
    },
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        bgcolor: darkMode ? '#0f172a' : '#f8fafc',
        py: 3,
      }}
    >
      <Container maxWidth="lg">
        {/* Header */}
        <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 2 }}>
          <IconButton
            onClick={() => navigate(-1)}
            sx={{
              color: currentColor.primary,
              bgcolor: currentColor.bg,
              '&:hover': { bgcolor: currentColor.border },
            }}
          >
            <ArrowBackIcon />
          </IconButton>
          <Box sx={{ flex: 1 }}>
            <Typography
              variant="h4"
              sx={{
                color: darkMode ? '#f9fafb' : '#1f2937',
                fontWeight: 'bold',
                mb: 0.5,
              }}
            >
              {getDisplayName()}
            </Typography>
            <Typography variant="body2" sx={{ color: darkMode ? '#9ca3af' : '#6b7280' }}>
              6-month expense analysis
            </Typography>
          </Box>
          
          {/* Enhanced Hierarchical Dropdown */}
          <FormControl 
            size="small" 
            sx={{ 
              minWidth: 220,
              '& .MuiInputLabel-root': {
                fontSize: '0.875rem',
                color: darkMode ? '#9ca3af' : '#6b7280',
                '&.Mui-focused': {
                  color: currentColor.primary,
                },
              },
            }}
          >
            <InputLabel id="category-subcategory-select-label">
              Category & Subcategory
            </InputLabel>
            <Select
              labelId="category-subcategory-select-label"
              value={selectedOption}
              label="Category & Subcategory"
              onChange={handleSelectionChange}
              IconComponent={ExpandMoreIcon}
              sx={{
                height: 40,
                borderRadius: 2,
                color: darkMode ? '#f3f4f6' : '#111827',
                backgroundColor: darkMode ? '#1f2937' : '#ffffff',
                fontSize: '0.875rem',
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: darkMode ? '#4b5563' : '#d1d5db',
                  borderWidth: 1,
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: currentColor.primary,
                  borderWidth: 2,
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: currentColor.primary,
                  borderWidth: 2,
                  boxShadow: `0 0 0 3px ${currentColor.primary}20`,
                },
                '& .MuiSelect-icon': {
                  color: currentColor.primary,
                  transition: 'transform 0.2s ease',
                },
                '&.Mui-focused .MuiSelect-icon': {
                  transform: 'rotate(180deg)',
                },
              }}
              renderValue={(selected) => getRenderValue(selected)}
              MenuProps={{
                PaperProps: {
                  sx: {
                    bgcolor: darkMode ? '#1f2937' : '#ffffff',
                    borderRadius: 2,
                    border: `1px solid ${darkMode ? '#374151' : '#e5e7eb'}`,
                    boxShadow: darkMode 
                      ? '0 10px 25px rgba(0, 0, 0, 0.5)' 
                      : '0 10px 25px rgba(0, 0, 0, 0.1)',
                    mt: 1,
                    maxHeight: 400,
                    '& .MuiMenuItem-root': {
                      color: darkMode ? '#f3f4f6' : '#111827',
                      fontSize: '0.875rem',
                      padding: '8px 16px',
                      borderRadius: 1,
                      margin: '2px 4px',
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        bgcolor: currentColor.bg,
                        color: currentColor.primary,
                        transform: 'translateX(4px)',
                      },
                      '&.Mui-selected': {
                        bgcolor: currentColor.primary,
                        color: '#ffffff',
                        fontWeight: 600,
                        '&:hover': {
                          bgcolor: currentColor.secondary,
                          transform: 'none',
                        },
                      },
                      // Category group header styling
                      '&.category-header': {
                        fontWeight: 'bold',
                        color: darkMode ? '#e5e7eb' : '#374151',
                        backgroundColor: darkMode ? '#374151' : '#f3f4f6',
                        cursor: 'default',
                        '&:hover': {
                          backgroundColor: darkMode ? '#374151' : '#f3f4f6',
                          transform: 'none',
                        },
                      },
                      // Subcategory indentation
                      '&.subcategory-item': {
                        paddingLeft: '32px',
                        fontWeight: 'normal',
                      },
                    },
                  },
                },
              }}
            >
            {categories.map((category, catIndex) => {
  const catColor = colorPalette[catIndex % colorPalette.length];
  const categorySubcategories = subcategories.filter(
    sub => sub.categoryId === category.id
  );

  return [
    // Category group header (non-selectable)
   <MenuItem 
  key={`cat_${category.id}`} 
  value={`cat_${category.id}`}
  sx={{ fontWeight: 'bold', pl: 4 }}
>
  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
    <Box
      sx={{
        width: 8,
        height: 8,
        borderRadius: '50%',
        backgroundColor: catColor.primary,
      }}
    />
    <Typography sx={{ fontWeight: 'bold' }}>
      {category.name}
    </Typography>
  </Box>
</MenuItem>,


    //  Selectable Category itself
    <MenuItem 
      key={`cat_${category.id}`} 
      value={`cat_${category.id}`}
      sx={{ fontWeight: 'bold', pl: 4 }}
    >
      <Typography>
    {category.category}
      </Typography>
    </MenuItem>,

    // Subcategories under this category
    ...categorySubcategories.map((subcategory, subIndex) => {
      const subColor = colorPalette[(catIndex + subIndex + 1) % colorPalette.length];
      return (
        <MenuItem
          key={`sub_${subcategory.id}`}
          value={`sub_${subcategory.id}`}
          className="subcategory-item"
          sx={{ 
            pl: 6,
            fontSize: '0.8rem',
            color: darkMode ? '#d1d5db' : '#6b7280',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Box
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: subColor.primary,
              }}
            />
            <Typography>{subcategory.subCategory}</Typography>
          </Box>
        </MenuItem>
      );
    })
  ];
})}

            </Select>
          </FormControl>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
            <CircularProgress size={50} sx={{ color: currentColor.primary }} />
          </Box>
        ) : data.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="h6" sx={{ color: darkMode ? '#f1f5f9' : '#1e293b' }}>
              No data available for this selection
            </Typography>
          </Box>
        ) : (
          <>
            {/* Stats Cards */}
            <Box
              sx={{
                display: 'grid',
                gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)', lg: 'repeat(4, 1fr)' },
                gap: 3,
                mb: 4,
              }}
            >
              {cardData.map((item, index) => {
                const IconComponent = item.icon;
                return (
                  <Box
                    key={index}
                    sx={{
                      position: 'relative',
                      overflow: 'hidden',
                      borderRadius: 3,
                      border: '1px solid',
                      borderColor: item.color.border,
                      backgroundColor: item.color.bg,
                      transition: 'all 0.3s ease',
                      cursor: 'pointer',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: `0 8px 25px ${item.color.primary}40`,
                        borderColor: item.color.primary,
                      },
                      '&:hover .top-bar': {
                        transform: 'scaleX(1)',
                      },
                      '&:hover .icon-container': {
                        transform: 'rotate(6deg) scale(1.05)',
                      },
                    }}
                  >
                    <Box
                      className="top-bar"
                      sx={{
                        height: '4px',
                        width: '100%',
                        background: `linear-gradient(to right, ${item.color.gradient[0]}, ${item.color.primary})`,
                        transform: 'scaleX(0)',
                        transformOrigin: 'left',
                        transition: 'transform 0.3s ease',
                      }}
                    />
                    <Box sx={{ p: 3 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                        <Box
                          className="icon-container"
                          sx={{
                            p: 1.5,
                            borderRadius: 2,
                            background: `linear-gradient(to bottom right, ${item.color.gradient[0]}, ${item.color.primary})`,
                            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                            transition: 'all 0.3s ease',
                            mr: 2,
                          }}
                        >
                          <IconComponent sx={{ color: 'white', fontSize: 20 }} />
                        </Box>
                        <Typography
                          variant="caption"
                          sx={{
                            fontSize: '0.75rem',
                            fontWeight: 'bold',
                            textTransform: 'uppercase',
                            letterSpacing: '0.05em',
                            color: darkMode ? '#d1d5db' : '#6b7280',
                            flex: 1,
                          }}
                        >
                          {item.label}
                        </Typography>
                        <Box
                          sx={{
                            width: 8,
                            height: 8,
                            borderRadius: '50%',
                            background: `linear-gradient(to right, ${item.color.gradient[0]}, ${item.color.primary})`,
                          }}
                        />
                      </Box>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography
                          variant="h5"
                          sx={{
                            fontWeight: 'bold',
                            color: darkMode ? '#f9fafb' : '#1f2937',
                            letterSpacing: '-0.025em',
                          }}
                        >
                          {item.value}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                );
              })}
            </Box>

            {/* Chart */}
            <Card
              sx={{
                bgcolor: darkMode ? '#1e293b' : '#ffffff',
                borderRadius: 3,
                border: '1px solid',
                borderColor: darkMode ? '#334155' : '#e2e8f0',
                mb: 3,
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <Box
                    sx={{
                      width: 12,
                      height: 12,
                      borderRadius: '50%',
                      backgroundColor: currentColor.primary,
                    }}
                  />
                  <Typography variant="h6" sx={{ color: darkMode ? '#f1f5f9' : '#1e293b', fontWeight: 'bold' }}>
                    {getDisplayName()} - 6 Month Trend
                  </Typography>
                </Box>
                <Box sx={{ height: 300 }}>
                  <Line data={chartData} options={chartOptions} />
                </Box>
              </CardContent>
            </Card>

            {/* Summary */}
            {data.length > 0 && (
              <Card
                sx={{
                  bgcolor: darkMode ? '#1e293b' : '#ffffff',
                  borderRadius: 3,
                  border: '1px solid',
                  borderColor: darkMode ? '#334155' : '#e2e8f0',
                }}
              >
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box>
                      <Typography
                        variant="body2"
                        sx={{ color: darkMode ? '#cbd5e1' : '#64748b', mb: 0.5 }}
                      >
                        Analysis Period
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{ color: darkMode ? '#f1f5f9' : '#1e293b', fontWeight: '500' }}
                      >
                        {data.length} months of data
                      </Typography>
                    </Box>
                    <Box sx={{ textAlign: 'right' }}>
                      <Typography
                        variant="body2"
                        sx={{ color: darkMode ? '#cbd5e1' : '#64748b', mb: 0.5 }}
                      >
                        Latest Month
                      </Typography>
                      <Typography
                        variant="h6"
                        sx={{ color: currentColor.primary, fontWeight: 'bold' }}
                      >
                        ${data[data.length - 1]?.total.toLocaleString() || '0'}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            )}
          </>
        )}
      </Container>
    </Box>
  );
};

export default CategoryDetailsPage;