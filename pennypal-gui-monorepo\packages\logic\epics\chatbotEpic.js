import { ofType, combineEpics } from 'redux-observable';
import { of, from } from 'rxjs';
import { switchMap, catchError } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchHistoryRequest,
  fetchHistorySuccess,
  fetchHistoryFailure,
  queryRequest,
  querySuccess,
  queryFailure,
} from '../redux/chatbotSlice';
import { invalidateChatbotCache } from '../redux/cacheSlice';

// Epic to fetch chatbot history
export const fetchChatbotHistoryEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchHistoryRequest.type),
    switchMap((action) => {
      try {
        const cache = state$.value?.cache;
        const { userId } = action.payload || {};
        
        // Check if chatbot history is already cached
        if (cache?.chatbotHistoryLoaded && cache?.chatbotHistory?.length >= 0 &&
            cache?.chatbotHistoryParams?.userId === userId) {
          console.log('✅ Using cached chatbot history in chatbot epic');
          return of(fetchHistorySuccess(cache.chatbotHistory));
        }
        
        console.log('🔄 Chatbot history not cached, making API call');
        
        if (!userId) {
          console.error('❌ User ID not found for chatbot history API');
          return of(fetchHistoryFailure('User ID not found'));
        }

        return from(axiosInstance.get(`/pennypal/api/v1/chatbot/history/${userId}`)).pipe(
          switchMap((response) => {
            console.log('✅ Chatbot history fetched successfully');
            return of(fetchHistorySuccess(response.data));
          }),
          catchError((error) => {
            console.error('❌ Failed to fetch chatbot history:', error);
            const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch chatbot history';
            return of(fetchHistoryFailure(errorMessage));
          })
        );
      } catch (error) {
        console.error('Error checking cache state in chatbot history epic:', error);
        // Make API call on error
        const { userId } = action.payload || {};
        return from(axiosInstance.get(`/pennypal/api/v1/chatbot/history/${userId}`)).pipe(
          switchMap((response) => of(fetchHistorySuccess(response.data))),
          catchError((error) => {
            const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch chatbot history';
            return of(fetchHistoryFailure(errorMessage));
          })
        );
      }
    })
  );

// Epic to handle chatbot queries
export const chatbotQueryEpic = (action$) =>
  action$.pipe(
    ofType(queryRequest.type),
    switchMap((action) => {
      console.log('🚀 Starting chatbot query...');
      const { userId, userQuery } = action.payload || {};

      if (!userId || !userQuery) {
        console.error('❌ User ID or query not found for chatbot query API');
        return of(queryFailure('User ID or query not found'));
      }

      return from(axiosInstance.post('/pennypal/api/v1/chatbot/query', {
        user_id: userId,
        user_query: userQuery,
      })).pipe(
        switchMap((response) => {
          console.log('✅ Chatbot query completed successfully');
          // Invalidate cache since new query was made
          return of(
            querySuccess(response.data),
            invalidateChatbotCache()
          );
        }),
        catchError((error) => {
          console.error('❌ Failed to process chatbot query:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to process chatbot query';
          return of(queryFailure(errorMessage));
        })
      );
    })
  );

// Combined chatbot epic
export const chatbotEpic = combineEpics(
  fetchChatbotHistoryEpic,
  chatbotQueryEpic
);

export default chatbotEpic;
