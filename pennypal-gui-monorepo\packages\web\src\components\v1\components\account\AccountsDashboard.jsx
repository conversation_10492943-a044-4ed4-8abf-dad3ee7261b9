// import React, { useState, useEffect, useMemo } from "react";
// import { useDispatch, useSelector } from "react-redux";
// import { PlaidLink } from "react-plaid-link";
// import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
// import {
//   faLink,
//   faSync,
//   faPlus,
//   faRedo,
//   faTimes,
//   faClock,
//   faUniversity,
//   faExclamationTriangle, 
//   faArrowUp, 
//   faArrowDown, 
//   faEquals,  
//   faSpinner,
//   faChartLine,
//   faCreditCard,
//   faCoins,
//   faLandmark,
//   faSackDollar,
//   faRobot,
//   faExchangeAlt
// } from "@fortawesome/free-solid-svg-icons";
// import AccountChart from "./AccountChart";
// import BankIcon from "./BankIcon";
// import AccountMiniChart from "./AccountMiniChart";
// import {
//   fetchLinkToken,
//   fetchAccountDetails,
//   exchangePublicToken,
//   refreshAllAccounts,
//   syncAccount,
//   connectMx,
//   connectStripe,
//   connectFinicity,
//   resetError
// } from "@pp-logic/redux/accountsDashboardSlice";
// import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
// import { getCurrentUserId } from "@pp-web/utils/AuthUtil";
// import { logEvent } from '@pp-web/utils/EventLogger';
// import { fetchDeltaData } from '@pp-logic/redux/deltaSlice';
// import { 
//   selectDeltaData, 
//   selectDeltaLoading, 
//   selectDeltaError,
//   selectDeltaAccounts,
//   selectDeltaSummary ,
//   selectDeltaAccountTypes
  
// } from '@pp-logic/redux/deltaSlice';
// import {  setChartType,
//   setTimePeriod,
//   fetchAccountData,
//   selectChartType,
//   selectTimePeriod, } from '@pp-logic/redux/accountChartSlice';

// import { formatLastSyncTime, 
//          formatCategoryName,
//          getCategoryIcon,
//          maskAccountNumber,
//          ALL_CATEGORIES
//         } from './AccountUtil';

// import DesignPaletteDropdown from "./DesignPlatteDropdown";
// import PortfolioChartControlView from "./PortfolioChartControlView";
// import StatCardGroupView from "./StatCardGroupView";
// import AccountConnectionProviderModal from "./AccountConnectionProviderModal";
// import AccountAIChat from "./AccountAIChat";


// // Enhanced drag handle with better visual design
// const DragHandleIcon = ({ darkMode }) => (
//   <svg
//     width="20"
//     height="20"
//     viewBox="0 0 20 20"
//     fill="none"
//     xmlns="http://www.w3.org/2000/svg"
//     className={`cursor-grab transition-all duration-200 hover:scale-110 ${
//       darkMode ? 'fill-gray-400 hover:fill-gray-300' : 'fill-gray-500 hover:fill-gray-700'
//     }`}
//   >
//     <circle cx="6" cy="5" r="2" />
//     <circle cx="6" cy="10" r="2" />
//     <circle cx="6" cy="15" r="2" />
//     <circle cx="14" cy="5" r="2" />
//     <circle cx="14" cy="10" r="2" />
//     <circle cx="14" cy="15" r="2" />
//   </svg>
// );

// const AccountsDashboard = ({ darkMode }) => {
//   const userId = getCurrentUserId();
//   const dispatch = useDispatch();
//   const selectedChartType = useSelector(selectChartType);
//   const selectedTimePeriod = useSelector(selectTimePeriod);
//   const deltaAccountTypes = useSelector(selectDeltaAccountTypes);
//   // NEW: Theme state management with granular design options
//   const [currentTheme, setCurrentTheme] = useState({
//     theme: 'default',
//     font: { name: 'Inter', value: "'Inter', sans-serif" },
//     colors: {
//       primary: "#8BC34A",
//       secondary: "#7CB342",
//       accent: "#9CCC65",
//       success: "#4CAF50",
//       background: "#f8fffe",
//       darkBackground: "#0f172a",
//       cardBg: "#ffffff",
//       darkCardBg: "#1e293b",
//       text: "#334155",
//       darkText: "#f1f5f9",
//       border: "#e2e8f0",
//       darkBorder: "#475569"
//     },
//     layout: {
//       maxWidth: 'none', // 'none', '1200px', '1400px', '1600px'
//       centered: false,
//       spacing: 'normal', // 'compact', 'normal', 'spacious'
//       gridDensity: 'normal', // 'compact', 'normal', 'spacious'
//       cardStyle: 'elevated', // 'flat', 'elevated', 'outlined'
//       borderRadius: 'rounded', // 'square', 'rounded', 'pill'
//       buttonStyle: 'rounded', // 'square', 'rounded', 'pill'
//       animations: true,
//       shadows: true
//     }
//   });

//   // NEW: Theme change handler (enhanced to handle all layout changes)
//   const handleThemeChange = (newTheme) => {
//     setCurrentTheme(newTheme);
//     // Force a re-render by updating the key state
//     setForceUpdate(prev => prev + 1);
//   };

//   // Add a force update state to ensure components re-render with new theme
//   const [forceUpdate, setForceUpdate] = useState(0);
  
//   // Get delta data
//   const deltaData = useSelector(selectDeltaData);
//   const deltaLoading = useSelector(selectDeltaLoading);
//   const deltaError = useSelector(selectDeltaError);
//   const deltaAccounts = useSelector(selectDeltaAccounts);
//   const deltaSummary = useSelector(selectDeltaSummary);
//   const handleChartTypeChange = (e) => {
//     dispatch(setChartType(e.target.value));
//   };

//   const handleTimePeriodChange = (e) => {
//     dispatch(setTimePeriod(e.target.value));
//   };
//   useEffect(() => {
//     dispatch(fetchAccountData()); // Optionally add payload if needed
//   }, [selectedChartType, selectedTimePeriod, dispatch]);

//   // Map chart time periods to delta time periods
//   const mapTimePeriodForDelta = (chartTimePeriod) => {
//     const mapping = {
//       'one-month': 'one-month',
//       'three-month': 'three-month',
//       'half-year': 'half-year',
//       'yearly': 'yearly',
//       'ytd': 'ytd',
//       'quarterly-aggregate': 'quarterly-rolling',
//       'quarterly-rolling': 'quarterly-rolling'
//     };
    
//     return mapping[chartTimePeriod] || 'three-month';
//   };

//   // Fetch delta data when time period changes
//   useEffect(() => {
//     if (selectedTimePeriod) {
//       const deltaTimePeriod = mapTimePeriodForDelta(selectedTimePeriod);
//       dispatch(fetchDeltaData({ timePeriod: deltaTimePeriod }));
//     }
//   }, [dispatch, selectedTimePeriod]);
// // Updated processAccountTypeDeltaData function to handle the actual API response structure
// const processAccountTypeDeltaData = (responseData, timePeriod) => {
//   if (!responseData) {
//     return {
//       comparisonType: timePeriod,
//       accounts: [],
//       accountTypes: [],
//       metadata: null,
//       summary: {
//         totalCurrentBalance: 0,
//         totalPastBalance: 0,
//         totalDeltaAmount: 0,
//         totalDeltaPercentage: 0,
//         overallTrend: 'neutral'
//       }
//     };
//   }

//   let accountTypes = [];
//   let metadata = {
//     comparisonType: responseData.comparisonType || timePeriod,
//     currentPeriodDate: responseData.currentPeriodDate || '',
//     pastPeriodDate: responseData.pastPeriodDate || '',
//     description: responseData.description || ''
//   };

//   // Handle quarterly rolling data structure for account types
//   if (timePeriod === 'quarterly-rolling' && responseData.quarters) {
//     accountTypes = processQuarterlyRollingAccountTypeDeltas(responseData.quarters);
//     metadata.quarterDetails = responseData.quarterDetails || {};
//   } 
//   // Handle YTD data structure for account types
//   else if (timePeriod === 'ytd') {
//     accountTypes = responseData.accountTypes ? responseData.accountTypes.map(accountType => ({
//       accountType: accountType.accountType || accountType.account_type,
//       accountCount: Number(accountType.accountCount || accountType.account_count || 0),
//       // Map the actual API response field names
//       currentBalance: Number(accountType.totalCurrentBalance || accountType.currentBalance || accountType.current_balance || 0),
//       pastBalance: Number(accountType.totalPastBalance || accountType.pastBalance || accountType.past_balance || 0),
//       deltaAmount: Number(accountType.totalDeltaAmount || accountType.deltaAmount || accountType.delta_amount || 0),
//       deltaPercentage: Number(accountType.deltaPercentage || accountType.delta_percentage || 0),
//       // Additional fields from your API response
//       accountCategories: accountType.accountCategories || [],
//       accountSubtypes: accountType.accountSubtypes || [],
//       currentPeriodDate: accountType.currentPeriodDate || '',
//       pastPeriodDate: accountType.pastPeriodDate || '',
//       // Computed fields
//       trend: determineTrend(accountType.totalDeltaAmount || accountType.deltaAmount || accountType.delta_amount || 0),
//       formattedDelta: formatDeltaDisplay(
//         accountType.totalDeltaAmount || accountType.deltaAmount || accountType.delta_amount || 0,
//         accountType.deltaPercentage || accountType.delta_percentage || 0
//       )
//     })) : [];
    
//     metadata.ytdDays = responseData.ytdDays || 0;
//   } 
//   // Handle regular period deltas for account types
//   else if (responseData.accountTypes) {
//     accountTypes = responseData.accountTypes.map(accountType => ({
//       accountType: accountType.accountType || accountType.account_type,
//       accountCount: Number(accountType.accountCount || accountType.account_count || 0),
//       // Map the actual API response field names
//       currentBalance: Number(accountType.totalCurrentBalance || accountType.currentBalance || accountType.current_balance || 0),
//       pastBalance: Number(accountType.totalPastBalance || accountType.pastBalance || accountType.past_balance || 0),
//       deltaAmount: Number(accountType.totalDeltaAmount || accountType.deltaAmount || accountType.delta_amount || 0),
//       deltaPercentage: Number(accountType.deltaPercentage || accountType.delta_percentage || 0),
//       // Additional fields from your API response
//       accountCategories: accountType.accountCategories || [],
//       accountSubtypes: accountType.accountSubtypes || [],
//       currentPeriodDate: accountType.currentPeriodDate || '',
//       pastPeriodDate: accountType.pastPeriodDate || '',
//       // Computed fields
//       trend: determineTrend(accountType.totalDeltaAmount || accountType.deltaAmount || accountType.delta_amount || 0),
//       formattedDelta: formatDeltaDisplay(
//         accountType.totalDeltaAmount || accountType.deltaAmount || accountType.delta_amount || 0,
//         accountType.deltaPercentage || accountType.delta_percentage || 0
//       )
//     }));
//   }

//   const summary = calculateAccountTypeSummary(accountTypes, timePeriod);

//   return {
//     comparisonType: metadata.comparisonType,
//     accounts: [],
//     accountTypes,
//     metadata,
//     summary,
//     lastUpdated: new Date().toISOString()
//   };
// };

// // Updated calculateAccountTypeSummary to handle the corrected field names
// const calculateAccountTypeSummary = (accountTypes, timePeriod) => {
//   if (!accountTypes || accountTypes.length === 0) {
//     return {
//       totalCurrentBalance: 0,
//       totalPastBalance: 0,
//       totalDeltaAmount: 0,
//       totalDeltaPercentage: 0,
//       overallTrend: 'neutral',
//       accountTypeCount: 0,
//       totalAccountCount: 0
//     };
//   }

//   let totalCurrentBalance, totalPastBalance;

//   if (timePeriod === 'quarterly-rolling') {
//     totalCurrentBalance = accountTypes.reduce((sum, acc) => 
//       sum + (acc.currentQuarterBalance || acc.currentBalance || 0), 0);
//     totalPastBalance = accountTypes.reduce((sum, acc) => 
//       sum + (acc.pastQuarterBalance || acc.pastBalance || 0), 0);
//   } else {
//     totalCurrentBalance = accountTypes.reduce((sum, acc) => 
//       sum + (acc.currentBalance || 0), 0);
//     totalPastBalance = accountTypes.reduce((sum, acc) => 
//       sum + (acc.pastBalance || 0), 0);
//   }

//   const totalDeltaAmount = totalCurrentBalance - totalPastBalance;
//   const totalDeltaPercentage = totalPastBalance !== 0 
//     ? (totalDeltaAmount / totalPastBalance) * 100 
//     : 0;

//   const totalAccountCount = accountTypes.reduce((sum, acc) => 
//     sum + (acc.accountCount || 0), 0);

//   return {
//     totalCurrentBalance,
//     totalPastBalance,
//     totalDeltaAmount,
//     totalDeltaPercentage,
//     overallTrend: determineTrend(totalDeltaAmount),
//     accountTypeCount: accountTypes.length,
//     totalAccountCount,
//     formattedSummary: formatDeltaDisplay(totalDeltaAmount, totalDeltaPercentage)
//   };
// };


//   // Group delta accounts by type for lookup
//   const deltaAccountsMap = useMemo(() => {
//     if (!deltaAccounts || deltaAccounts.length === 0) return {};
    
//     return deltaAccounts.reduce((map, account) => {
//       map[account.accountId] = account;
//       return map;
//     }, {});
//   }, [deltaAccounts]);

//   // UPDATED: Dynamic colors based on current theme (fixed border color issue)
//   const colors = useMemo(() => ({
//     positive: currentTheme.colors.primary,
//     negative: currentTheme.colors.secondary,
//     neutral: darkMode ? 'text-slate-400' : 'text-slate-600',
//     bg: darkMode ? currentTheme.colors.darkBackground : currentTheme.colors.background,
//     cardBg: darkMode ? currentTheme.colors.darkCardBg : currentTheme.colors.cardBg,
//     text: darkMode ? currentTheme.colors.darkText : currentTheme.colors.text,
//     border: darkMode ? currentTheme.colors.darkBorder : currentTheme.colors.border,
//     primary: currentTheme.colors.primary,
//     secondary: currentTheme.colors.secondary,
//     success: currentTheme.colors.success,
//     accent: currentTheme.colors.accent
//   }), [currentTheme, darkMode]);

//   // Format time period for display
//   const formatTimePeriodDisplay = (period) => {
//     const displayNames = {
//       'one-month': '1 Month',
//       'three-month': '3 Months',
//       'half-year': '6 Months',
//       'yearly': '1 Year',
//       'ytd': 'Year to Date',
//       'quarterly-aggregate': 'Quarterly'
//     };
//     return displayNames[period] || period;
//   };

//   // Enhanced trend icon with better styling
//   const TrendIcon = ({ trend, className = "" }) => {
//     const iconProps = { className: `${className} transition-all duration-200` };
    
//     switch (trend) {
//       case 'increase':
//         return <FontAwesomeIcon icon={faArrowUp} {...iconProps} />;
//       case 'decrease':
//         return <FontAwesomeIcon icon={faArrowDown} {...iconProps} />;
//       default:
//         return <FontAwesomeIcon icon={faEquals} {...iconProps} />;
//     }
//   };
  
//   const { 
//     linkToken, 
//     accounts, 
//     isLinked, 
//     isLoading,
//     balanceHistory,
//     error,
//     syncingAccounts = [],
//     refreshAllStatus
//   } = useSelector((state) => state.accounts);

//   const [tableData, setTableData] = useState({
//     Cash: [],
//     CreditCards: [],
//     Investments: [],
//     LoanAccounts: [],
//   });

//   const [showConnectionModal, setShowConnectionModal] = useState(false);
//   const [showAIChat, setShowAIChat] = useState(false);

//   const [boxOrder, setBoxOrder] = useState(() => {
//     const savedOrder = localStorage.getItem("accountsBoxOrder");
//     const parsedOrder = savedOrder ? JSON.parse(savedOrder) : ALL_CATEGORIES;
    
//     // Ensure we always have all 4 categories in the order
//     const completeOrder = [...ALL_CATEGORIES];
    
//     if (savedOrder) {
//       // Reorder based on saved order, but ensure all categories are present
//       const orderedCategories = [];
//       parsedOrder.forEach(category => {
//         if (ALL_CATEGORIES.includes(category)) {
//           orderedCategories.push(category);
//         }
//       });
      
//       // Add any missing categories to the end
//       ALL_CATEGORIES.forEach(category => {
//         if (!orderedCategories.includes(category)) {
//           orderedCategories.push(category);
//         }
//       });
      
//       return orderedCategories;
//     }
    
//     return completeOrder;
//   });
 
//   // Track if refresh is in progress to avoid showing premature errors
//   const [isRefreshInProgress, setIsRefreshInProgress] = useState(false);
//   const [chartType, setChartType] = useState('bar');
//   const [timePeriod, setTimePeriod] = useState('yearly');
//   const [selectedAccount, setSelectedAccount] = useState('all');

//   useEffect(() => {
//     logEvent('AccountsDashboard', 'PageLoad', {});
//     //dispatch(fetchLinkToken());
//     dispatch(fetchAccountDetails()); 
//   }, []);

//   useEffect(() => {
//     if (accounts && accounts.length > 0) {
//       updateTableData(accounts);
//     } else {
//       console.log("Fetched accounts array is empty:", accounts);
//     }
//   }, [accounts, syncingAccounts, deltaAccountsMap]);

//   useEffect(() => {
//     const savedTableOrder = localStorage.getItem("accountsRowOrder");
//     if (savedTableOrder) {
//       const parsed = JSON.parse(savedTableOrder);
//       setTableData((prev) => {
//         const newData = { ...prev };
//         for (const key in parsed) {
//           if (newData[key]) {
//             newData[key] = parsed[key]
//               .map((savedId) => newData[key].find((d) => d.id === savedId))
//               .filter(Boolean);
//           }
//         }
//         return newData;
//       });
//     }
//   }, []);

//   // Monitor refresh status to control error display
//   useEffect(() => {
//     if (refreshAllStatus === 'loading') {
//       setIsRefreshInProgress(true);
//     } else if (refreshAllStatus === 'success' || refreshAllStatus === 'failed') {
//       setIsRefreshInProgress(false);
//       // Clear the refresh status after showing success message briefly
//       if (refreshAllStatus === 'success') {
//         const timer = setTimeout(() => {
//           dispatch(resetError()); // This should also reset refreshAllStatus in your slice
//         }, 3000); // Show success message for 3 seconds
//         return () => clearTimeout(timer);
//       }
//     }
//   }, [refreshAllStatus]);

//   // Clear error after 5 seconds, but not if refresh is in progress
//   useEffect(() => {
//     if (error && !isRefreshInProgress) {
//       const timer = setTimeout(() => {
//         dispatch(resetError());
//       }, 5000);
//       return () => clearTimeout(timer);
//     }
//   }, [error, isRefreshInProgress]);

//   const handleRowDragEnd = (result, category) => {
//     if (!result.destination) return;

//     const draggedAccount = tableData[category][result.source.index];
//     logEvent('AccountsDashboard', 'ReorderAccountsInCategory', {
//       category,
//       accountId: draggedAccount?.id,
//       fromIndex: result.source.index,
//       toIndex: result.destination.index
//     });

//     const updatedRows = Array.from(tableData[category]);
//     const [movedRow] = updatedRows.splice(result.source.index, 1);
//     updatedRows.splice(result.destination.index, 0, movedRow);

//     const newTableData = {
//       ...tableData,
//       [category]: updatedRows,
//     };

//     setTableData(newTableData);

//     const rowOrder = JSON.parse(localStorage.getItem("accountsRowOrder") || "{}");
//     rowOrder[category] = updatedRows.map((row) => row.id);
//     localStorage.setItem("accountsRowOrder", JSON.stringify(rowOrder));
//   };

//   const formatAccounts = (type) =>
//     accounts
//       .filter((account) => account.accountType === type)
//       .map((account, index) => {
//         const accountId = account.id || account.accountId;
//         const deltaAccount = deltaAccountsMap[accountId];
        
//         return {
//           id: accountId || index,
//           institution: account.financialInstName || account.institutionId || "Unknown",
//           accountName: account.accountName,
//           accountNumber: maskAccountNumber(account.accountMask),
//           numericBalance: account.balance || 0,
//           balance: `$${account.balance ? account.balance.toFixed(2) : "0.00"}`,
//           lastSyncTime: account.lastSyncTime || null,
//           lastSyncFormatted: formatLastSyncTime(account.lastSyncTime),
//           monthlyBalances: account.aggregatedBalances || [
//             { month: "Jan", balance: (account.balance || 0) - 50 },
//             { month: "Feb", balance: account.balance || 0 },
//             { month: "Mar", balance: (account.balance || 0) + 75 },
//           ],
//           isSyncing: syncingAccounts.includes(accountId),
//           // Delta data integration
//           deltaData: deltaAccount ? {
//             trend: deltaAccount.trend,
//             percentage: deltaAccount.formattedDelta?.percentage || '0%',
//             amount: deltaAccount.formattedDelta?.amount || '$0.00',
//             hasDelta: true
//           } : {
//             trend: 'neutral',
//             percentage: '0%',
//             amount: '$0.00',
//             hasDelta: false
//           }
//         };
//       });

//   const clearSavedRowOrders = () => {
//     localStorage.removeItem("accountsRowOrder");
//     window.location.reload();
//   };

//   const updateTableData = (accounts) => {
//     const savedRowOrder = JSON.parse(localStorage.getItem("accountsRowOrder") || "{}");

//     const formatted = {
//       Cash: formatAccounts("depository"),
//       CreditCards: formatAccounts("credit"),
//       Investments: formatAccounts("investment"),
//       LoanAccounts: formatAccounts("loan"),
//     };

//     const reordered = {};

//     Object.entries(formatted).forEach(([key, list]) => {
//       const order = savedRowOrder[key];
//       if (order) {
//         const orderedList = [];
//         const idToRow = Object.fromEntries(list.map((row) => [row.id, row]));
//         order.forEach((id) => {
//           if (idToRow[id]) orderedList.push(idToRow[id]);
//         });
//         const remaining = list.filter((row) => !order.includes(row.id));
//         reordered[key] = [...orderedList, ...remaining];
//       } else {
//         reordered[key] = list;
//       }
//     });

//     setTableData(reordered);
//   };

//   const handleSyncAccount = (accountId) => {
//     logEvent('AccountsDashboard', 'SyncAccount', {
//       accountId,
//       accountType: accounts.find(acc => acc.id === accountId || acc.accountId === accountId)?.accountType
//     });

//     setTableData((prevData) => {
//       const newData = { ...prevData };
//       Object.keys(newData).forEach((category) => {
//         newData[category] = newData[category].map((acct) => {
//           if (acct.id === accountId) {
//             return { ...acct, isSyncing: true };
//           }
//           return acct;
//         });
//       });
//       return newData;
//     });

//     dispatch(syncAccount(accountId)).finally(() => {
//       setTableData((prevData) => {
//         const newData = { ...prevData };
//         Object.keys(newData).forEach((category) => {
//           newData[category] = newData[category].map((acct) => {
//             if (acct.id === accountId) {
//               return { ...acct, isSyncing: false };
//             }
//             return acct;
//           });
//         });
//         return newData;
//       });
//     });
//   };

//   const handleRefreshAll = () => {
//     logEvent('AccountsDashboard', 'RefreshAllAccounts', {
//       accountsCount: accounts?.length || 0,
//       linkedAccountsCount: Object.values(tableData).flat().length
//     });
//     // Clear any existing errors before starting refresh
//     dispatch(resetError());
//     setIsRefreshInProgress(true);
//     dispatch(refreshAllAccounts()).then(() => {
//       // Ensure we reset the progress state when the promise resolves
//       setIsRefreshInProgress(false);
//     }).catch(() => {
//       // Also reset on error
//       setIsRefreshInProgress(false);
//     });
//   };

//   const handleDragEnd = (result) => {
//     if (!result.destination) return;
    
//     logEvent('AccountsDashboard', 'ReorderAccountCategories', {
//       fromIndex: result.source.index,
//       toIndex: result.destination.index,
//       movedCategory: boxOrder[result.source.index]
//     });

//     const newOrder = Array.from(boxOrder);
//     const [removed] = newOrder.splice(result.source.index, 1);
//     newOrder.splice(result.destination.index, 0, removed);
//     setBoxOrder(newOrder);
//     localStorage.setItem("accountsBoxOrder", JSON.stringify(newOrder));
//   };

//   const onSuccess = async (publicToken, metadata) => {
//     logEvent('AccountsDashboard', 'PlaidConnectionSuccess', {
//       institutionId: metadata?.institution?.institution_id,
//       institutionName: metadata?.institution?.name,
//       accountsCount: metadata?.accounts?.length
//     });
//     try {
//       await dispatch(exchangePublicToken(publicToken));
//       await dispatch(fetchAccountDetails(1));
//       setShowConnectionModal(false);
//     } catch (err) {
//       logEvent('AccountsDashboard', 'PlaidConnectionError', {
//         error: err.message || 'Unknown error'
//       });
//       console.error("Error linking accounts:", err);
//     }
//   };

//   // Helper function to safely render error messages - exclude refresh-related errors while in progress
//   const renderError = (error) => {
//     if (!error) return null;
    
//     // Don't show refresh-related errors while refresh is in progress
//     if (isRefreshInProgress) {
//       return null;
//     }
    
//     // Don't show certain sync-related errors that are expected during long operations
//     const errorMessage = typeof error === 'string' ? error : (error?.message || error?.error || 'An error occurred');
//     const isRefreshError = errorMessage.toLowerCase().includes('refresh') || 
//                           errorMessage.toLowerCase().includes('synchronization') ||
//                           errorMessage.toLowerCase().includes('sync');
    
//     // Skip showing refresh/sync errors if refresh is in progress or recently completed
//     if (isRefreshError && (isRefreshInProgress || refreshAllStatus === 'loading')) {
//       return null;
//     }
    
//     return (
//       <div className={`border rounded-xl px-6 py-4 mb-6 flex items-center shadow-lg animate-slide-down`}
//         style={{
//           backgroundColor: darkMode ? `${currentTheme.colors.primary}10` : `${currentTheme.colors.primary}05`,
//           borderColor: `${currentTheme.colors.primary}30`,
//           color: currentTheme.colors.secondary
//         }}>
//         <div className={`p-2 rounded-full mr-4`} style={{ backgroundColor: `${currentTheme.colors.primary}20` }}>
//           <FontAwesomeIcon icon={faExclamationTriangle} className="text-lg" />
//         </div>
//         <span className="flex-1 font-medium">{errorMessage}</span>
//         <button 
//           onClick={() => {
//             logEvent('AccountsDashboard', 'DismissError', {
//               errorMessage: typeof error === 'string' ? error : (error?.message || error?.error || 'Unknown error')
//             });
//             dispatch(resetError());
//           }}
//           className={`ml-4 p-2 rounded-full transition-colors duration-200`}
//           style={{ color: currentTheme.colors.primary }}
//           onMouseEnter={(e) => e.target.style.backgroundColor = `${currentTheme.colors.primary}20`}
//           onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
//         >
//           <FontAwesomeIcon icon={faTimes} />
//         </button>
//       </div>
//     );
//   };

//   // Success message for refresh all
//   const renderRefreshSuccessMessage = () => {
//     if (refreshAllStatus === 'success' && !isRefreshInProgress) {
//       return (
//         <div className={`border rounded-xl px-6 py-4 mb-6 flex items-center shadow-lg animate-slide-down`}
//           style={{
//             backgroundColor: darkMode ? `${currentTheme.colors.success}20` : `${currentTheme.colors.success}10`,
//             borderColor: `${currentTheme.colors.success}30`,
//             color: currentTheme.colors.success
//           }}>
//           <div className={`p-2 rounded-full mr-4`} style={{ backgroundColor: `${currentTheme.colors.success}20` }}>
//             <FontAwesomeIcon icon={faSync} className="text-lg" />
//           </div>
//           <span className="flex-1 font-medium">All accounts refreshed successfully!</span>
//           <button 
//             onClick={() => dispatch(resetError())}
//             className={`ml-4 p-2 rounded-full transition-colors duration-200`}
//             style={{ color: currentTheme.colors.success }}
//             onMouseEnter={(e) => e.target.style.backgroundColor = `${currentTheme.colors.success}20`}
//             onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
//           >
//             <FontAwesomeIcon icon={faTimes} />
//           </button>
//         </div>
//       );
//     }
//     return null;
//   };

//   // Loading message for refresh all
//   const renderRefreshLoadingMessage = () => {
//     if (refreshAllStatus === 'loading' || isRefreshInProgress) {
//       return (
//         <div className={`border rounded-xl px-6 py-4 mb-6 flex items-center shadow-lg animate-pulse`}
//           style={{
//             backgroundColor: darkMode ? `${currentTheme.colors.primary}10` : `${currentTheme.colors.primary}05`,
//             borderColor: `${currentTheme.colors.primary}30`,
//             color: currentTheme.colors.secondary
//           }}>
//           <div className={`p-2 rounded-full mr-4`} style={{ backgroundColor: `${currentTheme.colors.primary}20` }}>
//             <FontAwesomeIcon icon={faSync} className="text-lg animate-spin" />
//           </div>
//           <span className="flex-1 font-medium">Refreshing all accounts... This may take a few moments.</span>
//         </div>
//       );
//     }
//     return null;
//   };

//   // Enhanced table rendering with modern design
//   const renderTable = (boxId, dragHandleProps) => {
//     const data = tableData[boxId] || [];
//     const categoryName = formatCategoryName(boxId);
//     const categoryIcon = getCategoryIcon(boxId);
    
//     // Create mock data when no real data exists
//     const displayData = data.length === 0 ? [{
//       id: `mock-${boxId}`,
//       institution: "No accounts connected",
//       accountNumber: "••••••0000",
//       numericBalance: 0,
//       balance: "$0.00",
//       lastSyncTime: null,
//       lastSyncFormatted: "Never synced",
//       monthlyBalances: [
//         { month: "Jan", balance: 0 },
//         { month: "Feb", balance: 0 },
//         { month: "Mar", balance: 0 }
//       ],
//       isSyncing: false,
//       isMock: true,
//       deltaData: {
//         trend: 'neutral',
//         percentage: '0%',
//         amount: '$0.00',
//         hasDelta: false
//       }
//     }] : data;

//     const totalBalance = data
//       .reduce((sum, row) => sum + (parseFloat(row.numericBalance) || 0), 0)
//       .toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });

//     const getTableCardStyle = () => {
//       const style = {
//         backgroundColor: colors.cardBg,
//         borderColor: colors.border
//       };
      
//       // Border radius
//       if (currentTheme.layout?.borderRadius === 'square') {
//         style.borderRadius = '8px';
//       } else if (currentTheme.layout?.borderRadius === 'pill') {
//         style.borderRadius = '24px';
//       } else {
//         style.borderRadius = '16px';
//       }
      
//       // Shadows
//       if (currentTheme.layout?.shadows === false) {
//         style.boxShadow = 'none';
//       }
      
//       return style;
//     };

//     const getTableClasses = () => {
//       let classes = `border overflow-hidden group transition-all duration-300`;
      
//       // Card style
//       if (currentTheme.layout?.cardStyle === 'flat') {
//         classes += ` shadow-none`;
//       } else if (currentTheme.layout?.cardStyle === 'outlined') {
//         classes += ` shadow-none border-2`;
//       } else {
//         classes += ` shadow-xl`;
//       }
      
//       // Hover effects
//       if (currentTheme.layout?.animations !== false) {
//         classes += ` `;
//       }
      
//       return classes;
//     };

//     return (
//       <div className={getTableClasses()} style={getTableCardStyle()}>
//         {/* Enhanced Header */}
//         <div className={`px-4 py-3 border-b`}
//           style={{ 
//             background: darkMode 
//               ? `linear-gradient(to right, ${currentTheme.colors.darkCardBg}, ${currentTheme.colors.darkCardBg})`
//               : `linear-gradient(to right, ${currentTheme.colors.primary}08, ${currentTheme.colors.accent}05)`,
//             borderColor: colors.border
//           }}>
//           <div className="flex items-center justify-between">
//             <div className="flex items-center space-x-3">
//               <div
//                 {...dragHandleProps}
//                 className="opacity-0 group-hover:opacity-100 transition-all duration-200 p-1 rounded-lg hover:bg-white/10"
//               >
//                 <DragHandleIcon darkMode={darkMode} />
//               </div>
//               <div className={`p-2 rounded-xl shadow-lg border`} 
//                 style={{ 
//                   backgroundColor: darkMode ? `${currentTheme.colors.darkCardBg}80` : `${currentTheme.colors.cardBg}80`,
//                   borderColor: colors.border
//                 }}>
//                 <FontAwesomeIcon icon={categoryIcon} className={`text-xl`} style={{ color: currentTheme.colors.primary }} />
//               </div>
//               <div>
//                 <h3 className={`text-lg font-bold`} style={{ color: colors.text }}>{categoryName}</h3>
//                 <p className={`text-xs ${colors.neutral}`}>{data.length} account{data.length !== 1 ? 's' : ''}</p>
//               </div>
//             </div>
//             <div className="text-right">
//               <div className={`text-xl font-bold`} style={{ color: colors.text }}>${totalBalance}</div>
//               <div className={`text-xs ${colors.neutral}`}>Total Balance</div>
//             </div>
//           </div>
//         </div>

//         {/* Enhanced Table Body */}
//         <DragDropContext onDragEnd={(result) => handleRowDragEnd(result, boxId)}>
//           <Droppable droppableId={`rows-${boxId}`}>
//             {(provided) => (
//               <div
//                 ref={provided.innerRef}
//                 {...provided.droppableProps}
//                 className="divide-y divide-slate-200/20"
//               >
//                 {displayData.map((row, index) => {
//                   const isPositive = row.numericBalance >= 0;
//                   const isMockRow = row.isMock;
//                   const deltaData = row.deltaData || {};
                  
//                   return (
//                     <Draggable key={row.id} draggableId={String(row.id)} index={index} isDragDisabled={isMockRow}>
//                       {(provided, snapshot) => (
//                         <div
//                           ref={provided.innerRef}
//                           {...provided.draggableProps}
//                           {...(isMockRow ? {} : provided.dragHandleProps)}
//                           className={`
//                             px-4 py-3 transition-all duration-200
//                             ${isMockRow 
//                               ? (darkMode ? 'bg-slate-800/30 text-slate-500' : 'bg-slate-50/50 text-slate-400') 
//                               : ''
//                             }
//                             ${snapshot.isDragging ? "shadow-2xl z-10 rounded-xl" : ""}
//                             ${!isPositive && !isMockRow ? "text-rose-500" : ""}
//                           `}
//                         >
//                           <div className="grid grid-cols-12 gap-3 items-center">
//                             {/* Institution & Account Info */}
//                             <div className="col-span-4">
//                               <div className="flex items-center space-x-3">
//                                 <div className={`p-3 rounded-xl shadow-lg border`}
//                                   style={{ 
//                                     backgroundColor: colors.cardBg,
//                                     borderColor: colors.border
//                                   }}>
//                                   <BankIcon
//                                     institutionName={row.institution}
//                                     className="w-12 h-12"
//                                   />
//                                 </div>
//                                 <div>
//                                   <div className={`font-semibold text-sm`} style={{ color: colors.text }}>{row.institution}</div>
//                                   <div className={`text-xs ${colors.neutral}`}>{row.accountName}</div>
//                                   <div className={`text-xs ${colors.neutral} font-mono`}>{row.accountNumber}</div>
//                                 </div>
//                               </div>
//                             </div>

//                             {/* Balance & Delta */}
//                             <div className="col-span-3">
//                               <div className="space-y-1">
//                                 <div className={`text-base font-bold`} style={{ color: colors.text }}>
//                                   {row.balance}
//                                 </div>
//                                 {!isMockRow && deltaData.hasDelta && (
//                                   <div className="flex items-center space-x-1">
//                                     <TrendIcon 
//                                       trend={deltaData.trend} 
//                                       className={`text-xs`}
//                                       style={{ 
//                                         color: deltaData.trend === 'increase' ? currentTheme.colors.primary : 
//                                                deltaData.trend === 'decrease' ? '#ef4444' : // Red for decrease
//                                                colors.neutral 
//                                       }}
//                                     />
//                                     <div className="flex items-center space-x-1">
//                                       <span className={`text-xs font-semibold`}
//                                         style={{ 
//                                           color: deltaData.trend === 'increase' ? currentTheme.colors.primary : 
//                                                  deltaData.trend === 'decrease' ? '#ef4444' : // Red for decrease
//                                                  colors.neutral 
//                                         }}>
//                                         {deltaData.percentage}
//                                       </span>
//                                       <span className={`text-xs`}
//                                         style={{ 
//                                           color: deltaData.trend === 'increase' ? currentTheme.colors.primary : 
//                                                  deltaData.trend === 'decrease' ? '#ef4444' : // Red for decrease
//                                                  colors.neutral 
//                                         }}>
//                                         {deltaData.amount}
//                                       </span>
//                                     </div>
//                                   </div>
//                                 )}
//                               </div>
//                             </div>

//                             {/* Chart */}
//                       <div className="col-span-3">
//                       {!isMockRow ? (
//                         <AccountMiniChart 
//                           accountId={row.accountId || row.id} // Pass the account ID
//                           data={row.monthlyBalances}
//                           darkMode={darkMode}
//                           currentTheme={currentTheme}
//                         />
//                       ) : (
//                         <div className={`h-16 rounded-lg flex items-center justify-center`}
//                           style={{ backgroundColor: darkMode ? '#********' : '#e2e8f0' }}>
//                           <span className={`text-xs ${colors.neutral}`}>No data</span>
//                         </div>
//                       )}
//                     </div>

//                             {/* Sync & Last Update */}
//                             <div className="col-span-2">
//                               <div className="flex flex-col items-center space-y-2">
//                                 {!isMockRow && (
//                                   <button
//                                     onClick={() => handleSyncAccount(row.id)}
//                                     className={`p-1.5 rounded-lg transition-all duration-200 ${
//                                       row.isSyncing 
//                                         ? 'bg-opacity-20 text-current' 
//                                         : (darkMode 
//                                           ? 'bg-slate-700/50 text-slate-400 hover:bg-opacity-20 hover:text-current' 
//                                           : 'bg-slate-100 text-slate-500 hover:bg-opacity-20 hover:text-current')
//                                     }`}
//                                     style={row.isSyncing 
//                                       ? { backgroundColor: `${currentTheme.colors.primary}20`, color: currentTheme.colors.primary }
//                                       : {}
//                                     }
//                                     onMouseEnter={(e) => {
//                                       if (!row.isSyncing) {
//                                         e.target.style.backgroundColor = `${currentTheme.colors.primary}20`;
//                                         e.target.style.color = currentTheme.colors.primary;
//                                       }
//                                     }}
//                                     onMouseLeave={(e) => {
//                                       if (!row.isSyncing) {
//                                         e.target.style.backgroundColor = '';
//                                         e.target.style.color = '';
//                                       }
//                                     }}
//                                     title="Sync Account"
//                                     disabled={row.isSyncing}
//                                   >
//                                     <FontAwesomeIcon
//                                       icon={faSync}
//                                       className={`text-sm ${row.isSyncing ? "animate-spin" : ""}`}
//                                     />
//                                   </button>
//                                 )}
//                                 <div className="text-center">
//                                   <div className="flex items-center justify-center space-x-1 mb-1">
//                                     <FontAwesomeIcon 
//                                       icon={faClock} 
//                                       className={`text-xs ${colors.neutral}`}
//                                     />
//                                     <div className={`text-xs ${colors.neutral} leading-tight`}>
//                                       {row.lastSyncFormatted}
//                                     </div>
//                                   </div>
//                                 </div>
//                               </div>
//                             </div>
//                           </div>
//                         </div>
//                       )}
//                     </Draggable>
//                   );
//                 })}
//                 {provided.placeholder}
//               </div>
//             )}
//           </Droppable>
//         </DragDropContext>
//       </div>
//     );
//   };

//   return (
//     <div className={`min-h-screen w-full font-inter transition-all duration-300`}
//       style={{ 
//         backgroundColor: colors.bg,
//         fontFamily: currentTheme.font.value
//       }}>
//       <div className={`px-4 sm:px-6 lg:px-8 py-8 w-full`}
//         style={{ 
//           maxWidth: currentTheme.layout?.maxWidth || 'none',
//           margin: currentTheme.layout?.centered ? '0 auto' : '0'
//         }}>
//         {/* Enhanced Header */}
//         <div className={`flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 space-y-4 lg:space-y-0 ${
//           currentTheme.layout?.spacing === 'compact' ? 'mb-4' : 
//           currentTheme.layout?.spacing === 'spacious' ? 'mb-12' : 'mb-8'
//         }`}>
//           <div className="flex items-center space-x-4">
//             <div className={`p-3 rounded-xl shadow-lg border`}
//               style={{ 
//                 backgroundColor: darkMode ? `${currentTheme.colors.primary}20` : `${currentTheme.colors.primary}10`,
//                 borderColor: colors.border
//               }}>
//               <FontAwesomeIcon icon={faRobot} className={`text-2xl`} style={{ color: currentTheme.colors.primary }} />
//             </div>
//             <h1 className={`text-4xl font-bold bg-gradient-to-r bg-clip-text text-transparent`}
//               style={{ 
//                 backgroundImage: `linear-gradient(to right, ${currentTheme.colors.primary}, ${currentTheme.colors.secondary})`,
//                 fontFamily: currentTheme.font.value
//               }}>
//               Accounts
//             </h1>
//           </div>
          
//           <div className={`flex space-x-4 ${
//             currentTheme.layout?.spacing === 'compact' ? 'space-x-2' : 
//             currentTheme.layout?.spacing === 'spacious' ? 'space-x-6' : 'space-x-4'
//           }`}>
//             {/* AI Chat Button */}
//             <button
//               className={`hover:shadow-lg text-white py-3 px-4 flex items-center space-x-2 transition-all duration-200 font-semibold shadow-md ${
//                 currentTheme.layout?.animations === false ? '' : 'hover:scale-105'
//               } ${showAIChat ? 'ring-2 ring-white/30' : ''}`}
//               style={{ 
//                 backgroundColor: currentTheme.colors.accent,
//                 borderRadius: currentTheme.layout?.borderRadius === 'square' ? '6px' : 
//                            currentTheme.layout?.borderRadius === 'pill' ? '50px' : '12px',
//                 boxShadow: currentTheme.layout?.shadows === false ? 'none' : ''
//               }}
//               onClick={() => setShowAIChat(!showAIChat)}
//               title="AI Assistant"
//             >
//               <FontAwesomeIcon icon={faRobot} />
//               <span>AI Chat</span>
//             </button>
            
//             {/* Design Palette Dropdown */}
//             <DesignPaletteDropdown 
//               darkMode={darkMode}
//               onThemeChange={handleThemeChange}
//             />
            
//             <button
//               className={`hover:shadow-lg text-black py-3 px-6 flex items-center space-x-2 transition-all duration-200 font-semibold shadow-md ${
//                 currentTheme.layout?.animations === false ? '' : 'hover:scale-105'
//               }`}
//               style={{ 
//                 backgroundColor: colors.primary,
//                 borderRadius: currentTheme.layout?.borderRadius === 'square' ? '6px' : 
//                            currentTheme.layout?.borderRadius === 'pill' ? '50px' : '12px',
//                 boxShadow: currentTheme.layout?.shadows === false ? 'none' : ''
//               }}
//               onClick={() => setShowConnectionModal(true)}
//             >
//               <FontAwesomeIcon icon={faPlus} />
//               <span>Add Account</span>
//             </button>
//             <button
//               className={`hover:shadow-lg text-black py-3 px-6 flex items-center space-x-2 transition-all duration-200 font-semibold shadow-md disabled:opacity-50 disabled:cursor-not-allowed ${
//                 currentTheme.layout?.animations === false ? '' : 'hover:scale-105'
//               }`}
//               style={{ 
//                 backgroundColor: colors.secondary,
//                 borderRadius: currentTheme.layout?.borderRadius === 'square' ? '6px' : 
//                            currentTheme.layout?.borderRadius === 'pill' ? '50px' : '12px',
//                 boxShadow: currentTheme.layout?.shadows === false ? 'none' : ''
//               }}
//               onClick={handleRefreshAll}
//               disabled={!isLinked || refreshAllStatus === 'loading' || isRefreshInProgress}
//             >
//               <FontAwesomeIcon 
//                 icon={faRedo} 
//                 className={(refreshAllStatus === 'loading' || isRefreshInProgress) ? "animate-spin" : ""} 
//               />
//               <span>{(refreshAllStatus === 'loading' || isRefreshInProgress) ? "Syncing..." : "Refresh All"}</span>
//             </button>
//           </div>
//         </div>

//         {/* Status Messages */}
//         {renderRefreshLoadingMessage()}
//         {renderError(error)}
//         {renderRefreshSuccessMessage()}

//         {/* Stat Cards */}
//         {isLinked && (
//             <StatCardGroupView darkMode={darkMode} currentTheme={currentTheme} accounts={accounts} />
//         )}

//         {/* Chart Controls & Main Chart */}
//         {isLinked && (
//             <PortfolioChartControlView 
//                 darkMode = {darkMode}
//                 currentTheme = {currentTheme}
//                 accounts = {accounts}
//                 selectedAccount = {selectedAccount}
//                 setChartType = {setChartType}
//                 selectedChartType = {selectedChartType}
//                 chartType = {chartType}
//                 handleChartTypeChange = {handleChartTypeChange}
//                 selectedTimePeriod = {selectedTimePeriod}
//                 handleTimePeriodChange = {handleTimePeriodChange}
//                 timePeriod = {timePeriod}
//             />
//         )}
        
//         {/* Enhanced Connection Modal */}
//         {showConnectionModal && (
//           <AccountConnectionProviderModal 
//                 darkMode = {darkMode}
//                 currentTheme = {currentTheme} 
//                 setShowConnectionModal = {setShowConnectionModal}  />
//         )}

//         {/* AI Chat Window */}
//         {showAIChat && (
//             <AccountAIChat 
//                 darkMode = {darkMode} 
//                 currentTheme = {currentTheme} 
//                 setShowAIChat = {setShowAIChat}  
//                 showAIChat  = {showAIChat} />
//         )}

//         {/* Enhanced No Accounts State */}
//         {!isLinked && !showConnectionModal && (
//           <div className={`text-center py-20 border-2 border-dashed rounded-2xl p-12 animate-fade-in`}
//             style={{ 
//               backgroundColor: colors.cardBg,
//               borderColor: colors.border
//             }}>
//             <div className={`inline-flex p-6 rounded-full mb-6`}
//               style={{ backgroundColor: darkMode ? colors.cardBg : `${currentTheme.colors.primary}10` }}>
//               <FontAwesomeIcon icon={faUniversity} className={`text-6xl`} style={{ color: currentTheme.colors.primary }} />
//             </div>
//             <h3 className={`text-2xl font-bold mb-4`} style={{ color: colors.text }}>No Accounts Connected</h3>
//             <p className={`text-lg ${colors.neutral} mb-8 max-w-md mx-auto`}>
//               Connect your first financial account to start tracking your finances and see insights about your money.
//             </p>
//             <button
//               className={`hover:shadow-lg text-black py-4 px-8 rounded-xl transition-all duration-200 font-semibold inline-flex items-center space-x-2`}
//               style={{ backgroundColor: colors.primary }}
//               onClick={() => setShowConnectionModal(true)}
//             >
//               <FontAwesomeIcon icon={faPlus} />
//               <span>Connect Your First Account</span>
//             </button>
//           </div>
//         )}

//         {/* Enhanced Account Tables */}
//         {isLinked && (
//           <DragDropContext onDragEnd={handleDragEnd}>
//             <Droppable droppableId="categories">
//               {(provided) => (
//                 <div
//                   className={`${
//                     currentTheme.layout?.spacing === 'compact' ? 'space-y-4' :
//                     currentTheme.layout?.spacing === 'spacious' ? 'space-y-12' : 'space-y-8'
//                   }`}
//                   {...provided.droppableProps}
//                   ref={provided.innerRef}
//                 >
//                   {boxOrder.map((boxId, index) => (
//                     <Draggable draggableId={boxId} index={index} key={boxId}>
//                       {(provided, snapshot) => (
//                         <div
//                           ref={provided.innerRef}
//                           {...provided.draggableProps}
//                           className={`transition-all duration-300 ${
//                             snapshot.isDragging ? "z-50 rotate-2" : ""
//                           } ${
//                             currentTheme.layout?.animations === false ? '' : ''
//                           }`}
//                         >
//                           {renderTable(boxId, provided.dragHandleProps)}
//                         </div>
//                       )}
//                     </Draggable>
//                   ))}
//                   {provided.placeholder}
//                 </div>
//               )}
//             </Droppable>
//           </DragDropContext>
//         )}
//       </div>

//       {/* Enhanced Styles */}
//       <style jsx>{`
//         @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
//         @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700;800;900&display=swap');
//         @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');
//         @import url('https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400;500;600;700;800;900&display=swap');
//         @import url('https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;500;600;700;800;900&display=swap');
//         @import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400;500;600;700;800;900&display=swap');
//         @import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700;800&display=swap');
//         @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800;900&display=swap');
        
//         .font-inter {
//           font-family: ${currentTheme.font.value};
//         }

//         @keyframes fade-in {
//           from {
//             opacity: 0;
//           }
//           to {
//             opacity: 1;
//           }
//         }
        
//         @keyframes slide-down {
//           from {
//             opacity: 0;
//             transform: translateY(-20px);
//           }
//           to {
//             opacity: 1;
//             transform: translateY(0);
//           }
//         }

//         @keyframes slide-up {
//           from {
//             opacity: 0;
//             transform: translateY(20px);
//           }
//           to {
//             opacity: 1;
//             transform: translateY(0);
//           }
//         }
        
//         .animate-fade-in {
//           animation: fade-in 0.5s ease-out;
//         }
        
//         .animate-slide-down {
//           animation: slide-down 0.3s ease-out;
//         }

//         .animate-slide-up {
//           animation: slide-up 0.3s ease-out;
//         }
        
//         /* Improved scrollbar */
//         ::-webkit-scrollbar {
//           width: 8px;
//         }
        
//         ::-webkit-scrollbar-track {
//           background: transparent;
//         }
        
//         ::-webkit-scrollbar-thumb {
//           background: rgba(148, 163, 184, 0.3);
//           border-radius: 10px;
//         }
        
//         ::-webkit-scrollbar-thumb:hover {
//           background: rgba(148, 163, 184, 0.5);
//         }
        
//         /* Enhanced hover effects */
//         .group:hover .drag-handle {
//           opacity: 1;
//         }
        
//         .drag-handle {
//           opacity: 0;
//           transition: opacity 0.2s ease;
//         }

//         /* Better responsive behavior */
//         @media (max-width: 768px) {
//           .grid-cols-12 {
//             grid-template-columns: 1fr;
//             gap: 1rem;
//           }
          
//           .col-span-4,
//           .col-span-3,
//           .col-span-2 {
//             grid-column: span 1;
//           }
//         }

//         /* Dynamic theme-based styles */
//         .theme-gradient {
//           background: linear-gradient(135deg, ${currentTheme.colors.primary}, ${currentTheme.colors.secondary});
//         }
        
//         .theme-shadow {
//           box-shadow: 0 10px 25px ${currentTheme.colors.primary}20;
//         }
        
//         /* Smooth theme transitions */
//         * {
//           transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
//         }

//         /* AI Chat Window Animation */
//         .translate-x-0 {
//           transform: translateX(0);
//         }
        
//         .translate-x-full {
//           transform: translateX(100%);
//         }
        
//         /* Chat window scrollbar */
//         .overflow-y-auto::-webkit-scrollbar {
//           width: 6px;
//         }
        
//         .overflow-y-auto::-webkit-scrollbar-track {
//           background: transparent;
//         }
        
//         .overflow-y-auto::-webkit-scrollbar-thumb {
//           background: rgba(148, 163, 184, 0.3);
//           border-radius: 10px;
//         }
        
//         .overflow-y-auto::-webkit-scrollbar-thumb:hover {
//           background: rgba(148, 163, 184, 0.5);
//         }
//       `}</style>
//     </div>
//   );
// };

// export default AccountsDashboard;
import React, { useState, useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { PlaidLink } from "react-plaid-link";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faLink,
  faSync,
  faPlus,
  faRedo,
  faTimes,
  faClock,
  faUniversity,
  faExclamationTriangle, 
  faArrowUp, 
  faArrowDown, 
  faEquals,  
  faSpinner,
  faChartLine,
  faCreditCard,
  faCoins,
  faLandmark,
  faSackDollar,
  faRobot,
  faExchangeAlt
} from "@fortawesome/free-solid-svg-icons";
import AccountChart from "./AccountChart";
import BankIcon from "./BankIcon";
import AccountMiniChart from "./AccountMiniChart";
import {
  fetchLinkToken,
  fetchAccountDetails,
  exchangePublicToken,
  refreshAllAccounts,
  syncAccount,
  connectMx,
  connectStripe,
  connectFinicity,
  resetError
} from "../../../../../../logic/redux/accountsDashboardSlice";
import { fetchAccountTypeDeltaData, selectSelectedTimePeriod } from '../../../../../../logic/redux/deltaSlice';
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { getCurrentUserId } from "../../../../utils/AuthUtil";
import { logEvent } from '../../../../utils/EventLogger';
import { fetchDeltaData } from '../../../../../../logic/redux/deltaSlice';
import { 
  selectDeltaData, 
  selectDeltaLoading, 
  selectDeltaError,
  selectDeltaAccounts,
  selectDeltaSummary ,
  selectDeltaAccountTypes
  
} from '../../../../../../logic/redux/deltaSlice';
import {  setChartType,
  setTimePeriod,
  fetchAccountData,
  selectChartType,
  selectTimePeriod, } from '../../../../../../logic/redux/accountChartSlice';
import { formatLastSyncTime, 
         formatCategoryName,
         getCategoryIcon,
         maskAccountNumber,
         ALL_CATEGORIES
        } from './AccountUtil';

import DesignPaletteDropdown from "./DesignPlatteDropdown";
import PortfolioChartControlView from "./PortfolioChartControlView";
import StatCardGroupView from "./StatCardGroupView";
import AccountConnectionProviderModal from "./AccountConnectionProviderModal";
import AccountAIChat from "./AccountAIChat";
import PaymentLoader from "../../../load/PaymentLoader"
import {formatAmountWithCommas}from '../../../../../../logic/redux/accountChartSlice';

// Enhanced drag handle with better visual design
const DragHandleIcon = ({ darkMode }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={`cursor-grab transition-all duration-200 hover:scale-110 ${
      darkMode ? 'fill-gray-400 hover:fill-gray-300' : 'fill-gray-500 hover:fill-gray-700'
    }`}
  >
    <circle cx="6" cy="5" r="2" />
    <circle cx="6" cy="10" r="2" />
    <circle cx="6" cy="15" r="2" />
    <circle cx="14" cy="5" r="2" />
    <circle cx="14" cy="10" r="2" />
    <circle cx="14" cy="15" r="2" />
  </svg>
);

const AccountsDashboard = ({ darkMode }) => {
  const userId = getCurrentUserId();
  const dispatch = useDispatch();
  
  // FIXED: Move all useSelector hooks to the top, before any conditional logic
  const selectedChartType = useSelector(selectChartType);
  const selectedTimePeriod = useSelector(selectTimePeriod);
  const deltaAccountTypes = useSelector(selectDeltaAccountTypes);
  const selecteTimePeriod = useSelector(selectSelectedTimePeriod);
  
  // Get all account state at the top
  const { 
    linkToken, 
    accounts, 
    isLinked, 
    isLoading,  
    hasLoadedOnce,// FIXED: This is now declared before it's used
    balanceHistory,
    error,
    syncingAccounts = [],
    refreshAllStatus
  } = useSelector((state) => state.accounts);
  
  // Get delta data
  const deltaData = useSelector(selectDeltaData);
  const deltaLoading = useSelector(selectDeltaLoading);
  const deltaError = useSelector(selectDeltaError);
  const deltaAccounts = useSelector(selectDeltaAccounts);
  const deltaSummary = useSelector(selectDeltaSummary);


  
   useEffect(() => {
        // Fetch account type delta data when component mounts or time period changes
        const timePeriod = selecteTimePeriod || 'one-month'; // Default to one month
        
        dispatch(fetchAccountTypeDeltaData({ timePeriod }));
    }, [dispatch, selecteTimePeriod]);

  // NEW: Theme state management with granular design options
  const [currentTheme, setCurrentTheme] = useState({
    theme: 'default',
    font: { name: 'Inter', value: "'Inter', sans-serif" },
    colors: {
      primary: "#8BC34A",
      secondary: "#7CB342",
      accent: "#9CCC65",
      success: "#4CAF50",
      background: "#f8fffe",
      darkBackground: "#0f172a",
      cardBg: "#ffffff",
      darkCardBg: "#1e293b",
      text: "#334155",
      darkText: "#f1f5f9",
      border: "#e2e8f0",
      darkBorder: "#475569"
    },
    layout: {
      maxWidth: 'none', // 'none', '1200px', '1400px', '1600px'
      centered: false,
      spacing: 'normal', // 'compact', 'normal', 'spacious'
      gridDensity: 'normal', // 'compact', 'normal', 'spacious'
      cardStyle: 'elevated', // 'flat', 'elevated', 'outlined'
      borderRadius: 'rounded', // 'square', 'rounded', 'pill'
      buttonStyle: 'rounded', // 'square', 'rounded', 'pill'
      animations: true,
      shadows: true
    }
  });

  // NEW: Theme change handler (enhanced to handle all layout changes)
  const handleThemeChange = (newTheme) => {
    setCurrentTheme(newTheme);
    // Force a re-render by updating the key state
    setForceUpdate(prev => prev + 1);
  };

  // Add a force update state to ensure components re-render with new theme
  const [forceUpdate, setForceUpdate] = useState(0);
  
  // Get delta data

  const handleChartTypeChange = (e) => {
    dispatch(setChartType(e.target.value));
  };

  const handleTimePeriodChange = (e) => {
    dispatch(setTimePeriod(e.target.value));
  };
  useEffect(() => {
    dispatch(fetchAccountData()); // Optionally add payload if needed
  }, [selectedChartType, selectedTimePeriod, dispatch]);

  // Map chart time periods to delta time periods
  const mapTimePeriodForDelta = (chartTimePeriod) => {
    const mapping = {
      'one-month': 'one-month',
      'three-month': 'three-month',
      'half-year': 'half-year',
      'yearly': 'yearly',
      'ytd': 'ytd',
      'quarterly-aggregate': 'quarterly-rolling',
      'quarterly-rolling': 'quarterly-rolling'
    };
    
    return mapping[chartTimePeriod] || 'three-month';
  };

  // Fetch delta data when time period changes
  useEffect(() => {
    if (selectedTimePeriod) {
      const deltaTimePeriod = mapTimePeriodForDelta(selectedTimePeriod);
      dispatch(fetchDeltaData({ timePeriod: deltaTimePeriod }));
    }
  }, [dispatch, selectedTimePeriod]);
// Updated processAccountTypeDeltaData function to handle the actual API response structure
const processAccountTypeDeltaData = (responseData, timePeriod) => {
  if (!responseData) {
    return {
      comparisonType: timePeriod,
      accounts: [],
      accountTypes: [],
      metadata: null,
      summary: {
        totalCurrentBalance: 0,
        totalPastBalance: 0,
        totalDeltaAmount: 0,
        totalDeltaPercentage: 0,
        overallTrend: 'neutral'
      }
    };
  }

  let accountTypes = [];
  let metadata = {
    comparisonType: responseData.comparisonType || timePeriod,
    currentPeriodDate: responseData.currentPeriodDate || '',
    pastPeriodDate: responseData.pastPeriodDate || '',
    description: responseData.description || ''
  };

  // Handle quarterly rolling data structure for account types
  if (timePeriod === 'quarterly-rolling' && responseData.quarters) {
    accountTypes = processQuarterlyRollingAccountTypeDeltas(responseData.quarters);
    metadata.quarterDetails = responseData.quarterDetails || {};
  } 
  // Handle YTD data structure for account types
  else if (timePeriod === 'ytd') {
    accountTypes = responseData.accountTypes ? responseData.accountTypes.map(accountType => ({
      accountType: accountType.accountType || accountType.account_type,
      accountCount: Number(accountType.accountCount || accountType.account_count || 0),
      // Map the actual API response field names
      currentBalance: Number(accountType.totalCurrentBalance || accountType.currentBalance || accountType.current_balance || 0),
      pastBalance: Number(accountType.totalPastBalance || accountType.pastBalance || accountType.past_balance || 0),
      deltaAmount: Number(accountType.totalDeltaAmount || accountType.deltaAmount || accountType.delta_amount || 0),
      deltaPercentage: Number(accountType.deltaPercentage || accountType.delta_percentage || 0),
      // Additional fields from your API response
      accountCategories: accountType.accountCategories || [],
      accountSubtypes: accountType.accountSubtypes || [],
      currentPeriodDate: accountType.currentPeriodDate || '',
      pastPeriodDate: accountType.pastPeriodDate || '',
      // Computed fields
      trend: determineTrend(accountType.totalDeltaAmount || accountType.deltaAmount || accountType.delta_amount || 0),
      formattedDelta: formatDeltaDisplay(
        accountType.totalDeltaAmount || accountType.deltaAmount || accountType.delta_amount || 0,
        accountType.deltaPercentage || accountType.delta_percentage || 0
      )
    })) : [];
    
    metadata.ytdDays = responseData.ytdDays || 0;
  } 
  // Handle regular period deltas for account types
  else if (responseData.accountTypes) {
    accountTypes = responseData.accountTypes.map(accountType => ({
      accountType: accountType.accountType || accountType.account_type,
      accountCount: Number(accountType.accountCount || accountType.account_count || 0),
      // Map the actual API response field names
      currentBalance: Number(accountType.totalCurrentBalance || accountType.currentBalance || accountType.current_balance || 0),
      pastBalance: Number(accountType.totalPastBalance || accountType.pastBalance || accountType.past_balance || 0),
      deltaAmount: Number(accountType.totalDeltaAmount || accountType.deltaAmount || accountType.delta_amount || 0),
      deltaPercentage: Number(accountType.deltaPercentage || accountType.delta_percentage || 0),
      // Additional fields from your API response
      accountCategories: accountType.accountCategories || [],
      accountSubtypes: accountType.accountSubtypes || [],
      currentPeriodDate: accountType.currentPeriodDate || '',
      pastPeriodDate: accountType.pastPeriodDate || '',
      // Computed fields
      trend: determineTrend(accountType.totalDeltaAmount || accountType.deltaAmount || accountType.delta_amount || 0),
      formattedDelta: formatDeltaDisplay(
        accountType.totalDeltaAmount || accountType.deltaAmount || accountType.delta_amount || 0,
        accountType.deltaPercentage || accountType.delta_percentage || 0
      )
    }));
  }

  const summary = calculateAccountTypeSummary(accountTypes, timePeriod);

  return {
    comparisonType: metadata.comparisonType,
    accounts: [],
    accountTypes,
    metadata,
    summary,
    lastUpdated: new Date().toISOString()
  };
};

// Updated calculateAccountTypeSummary to handle the corrected field names
const calculateAccountTypeSummary = (accountTypes, timePeriod) => {
  if (!accountTypes || accountTypes.length === 0) {
    return {
      totalCurrentBalance: 0,
      totalPastBalance: 0,
      totalDeltaAmount: 0,
      totalDeltaPercentage: 0,
      overallTrend: 'neutral',
      accountTypeCount: 0,
      totalAccountCount: 0
    };
  }

  let totalCurrentBalance, totalPastBalance;

  if (timePeriod === 'quarterly-rolling') {
    totalCurrentBalance = accountTypes.reduce((sum, acc) => 
      sum + (acc.currentQuarterBalance || acc.currentBalance || 0), 0);
    totalPastBalance = accountTypes.reduce((sum, acc) => 
      sum + (acc.pastQuarterBalance || acc.pastBalance || 0), 0);
  } else {
    totalCurrentBalance = accountTypes.reduce((sum, acc) => 
      sum + (acc.currentBalance || 0), 0);
    totalPastBalance = accountTypes.reduce((sum, acc) => 
      sum + (acc.pastBalance || 0), 0);
  }

  const totalDeltaAmount = totalCurrentBalance - totalPastBalance;
  const totalDeltaPercentage = totalPastBalance !== 0 
    ? (totalDeltaAmount / totalPastBalance) * 100 
    : 0;

  const totalAccountCount = accountTypes.reduce((sum, acc) => 
    sum + (acc.accountCount || 0), 0);

  return {
    totalCurrentBalance,
    totalPastBalance,
    totalDeltaAmount,
    totalDeltaPercentage,
    overallTrend: determineTrend(totalDeltaAmount),
    accountTypeCount: accountTypes.length,
    totalAccountCount,
    formattedSummary: formatDeltaDisplay(totalDeltaAmount, totalDeltaPercentage)
  };
};


  // Group delta accounts by type for lookup
  const deltaAccountsMap = useMemo(() => {
    if (!deltaAccounts || deltaAccounts.length === 0) return {};
    
    return deltaAccounts.reduce((map, account) => {
      map[account.accountId] = account;
      return map;
    }, {});
  }, [deltaAccounts]);

  // UPDATED: Dynamic colors based on current theme (fixed border color issue)
  const colors = useMemo(() => ({
    positive: currentTheme.colors.primary,
    negative: currentTheme.colors.secondary,
    neutral: darkMode ? 'text-slate-400' : 'text-slate-600',
    bg: darkMode ? currentTheme.colors.darkBackground : currentTheme.colors.background,
    cardBg: darkMode ? currentTheme.colors.darkCardBg : currentTheme.colors.cardBg,
    text: darkMode ? currentTheme.colors.darkText : currentTheme.colors.text,
    border: darkMode ? currentTheme.colors.darkBorder : currentTheme.colors.border,
    primary: currentTheme.colors.primary,
    secondary: currentTheme.colors.secondary,
    success: currentTheme.colors.success,
    accent: currentTheme.colors.accent
  }), [currentTheme, darkMode]);

  // Format time period for display
  const formatTimePeriodDisplay = (period) => {
    const displayNames = {
      'one-month': '1 Month',
      'three-month': '3 Months',
      'half-year': '6 Months',
      'yearly': '1 Year',
      'ytd': 'Year to Date',
      'quarterly-aggregate': 'Quarterly'
    };
    return displayNames[period] || period;
  };

  // Enhanced trend icon with better styling
  const TrendIcon = ({ trend, className = "" }) => {
    const iconProps = { className: `${className} transition-all duration-200` };
    
    switch (trend) {
      case 'increase':
        return <FontAwesomeIcon icon={faArrowUp} {...iconProps} />;
      case 'decrease':
        return <FontAwesomeIcon icon={faArrowDown} {...iconProps} />;
      default:
        return <FontAwesomeIcon icon={faEquals} {...iconProps} />;
    }
  };
  
 
 
  const [tableData, setTableData] = useState({
    Cash: [],
    CreditCards: [],
    Investments: [],
    LoanAccounts: [],
  });

  const [showConnectionModal, setShowConnectionModal] = useState(false);
  const [showAIChat, setShowAIChat] = useState(false);

  const [boxOrder, setBoxOrder] = useState(() => {
    const savedOrder = localStorage.getItem("accountsBoxOrder");
    const parsedOrder = savedOrder ? JSON.parse(savedOrder) : ALL_CATEGORIES;
    
    // Ensure we always have all 4 categories in the order
    const completeOrder = [...ALL_CATEGORIES];
    
    if (savedOrder) {
      // Reorder based on saved order, but ensure all categories are present
      const orderedCategories = [];
      parsedOrder.forEach(category => {
        if (ALL_CATEGORIES.includes(category)) {
          orderedCategories.push(category);
        }
      });
      
      // Add any missing categories to the end
      ALL_CATEGORIES.forEach(category => {
        if (!orderedCategories.includes(category)) {
          orderedCategories.push(category);
        }
      });
      
      return orderedCategories;
    }
    
    return completeOrder;
  });
 
  // Track if refresh is in progress to avoid showing premature errors
  const [isRefreshInProgress, setIsRefreshInProgress] = useState(false);
  const [chartType, setChartType] = useState('bar');
  const [timePeriod, setTimePeriod] = useState('yearly');
  const [selectedAccount, setSelectedAccount] = useState('all');

  useEffect(() => {
    logEvent('AccountsDashboard', 'PageLoad', {});
    // dispatch(fetchLinkToken());
    dispatch(fetchAccountDetails()); 
  }, []);

 useEffect(() => {
  if (accounts && accounts.length > 0) {
    updateTableData(accounts);
  } else {
    console.log("Fetched accounts array is empty:", accounts);
  }
}, [accounts, syncingAccounts, deltaAccountsMap]);

  useEffect(() => {
    const savedTableOrder = localStorage.getItem("accountsRowOrder");
    if (savedTableOrder) {
      const parsed = JSON.parse(savedTableOrder);
      setTableData((prev) => {
        const newData = { ...prev };
        for (const key in parsed) {
          if (newData[key]) {
            newData[key] = parsed[key]
              .map((savedId) => newData[key].find((d) => d.id === savedId))
              .filter(Boolean);
          }
        }
        return newData;
      });
    }
  }, []);

  // Monitor refresh status to control error display
  useEffect(() => {
    if (refreshAllStatus === 'loading') {
      setIsRefreshInProgress(true);
    } else if (refreshAllStatus === 'success' || refreshAllStatus === 'failed') {
      setIsRefreshInProgress(false);
      // Clear the refresh status after showing success message briefly
      if (refreshAllStatus === 'success') {
        const timer = setTimeout(() => {
          dispatch(resetError()); // This should also reset refreshAllStatus in your slice
        }, 3000); // Show success message for 3 seconds
        return () => clearTimeout(timer);
      }
    }
  }, [refreshAllStatus]);

  // Clear error after 5 seconds, but not if refresh is in progress
  useEffect(() => {
    if (error && !isRefreshInProgress) {
      const timer = setTimeout(() => {
        dispatch(resetError());
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, isRefreshInProgress]);

  const handleRowDragEnd = (result, category) => {
    if (!result.destination) return;

    const draggedAccount = tableData[category][result.source.index];
    logEvent('AccountsDashboard', 'ReorderAccountsInCategory', {
      category,
      accountId: draggedAccount?.id,
      fromIndex: result.source.index,
      toIndex: result.destination.index
    });
    const updatedRows = Array.from(tableData[category]);
    const [movedRow] = updatedRows.splice(result.source.index, 1);
    updatedRows.splice(result.destination.index, 0, movedRow);

    const newTableData = {
      ...tableData,
      [category]: updatedRows,
    };

    setTableData(newTableData);
    const rowOrder = JSON.parse(localStorage.getItem("accountsRowOrder") || "{}");
    rowOrder[category] = updatedRows.map((row) => row.id);
    localStorage.setItem("accountsRowOrder", JSON.stringify(rowOrder));
  };

  const formatAccounts = (type) =>
    accounts
      .filter((account) => account.accountType === type)
      .map((account, index) => {
        const accountId = account.id || account.accountId;
        const deltaAccount = deltaAccountsMap[accountId];
        return {
          id: accountId || index,
          institution: account.financialInstName || account.institutionId || "Unknown",
          accountName: account.accountName,
          accountNumber: maskAccountNumber(account.accountMask),
          numericBalance: account.balance || 0,
          balance: `$${account.balance ? account.balance.toFixed(2) : "0.00"}`,
          lastSyncTime: account.lastSyncTime || null,
          lastSyncFormatted: formatLastSyncTime(account.lastSyncTime),
          monthlyBalances: account.aggregatedBalances || [
            { month: "Jan", balance: (account.balance || 0) - 50 },
            { month: "Feb", balance: account.balance || 0 },
            { month: "Mar", balance: (account.balance || 0) + 75 },
          ],
          isSyncing: syncingAccounts.includes(accountId),
          // Delta data integration
          deltaData: deltaAccount ? {
            trend: deltaAccount.trend,
            percentage: deltaAccount.formattedDelta?.percentage || '0%',
            amount: deltaAccount.formattedDelta?.amount || '$0.00',
            hasDelta: true
          } : {
            trend: 'neutral',
            percentage: '0%',
            amount: '$0.00',
            hasDelta: false
          }
        };
      });

  const clearSavedRowOrders = () => {
    localStorage.removeItem("accountsRowOrder");
    window.location.reload();
  };

const updateTableData = (accounts) => {
  const savedRowOrder = JSON.parse(localStorage.getItem("accountsRowOrder") || "{}");
  const formatted = {
    Cash: formatAccounts("depository"),
    CreditCards: formatAccounts("credit"),
    Investments: formatAccounts("investment"),
    LoanAccounts: formatAccounts("loan"),
  };
  const reordered = {};
  Object.entries(formatted).forEach(([key, list]) => {
    const order = savedRowOrder[key];
    if (order) {
      const orderedList = [];
      const idToRow = Object.fromEntries(list.map((row) => [row.id, row]));
      order.forEach((id) => {
        if (idToRow[id]) orderedList.push(idToRow[id]);
      });
      const remaining = list.filter((row) => !order.includes(row.id));
      reordered[key] = [...orderedList, ...remaining];
    } else {
      reordered[key] = list;
    }
  });
  
  // Use functional update to prevent unnecessary re-renders
  setTableData(prevData => {
    // Only update if data actually changed
    const hasChanged = JSON.stringify(prevData) !== JSON.stringify(reordered);
    return hasChanged ? reordered : prevData;
  });
};
const handleSyncAccount = (accountId) => {
  logEvent('AccountsDashboard', 'SyncAccount', {
    accountId,
    accountType: accounts.find(acc => acc.id === accountId || acc.accountId === accountId)?.accountType
  });

  // More efficient approach - only update the specific account
const updateAccountSyncStatus = (isSyncing) => {
    setTableData((prevData) => {
      const newData = { ...prevData };
      
      // Find which category contains this account
      for (const category of Object.keys(newData)) {
        const accountIndex = newData[category].findIndex(acct => acct.id === accountId);
        if (accountIndex !== -1) {
          // Only update this specific account
          newData[category] = [...newData[category]];
          newData[category][accountIndex] = {
            ...newData[category][accountIndex],
            isSyncing
          };
          break; // Exit loop once we find and update the account
        }
      }
      
      return newData;
    });
  };

  // Set syncing to true
  updateAccountSyncStatus(true);

  // Dispatch the sync action
dispatch(syncAccount(accountId))
    .then(() => {
      dispatch(fetchAccountDetails()).then(() => {
        updateAccountSyncStatus(false);
      });
    })
    .catch((error) => {
      updateAccountSyncStatus(false);
    });
};

  // FIXED: Now isLoading is available for this conditional check
  if (isLoading && !hasLoadedOnce) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <PaymentLoader darkMode={darkMode} />
        <p className={`mt-4 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          Loading your accounts...
        </p>
      </div>
    );
  }
  const handleRefreshAll = () => {
    logEvent('AccountsDashboard', 'RefreshAllAccounts', {
      accountsCount: accounts?.length || 0,
      linkedAccountsCount: Object.values(tableData).flat().length
    });
    // Clear any existing errors before starting refresh
    dispatch(resetError());
    setIsRefreshInProgress(true);
 dispatch(refreshAllAccounts())
    .then(() => {
      // After successful refresh, fetch updated account details
      return dispatch(fetchAccountDetails());
    })
    .then(() => {
      setIsRefreshInProgress(false);
    })
    .catch((error) => {
      console.error('Refresh failed:', error);
      setIsRefreshInProgress(false);
    });
};
  const handleDragEnd = (result) => {
    if (!result.destination) return;
    logEvent('AccountsDashboard', 'ReorderAccountCategories', {
      fromIndex: result.source.index,
      toIndex: result.destination.index,
      movedCategory: boxOrder[result.source.index]
    });
    const newOrder = Array.from(boxOrder);
    const [removed] = newOrder.splice(result.source.index, 1);
    newOrder.splice(result.destination.index, 0, removed);
    setBoxOrder(newOrder);
    localStorage.setItem("accountsBoxOrder", JSON.stringify(newOrder));
  };

  const onSuccess = async (publicToken, metadata) => {
    logEvent('AccountsDashboard', 'PlaidConnectionSuccess', {
      institutionId: metadata?.institution?.institution_id,
      institutionName: metadata?.institution?.name,
      accountsCount: metadata?.accounts?.length
    });
    try {
      await dispatch(exchangePublicToken(publicToken));
      await dispatch(fetchAccountDetails(1));
      setShowConnectionModal(false);
    } catch (err) {
      logEvent('AccountsDashboard', 'PlaidConnectionError', {
        error: err.message || 'Unknown error'
      });
      console.error("Error linking accounts:", err);
    }
  };

  // Helper function to safely render error messages - exclude refresh-related errors while in progress
  const renderError = (error) => {
    if (!error) return null;
    
    // Don't show refresh-related errors while refresh is in progress
    if (isRefreshInProgress) {
      return null;
    }
    
    // Don't show certain sync-related errors that are expected during long operations
    const errorMessage = typeof error === 'string' ? error : (error?.message || error?.error || 'An error occurred');
    const isRefreshError = errorMessage.toLowerCase().includes('refresh') || 
                          errorMessage.toLowerCase().includes('synchronization') ||
                          errorMessage.toLowerCase().includes('sync');
    
    // Skip showing refresh/sync errors if refresh is in progress or recently completed
    if (isRefreshError && (isRefreshInProgress || refreshAllStatus === 'loading')) {
      return null;
    }
    
  };

  // Success message for refresh all
  const renderRefreshSuccessMessage = () => {
    if (refreshAllStatus === 'success' && !isRefreshInProgress) {
      return (
        <div className={`border rounded-xl px-6 py-4 mb-6 flex items-center shadow-lg animate-slide-down`}
          style={{
            backgroundColor: darkMode ? `${currentTheme.colors.success}20` : `${currentTheme.colors.success}10`,
            borderColor: `${currentTheme.colors.success}30`,
            color: currentTheme.colors.success
          }}>
          <div className={`p-2 rounded-full mr-4`} style={{ backgroundColor: `${currentTheme.colors.success}20` }}>
            <FontAwesomeIcon icon={faSync} className="text-lg" />
          </div>
          <span className="flex-1 font-medium">All accounts refreshed successfully!</span>
          <button 
            onClick={() => dispatch(resetError())}
            className={`ml-4 p-2 rounded-full transition-colors duration-200`}
            style={{ color: currentTheme.colors.success }}
            onMouseEnter={(e) => e.target.style.backgroundColor = `${currentTheme.colors.success}20`}
            onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
          >
            <FontAwesomeIcon icon={faTimes} />
          </button>
        </div>
      );
    }
    return null;
  };

  // Loading message for refresh all
  const renderRefreshLoadingMessage = () => {
    if (refreshAllStatus === 'loading' || isRefreshInProgress) {
   
    }
    return null;
  };

  // Enhanced table rendering with modern design
  const renderTable = (boxId, dragHandleProps) => {
    const data = tableData[boxId] || [];
    const categoryName = formatCategoryName(boxId);
    const categoryIcon = getCategoryIcon(boxId);
    
    // Create mock data when no real data exists
    const displayData = data.length === 0 ? [{
      id: `mock-${boxId}`,
      institution: "No accounts connected",
      accountNumber: "••••••0000",
      numericBalance: 0,
      balance: "$0.00",
      lastSyncTime: null,
      lastSyncFormatted: "Never synced",
      monthlyBalances: [
        { month: "Jan", balance: 0 },
        { month: "Feb", balance: 0 },
        { month: "Mar", balance: 0 }
      ],
      isSyncing: false,
      isMock: true,
      deltaData: {
        trend: 'neutral',
        percentage: '0%',
        amount: '$0.00',
        hasDelta: false
      }
    }] : data;
   const totalBalance = data
  .reduce((sum, row) => sum + (parseFloat(row.numericBalance) || 0), 0)
  .toLocaleString('en-US', { 
    style: 'currency', 
    currency: 'USD',
    minimumFractionDigits: 2, 
    maximumFractionDigits: 2 
  });
    const getTableCardStyle = () => {
      const style = {
        backgroundColor: colors.cardBg,
        borderColor: colors.border
      };
      
      // Border radius
      if (currentTheme.layout?.borderRadius === 'square') {
        style.borderRadius = '8px';
      } else if (currentTheme.layout?.borderRadius === 'pill') {
        style.borderRadius = '24px';
      } else {
        style.borderRadius = '16px';
      }
      
      // Shadows
      if (currentTheme.layout?.shadows === false) {
        style.boxShadow = 'none';
      }
      return style;
    };
    const getTableClasses = () => {
      let classes = `border overflow-hidden group transition-all duration-300`;
      
      // Card style
      if (currentTheme.layout?.cardStyle === 'flat') {
        classes += ` shadow-none`;
      } else if (currentTheme.layout?.cardStyle === 'outlined') {
        classes += ` shadow-none border-2`;
      } else {
        classes += ` shadow-xl`;
      }
      
      // Hover effects
      if (currentTheme.layout?.animations !== false) {
        classes += ` `;
      }
      return classes;
    };
    return (
      <div className={getTableClasses()} style={getTableCardStyle()}>
        {/* Enhanced Header */}
        <div className={`px-4 py-3 border-b`}
          style={{ 
            background: darkMode 
              ? `linear-gradient(to right, ${currentTheme.colors.darkCardBg}, ${currentTheme.colors.darkCardBg})`
              : `linear-gradient(to right, ${currentTheme.colors.primary}08, ${currentTheme.colors.accent}05)`,
            borderColor: colors.border
          }}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div
                {...dragHandleProps}
                className="opacity-0 group-hover:opacity-100 transition-all duration-200 p-1 rounded-lg hover:bg-white/10"
              >
                <DragHandleIcon darkMode={darkMode} />
              </div>
              <div className={`p-2 rounded-xl shadow-lg border`} 
                style={{ 
                  backgroundColor: darkMode ? `${currentTheme.colors.darkCardBg}80` : `${currentTheme.colors.cardBg}80`,
                  borderColor: colors.border
                }}>
                <FontAwesomeIcon icon={categoryIcon} className={`text-xl`} style={{ color: currentTheme.colors.primary }} />
              </div>
              <div>
                <h3 className={`text-lg font-bold`} style={{ color: colors.text }}>{categoryName}</h3>
                <p className={`text-xs ${colors.neutral}`}>{data.length} account{data.length !== 1 ? 's' : ''}</p>
              </div>
            </div>
            <div className="text-right">
              <div className={`text-xl font-bold`} style={{ color: colors.text }}>{totalBalance}</div>
              <div className={`text-xs ${colors.neutral}`}>Total Balance</div>
            </div>
          </div>
        </div>

        {/* Enhanced Table Body */}
        <DragDropContext onDragEnd={(result) => handleRowDragEnd(result, boxId)}>
          <Droppable droppableId={`rows-${boxId}`}>
            {(provided) => (
              <div
                ref={provided.innerRef}
                {...provided.droppableProps}
                className="divide-y divide-slate-200/20"
              >
                {displayData.map((row, index) => {
                  const isPositive = row.numericBalance >= 0;
                  const isMockRow = row.isMock;
                  const deltaData = row.deltaData || {};
                  return (
                    <Draggable key={row.id} draggableId={String(row.id)} index={index} isDragDisabled={isMockRow}>
                      {(provided, snapshot) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...(isMockRow ? {} : provided.dragHandleProps)}
                          className={`
                            px-4 py-3 transition-all duration-200
                            ${isMockRow 
                              ? (darkMode ? 'bg-slate-800/30 text-slate-500' : 'bg-slate-50/50 text-slate-400') 
                              : ''
                            }
                            ${snapshot.isDragging ? "shadow-2xl z-10 rounded-xl" : ""}
                            ${!isPositive && !isMockRow ? "text-rose-500" : ""}
                          `}
                        >
                          <div className="grid grid-cols-12 gap-3 items-center">
                            {/* Institution & Account Info */}
                            <div className="col-span-4">
                              <div className="flex items-center space-x-3">
                                <div className={`p-3 rounded-xl shadow-lg border`}
                                  style={{ 
                                    backgroundColor: colors.cardBg,
                                    borderColor: colors.border
                                  }}>
                                  <BankIcon
                                    institutionName={row.institution}
                                    className="w-12 h-12"
                                  />
                                </div>
                                <div>
                                  <div className={`font-semibold text-sm`} style={{ color: colors.text }}>{row.institution}</div>
                                  <div className={`text-xs ${colors.neutral}`}>{row.accountName}</div>
                                  <div className={`text-xs ${colors.neutral} font-mono`}>{row.accountNumber}</div>
                                </div>
                              </div>
                            </div>

                            {/* Balance & Delta */}
                            <div className="col-span-3">
                              <div className="space-y-1">
                                <div className={`text-base font-bold`} style={{ color: colors.text }}>
                                  {formatAmountWithCommas(row.numericBalance)}
                                </div>
                                {!isMockRow && deltaData.hasDelta && (
                                  <div className="flex items-center space-x-1">
                                    <TrendIcon 
                                      trend={deltaData.trend} 
                                      className={`text-xs`}
                                      style={{ 
                                        color: deltaData.trend === 'increase' ? currentTheme.colors.primary : 
                                               deltaData.trend === 'decrease' ? '#ef4444' : // Red for decrease
                                               colors.neutral 
                                      }}
                                    />
                                    <div className="flex items-center space-x-1">
                                      <span className={`text-xs font-semibold`}
                                        style={{ 
                                          color: deltaData.trend === 'increase' ? currentTheme.colors.primary : 
                                                 deltaData.trend === 'decrease' ? '#ef4444' : // Red for decrease
                                                 colors.neutral 
                                        }}>
                                        {deltaData.percentage}
                                      </span>
                                      <span className={`text-xs`}
                                        style={{ 
                                          color: deltaData.trend === 'increase' ? currentTheme.colors.primary : 
                                                 deltaData.trend === 'decrease' ? '#ef4444' : // Red for decrease
                                                 colors.neutral 
                                        }}>
                                        {deltaData.amount}
                                      </span>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Chart */}
                      <div className="col-span-3">
                      {/* {!isMockRow ? (
                        <AccountMiniChart 
                          accountId={row.accountId || row.id} // Pass the account ID
                          data={row.monthlyBalances}
                          darkMode={darkMode}
                          currentTheme={currentTheme}
                        />
                      ) : (
                        <div className={`h-16 rounded-lg flex items-center justify-center`}
                          style={{ backgroundColor: darkMode ? '#********' : '#e2e8f0' }}>
                          <span className={`text-xs ${colors.neutral}`}>No data</span>
                        </div>
                      )} */}
                      <AccountMiniChart
                        accountId={row.accountId || row.id}
                        data={isMockRow ? [] : row.monthlyBalances}
                        darkMode={darkMode}
                        currentTheme={currentTheme}
                      />
                    </div>

                            {/* Sync & Last Update */}
                            <div className="col-span-2">
                              <div className="flex flex-col items-center space-y-2">
                                {!isMockRow && (
                                  <button
                                    onClick={() => handleSyncAccount(row.id)}
                                    className={`p-1.5 rounded-lg transition-all duration-200 ${
                                      row.isSyncing 
                                        ? 'bg-opacity-20 text-current' 
                                        : (darkMode 
                                          ? 'bg-slate-700/50 text-slate-400 hover:bg-opacity-20 hover:text-current' 
                                          : 'bg-slate-100 text-slate-500 hover:bg-opacity-20 hover:text-current')
                                    }`}
                                    style={row.isSyncing 
                                      ? { backgroundColor: `${currentTheme.colors.primary}20`, color: currentTheme.colors.primary }
                                      : {}
                                    }
                                    onMouseEnter={(e) => {
                                      if (!row.isSyncing) {
                                        e.target.style.backgroundColor = `${currentTheme.colors.primary}20`;
                                        e.target.style.color = currentTheme.colors.primary;
                                      }
                                    }}
                                    onMouseLeave={(e) => {
                                      if (!row.isSyncing) {
                                        e.target.style.backgroundColor = '';
                                        e.target.style.color = '';
                                      }
                                    }}
                                    title="Sync Account"
                                    disabled={row.isSyncing}
                                  >
                                    <FontAwesomeIcon
                                      icon={faSync}
                                      className={`text-sm ${row.isSyncing ? "animate-spin" : ""}`}
                                    />
                                  </button>
                                )}
                                <div className="text-center">
                                  <div className="flex items-center justify-center space-x-1 mb-1">
                                    <FontAwesomeIcon 
                                      icon={faClock} 
                                      className={`text-xs ${colors.neutral}`}
                                    />
                                    <div className={`text-xs ${colors.neutral} leading-tight`}>
                                      {row.lastSyncFormatted}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </Draggable>
                  );
                })}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      </div>
    );
  };

  return (
    <div className={`min-h-screen w-full font-inter transition-all duration-300`}
      style={{ 
        backgroundColor: colors.bg,
        fontFamily: currentTheme.font.value
      }}>
      <div className={`px-4 sm:px-6 lg:px-8 py-8 w-full`}
        style={{ 
          maxWidth: currentTheme.layout?.maxWidth || 'none',
          margin: currentTheme.layout?.centered ? '0 auto' : '0'
        }}>
        {/* Enhanced Header */}
        <div className={`flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 space-y-4 lg:space-y-0 ${
          currentTheme.layout?.spacing === 'compact' ? 'mb-4' : 
          currentTheme.layout?.spacing === 'spacious' ? 'mb-12' : 'mb-8'
        }`}>
          <div className="flex items-center space-x-4">
            <div className={`p-3 rounded-xl shadow-lg border`}
              style={{ 
                backgroundColor: darkMode ? `${currentTheme.colors.primary}20` : `${currentTheme.colors.primary}10`,
                borderColor: colors.border
              }}>
              <FontAwesomeIcon icon={faRobot} className={`text-2xl`} style={{ color: currentTheme.colors.primary }} />
            </div>
            <h1 className={`text-4xl font-bold bg-gradient-to-r bg-clip-text text-transparent`}
              style={{ 
                backgroundImage: `linear-gradient(to right, ${currentTheme.colors.primary}, ${currentTheme.colors.secondary})`,
                fontFamily: currentTheme.font.value
              }}>
              Accounts
            </h1>
          </div>
          
          <div className={`flex space-x-4 ${
            currentTheme.layout?.spacing === 'compact' ? 'space-x-2' : 
            currentTheme.layout?.spacing === 'spacious' ? 'space-x-6' : 'space-x-4'
          }`}>
            {/* AI Chat Button */}
            <button
              className={`hover:shadow-lg text-white py-3 px-4 flex items-center space-x-2 transition-all duration-200 font-semibold shadow-md ${
                currentTheme.layout?.animations === false ? '' : 'hover:scale-105'
              } ${showAIChat ? 'ring-2 ring-white/30' : ''}`}
              style={{ 
                backgroundColor: currentTheme.colors.accent,
                borderRadius: currentTheme.layout?.borderRadius === 'square' ? '6px' : 
                           currentTheme.layout?.borderRadius === 'pill' ? '50px' : '12px',
                boxShadow: currentTheme.layout?.shadows === false ? 'none' : ''
              }}
              onClick={() => setShowAIChat(!showAIChat)}
              title="AI Assistant"
            >
              <FontAwesomeIcon icon={faRobot} />
              <span>AI Chat</span>
            </button>
            
            {/* Design Palette Dropdown */}
            <DesignPaletteDropdown 
              darkMode={darkMode}
              onThemeChange={handleThemeChange}
            />
            
            <button
              className={`hover:shadow-lg text-black py-3 px-6 flex items-center space-x-2 transition-all duration-200 font-semibold shadow-md ${
                currentTheme.layout?.animations === false ? '' : 'hover:scale-105'
              }`}
              style={{ 
                backgroundColor: colors.primary,
                borderRadius: currentTheme.layout?.borderRadius === 'square' ? '6px' : 
                           currentTheme.layout?.borderRadius === 'pill' ? '50px' : '12px',
                boxShadow: currentTheme.layout?.shadows === false ? 'none' : ''
              }}
              onClick={() => setShowConnectionModal(true)}
            >
              <FontAwesomeIcon icon={faPlus} />
              <span>Add Account</span>
            </button>
            <button
              className={`hover:shadow-lg text-black py-3 px-6 flex items-center space-x-2 transition-all duration-200 font-semibold shadow-md disabled:opacity-50 disabled:cursor-not-allowed ${
                currentTheme.layout?.animations === false ? '' : 'hover:scale-105'
              }`}
              style={{ 
                backgroundColor: colors.secondary,
                borderRadius: currentTheme.layout?.borderRadius === 'square' ? '6px' : 
                           currentTheme.layout?.borderRadius === 'pill' ? '50px' : '12px',
                boxShadow: currentTheme.layout?.shadows === false ? 'none' : ''
              }}
              onClick={handleRefreshAll}
              disabled={!isLinked || refreshAllStatus === 'loading' || isRefreshInProgress}
            >
              <FontAwesomeIcon 
                icon={faRedo} 
                className={(refreshAllStatus === 'loading' || isRefreshInProgress) ? "animate-spin" : ""} 
              />
              <span>{(refreshAllStatus === 'loading' || isRefreshInProgress) ? "Syncing..." : "Refresh All"}</span>
            </button>
          </div>
        </div>

        {/* Status Messages */}
        {renderRefreshLoadingMessage()}
        {renderError(error)}
        {renderRefreshSuccessMessage()}

        {/* Stat Cards */}
       <StatCardGroupView darkMode={darkMode} currentTheme={currentTheme} accounts={accounts} />

        {/* Chart Controls & Main Chart */}
        
            <PortfolioChartControlView 
                darkMode = {darkMode}
                currentTheme = {currentTheme}
                accounts = {accounts}
                selectedAccount = {selectedAccount}
                setChartType = {setChartType}
                selectedChartType = {selectedChartType}
                chartType = {chartType}
                handleChartTypeChange = {handleChartTypeChange}
                selectedTimePeriod = {selectedTimePeriod}
                handleTimePeriodChange = {handleTimePeriodChange}
                timePeriod = {timePeriod}
            />

     
          {/* {isLinked && (
        <PortfolioChartControlView
          darkMode={darkMode}
          
          currentTheme={currentTheme}
          accounts={accounts}
        />
      )} */}
        {/* Enhanced Connection Modal */}
        {showConnectionModal && (
          <AccountConnectionProviderModal 
                darkMode = {darkMode}
                currentTheme = {currentTheme} 
                setShowConnectionModal = {setShowConnectionModal}  />
        )}

        {/* AI Chat Window */}
        {showAIChat && (
            <AccountAIChat 
                darkMode = {darkMode} 
                currentTheme = {currentTheme} 
                setShowAIChat = {setShowAIChat}  
                showAIChat  = {showAIChat} />
        )}

        {/* Enhanced No Accounts State */}
      

        {/* Enhanced Account Tables */}
       
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="categories">
              {(provided) => (
                <div
                  className={`${
                    currentTheme.layout?.spacing === 'compact' ? 'space-y-4' :
                    currentTheme.layout?.spacing === 'spacious' ? 'space-y-12' : 'space-y-8'
                  }`}
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                >
                  {boxOrder.map((boxId, index) => (
                    <Draggable draggableId={boxId} index={index} key={boxId}>
                      {(provided, snapshot) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          className={`transition-all duration-300 ${
                            snapshot.isDragging ? "z-50 rotate-2" : ""
                          } ${
                            currentTheme.layout?.animations === false ? '' : ''
                          }`}
                        >
                          {renderTable(boxId, provided.dragHandleProps)}
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
      
      </div>

      {/* Enhanced Styles */}
      <style jsx>{`
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700;800;900&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400;500;600;700;800;900&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;500;600;700;800;900&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400;500;600;700;800;900&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700;800&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800;900&display=swap');
        
        .font-inter {
          font-family: ${currentTheme.font.value};
        }

        @keyframes fade-in {
          from {
            opacity: 0;
          }
          to {
            opacity: 1;
          }
        }
        
        @keyframes slide-down {
          from {
            opacity: 0;
            transform: translateY(-20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes slide-up {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        .animate-fade-in {
          animation: fade-in 0.5s ease-out;
        }
        
        .animate-slide-down {
          animation: slide-down 0.3s ease-out;
        }

        .animate-slide-up {
          animation: slide-up 0.3s ease-out;
        }
        
        /* Improved scrollbar */
        ::-webkit-scrollbar {
          width: 8px;
        }
        
        ::-webkit-scrollbar-track {
          background: transparent;
        }
        
        ::-webkit-scrollbar-thumb {
          background: rgba(148, 163, 184, 0.3);
          border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
          background: rgba(148, 163, 184, 0.5);
        }
        
        /* Enhanced hover effects */
        .group:hover .drag-handle {
          opacity: 1;
        }
        
        .drag-handle {
          opacity: 0;
          transition: opacity 0.2s ease;
        }

        /* Better responsive behavior */
        @media (max-width: 768px) {
          .grid-cols-12 {
            grid-template-columns: 1fr;
            gap: 1rem;
          }
          
          .col-span-4,
          .col-span-3,
          .col-span-2 {
            grid-column: span 1;
          }
        }

        /* Dynamic theme-based styles */
        .theme-gradient {
          background: linear-gradient(135deg, ${currentTheme.colors.primary}, ${currentTheme.colors.secondary});
        }
        
        .theme-shadow {
          box-shadow: 0 10px 25px ${currentTheme.colors.primary}20;
        }
        
        /* Smooth theme transitions */
        * {
          transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
        }

        /* AI Chat Window Animation */
        .translate-x-0 {
          transform: translateX(0);
        }
        
        .translate-x-full {
          transform: translateX(100%);
        }
        
        /* Chat window scrollbar */
        .overflow-y-auto::-webkit-scrollbar {
          width: 6px;
        }
        
        .overflow-y-auto::-webkit-scrollbar-track {
          background: transparent;
        }
        
        .overflow-y-auto::-webkit-scrollbar-thumb {
          background: rgba(148, 163, 184, 0.3);
          border-radius: 10px;
        }
        
        .overflow-y-auto::-webkit-scrollbar-thumb:hover {
          background: rgba(148, 163, 184, 0.5);
        }
      `}</style>
    </div>
  );
};

export default AccountsDashboard;