import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Shield,
  Lock,
  Trash2,
  Check,
  Eye,
  EyeOff,
  AlertTriangle,
  Loader2,
  Copy,
  QrCode,
  Key,
  X
} from 'lucide-react';

import {
  setupTwoFactorAuthRequest,
  enableTwoFactorAuthRequest,
  disableTwoFactorAuthRequest,
  verifyTwoFactorCodeRequest,
  getTwoFactorStatusRequest,
  resetTwoFactorAuthStatus,
  clearSetupData,
  clearVerificationStatus
} from '../../../../logic/redux/twoFactorAuthSlice';

import {
  changePassword,
  deleteAccountPermanently,
  clearChangePasswordState,
  clearDeleteAccountState
} from '../../../../logic/redux/accountManagementSlice';

import { getCurrentUserId } from '../../utils/AuthUtil';

const Security = () => {
  const dispatch = useDispatch();
  const userId = getCurrentUserId();

  const twoFactorState = useSelector(state => state.twoFactorAuth);
  const accountState = useSelector(state => state.accountManagement);

  const [showPasswordChange, setShowPasswordChange] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [show2FASetup, setShow2FASetup] = useState(false);
  const [show2FADisable, setShow2FADisable] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [disableCode, setDisableCode] = useState('');
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });

  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [deleteForm, setDeleteForm] = useState({
    password: '',
    confirmText: ''
  });

  useEffect(() => {
    if (userId) {
      dispatch(getTwoFactorStatusRequest({ userId }));
    }
  }, [dispatch, userId]);

  useEffect(() => {
    if (accountState.changePasswordSuccess) {
      const timer = setTimeout(() => {
        dispatch(clearChangePasswordState());
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [accountState.changePasswordSuccess, dispatch]);

  useEffect(() => {
    if (twoFactorState.success) {
      const timer = setTimeout(() => {
        dispatch(resetTwoFactorAuthStatus());
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [twoFactorState.success, dispatch]);

  const handlePasswordChange = async (e) => {
    e.preventDefault();

    if (!userId) {
      alert('User ID is missing');
      return;
    }

    const { currentPassword, newPassword, confirmPassword } = passwordForm;

    if (!currentPassword || !newPassword || !confirmPassword) {
      alert('All fields are required');
      return;
    }

    if (newPassword !== confirmPassword) {
      alert('Passwords do not match');
      return;
    }

    try {
      dispatch(clearChangePasswordState());
      await dispatch(changePassword({
        userId,
        oldPassword: currentPassword.trim(),
        newPassword: newPassword.trim(),
        confirmPassword: confirmPassword.trim()
      })).unwrap();

      setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' });
      setShowPasswordChange(false);
    } catch (err) {
      console.error('Password update failed:', err);
    }
  };

  const handleDeleteAccount = async (e) => {
    e.preventDefault();

    if (!userId) return;

    if (deleteForm.confirmText !== 'DELETE') {
      alert('Type DELETE to confirm');
      return;
    }

    try {
      await dispatch(deleteAccountPermanently({
        userId,
        password: deleteForm.password.trim()
      })).unwrap();

      setShowDeleteConfirm(false);
      setDeleteForm({ password: '', confirmText: '' });
    } catch (error) {
      console.error('Account deletion failed:', error);
    }
  };

  const handleTwoFactorToggle = () => {
    if (twoFactorState.isEnabled) {
      setShow2FADisable(true);
    } else {
      setShow2FASetup(true);
      dispatch(setupTwoFactorAuthRequest({ userId }));
    }
  };

  const handleSetup2FA = () => {
    if (verificationCode.trim().length === 6) {
      dispatch(enableTwoFactorAuthRequest({
        userId,
        secret: twoFactorState.setupData.secret,
        verificationCode: verificationCode.trim()
      }));
    } else {
      alert('Enter valid 6-digit code');
    }
  };

  const handleDisable2FA = () => {
    if (disableCode.trim().length === 6) {
      dispatch(disableTwoFactorAuthRequest({
        userId,
        verificationCode: disableCode.trim()
      }));
    } else {
      alert('Enter valid 6-digit code');
    }
  };

  const handleCopySecret = (secret) => {
    navigator.clipboard.writeText(secret);
    alert('Secret copied!');
  };

  const closeSetupModal = () => {
    setShow2FASetup(false);
    setVerificationCode('');
    dispatch(clearSetupData());
    dispatch(resetTwoFactorAuthStatus());
  };

  const closeDisableModal = () => {
    setShow2FADisable(false);
    setDisableCode('');
    dispatch(resetTwoFactorAuthStatus());
  };

  useEffect(() => {
    if (twoFactorState.success && twoFactorState.isEnabled && show2FASetup) {
      setTimeout(closeSetupModal, 2000);
    }
    if (twoFactorState.success && !twoFactorState.isEnabled && show2FADisable) {
      setTimeout(closeDisableModal, 2000);
    }
  }, [twoFactorState.success, twoFactorState.isEnabled, show2FASetup, show2FADisable]);


  return (
    <div className="space-y-8">
      <h2 className="text-2xl font-bold text-gray-900">Security Settings</h2>

      {/* Success/Error Messages */}
      {accountState.changePasswordSuccess && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <Check className="w-5 h-5 text-green-600" />
            <p className="text-green-800">Password updated successfully!</p>
          </div>
        </div>
      )}

      {accountState.changePasswordError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-red-600" />
            <p className="text-red-800">{accountState.changePasswordError}</p>
          </div>
        </div>
      )}

      {twoFactorState.success && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <Check className="w-5 h-5 text-green-600" />
            <p className="text-green-800">{twoFactorState.message}</p>
          </div>
        </div>
      )}

      {/* Password Change */}
      <div className="bg-gray-50 rounded-xl p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Password</h3>
            <p className="text-sm text-gray-600">Update your password to keep your account secure</p>
          </div>
          <button
            onClick={() => setShowPasswordChange(!showPasswordChange)}
            className="px-4 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors"
          >
            Change Password
          </button>
        </div>

        {showPasswordChange && (
          <form onSubmit={handlePasswordChange} className="space-y-4 mt-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
              <div className="relative">
                <input
                  type={showPasswords.current ? 'text' : 'password'}
                  value={passwordForm.currentPassword}
                  onChange={(e) => setPasswordForm({...passwordForm, currentPassword: e.target.value})}
                  className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPasswords({...showPasswords, current: !showPasswords.current})}
                  className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
                >
                  {showPasswords.current ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">New Password</label>
              <div className="relative">
                <input
                  type={showPasswords.new ? 'text' : 'password'}
                  value={passwordForm.newPassword}
                  onChange={(e) => setPasswordForm({...passwordForm, newPassword: e.target.value})}
                  className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPasswords({...showPasswords, new: !showPasswords.new})}
                  className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
                >
                  {showPasswords.new ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
              <div className="relative">
                <input
                  type={showPasswords.confirm ? 'text' : 'password'}
                  value={passwordForm.confirmPassword}
                  onChange={(e) => setPasswordForm({...passwordForm, confirmPassword: e.target.value})}
                  className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPasswords({...showPasswords, confirm: !showPasswords.confirm})}
                  className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
                >
                  {showPasswords.confirm ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>

            {passwordForm.newPassword && passwordForm.confirmPassword && 
             passwordForm.newPassword !== passwordForm.confirmPassword && (
              <div className="text-red-600 text-sm">
                Passwords do not match
              </div>
            )}

            <div className="flex items-center space-x-4">
              <button
                type="submit"
                disabled={accountState.changePasswordLoading || 
                         passwordForm.newPassword !== passwordForm.confirmPassword ||
                         !passwordForm.currentPassword || 
                         !passwordForm.newPassword || 
                         !passwordForm.confirmPassword}
                className="flex items-center space-x-2 px-6 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors disabled:opacity-50"
              >
                {accountState.changePasswordLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Lock className="w-4 h-4" />
                )}
                <span>Update Password</span>
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowPasswordChange(false);
                  setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' });
                  dispatch(clearChangePasswordState());
                }}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
            </div>
          </form>
        )}
      </div>

      {/* Two-Factor Authentication */}
      <div className="bg-gray-50 rounded-xl p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Two-Factor Authentication</h3>
            <p className="text-sm text-gray-600">Add an extra layer of security to your account</p>
          </div>
          <div className="flex items-center space-x-3">
            <span className={`text-sm font-medium ${twoFactorState.isEnabled ? 'text-green-600' : 'text-gray-500'}`}>
              {twoFactorState.isEnabled ? 'Enabled' : 'Disabled'}
            </span>
            <button
              onClick={handleTwoFactorToggle}
              disabled={twoFactorState.loading}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
                twoFactorState.isEnabled ? 'bg-indigo-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  twoFactorState.isEnabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        </div>
      </div>

      {/* 2FA Setup Modal */}
      {show2FASetup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Setup Two-Factor Authentication</h3>
              <button
                onClick={closeSetupModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {twoFactorState.setupLoading ? (
              <div className="text-center py-8">
                <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
                <p className="text-gray-600">Setting up 2FA...</p>
              </div>
            ) : twoFactorState.setupData ? (
              <div className="space-y-4">
                <div className="text-center">
                  <div className="bg-gray-100 p-4 rounded-lg mb-4">
                    <QrCode className="w-12 h-12 mx-auto mb-2 text-gray-600" />
                    <p className="text-sm text-gray-600">Scan this QR code with your authenticator app</p>
                  </div>
                  
                  {twoFactorState.setupData.qrCode && (
                    <div className="mb-4">
                      <img 
                        src={twoFactorState.setupData.qrCode} 
                        alt="QR Code" 
                        className="mx-auto border border-gray-300 rounded"
                      />
                    </div>
                  )}

                  <div className="bg-gray-50 p-3 rounded-lg mb-4">
                    <p className="text-xs text-gray-600 mb-1">Or enter this secret manually:</p>
                    <div className="flex items-center space-x-2">
                      <code className="text-sm bg-white px-2 py-1 rounded border flex-1">
                        {twoFactorState.setupData.secret}
                      </code>
                      <button
                        onClick={() => handleCopySecret(twoFactorState.setupData.secret)}
                        className="p-1 text-gray-500 hover:text-gray-700"
                      >
                        <Copy className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Enter verification code from your authenticator app
                  </label>
                  <input
                    type="text"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                    placeholder="123456"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-center text-lg tracking-widest"
                    maxLength="6"
                  />
                </div>

                <div className="flex space-x-4">
                  <button
                    onClick={handleSetup2FA}
                    disabled={twoFactorState.enableLoading || verificationCode.length !== 6}
                    className="flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors disabled:opacity-50"
                  >
                    {twoFactorState.enableLoading ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Shield className="w-4 h-4" />
                    )}
                    <span>Enable 2FA</span>
                  </button>
                  <button
                    onClick={closeSetupModal}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : twoFactorState.error ? (
              <div className="text-center py-4">
                <AlertTriangle className="w-8 h-8 mx-auto mb-2 text-red-500" />
                <p className="text-red-600">{twoFactorState.error}</p>
                <button
                  onClick={closeSetupModal}
                  className="mt-4 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                >
                  Close
                </button>
              </div>
            ) : null}
          </div>
        </div>
      )}

      {/* 2FA Disable Modal */}
      {show2FADisable && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Disable Two-Factor Authentication</h3>
              <button
                onClick={closeDisableModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="w-5 h-5 text-yellow-600" />
                  <p className="text-yellow-800">This will reduce your account security</p>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Enter verification code from your authenticator app
                </label>
                <input
                  type="text"
                  value={disableCode}
                  onChange={(e) => setDisableCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                  placeholder="123456"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-center text-lg tracking-widest"
                  maxLength="6"
                />
              </div>

              <div className="flex space-x-4">
                <button
                  onClick={handleDisable2FA}
                  disabled={twoFactorState.disableLoading || disableCode.length !== 6}
                  className="flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50"
                >
                  {twoFactorState.disableLoading ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <X className="w-4 h-4" />
                  )}
                  <span>Disable 2FA</span>
                </button>
                <button
                  onClick={closeDisableModal}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Account */}
      <div className="bg-red-50 rounded-xl p-6 border border-red-200">
        <div className="flex items-start space-x-3">
          <AlertTriangle className="w-6 h-6 text-red-500 mt-0.5" />
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-red-900">Delete Account</h3>
            <p className="text-sm text-red-700 mt-1">
              Once you delete your account, there is no going back. Please be certain.
            </p>
            <button
              onClick={() => setShowDeleteConfirm(true)}
              className="mt-4 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
            >
              Delete Account
            </button>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
            <div className="flex items-center space-x-3 mb-4">
              <AlertTriangle className="w-6 h-6 text-red-500" />
              <h3 className="text-lg font-semibold text-gray-900">Confirm Account Deletion</h3>
            </div>
            <p className="text-gray-600 mb-4">
              This action cannot be undone. Type "DELETE" to confirm.
            </p>
            <form onSubmit={handleDeleteAccount} className="space-y-4">
              <input
                type="password"
                placeholder="Enter your password"
                value={deleteForm.password}
                onChange={(e) => setDeleteForm({...deleteForm, password: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                required
              />
              <input
                type="text"
                placeholder="Type DELETE to confirm"
                value={deleteForm.confirmText}
                onChange={(e) => setDeleteForm({...deleteForm, confirmText: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                required
              />
              <div className="flex items-center space-x-4">
                <button
                  type="submit"
                  disabled={accountState.deleteAccountLoading || deleteForm.confirmText !== 'DELETE'}
                  className="flex items-center space-x-2 px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50"
                >
                  {accountState.deleteAccountLoading ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Trash2 className="w-4 h-4" />
                  )}
                  <span>Delete Account</span>
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowDeleteConfirm(false);
                    setDeleteForm({ password: '', confirmText: '' });
                  }}
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Additional Error Messages */}
      {twoFactorState.error && !show2FASetup && !show2FADisable && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-red-600" />
            <p className="text-red-800">{twoFactorState.error}</p>
          </div>
        </div>
      )}

      {accountState.deleteAccountError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-red-600" />
            <p className="text-red-800">{accountState.deleteAccountError}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default Security;