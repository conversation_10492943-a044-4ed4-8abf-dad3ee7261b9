import { ofType } from 'redux-observable';
import { from, of } from 'rxjs';
import { mergeMap, map, catchError } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchUserStart,
  fetchUserSuccess,
  fetchUserFailure,
  updateUserStart,
  updateUserSuccess,
  updateUserFailure,
} from '../redux/profileSlice';
import { getCurrentUserId } from '../../web/src/utils/AuthUtil';

/* ─────────────────────────────────────────
   FETCH CURRENT USER
───────────────────────────────────────── */
export const fetchUserEpic = (action$) =>
  action$.pipe(
    ofType(fetchUserStart.type),
    mergeMap(() => {
      const userId = getCurrentUserId();
      if (!userId) {
        return of(fetchUserFailure('User ID not found'));
      }

      // GET /api/v1/user/profile/{userId}
      return from(
        axiosInstance.get(`/pennypal/api/v1/user/profile/${userId}`)
      ).pipe(
        map((response) => {
          console.log('Fetch user response:', response.data);
          return fetchUserSuccess(response.data);
        }),
        catchError((error) => {
          console.error('Fetch user error:', error);
          const errorMessage = error.response?.data?.message || 
                              error.response?.data || 
                              error.message || 
                              'Failed to fetch user';
          return of(fetchUserFailure(errorMessage));
        })
      );
    })
  );

/* ─────────────────────────────────────────
   UPDATE CURRENT USER
───────────────────────────────────────── */
export const updateUserEpic = (action$) =>
  action$.pipe(
    ofType(updateUserStart.type),
    mergeMap(({ payload: userDto }) => {
      const userId = getCurrentUserId();
      if (!userId) {
        return of(updateUserFailure('User ID not found'));
      }

      // Ensure the userDto has the correct structure
      const updatePayload = {
        ...userDto,
        id: userId // This will be overridden by the controller anyway
      };

      console.log('Updating user with payload:', updatePayload);

      // PUT /api/v1/user/profile/{id}
      return from(
        axiosInstance.put(`/pennypal/api/v1/user/profile/${userId}`, updatePayload)
      ).pipe(
        map((response) => {
          console.log('Update user response:', response.data);
          return updateUserSuccess(response.data);
        }),
        catchError((error) => {
          console.error('Update user error:', error);
          const errorMessage = error.response?.data?.message || 
                              error.response?.data || 
                              error.message || 
                              'Failed to update user';
          return of(updateUserFailure(errorMessage));
        })
      );
    })
  );

/* ─────────────────────────────────────────
   EXPORT EPICS
───────────────────────────────────────── */
export default [fetchUserEpic, updateUserEpic];