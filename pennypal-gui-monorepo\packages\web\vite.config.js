import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import tailwindcss from '@tailwindcss/vite';


import path from 'path'

export default defineConfig({
  plugins: [
    react(),
    tailwindcss()
  ],
  resolve: {
    alias: {
      // Exclude react-native from web build
      'react-native': 'react-native-web',
      '@pp-web': path.resolve(__dirname, './src'),
      '@pp-logic': path.resolve(__dirname, '../logic'),
      '@pp-web/utils': path.resolve(__dirname, './src/utils'),
      '@pp-web/assets': path.resolve(__dirname, './src/assets'),
      '@pp-web/components': path.resolve(__dirname, './src/components'),
      

      
    },
  },
  optimizeDeps: {
    exclude: ['react-native']
  }
});
