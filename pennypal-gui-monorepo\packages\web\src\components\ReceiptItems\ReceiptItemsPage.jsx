import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import { fetchReceiptItemsRequest, fetchItemSummaryRequest } from '../../../../logic/redux/receiptItemsSlice';
import PaymentLoader from '../load/PaymentLoader';

const ReceiptItemsPage = () => {
  const { receiptId } = useParams();
  const dispatch = useDispatch();
  const { receiptItems, loading, error, itemSummary, summaryLoading, summaryError } = useSelector(
    (state) => state.receiptItems
  );
  const [activeTab, setActiveTab] = useState('grouped'); // 'grouped' or 'summary'

  useEffect(() => {
    const payload = receiptId ? { receiptId: parseInt(receiptId) } : {};
    dispatch(fetchReceiptItemsRequest(payload));
  }, [dispatch, receiptId]);

  useEffect(() => {
    dispatch(fetchReceiptItemsRequest(receiptId ? { receiptId: parseInt(receiptId) } : {}));
    dispatch(fetchItemSummaryRequest()); // Fetch summary on load
  }, [dispatch, receiptId]);

  const groupedItems = receiptItems.reduce((acc, item) => {
    const id = item.receiptId;
    if (!acc[id]) acc[id] = [];
    acc[id].push(item);
    return acc;
  }, {});

  // Function to get progress bar color based on percentage of highest amount
const getProgressBarColor = (index, totalItems) => {
  if (totalItems <= 1) return 'hsla(0, 85%, 70%, 0.4)'; // Slightly stronger light red

  const ratio = index / (totalItems - 1); // 0 = highest, 1 = lowest
  const hue = 0 + (120 * ratio);          // Red → Green
  const saturation = 85;                  // Vivid color
  const lightness = 70;                   // Slightly deeper than pastel
  const alpha = 0.4;                      // More visible

  return `hsla(${hue}, ${saturation}%, ${lightness}%, ${alpha})`;
};


if (loading || summaryLoading) {
  return <PaymentLoader />;
}


  if (error) {
    return (
      <div style={styles.errorContainer}>
        <div style={styles.errorBox}>
          <p style={styles.errorText}>Error: {error}</p>
        </div>
      </div>
    );
  }

  // Sort itemSummary by totalSpent in descending order
  const sortedItemSummary = [...itemSummary].sort((a, b) => parseFloat(b.totalSpent) - parseFloat(a.totalSpent));

  return (
    <div style={styles.pageWrapper}>
      <div style={styles.container}>
        <header style={styles.pageHeader}>
          <h1 style={styles.title}>Receipt Items</h1>
        </header>

        <div style={styles.tabs}>
          <button
            onClick={() => setActiveTab('grouped')}
            style={activeTab === 'grouped' ? styles.activeTab : styles.inactiveTab}
          >
            Grouped by Receipt
          </button>
          <button
            onClick={() => setActiveTab('summary')}
            style={activeTab === 'summary' ? styles.activeTab : styles.inactiveTab}
          >
            Summary by Item
          </button>
        </div>

        <main style={styles.mainContent}>
          {activeTab === 'grouped' ? (
            Object.entries(groupedItems).length === 0 ? (
              <div style={styles.noData}>
                <p>No receipt items available</p>
              </div>
            ) : (
              <div style={styles.receiptsGrid}>
                {Object.entries(groupedItems).map(([receiptId, items]) => (
                  <div key={receiptId} style={styles.receiptContainer}>
                    <div style={styles.receiptTitleBar}>
                      <span style={styles.receiptId}>Receipt #{receiptId}</span>
                      <span style={styles.itemsCount}>{items.length} items</span>
                    </div>

                    <div style={styles.itemsList}>
                      {items
                        .sort((a, b) => b.price - a.price) // Sort items by price descending
                        .map((item) => (
                          <div key={item.id} style={styles.itemCard}>
                            <span style={styles.itemName}>{item.item}</span>
                            <span style={styles.itemPrice}>${item.price.toFixed(2)}</span>
                          </div>
                        ))}

                      <div style={styles.totalBar}>
                        <span style={styles.totalLabel}>Total</span>
                        <span style={styles.totalValue}>
                          ${items.reduce((sum, item) => sum + item.price, 0).toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )
          ) : (
            <div style={styles.summaryTable}>
              <h3 style={styles.summaryTitle}>Item Summary</h3>
          {sortedItemSummary.map((summary, index) => {
const item = summary?.receiptItem || summary?.itemName || 'Unknown Item';
const totalSpent = summary?.price || summary?.totalSpent || '0';

  const currentAmount = parseFloat(totalSpent);
  const progressWidth = sortedItemSummary.length > 1 
    ? Math.max(20, 100 - (index * (80 / (sortedItemSummary.length - 1))))
    : 100;

  const progressColor = getProgressBarColor(index, sortedItemSummary.length);

                
                return (
                  <div key={item} style={{...styles.summaryCard, position: 'relative', overflow: 'hidden'}}>
                    {/* Progress bar background */}
                    <div 
                      style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: `${progressWidth}%`,
                        height: '100%',
                        backgroundColor: progressColor,
                        transition: 'width 0.6s ease-in-out',
                        zIndex: 1,
                      }}
                    ></div>
                    
                    {/* Content overlay */}
                    <div style={{...styles.summaryRowContent, position: 'relative', zIndex: 2}}>
                      <div style={styles.summaryItemDetails}>
                        <span style={styles.summaryItemName}>{item}</span>
                      </div>
                      <div style={styles.summaryAmountSection}>
                        <span style={styles.summaryAmount}>
            {isNaN(currentAmount) ? '$0.00' : `$${currentAmount.toFixed(2)}`}
                        </span>
                       
                      </div>
                    </div>
                  </div>
                );
              })}
              
              {sortedItemSummary.length === 0 && (
                <div style={styles.noData}>
                  <p>No summary data available</p>
                </div>
              )}
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

const styles = {
  pageWrapper: {
    minHeight: '100vh',
    backgroundColor: '#f5f7f5',
    fontFamily: 'system-ui, -apple-system, sans-serif',
  },
  container: {
    maxWidth: '900px',
    margin: '0 auto',
    padding: '20px',
  },
  pageHeader: {
    textAlign: 'center',
    marginBottom: '30px',
    paddingBottom: '20px',
    borderBottom: '3px solid #8BC34A',
  },
  title: {
    fontSize: '2rem',
    fontWeight: '700',
    color: '#2c3e50',
    margin: '0',
    letterSpacing: '-0.5px',
  },
  mainContent: {
    width: '100%',
  },
  tabs: {
    display: 'flex',
    justifyContent: 'center',
    marginBottom: '20px',
  },
  activeTab: {
    backgroundColor: '#8BC34A',
    color: '#fff',
    padding: '10px 20px',
    border: 'none',
    borderRadius: '5px 5px 0 0',
    cursor: 'pointer',
    marginRight: '10px',
    fontSize: '0.9rem',
    fontWeight: '500',
  },
  inactiveTab: {
    backgroundColor: '#e0e0e0',
    color: '#333',
    padding: '10px 20px',
    border: 'none',
    borderRadius: '5px 5px 0 0',
    cursor: 'pointer',
    marginRight: '10px',
    fontSize: '0.9rem',
    fontWeight: '500',
  },
  receiptsGrid: {
    display: 'flex',
    flexDirection: 'column',
    gap: '25px',
  },
  receiptContainer: {
    backgroundColor: '#ffffff',
    borderRadius: '10px',
    overflow: 'hidden',
    boxShadow: '0 2px 10px rgba(0,0,0,0.08)',
    border: '1px solid #e1e8e1',
  },
  receiptTitleBar: {
    backgroundColor: '#8BC34A',
    padding: '15px 20px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  receiptId: {
    fontSize: '1.1rem',
    fontWeight: '600',
    color: '#ffffff',
  },
  itemsCount: {
    fontSize: '0.9rem',
    color: '#ffffff',
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: '4px 12px',
    borderRadius: '15px',
  },
  itemsList: {
    padding: '15px',
    display: 'flex',
    flexDirection: 'column',
    gap: '10px',
  },
  itemCard: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '15px 20px',
    backgroundColor: '#ffffff',
    borderRadius: '8px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.08)',
    border: '1px solid #e1e8e1',
    transition: 'all 0.2s ease',
    minHeight: '50px',
  },
  itemName: {
    fontSize: '0.95rem',
    color: '#2c3e50',
    flex: 1,
  },
  itemPrice: {
    fontSize: '0.95rem',
    fontWeight: '600',
    color: '#8BC34A',
    minWidth: '80px',
    textAlign: 'right',
  },
  totalBar: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '15px 20px',
    backgroundColor: '#f8f9fa',
    borderTop: '2px solid #8BC34A',
  },
  totalLabel: {
    fontSize: '1.1rem',
    fontWeight: '600',
    color: '#2c3e50',
  },
  totalValue: {
    fontSize: '1.2rem',
    fontWeight: '700',
    color: '#8BC34A',
  },
  summaryTable: {
    padding: '20px',
    backgroundColor: '#ffffff',
    borderRadius: '10px',
    boxShadow: '0 2px 10px rgba(0,0,0,0.08)',
    display: 'flex',
    flexDirection: 'column',
    gap: '12px',
  },
  summaryTitle: {
    fontSize: '1.2rem',
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: '10px',
    marginTop: '0',
  },
  summaryCard: {
    padding: '0',
    backgroundColor: 'transparent',
    borderRadius: '8px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.08)',
    border: '1px solid #e1e8e1',
    transition: 'all 0.2s ease',
    minHeight: '50px',
  },
  summaryRowContent: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: '15px',
    padding: '15px 20px',
  },
  summaryItemDetails: {
    flex: '1',
    minWidth: '0',
  },
  summaryItemName: {
    fontSize: '0.95rem',
    color: '#2c3e50',
    fontWeight: '500',
  },
  summaryAmountSection: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    flex: '0 0 auto',
  },
  summaryAmount: {
    fontSize: '0.9rem',
    fontWeight: '600',
    color: '#2c3e50',
    minWidth: '70px',
    textAlign: 'right',
  },
  percentage: {
    fontSize: '0.8rem',
    color: '#666',
    fontWeight: '500',
  },
  loadingContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: '100vh',
    backgroundColor: '#f5f7f5',
  },
  loadingSpinner: {
    width: '40px',
    height: '40px',
    border: '4px solid #e3f2fd',
    borderTop: '4px solid #8BC34A',
    borderRadius: '50%',
    animation: 'spin 1s linear infinite',
    marginBottom: '15px',
  },
  loadingText: {
    fontSize: '1rem',
    color: '#2c3e50',
    margin: '0',
  },
  errorContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: '100vh',
    backgroundColor: '#f5f7f5',
    padding: '20px',
  },
  errorBox: {
    backgroundColor: '#ffffff',
    padding: '30px',
    borderRadius: '10px',
    boxShadow: '0 2px 10px rgba(0,0,0,0.08)',
    border: '1px solid #ff5252',
    textAlign: 'center',
  },
  errorText: {
    color: '#d32f2f',
    fontSize: '1rem',
    margin: '0',
  },
  noData: {
    textAlign: 'center',
    padding: '50px',
    backgroundColor: '#ffffff',
    borderRadius: '10px',
    boxShadow: '0 2px 10px rgba(0,0,0,0.08)',
    color: '#2c3e50',
  },
};

// Spinner animation
const styleElement = document.createElement('style');
styleElement.textContent = `
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  .summaryCard:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  }
`;
document.head.appendChild(styleElement);

export default ReceiptItemsPage;