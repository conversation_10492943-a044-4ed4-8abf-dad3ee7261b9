import React from 'react';

/**
 * Displays an error message to the user
 * @param {Object} props - Component props
 * @param {string} props.message - The error message to display
 * @param {string} [props.className] - Additional CSS classes
 * @returns {JSX.Element} Error message component
 */
const ErrorMessage = ({ message, className = '' }) => {
  return (
    <div className={`alert alert-danger ${className}`} role="alert">
      <h4 className="alert-heading">Error</h4>
      <p>{message || 'An unexpected error has occurred.'}</p>
      <hr />
      <p className="mb-0">
        Please try again later or contact support if the problem persists.
      </p>
    </div>
  );
};

export default ErrorMessage;