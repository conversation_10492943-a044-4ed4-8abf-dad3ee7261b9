import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  changeMonth,
  setLoading
} from '../../../../logic/redux/budgetSlice';
import {
  FaWallet,
  FaDollarSign,
  FaCreditCard,
  FaBuilding,
  FaChevronDown,
  FaChevronUp,
  FaArrowCircleRight,
  FaArrowCircleLeft,
  FaPlus
} from 'react-icons/fa';

const Budget2 = () => {
  const dispatch = useDispatch();
  const { currentMonth, currentYear, budgetData, loading, error } = useSelector((state) => state.budget);
  const [showAllTables, setShowAllTables] = useState(true);
  const [expandedCategories, setExpandedCategories] = useState({}); // Store expanded state for each category
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [editingSubcategory, setEditingSubcategory] = useState(null);
  const [editedBudget, setEditedBudget] = useState({ id: null, value: 0 });

  // Convert month index to name
  const monthNames = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];

  const handleToggleAllTables = () => {
    setShowAllTables(!showAllTables);
  };

  // Handle toggling of subcategories for each category
  const handleToggleCategory = (categoryId) => {
    setExpandedCategories((prevState) => ({
      ...prevState,
      [categoryId]: !prevState[categoryId],
    }));
  };

  // Fetch budget data when the component mounts or month changes
  useEffect(() => {
    dispatch(setLoading(true));
  }, [dispatch]); // This will trigger the fetchBudgetDataEpic

  if (loading) return (
    <div className="flex justify-center items-center h-screen">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      <p className="ml-4 text-gray-600">Loading budget data...</p>
    </div>
  );

  if (error) return (
    <div className="p-4 bg-red-100 text-red-700 rounded-md">
      <p>Error: {error}</p>
    </div>
  );

  // Calculate total actual for Category 1 (Income)
  const calculateCategory1Actual = () => {
    if (budgetData.length === 0) return 0;

    const category1 = budgetData[0]; // Get Category 1
    let totalActual = category1?.category?.actual || 0;

    // Add subcategory actual (spent) values
    category1?.category?.subCategories?.forEach((subcategory) => {
      totalActual += subcategory.spent || 0;
    });

    return totalActual;
  };

  const category1Actual = calculateCategory1Actual(); // Get actual total for Category 1

  // Calculate other totals for summary cards (Income, Budget, Actual, Remaining)
  const calculateTotals = () => {
    let totalBudget = 0;
    let totalActual = 0;
    let totalRemaining = 0;

    budgetData.forEach((category) => {
      totalBudget += category.category?.allocated || 0;
      totalActual += category.category?.actual || 0;
      totalRemaining += category.category?.remaining || 0;

      // Subcategories totals
      category.category?.subCategories?.forEach((subcategory) => {
        totalBudget += subcategory.budget || 0;
        totalActual += subcategory.spent || 0;  // Add subcategory spent to actual value
        totalRemaining += subcategory.remaining || 0;
      });
    });

    return { totalBudget, totalActual, totalRemaining };
  };

  const handleBudgetDoubleClick = (subcategory) => {
    setEditedBudget({ id: subcategory.id, value: subcategory.budget || 0 });
    setEditingSubcategory(subcategory.id);
  };

  const handleBudgetChange = (e) => {
    setEditedBudget((prev) => ({
      ...prev,
      value: Number(e.target.innerText) || 0, // Ensure it's a valid number
    }));
  };

  const saveBudgetChange = (categoryId, subcategoryId) => {
    setEditingSubcategory(null); // Exit edit mode

    // Find the updated subcategory in Redux state
    const category = budgetData.find((cat) => cat.id === categoryId);
    if (!category) {
      console.error("Category not found!");
      return;
    }

    const subcategory = category.category.subCategories.find((sub) => sub.id === subcategoryId);
    if (!subcategory) {
      console.error("Subcategory not found!");
      return;
    }

    const newAllocatedValue = Number(editedBudget.value);
    if (isNaN(newAllocatedValue)) {
      console.error("Invalid budget value");
      return;
    }

    const budgetId = subcategory.budgetId || subcategoryId;

    // Dispatch action to update budget
    dispatch({
      type: 'budget/updateBudgetValue',
      payload: {
        budgetId,
        categoryId,
        subcategoryId,
        newBudget: newAllocatedValue
      }
    });
  };

  const handlePopupToggle = () => {
    setIsPopupOpen(!isPopupOpen);
  };

  const handleSaveBudget = (budgetItem) => {
    console.log('Budget item saved:', budgetItem);
    setIsPopupOpen(false);
  };

  const { totalBudget, totalActual, totalRemaining } = calculateTotals();

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header Section */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Budget</h1>
        <div className="flex items-center space-x-4">
          <button
            className="text-blue-500 hover:text-blue-700 transition-colors"
            onClick={() => dispatch(changeMonth(-1))}
          >
            <FaArrowCircleLeft className="text-xl" />
          </button>
          <span className="text-lg font-medium">{monthNames[currentMonth]} {currentYear}</span>
          <button
            className="text-blue-500 hover:text-blue-700 transition-colors"
            onClick={() => dispatch(changeMonth(1))}
          >
            <FaArrowCircleRight className="text-xl" />
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-white rounded-lg shadow-md p-4 flex justify-between items-center">
          <div>
            <p className="text-gray-500 font-medium">Income</p>
            <p className="text-2xl font-bold">${category1Actual}</p>
          </div>
          <FaWallet className="text-blue-500 text-xl" />
        </div>
        <div className="bg-white rounded-lg shadow-md p-4 flex justify-between items-center">
          <div>
            <p className="text-gray-500 font-medium">Budget</p>
            <p className="text-2xl font-bold">${totalBudget}</p>
          </div>
          <FaDollarSign className="text-green-500 text-xl" />
        </div>
        <div className="bg-white rounded-lg shadow-md p-4 flex justify-between items-center">
          <div>
            <p className="text-gray-500 font-medium">Actual</p>
            <p className="text-2xl font-bold">${totalActual}</p>
          </div>
          <FaCreditCard className="text-purple-500 text-xl" />
        </div>
        <div className="bg-white rounded-lg shadow-md p-4 flex justify-between items-center">
          <div>
            <p className="text-gray-500 font-medium">Remaining</p>
            <p className={`text-2xl font-bold ${totalRemaining < 0 ? 'text-red-500' : 'text-green-500'}`}>
              ${totalRemaining}
            </p>
          </div>
          <FaBuilding className="text-gray-500 text-xl" />
        </div>
      </div>

      {/* Toggle Button with Dropdown Icon */}
      <div className="flex justify-between items-center mb-4">
        <button
          onClick={handleToggleAllTables}
          className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-3 py-2 rounded-md transition-colors"
        >
          {showAllTables ? <FaChevronDown /> : <FaChevronUp />}
        </button>
        <button
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center transition-colors"
          onClick={handlePopupToggle}
        >
          <FaPlus className="mr-2" /> Add Budget
        </button>
      </div>

      {/* Popup for adding budget */}
      {isPopupOpen && (
        <BudgetPopupComponent
          budgetData={budgetData}
          onSave={handleSaveBudget}
          onClose={handlePopupToggle}
        />
      )}

      {/* Budget Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="grid grid-cols-4 bg-gray-100 p-3 font-medium text-gray-700 border-b">
          <div className="text-left">Category</div>
          <div className="text-center">Budget</div>
          <div className="text-center">Actual</div>
          <div className="text-center">Remaining</div>
        </div>

        {budgetData.length > 0 ? (
          budgetData.map((category) => {
            const categoryTotalBudget = category.category?.subCategories?.reduce(
              (acc, subcategory) => acc + (subcategory.budget || 0), category.category?.allocated || 0
            );
            const categoryTotalActual = category.category?.subCategories?.reduce(
              (acc, subcategory) => acc + (subcategory.spent || 0), category.category?.actual || 0
            );
            const categoryTotalRemaining = category.category?.subCategories?.reduce(
              (acc, subcategory) => acc + (subcategory.remaining || 0), category.category?.remaining || 0
            );

            const isExpanded = expandedCategories[category.id] ?? showAllTables; // Check if category is expanded

            return (
              <React.Fragment key={category.id}>
                {/* Category Row */}
                <div
                  className="grid grid-cols-4 p-3 border-b cursor-pointer hover:bg-gray-50 transition-colors"
                  onClick={() => handleToggleCategory(category.id)}
                >
                  <div className="text-left font-medium pl-2">
                    {category.category?.category || "Unknown"}
                  </div>
                  <div className="text-center">{categoryTotalBudget}</div>
                  <div className="text-center">{categoryTotalActual}</div>
                  <div className={`text-center ${categoryTotalRemaining < 0 ? 'text-red-500' : 'text-green-500'}`}>
                    {categoryTotalRemaining}
                  </div>
                </div>

                {/* Subcategories */}
                {isExpanded && category.category?.subCategories?.length > 0 && (
                  <div className="bg-gray-50">
                    {category.category.subCategories
                      .filter((subcategory) => subcategory.budget !== 0) // Hide rows with budget 0
                      .map((subcategory) => (
                        <div key={subcategory.id} className="grid grid-cols-4 p-2 border-b border-gray-100 pl-8">
                          <div className="text-left text-gray-700">
                            {subcategory.subCategory || subcategory.customSubCategory || "N/A"}
                          </div>

                          {/* Budget Column (Editable on Double Click) */}
                          <div
                            className={`text-center cursor-pointer ${
                              editingSubcategory === subcategory.id ? 'outline outline-1 outline-blue-500' : ''
                            }`}
                            onDoubleClick={() => handleBudgetDoubleClick(subcategory)}
                            contentEditable={editingSubcategory === subcategory.id}
                            suppressContentEditableWarning={true}
                            onInput={handleBudgetChange}
                            onBlur={() => saveBudgetChange(category.id, subcategory.id)}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                e.preventDefault();
                                saveBudgetChange(category.id, subcategory.id);
                              }
                            }}
                          >
                            {subcategory.budget || 0}
                          </div>

                          {/* Display Actual Spent */}
                          <div className="text-center">{subcategory.spent || 0}</div>

                          {/* Display Remaining Amount */}
                          <div
                            className={`text-center ${
                              (subcategory.budget - (subcategory.spent || 0)) < 0 ? 'text-red-500' : 'text-green-500'
                            }`}
                          >
                            {subcategory.budget - (subcategory.spent || 0)}
                          </div>
                        </div>
                      ))
                    }
                  </div>
                )}
              </React.Fragment>
            );
          })
        ) : (
          <div className="p-4 text-center text-gray-500">No budget data available.</div>
        )}
      </div>
    </div>
  );
};

const BudgetPopupComponent = ({ onSave, onClose }) => {
  const dispatch = useDispatch();

  // State for categories and subcategories
  const [selectedCategory, setSelectedCategory] = useState("");
  const [categories, setCategories] = useState([]);
  const [selectedSubcategory, setSelectedSubcategory] = useState("");
  const [newBudgetAmount, setNewBudgetAmount] = useState(0);
  const [isRollover, setIsRollover] = useState(false);
  const [isExcluded, setIsExcluded] = useState(false);
  const [isSliding, setIsSliding] = useState(false);

  // States for subcategories (filtered and full list) and new subcategory input
  const [newSubcategory, setNewSubcategory] = useState("");
  const [subcategories, setSubcategories] = useState([]); // filtered for selected category
  const [allSubcategories, setAllSubcategories] = useState([]); // full list from backend

  // Dropdown state
  const [dropdownOpen, setDropdownOpen] = useState(false);

  // Handle slider change
  const handleSliderChange = (e) => {
    setNewBudgetAmount(parseInt(e.target.value, 10));
    setIsSliding(true);
  };

  // Fetch categories and subcategories when component mounts
  useEffect(() => {
    fetchCategories();
    fetchSubcategories();
  }, []);

  // Get cached data from Redux
  const cache = useSelector(state => state.cache);

  useEffect(() => {
    // Use cached data if available, otherwise fetch
    if (cache?.categoriesLoaded && cache?.categories?.length > 0) {
      console.log('✅ Using cached categories data in Budget2 component');
      setCategories(cache.categories);
    } else {
      fetchCategories();
    }

    if (cache?.subcategoriesLoaded && cache?.subcategories?.length > 0) {
      console.log('✅ Using cached subcategories data in Budget2 component');
      setAllSubcategories(cache.subcategories);
    } else {
      fetchSubcategories();
    }
  }, [cache?.categoriesLoaded, cache?.subcategoriesLoaded]);

  const fetchCategories = async () => {
    try {
      const response = await fetch(`http://${import.meta.env.VITE_API_URL}/api/categories/all`);
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      const data = await response.json();
      setCategories(data);
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  const fetchSubcategories = async () => {
    try {
      const response = await fetch(`http://${import.meta.env.VITE_API_URL}/api/subcategories/all`);
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      const data = await response.json();
      setAllSubcategories(data);
    } catch (error) {
      console.error("Error fetching subcategories:", error);
    }
  };

  // Filter subcategories based on the selected category's id
  useEffect(() => {
    if (selectedCategory) {
      const filteredSubs = allSubcategories.filter(
        (sub) =>
          sub.category &&
          sub.category.id.toString() === selectedCategory.toString()
      );
      setSubcategories(filteredSubs);
    } else {
      setSubcategories([]);
    }
  }, [selectedCategory, allSubcategories]);

  // Add a new subcategory (temporary, local only)
  const handleAddSubcategory = () => {
    if (newSubcategory.trim() !== "") {
      const newSub = {
        id: Date.now(), // temporary id
        category: { id: parseInt(selectedCategory, 10) },
        subCategory: newSubcategory,
      };
      setSubcategories([...subcategories, newSub]);
      setAllSubcategories([...allSubcategories, newSub]); // if you want to update full list
      setNewSubcategory("");
    }
  };

  // Submit the new budget item
  const handleAddToBudget = () => {
    if (!selectedCategory || !selectedSubcategory) {
      alert("Please select a category and subcategory.");
      return;
    }

    const selectedCategoryData = categories.find(
      (cat) => cat.id.toString() === selectedCategory.toString()
    );
    const selectedSubcategoryData = subcategories.find(
      (sub) => sub.subCategory === selectedSubcategory
    );

    if (!selectedCategoryData || !selectedSubcategoryData) {
      alert("Invalid category or subcategory.");
      return;
    }

    // Dispatch action to add budget
    dispatch({
      type: 'budget/addBudgetItem',
      payload: {
        categoryId: selectedCategoryData.id,
        subCategoryId: selectedSubcategoryData.id || null,
        customSubCategory: selectedSubcategory,
        allocated: newBudgetAmount,
        isRollover,
        isExcluded,
      }
    });

    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[85vh] overflow-y-auto">
        <div className="p-4 border-b">
          <h3 className="text-lg font-semibold text-gray-800">Budget Details</h3>
        </div>

        <div className="p-6">
          {/* Category Selection */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
            <select
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              onChange={(e) => {
                const selectedValue = e.target.value;
                setSelectedCategory(selectedValue);
                setSelectedSubcategory(""); // reset subcategory on category change
              }}
              value={selectedCategory}
            >
              <option value="">Select Category</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.category}
                </option>
              ))}
            </select>
          </div>

          {/* Subcategory Dropdown */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">Subcategory</label>
            <div className="relative">
              <div
                className="w-full p-2 border border-gray-300 rounded-md flex justify-between items-center cursor-pointer"
                onClick={() => setDropdownOpen(!dropdownOpen)}
              >
                <span>{selectedSubcategory || "Select a Subcategory"}</span>
                <span className="text-gray-500">&#9662;</span>
              </div>

              {dropdownOpen && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg">
                  <div className="p-2 border-b flex">
                    <input
                      type="text"
                      value={newSubcategory}
                      onChange={(e) => setNewSubcategory(e.target.value)}
                      placeholder="Add new subcategory"
                      className="flex-1 p-1 border border-gray-300 rounded-md"
                    />
                    <button
                      onClick={handleAddSubcategory}
                      className="ml-2 px-2 bg-blue-500 text-white rounded-md"
                    >
                      +
                    </button>
                  </div>
                  <div className="max-h-40 overflow-y-auto">
                    {subcategories.map((sub) => (
                      <div
                        key={sub.id}
                        className="p-2 hover:bg-gray-100 cursor-pointer"
                        onClick={() => {
                          setSelectedSubcategory(sub.subCategory);
                          setDropdownOpen(false);
                        }}
                      >
                        {sub.subCategory}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Budget Amount Slider */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">Budget Amount</label>
            <div className="text-right font-medium text-lg mb-2">${newBudgetAmount.toLocaleString()}</div>
            <input
              type="range"
              min="0"
              max="10000"
              step="100"
              value={newBudgetAmount}
              onChange={handleSliderChange}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
          </div>

          {/* Rollover and Exclusion Options */}
          <div className="flex justify-between items-center mb-4">
            <div className="relative group">
              <label className="text-sm font-medium text-gray-700 cursor-help">
                Make this a rollover budget
                <div className="absolute bottom-full left-0 hidden group-hover:block w-48 bg-gray-800 text-white text-xs rounded p-2 mb-1">
                  Unused budget will carry over to the next month
                </div>
              </label>
            </div>
            <div className="relative inline-block w-10 mr-2 align-middle select-none">
              <input
                type="checkbox"
                checked={isRollover}
                onChange={() => setIsRollover(!isRollover)}
                className="sr-only"
                id="toggle-rollover"
              />
              <label
                htmlFor="toggle-rollover"
                className={`block overflow-hidden h-6 rounded-full cursor-pointer ${
                  isRollover ? 'bg-blue-500' : 'bg-gray-300'
                }`}
              >
                <span
                  className={`block h-5 w-5 rounded-full bg-white shadow transform transition-transform ${
                    isRollover ? 'translate-x-5' : 'translate-x-0'
                  }`}
                />
              </label>
            </div>
          </div>

          <div className="flex justify-between items-center mb-4">
            <div className="relative group">
              <label className="text-sm font-medium text-gray-700 cursor-help">
                Exclude from budget
                <div className="absolute bottom-full left-0 hidden group-hover:block w-48 bg-gray-800 text-white text-xs rounded p-2 mb-1">
                  This item won't appear in budget summary reports
                </div>
              </label>
            </div>
            <div className="relative inline-block w-10 mr-2 align-middle select-none">
              <input
                type="checkbox"
                checked={isExcluded}
                onChange={() => setIsExcluded(!isExcluded)}
                className="sr-only"
                id="toggle-exclude"
              />
              <label
                htmlFor="toggle-exclude"
                className={`block overflow-hidden h-6 rounded-full cursor-pointer ${
                  isExcluded ? 'bg-blue-500' : 'bg-gray-300'
                }`}
              >
                <span
                  className={`block h-5 w-5 rounded-full bg-white shadow transform transition-transform ${
                    isExcluded ? 'translate-x-5' : 'translate-x-0'
                  }`}
                />
              </label>
            </div>
          </div>
        </div>

        <div className="p-4 border-t flex justify-end space-x-2">
          <button
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
            onClick={onClose}
          >
            Cancel
          </button>
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
            onClick={handleAddToBudget}
          >
            Save Budget
          </button>
        </div>
      </div>
    </div>
  );
};

export default Budget2;
