    import React from 'react'
    import { 
        FaMoneyBillWave, 
        FaArrowDown, 
        FaArrowUp, 
        FaDollarSign, 
        FaTrophy, 
        FaExclamationTriangle 
    } from 'react-icons/fa';

    const TransactionStatCard = ({darkMode,summary}) => {

           // Additional data for the cards
            const additionalData1 = [
              { 
                label: 'Total Transaction', 
                value: summary?.totalTransaction || '$0',
                icon: FaMoneyBillWave,
                color: 'from-lime-400 to-lime-500',
                bgColor: 'bg-lime-50',
                darkBgColor: 'bg-lime-900/20'
              },
              { 
                label: 'Total Debit', 
                value: `$${summary?.totalDebit || 0}`,
                icon: FaArrowDown,
                color: 'from-red-400 to-red-500',
                bgColor: 'bg-red-50',
                darkBgColor: 'bg-red-900/20'
              },
              { 
                label: 'Total Credit', 
                value: `$${summary?.totalCredit || 0}`,
                icon: FaArrowUp,
                color: 'from-green-400 to-green-500',
                bgColor: 'bg-green-50',
                darkBgColor: 'bg-green-900/20'
              },
            ];
        
            const additionalData2 = [
              { 
                label: 'Total Amount', 
                value: `$${summary?.totalAmount || 0}`,
                icon: FaDollarSign,
                color: 'from-lime-400 to-lime-600',
                bgColor: 'bg-lime-50',
                darkBgColor: 'bg-lime-900/20'
              },
              { 
                label: 'Largest Transaction', 
                value: `$${summary?.largestTransaction || 0}`,
                icon: FaTrophy,
                color: 'from-yellow-400 to-yellow-500',
                bgColor: 'bg-yellow-50',
                darkBgColor: 'bg-yellow-900/20'
              },
              { 
                label: 'Dispute Transaction', 
                value: `${summary?.disputeTransaction || 0}`,
                icon: FaExclamationTriangle,
                color: 'from-orange-400 to-orange-500',
                bgColor: 'bg-orange-50',
                darkBgColor: 'bg-orange-900/20'
              },
            ];
        
    return (
        <div className="relative z-10 max-w-7xl mx-auto">
            {/* Single grid for all cards in 3 columns */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {additionalData1.map((item, index) => {
                const IconComponent = item.icon;
                return (
                <div
                    key={`card-${index}`}
                    className={`
                    group relative overflow-hidden rounded-xl shadow-lg hover:shadow-xl
                    transform hover:-translate-y-2 transition-all duration-300 ease-out cursor-pointer
                    border border-lime-200/50 hover:border-lime-300 w-full
                    ${darkMode
                        ? `${item.darkBgColor} hover:bg-opacity-40 backdrop-blur-lg`
                        : `${item.bgColor} hover:bg-opacity-80`
                    }
                    `}
                >
                    {/* Top Decorative Bar */}
                    <div className={`
                    h-1 w-full bg-gradient-to-r ${item.color}
                    transform scale-x-0 group-hover:scale-x-100 
                    transition-transform duration-300 origin-left
                    `}></div>
                            
                    <div className="p-4 relative z-10">
                    {/* Header with Icon and Label on Left */}
                    <div className="flex items-center mb-4">
                        <div className={`
                        p-2 rounded-lg bg-gradient-to-br ${item.color}
                        shadow-lg transform group-hover:rotate-6 group-hover:scale-105
                        transition-all duration-300 mr-3
                        `}>
                        <IconComponent className="text-white text-lg" />
                        </div>
                        
                        {/* Label next to icon */}
                        <label className={`
                        text-sm font-bold uppercase tracking-wide
                        ${darkMode ? 'text-gray-300' : 'text-gray-600'}
                        group-hover:text-lime-600 transition-colors duration-300
                        `}>
                        {item.label}
                        </label>

                        {/* Fixed Dot - moved to far right */}
                        <div className="ml-auto">
                        <div className={`
                            w-2 h-2 rounded-full bg-gradient-to-r ${item.color}
                        `}></div>
                        </div>
                    </div>
                                
                    {/* Value - Centered */}
                    <div className="text-center">
                        <p className={`
                        text-2xl font-bold tracking-tight
                        ${darkMode ? 'text-white' : 'text-gray-900'}
                        group-hover:text-lime-700 transition-colors duration-300
                        `}>
                        {item.value}
                        </p>
                    </div>
                    </div>
                </div>
                );
            })}
            
            {additionalData2.map((item, index) => {
                const IconComponent = item.icon;
                return (
                <div
                    key={`card-${index + 3}`}
                    className={`
                    group relative overflow-hidden rounded-xl shadow-lg hover:shadow-xl
                    transform hover:-translate-y-2 transition-all duration-300 ease-out cursor-pointer
                    border border-lime-200/50 hover:border-lime-300 w-full
                    ${darkMode
                        ? `${item.darkBgColor} hover:bg-opacity-40 backdrop-blur-lg`
                        : `${item.bgColor} hover:bg-opacity-80`
                    }
                    `}
                >
                    {/* Top Decorative Bar */}
                    <div className={`
                    h-1 w-full bg-gradient-to-r ${item.color}
                    transform scale-x-0 group-hover:scale-x-100 
                    transition-transform duration-300 origin-left
                    `}></div>
                            
                    <div className="p-4 relative z-10">
                    {/* Header with Icon and Label on Left */}
                    <div className="flex items-center mb-4">
                        <div className={`
                        p-2 rounded-lg bg-gradient-to-br ${item.color}
                        shadow-lg transform group-hover:rotate-6 group-hover:scale-105
                        transition-all duration-300 mr-3
                        `}>
                        <IconComponent className="text-white text-lg" />
                        </div>
                        
                        {/* Label next to icon */}
                        <label className={`
                        text-sm font-bold uppercase tracking-wide
                        ${darkMode ? 'text-gray-300' : 'text-gray-600'}
                        group-hover:text-lime-600 transition-colors duration-300
                        `}>
                        {item.label}
                        </label>

                        {/* Fixed Dot - moved to far right */}
                        <div className="ml-auto">
                        <div className={`
                            w-2 h-2 rounded-full bg-gradient-to-r ${item.color}
                        `}></div>
                        </div>
                    </div>
                                
                    {/* Value - Centered */}
                    <div className="text-center">
                        <p className={`
                        text-2xl font-bold tracking-tight
                        ${darkMode ? 'text-white' : 'text-gray-900'}
                        group-hover:text-lime-700 transition-colors duration-300
                        `}>
                        {item.value}
                        </p>
                    </div>
                    </div>
                </div>
                );
            })}
            </div>
        </div>
        )
    }

    export default TransactionStatCard