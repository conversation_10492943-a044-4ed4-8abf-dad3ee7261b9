import React, { useEffect, useRef, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { FaPlus, FaEdit, FaTrash, FaInfoCircle } from 'react-icons/fa';
import { getCurrentUserId } from '../../utils/AuthUtil';
import {
  toggleCreateRuleModal,
  updateNewRule,
  resetNewRule,
  setLoading,
  clearMessages,
  createBudgetRule,
  setEditingRule,
} from '../../../../logic/redux/budgetRulesSlice';
import { themeClasses } from '../../utils/tailwindUtils';
import PaymentLoader from '../load/PaymentLoader';

const BudgetRulePage = ({ darkMode }) => {
  const dispatch = useDispatch();
  const {
    budgetRules,
    isCreateRuleModalOpen,
    subcategories,
    newBudgetRule,
    loading,
    error,
    successMessage,
    editingRule,
    accounts,
  } = useSelector((state) => state.budgetRule);

  const [formErrors, setFormErrors] = useState({
    merchantNamePattern: '',
    thresholdAmount: '',
    fromSubCategoryId: '',
    accountId: '',
    toSubCategoryId: '',
    renamedMerchant: '',
    general: '',
  });

  const [selectedFields, setSelectedFields] = useState({
    merchantNamePattern: false,
    thresholdAmount: false,
    fromSubCategoryId: false,
    renamedMerchant: false,
    cascadeFlag: false,
    accountId: false,
  });

  const [conditionType, setConditionType] = useState('MERCHANT');
  const [tooltip, setTooltip] = useState({ open: false, rule: null, x: 0, y: 0 });
  const previousFocus = useRef(null);
  const [deleteConfirmation, setDeleteConfirmation] = useState({
    open: false,
    ruleId: null,
    isDeleting: false,
  });

  const userId = getCurrentUserId();

  useEffect(() => {
    setLoading(true);
    dispatch({ type: 'budgetRule/fetchSubcategories', payload: userId });
    dispatch({ type: 'budgetRule/fetchBudgetRules', payload: userId });
    dispatch({ type: 'budgetRule/fetchAccounts', payload: userId });
  }, [userId, dispatch]);

  useEffect(() => {
    if (successMessage === 'Rule deleted successfully') {
      const timer = setTimeout(() => {
        setDeleteConfirmation({ open: false, ruleId: null, isDeleting: false });
        dispatch(clearMessages());
      }, 2000);
      return () => clearTimeout(timer);
    }
    if (successMessage && successMessage !== 'Rule deleted successfully' && isCreateRuleModalOpen && !loading && !error) {
      const timer = setTimeout(() => {
        dispatch(resetNewRule());
        dispatch(setEditingRule(null));
        dispatch(clearMessages());
        setFormErrors({
          merchantNamePattern: '',
          thresholdAmount: '',
          fromSubCategoryId: '',
          accountId: '',
          toSubCategoryId: '',
          renamedMerchant: '',
          general: '',
        });
        setSelectedFields({
          merchantNamePattern: false,
          thresholdAmount: false,
          fromSubCategoryId: false,
          renamedMerchant: false,
          cascadeFlag: false,
          accountId: false,
        });
        setConditionType('MERCHANT');
        // APT-176 fix for closing the modal after creating/updating rule
        dispatch(toggleCreateRuleModal(false)); // Close the modal
      }, 2000);
      return () => clearTimeout(timer);
    }
    if (error && deleteConfirmation.isDeleting) {
      setDeleteConfirmation((prev) => ({ ...prev, isDeleting: false }));
    }
  }, [successMessage, error, loading, dispatch, isCreateRuleModalOpen]);

  const handleCloseRuleModal = () => {
    dispatch(toggleCreateRuleModal(false));
    dispatch(resetNewRule());
    dispatch(setEditingRule(null));
    dispatch(clearMessages());
    setFormErrors({
      merchantNamePattern: '',
      thresholdAmount: '',
      fromSubCategoryId: '',
      accountId: '',
      toSubCategoryId: '',
      renamedMerchant: '',
      general: '',
    });
    setSelectedFields({
      merchantNamePattern: false,
      thresholdAmount: false,
      fromSubCategoryId: false,
      renamedMerchant: false,
      cascadeFlag: false,
      accountId: false,
    });
    setConditionType('MERCHANT');
  };

  const handleRuleInputChange = (field) => (event) => {
    const value = field === 'merchantNamePattern' || field === 'renamedMerchant' || field === 'tags' || field === 'goal'
      ? event.target.value
      : Number(event.target.value);
    dispatch(updateNewRule({ [field]: value }));
  };

  const handleCategoryChange = (field) => (e) => {
    if (field === 'fromSubCategoryId') {
      const fromSubCatId = Number(e.target.value);
      const selectedSubcategory = subcategories.find((subcat) => subcat.subCategoryId === fromSubCatId);
      
      dispatch(
        updateNewRule({
          fromSubCategoryId: fromSubCatId,
          fromCategoryId: selectedSubcategory?.categoryId || null,
        })
      );
    }

    if (field === 'toSubCategoryId') {
      const [toSubCatId, type] = e.target.value.split('|');
      const selectedSubcategory = subcategories.find(
        (subcat) =>
          String(subcat.subCategoryId) === toSubCatId &&
          String(subcat.type) === type
      );
      
      if (selectedSubcategory) {
        const updateData = {
          toCategoryId: selectedSubcategory.categoryId,
          toSubCategoryId: type === 'standard' ? Number(toSubCatId) : null,
          toCustomSubCategoryId: type === 'custom' ? Number(toSubCatId) : null,
        };
        dispatch(updateNewRule(updateData));
        
        if (!toSubCatId) {
          setFormErrors((prev) => ({ ...prev, toSubCategoryId: 'Target subcategory is required' }));
        } else {
          setFormErrors((prev) => ({ ...prev, toSubCategoryId: '' }));
        }
      }
    }

    if (field === 'fromSubCategoryId' || field === 'toSubCategoryId') {
      validateCategoryMatch();
    }
  };

  const handleFieldToggle = (field) => {
    setSelectedFields((prev) => {
      const newSelected = { ...prev, [field]: !prev[field] };
      if (!newSelected[field]) {
        if (field === 'thresholdAmount') {
          dispatch(updateNewRule({ thresholdAmount: 0, amountType: null, amountMatch: null }));
        } else if (field === 'renamedMerchant') {
          dispatch(updateNewRule({ renamedMerchant: '' }));
        } else if (field === 'cascadeFlag') {
          dispatch(updateNewRule({ cascadeFlag: false }));
        } else if (field === 'merchantNamePattern') {
          dispatch(updateNewRule({ merchantNamePattern: null, merchantMatchRegex: false }));
        }
        setFormErrors((prev) => ({ ...prev, [field]: '' }));
      } else if (field === 'thresholdAmount') {
        dispatch(updateNewRule({
          amountType: newBudgetRule.amountType || 'credit',
          amountMatch: newBudgetRule.amountMatch || 'greater',
        }));
      }
      return newSelected;
    });
  };

  const validateCategoryMatch = () => {
    const fromCategoryId = newBudgetRule.fromSubCategoryId
      ? subcategories.find((subcat) => subcat.subCategoryId === newBudgetRule.fromSubCategoryId)?.categoryId
      : null;
    const toCategoryId = newBudgetRule.toCategoryId;

    if (selectedFields.fromSubCategoryId && fromCategoryId && toCategoryId && fromCategoryId !== toCategoryId) {
      setFormErrors((prev) => ({
        ...prev,
        fromSubCategoryId: 'Source category ID must match target category ID',
      }));
    } else {
      setFormErrors((prev) => ({ ...prev, fromSubCategoryId: '' }));
    }
  };

  const handleSaveRule = () => {
    setFormErrors({
      merchantNamePattern: '',
      thresholdAmount: '',
      fromSubCategoryId: '',
      accountId: '',
      toSubCategoryId: '',
      renamedMerchant: '',
      general: '',
    });

    let hasErrors = false;

    if (
      !selectedFields.merchantNamePattern &&
      !selectedFields.thresholdAmount &&
      !selectedFields.fromSubCategoryId &&
      !selectedFields.accountId
    ) {
      setFormErrors((prev) => ({
        ...prev,
        general: 'At least one of Merchants, Amount, Source Category, or Account must be selected',
      }));
      hasErrors = true;
    }

    if (selectedFields.merchantNamePattern && !newBudgetRule.merchantNamePattern?.trim()) {
      setFormErrors((prev) => ({
        ...prev,
        merchantNamePattern: 'Merchant name is required',
      }));
      hasErrors = true;
    }

    if (selectedFields.thresholdAmount && (!newBudgetRule.thresholdAmount || newBudgetRule.thresholdAmount <= 0)) {
      setFormErrors((prev) => ({
        ...prev,
        thresholdAmount: 'Amount is required and must be greater than 0',
      }));
      hasErrors = true;
    }

    if (selectedFields.fromSubCategoryId && !newBudgetRule.fromSubCategoryId) {
      setFormErrors((prev) => ({
        ...prev,
        fromSubCategoryId: 'Source subcategory is required',
      }));
      hasErrors = true;
    }

    if (selectedFields.accountId && !newBudgetRule.accountId) {
      setFormErrors((prev) => ({
        ...prev,
        accountId: 'Account is required',
      }));
      hasErrors = true;
    }

    if (!newBudgetRule.toSubCategoryId && !newBudgetRule.toCustomSubCategoryId) {
      setFormErrors((prev) => ({
        ...prev,
        toSubCategoryId: 'Target subcategory is required',
      }));
      hasErrors = true;
    }

    if (selectedFields.renamedMerchant && !newBudgetRule.renamedMerchant?.trim()) {
      setFormErrors((prev) => ({
        ...prev,
        renamedMerchant: 'Renamed merchant is required',
      }));
      hasErrors = true;
    }

    validateCategoryMatch();
    if (formErrors.fromSubCategoryId) hasErrors = true;

    if (hasErrors) {
      dispatch(setLoading(false));
      return;
    }

    dispatch(setLoading(true));
    const ruleData = {
      ...newBudgetRule,
      userId: userId,
      merchantMatchRegex: selectedFields.merchantNamePattern ? (newBudgetRule.merchantMatchRegex ? true : false) : false,
      merchantNamePattern: selectedFields.merchantNamePattern ? newBudgetRule.merchantNamePattern : null,
      amountType: selectedFields.thresholdAmount ? (newBudgetRule.amountType || 'credit') : null,
      amountMatch: selectedFields.thresholdAmount ? (newBudgetRule.amountMatch || 'greater') : null,
      thresholdAmount: selectedFields.thresholdAmount ? newBudgetRule.thresholdAmount : 0,
      fromSubCategoryId: selectedFields.fromSubCategoryId ? newBudgetRule.fromSubCategoryId : null,
      fromCategoryId: selectedFields.fromSubCategoryId
        ? newBudgetRule.fromCategoryId
        : newBudgetRule.toCategoryId,
      accountId: selectedFields.accountId ? newBudgetRule.accountId : null,
      renamedMerchant: selectedFields.renamedMerchant ? newBudgetRule.renamedMerchant : null,
      cascadeFlag: newBudgetRule.cascadeFlag || false,
      hideTransactionsFlag: newBudgetRule.hideTransactionsFlag || false,
      tags: newBudgetRule.tags || null,
      goal: newBudgetRule.goal || null,
      conditionType,
      isActive: true,
      ruleType: newBudgetRule.ruleType || 'CUSTOM',
    };
    dispatch(createBudgetRule(ruleData));
  };

  const handleDelete = (ruleId) => {
    previousFocus.current = document.activeElement;
    setDeleteConfirmation({ open: true, ruleId, isDeleting: false });
  };

  const handleConfirmDelete = () => {
    setDeleteConfirmation((prev) => ({ ...prev, isDeleting: true }));
    dispatch({ type: 'budgetRule/deleteBudgetRule', payload: deleteConfirmation.ruleId });
  };

  const handleCancelDelete = () => {
    setDeleteConfirmation({ open: false, ruleId: null, isDeleting: false });
    dispatch(clearMessages());
  };

  const handleEdit = (rule) => {
    dispatch(setEditingRule(rule));
    dispatch(toggleCreateRuleModal(true));
    setConditionType(rule.conditionType || 'MERCHANT');
    setSelectedFields({
      merchantNamePattern: !!rule.merchantNamePattern,
      thresholdAmount: rule.thresholdAmount > 0,
      fromSubCategoryId: !!rule.fromSubCategoryId,
      renamedMerchant: !!rule.renamedMerchant,
      cascadeFlag: !!rule.cascadeFlag,
      accountId: !!rule.accountId,
    });
    dispatch(
      updateNewRule({
        id: rule.id,
        userId: rule.userId,
        ruleType: rule.ruleType,
        fromCategoryId: rule.fromCategoryId,
        fromSubCategoryId: rule.fromSubCategoryId,
        toCategoryId: rule.toCategoryId,
        toSubCategoryId: rule.toSubCategoryId,
        toCustomSubCategoryId: rule.toCustomSubCategoryId,
        conditionType: rule.conditionType,
        thresholdAmount: rule.thresholdAmount,
        thresholdPercentage: rule.thresholdPercentage,
        transferDay: rule.transferDay,
        merchantNamePattern: rule.merchantNamePattern,
        isActive: rule.isActive,
        maxTransferAmount: rule.maxTransferAmount,
        minTransferAmount: rule.minTransferAmount,
        maxTransferPercent: rule.maxTransferPercent,
        minTransferPercent: rule.minTransferPercent,
        executionFrequency: rule.executionFrequency,
        renamedMerchant: rule.renamedMerchant || '',
        cascadeFlag: rule.cascadeFlag,
        merchantMatchRegex: rule.merchantMatchRegex || false,
        amountType: rule.amountType,
        amountMatch: rule.amountMatch,
        accountId: rule.accountId,
        hideTransactionsFlag: rule.hideTransactionsFlag,
        tags: rule.tags,
        goal: rule.goal,
      })
    );
  };

  const handleInfoClick = (event, rule) => {
    event.preventDefault();
    const rect = event.currentTarget.getBoundingClientRect();
    const scrollX = window.scrollX;
    const scrollY = window.scrollY;
    const tooltipWidth = 250;
    let x = rect.left + scrollX - tooltipWidth - 10;
    let y = rect.top + scrollY;
    if (x < scrollX) x = rect.left + scrollX + 20;
    if (y < scrollY) y = scrollY;
    setTooltip({ open: true, rule, x, y });
  };

  const handleCloseTooltip = (event) => {
    if ((event.type === 'keydown' && event.key === 'Escape') ||
       (event.type === 'click' && !event.target.closest('.tooltip'))){
      setTooltip({ open: false, rule: null, x: 0, y: 0 });
    }
  };

  useEffect(() => {
    if (tooltip.open) {
      const timeoutId = setTimeout(() => {
        document.addEventListener('click', handleCloseTooltip);
      }, 0);
      return () => {
        clearTimeout(timeoutId);
        document.removeEventListener('click', handleCloseTooltip);
      };
    }
  }, [tooltip.open]);

  if (loading) {
    return (
      <div className={`min-h-screen w-full p-8 flex flex-col justify-center items-center ${darkMode ? 'bg-gray-900' : 'bg-white'}`}>
        <PaymentLoader darkMode={darkMode} />
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-gray-900' : 'bg-white'} transition-colors duration-200`}>
      {/* Header Section */}
      <div className="px-8 pt-8 pb-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
                Budget Rules
              </h1>
              <p className={`text-base ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Manage automated rules for categorizing and organizing your transactions
              </p>
            </div>
            <button
              className={`inline-flex items-center gap-3 px-6 py-3 rounded-xl text-white font-semibold text-sm transition-all duration-200 transform hover:scale-105 shadow-lg ${
                darkMode 
                  ? 'bg-gray-700 hover:bg-gray-600 shadow-gray-800/25' 
                  : 'bg-[#8bc34a] hover:bg-[#6ec122] shadow-green-500/25'
              }`}
              onClick={() => dispatch(toggleCreateRuleModal(true))}
            >
              <FaPlus className="w-4 h-4" /> 
              Create Rule
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-8 pb-8">
        <div className="max-w-7xl mx-auto">
          <div className={`bg-gradient-to-br ${
            darkMode 
              ? 'from-gray-800 to-gray-850 border-gray-700' 
              : 'from-white to-gray-50 border-gray-200'
            } border rounded-2xl shadow-xl overflow-hidden backdrop-blur-sm`}>
            
            {/* Table Container */}
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className={`${
                  darkMode 
                    ? 'bg-gradient-to-r from-gray-700 to-gray-750' 
                    : 'bg-gradient-to-r from-gray-50 to-gray-100'
                  }`}>
                  <tr>
                    {/* APT-195 - removing rule name */}
                    {/* {['Rule Name', 'Merchant', 'Amount', 'Account', 'From Category', 'To Category', 'Actions'].map((header) => ( */}
                    {['Merchant', 'Amount', 'Account', 'From Category', 'To Category', 'Actions'].map((header) => (
                      <th key={header} className={`px-6 py-4 text-left text-sm font-semibold tracking-wide ${
                        darkMode ? 'text-gray-200' : 'text-gray-700'
                      }`}>
                        {header}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  {budgetRules.length === 0 ? (
                    <tr>
                      <td colSpan={7} className="text-center py-12">
                        <div className="flex flex-col items-center gap-3">
                          <div className={`w-16 h-16 rounded-full flex items-center justify-center ${
                            darkMode ? 'bg-gray-700' : 'bg-gray-100'
                          }`}>
                            <FaPlus className={`w-6 h-6 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                          </div>
                          <div>
                            <h3 className={`text-lg font-medium ${darkMode ? 'text-gray-200' : 'text-gray-900'} mb-1`}>
                              No budget rules found
                            </h3>
                            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                              Create your first rule to get started with automated transaction categorization
                            </p>
                          </div>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    budgetRules.map((rule, index) => (
                      <tr key={rule.id} className={`group hover:bg-opacity-50 transition-all duration-150 ${
                        darkMode 
                          ? 'hover:bg-gray-700' 
                          : 'hover:bg-gray-50'
                        } ${index % 2 === 0 ? (darkMode ? 'bg-gray-800/30' : 'bg-gray-50/30') : ''}`}>
                        
                        {/* APT-195 - removing rule name */}
                        {/* <td className={`px-6 py-4 ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                          <div className="flex items-center gap-2">
                            <div className={`w-2 h-8 rounded-full ${
                              darkMode ? 'bg-gray-600' : 'bg-[#8bc34a]'
                            }`}></div>
                            <span className="font-medium">{rule.ruleType}</span>
                          </div>
                        </td> */}
                        
                        <td className={`px-6 py-4 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                          {rule.merchantNamePattern ? (
                            <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                              darkMode 
                                ? 'bg-blue-900/30 text-blue-300 border border-blue-700/50' 
                                : 'bg-blue-100 text-blue-800 border border-blue-200'
                            }`}>
                              {rule.merchantNamePattern}
                            </span>
                          ) : (
                            <span className={`text-sm ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}>—</span>
                          )}
                        </td>
                        
                        <td className={`px-6 py-4 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                          {rule.thresholdAmount ? (
                            <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                              darkMode 
                                ? 'bg-green-900/30 text-green-300 border border-green-700/50' 
                                : 'bg-green-100 text-green-800 border border-green-200'
                            }`}>
                              ${rule.thresholdAmount}
                            </span>
                          ) : (
                            <span className={`text-sm ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}>—</span>
                          )}
                        </td>
                        
                        <td className={`px-6 py-4 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                          {rule.accountName || <span className={`text-sm ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}>—</span>}
                        </td>
                        
                        <td className={`px-6 py-4 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                          {rule.fromCatName || <span className={`text-sm ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}>—</span>}
                        </td>
                        
                        <td className={`px-6 py-4 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                          {rule.toCatName ? (
                            <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                              darkMode 
                                ? 'bg-purple-900/30 text-purple-300 border border-purple-700/50' 
                                : 'bg-purple-100 text-purple-800 border border-purple-200'
                            }`}>
                              {rule.toCatName}
                            </span>
                          ) : (
                            <span className={`text-sm ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}>—</span>
                          )}
                        </td>
                        
                        <td className="px-6 py-4">
                          <div className="flex items-center gap-2">
                            <button
                              className={`p-2 rounded-lg transition-all duration-200 hover:scale-110 ${
                                darkMode 
                                  ? 'text-gray-400 hover:text-gray-200 hover:bg-gray-700' 
                                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                              }`}
                              onClick={(e) => handleInfoClick(e, rule)}
                              title="View Rule Details"
                              aria-label={`View details for rule ${rule.ruleType}`}
                            >
                              <FaInfoCircle className="w-4 h-4" />
                            </button>
                            
                            <button
                              className={`p-2 rounded-lg transition-all duration-200 hover:scale-110 ${
                                darkMode 
                                  ? 'text-blue-400 hover:text-blue-300 hover:bg-blue-900/30' 
                                  : 'text-blue-600 hover:text-blue-700 hover:bg-blue-50'
                              }`}
                              onClick={() => handleEdit(rule)}
                              title="Edit Rule"
                              aria-label={`Edit rule ${rule.ruleType}`}
                            >
                              <FaEdit className="w-4 h-4" />
                            </button>
                            
                            <button
                              className={`p-2 rounded-lg transition-all duration-200 hover:scale-110 ${
                                darkMode 
                                  ? 'text-red-400 hover:text-red-300 hover:bg-red-900/30' 
                                  : 'text-red-600 hover:text-red-700 hover:bg-red-50'
                              }`}
                              onClick={() => handleDelete(rule.id)}
                              title="Delete Rule"
                              aria-label={`Delete rule ${rule.ruleType}`}
                              tabIndex={deleteConfirmation.open ? -1 : 0}
                            >
                              <FaTrash className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Tooltip */}
      {tooltip.open && tooltip.rule && (
        <div
          className="tooltip fixed z-50 pointer-events-none"
          style={{ left: tooltip.x, top: tooltip.y }}
        >
          <div className={`w-72 p-4 rounded-xl shadow-2xl border backdrop-blur-sm ${
            darkMode 
              ? 'bg-gray-800/95 border-gray-600 text-gray-200' 
              : 'bg-white/95 border-gray-200 text-gray-800'
          }`}>
            <h4 className="font-semibold mb-3 text-lg">Rule Details</h4>
            <div className="space-y-2 text-sm">
              {[
                { label: 'Rename Flag', value: tooltip.rule.renamedMerchant ? 'Yes' : 'No' },
                { label: 'Rename Merchant To', value: tooltip.rule.renamedMerchant || '—' },
                { label: 'Cascade Flag', value: tooltip.rule.cascadeFlag ? 'Yes' : 'No' },
                { label: 'Hide Transactions', value: tooltip.rule.hideTransactionsFlag ? 'Yes' : 'No' },
                { label: 'Tags', value: tooltip.rule.tags || '—' },
                { label: 'Goal', value: tooltip.rule.goal || '—' },
              ].map((item, index) => (
                <div key={index} className="flex justify-between py-1">
                  <span className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    {item.label}:
                  </span>
                  <span className={`${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                    {item.value}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Delete Confirmation Modal */}
      {deleteConfirmation.open && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className={`w-full max-w-md rounded-2xl shadow-2xl ${
            darkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'
          }`}>
            <div className="p-6">
              {deleteConfirmation.isDeleting ? (
                successMessage === 'Rule deleted successfully' ? (
                  <div className="flex flex-col items-center gap-4">
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                      <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <p className="text-green-700 font-medium text-lg">Rule deleted successfully</p>
                  </div>
                ) : (
                  <div className="flex flex-col items-center gap-4" role="status">
                    <div className={`w-12 h-12 border-4 rounded-full animate-spin ${
                      darkMode ? 'border-gray-600 border-t-gray-400' : 'border-gray-300 border-t-gray-600'
                    }`}></div>
                    <p className={`${darkMode ? 'text-gray-200' : 'text-gray-800'} font-medium`}>
                      Deleting budget rule...
                    </p>
                  </div>
                )
              ) : (
                <div className="text-center">
                  <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <FaTrash className="w-8 h-8 text-red-600" />
                  </div>
                  <h3 className={`text-lg font-semibold mb-2 ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                    Delete Budget Rule?
                  </h3>
                  <p className={`text-sm mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Are you sure you want to delete this budget rule?
                  </p>
                  <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    This action cannot be undone.
                  </p>
                </div>
              )}
              
              {error && (
                <div className={`mt-4 p-3 rounded-lg flex items-center justify-between ${
                  darkMode 
                    ? 'bg-red-900/30 border border-red-700/50 text-red-300' 
                    : 'bg-red-50 border border-red-200 text-red-700'
                }`}>
                  <span className="text-sm">{error}</span>
                  <button 
                    onClick={() => dispatch(clearMessages())} 
                    className={`ml-2 hover:opacity-75 ${darkMode ? 'text-red-300' : 'text-red-700'}`}>
                    ×
                  </button>
                </div>
              )}
            </div>
            
            {!deleteConfirmation.isDeleting && (
              <div className={`flex gap-3 p-6 border-t ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                <button
                  onClick={handleCancelDelete}
                  className={`flex-1 px-4 py-3 rounded-xl font-medium transition-all duration-200 ${
                    darkMode 
                      ? 'bg-gray-700 text-gray-200 hover:bg-gray-600 border border-gray-600' 
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300'
                  }`}
                >
                  Cancel
                </button>
                <button
                  onClick={handleConfirmDelete}
                  className="flex-1 px-4 py-3 bg-red-600 text-white rounded-xl font-medium hover:bg-red-700 transition-all duration-200 transform hover:scale-105"
                >
                  Delete
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Enhanced Create/Edit Rule Modal */}
      {isCreateRuleModalOpen && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className={`w-full max-w-6xl h-[90vh] rounded-2xl shadow-2xl flex flex-col ${
            darkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'
          }`}>
            
            {/* Modal Header */}
            <div className={`px-6 py-4 border-b flex items-center justify-between ${
              darkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
            } rounded-t-2xl`}>
              <h2 className={`text-xl font-semibold ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                {editingRule ? 'Edit Budget Rule' : 'Create Budget Rule'}
              </h2>
              <button
                className={`p-2 rounded-lg transition-all duration-200 hover:scale-110 ${
                  darkMode 
                    ? 'text-gray-400 hover:text-gray-200 hover:bg-gray-700' 
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                }`}
                onClick={handleCloseRuleModal}
                disabled={loading}
                aria-label="Close modal"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Modal Content */}
            <div className="flex flex-1 overflow-hidden">
              {/* Left Panel - Conditions */}
              <div className={`w-1/2 p-6 overflow-y-auto border-r ${
                darkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
              }`}>
                <h3 className={`text-lg font-semibold mb-6 ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                  Conditions
                </h3>
                
                <div className="space-y-6">
                  {/* Merchant Condition */}
                  <div className={`p-4 rounded-xl border ${
                    darkMode 
                      ? 'border-gray-600 bg-gray-700/50' 
                      : 'border-gray-200 bg-white'
                  }`}>
                    <label className="flex items-center gap-3 mb-3">
                      <input
                        type="checkbox"
                        className={`w-5 h-5 rounded transition-all duration-200 ${
                          darkMode 
                            ? 'text-gray-600 bg-gray-700 border-gray-600 focus:ring-gray-500' 
                            : 'text-[#8bc34a] bg-white border-gray-300 focus:ring-[#8bc34a]'
                        }`}
                        checked={selectedFields.merchantNamePattern}
                        onChange={() => {
                          handleFieldToggle('merchantNamePattern');
                          if (!selectedFields.merchantNamePattern) {
                            setFormErrors((prev) => ({ ...prev, merchantNamePattern: '' }));
                          }
                        }}
                      />
                      <span className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                        Merchant
                      </span>
                    </label>
                    
                    {selectedFields.merchantNamePattern && (
                      <div className="space-y-3">
                        <div className="flex gap-3">
                          <select
                            className={`flex-1 px-3 py-2 rounded-lg text-sm transition-all duration-200 ${
                              darkMode 
                                ? 'bg-gray-600 border-gray-500 text-gray-200 focus:border-gray-400 focus:ring-gray-400' 
                                : 'bg-white border-gray-300 text-gray-900 focus:border-[#8bc34a] focus:ring-[#8bc34a]'
                            }`}
                            value={newBudgetRule.merchantMatchRegex ? 'contains' : 'equals'}
                            onChange={(e) => dispatch(updateNewRule({ merchantMatchRegex: e.target.value === 'contains' }))}
                          >
                            <option value="contains">Contains</option>
                            <option value="equals">Exact Match</option>
                          </select>
                          <input
                            type="text"
                            className={`flex-[2] px-3 py-2 rounded-lg text-sm transition-all duration-200 ${
                              darkMode 
                                ? 'bg-gray-600 border-gray-500 text-gray-200 focus:border-gray-400 focus:ring-gray-400' 
                                : 'bg-white border-gray-300 text-gray-900 focus:border-[#8bc34a] focus:ring-[#8bc34a]'
                            } ${formErrors.merchantNamePattern ? 'border-red-500' : ''}`}
                            value={newBudgetRule.merchantNamePattern || ''}
                            onChange={(e) => {
                              const value = e.target.value;
                              if (!value.trim()) {
                                setFormErrors((prev) => ({ ...prev, merchantNamePattern: 'Merchant name is required' }));
                              } else {
                                setFormErrors((prev) => ({ ...prev, merchantNamePattern: '' }));
                              }
                              handleRuleInputChange('merchantNamePattern')(e);
                            }}
                            placeholder="Enter merchant name"
                          />
                        </div>
                        {formErrors.merchantNamePattern && (
                          <p className="text-red-500 text-xs">{formErrors.merchantNamePattern}</p>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Amount Condition */}
                  <div className={`p-4 rounded-xl border ${
                    darkMode 
                      ? 'border-gray-600 bg-gray-700/50' 
                      : 'border-gray-200 bg-white'
                  }`}>
                    <label className="flex items-center gap-3 mb-3">
                      <input
                        type="checkbox"
                        className={`w-5 h-5 rounded transition-all duration-200 ${
                          darkMode 
                            ? 'text-gray-600 bg-gray-700 border-gray-600 focus:ring-gray-500' 
                            : 'text-[#8bc34a] bg-white border-gray-300 focus:ring-[#8bc34a]'
                        }`}
                        checked={selectedFields.thresholdAmount}
                        onChange={() => {
                          handleFieldToggle('thresholdAmount');
                          if (!selectedFields.thresholdAmount) {
                            setFormErrors((prev) => ({ ...prev, thresholdAmount: '' }));
                          }
                        }}
                      />
                      <span className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                        Amount
                      </span>
                    </label>
                    
                    {selectedFields.thresholdAmount && (
                      <div className="space-y-3">
                        <div className="grid grid-cols-3 gap-3">
                          <select
                            className={`px-3 py-2 rounded-lg text-sm transition-all duration-200 ${
                              darkMode 
                                ? 'bg-gray-600 border-gray-500 text-gray-200 focus:border-gray-400 focus:ring-gray-400' 
                                : 'bg-white border-gray-300 text-gray-900 focus:border-[#8bc34a] focus:ring-[#8bc34a]'
                            }`}
                            value={newBudgetRule.amountType || 'credit'}
                            onChange={(e) => dispatch(updateNewRule({ amountType: e.target.value }))}
                          >
                            <option value="credit">Credit</option>
                            <option value="debit">Debit</option>
                          </select>
                          <select
                            className={`px-3 py-2 rounded-lg text-sm transition-all duration-200 ${
                              darkMode 
                                ? 'bg-gray-600 border-gray-500 text-gray-200 focus:border-gray-400 focus:ring-gray-400' 
                                : 'bg-white border-gray-300 text-gray-900 focus:border-[#8bc34a] focus:ring-[#8bc34a]'
                            }`}
                            value={newBudgetRule.amountMatch || 'greater'}
                            onChange={(e) => dispatch(updateNewRule({ amountMatch: e.target.value }))}
                          >
                            <option value="equals">Equals</option>
                            <option value="greater">Greater than</option>
                            <option value="less">Less than</option>
                          </select>
                          <input
                            type="number"
                            className={`px-3 py-2 rounded-lg text-sm transition-all duration-200 ${
                              darkMode 
                                ? 'bg-gray-600 border-gray-500 text-gray-200 focus:border-gray-400 focus:ring-gray-400' 
                                : 'bg-white border-gray-300 text-gray-900 focus:border-[#8bc34a] focus:ring-[#8bc34a]'
                            } ${formErrors.thresholdAmount ? 'border-red-500' : ''}`}
                            value={newBudgetRule.thresholdAmount || ''}
                            onChange={(e) => {
                              const value = e.target.value;
                              if (!value || value <= 0) {
                                setFormErrors((prev) => ({ ...prev, thresholdAmount: 'Amount is required and must be greater than 0' }));
                              } else {
                                setFormErrors((prev) => ({ ...prev, thresholdAmount: '' }));
                              }
                              handleRuleInputChange('thresholdAmount')(e);
                            }}
                            placeholder="Amount"
                          />
                        </div>
                        {formErrors.thresholdAmount && (
                          <p className="text-red-500 text-xs">{formErrors.thresholdAmount}</p>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Source Subcategory Condition */}
                  <div className={`p-4 rounded-xl border ${
                    darkMode 
                      ? 'border-gray-600 bg-gray-700/50' 
                      : 'border-gray-200 bg-white'
                  }`}>
                    <label className="flex items-center gap-3 mb-3">
                      <input
                        type="checkbox"
                        className={`w-5 h-5 rounded transition-all duration-200 ${
                          darkMode 
                            ? 'text-gray-600 bg-gray-700 border-gray-600 focus:ring-gray-500' 
                            : 'text-[#8bc34a] bg-white border-gray-300 focus:ring-[#8bc34a]'
                        }`}
                        checked={selectedFields.fromSubCategoryId}
                        onChange={() => {
                          handleFieldToggle('fromSubCategoryId');
                          if (!selectedFields.fromSubCategoryId) {
                            setFormErrors((prev) => ({ ...prev, fromSubCategoryId: '' }));
                          }
                        }}
                      />
                      <span className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                        Source Subcategory
                      </span>
                    </label>
                    
                    {selectedFields.fromSubCategoryId && (
                      <div className="space-y-3">
                        <select
                          className={`w-full px-3 py-2 rounded-lg text-sm transition-all duration-200 ${
                            darkMode 
                              ? 'bg-gray-600 border-gray-500 text-gray-200 focus:border-gray-400 focus:ring-gray-400' 
                              : 'bg-white border-gray-300 text-gray-900 focus:border-[#8bc34a] focus:ring-[#8bc34a]'
                          } ${formErrors.fromSubCategoryId ? 'border-red-500' : ''}`}
                          value={newBudgetRule.fromSubCategoryId || ''}
                          onChange={(e) => {
                            const fromSubCatId = Number(e.target.value);
                            if (!fromSubCatId) {
                              setFormErrors((prev) => ({ ...prev, fromSubCategoryId: 'Source subcategory is required' }));
                            } else {
                              handleCategoryChange('fromSubCategoryId')(e);
                            }
                          }}
                        >
                          <option value="" disabled>Select source subcategory</option>
                          {subcategories && subcategories.length > 0 ? (
                            subcategories.filter((subcat) => subcat.type === 'standard').map((subcat) => (
                              <option key={`${subcat.subCategoryId}-${subcat.type}`} value={subcat.subCategoryId}>
                                {subcat.subCategoryName}
                              </option>
                            ))
                          ) : (
                            <option value="" disabled>No subcategories available</option>
                          )}
                        </select>
                        {formErrors.fromSubCategoryId && (
                          <p className="text-red-500 text-xs">{formErrors.fromSubCategoryId}</p>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Account Condition */}
                  <div className={`p-4 rounded-xl border ${
                    darkMode 
                      ? 'border-gray-600 bg-gray-700/50' 
                      : 'border-gray-200 bg-white'
                  }`}>
                    <label className="flex items-center gap-3 mb-3">
                      <input
                        type="checkbox"
                        className={`w-5 h-5 rounded transition-all duration-200 ${
                          darkMode 
                            ? 'text-gray-600 bg-gray-700 border-gray-600 focus:ring-gray-500' 
                            : 'text-[#8bc34a] bg-white border-gray-300 focus:ring-[#8bc34a]'
                        }`}
                        checked={selectedFields.accountId}
                        onChange={() => handleFieldToggle('accountId')}
                      />
                      <span className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                        Account
                      </span>
                    </label>
                    
                    {selectedFields.accountId && (
                      <div className="space-y-3">
                        {loading ? (
                          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            Loading accounts...
                          </div>
                        ) : (
                          <select
                            className={`w-full px-3 py-2 rounded-lg text-sm transition-all duration-200 ${
                              darkMode 
                                ? 'bg-gray-600 border-gray-500 text-gray-200 focus:border-gray-400 focus:ring-gray-400' 
                                : 'bg-white border-gray-300 text-gray-900 focus:border-[#8bc34a] focus:ring-[#8bc34a]'
                            } ${formErrors.accountId ? 'border-red-500' : ''}`}
                            value={newBudgetRule.accountId || ''}
                            onChange={(e) => {
                              const accountId = Number(e.target.value);
                              dispatch(updateNewRule({ accountId }));
                              if (!accountId) {
                                setFormErrors((prev) => ({ ...prev, accountId: 'Account is required' }));
                              } else {
                                setFormErrors((prev) => ({ ...prev, accountId: '' }));
                              }
                            }}
                          >
                            <option value="">Select account</option>
                            {Array.isArray(accounts) && accounts.length > 0 ? (
                              accounts.map((account) => (
                                <option key={account.accountId} value={account.accountId}>
                                  {account.accountName}
                                </option>
                              ))
                            ) : (
                              <option value="" disabled>No accounts available</option>
                            )}
                          </select>
                        )}
                        {formErrors.accountId && (
                          <p className="text-red-500 text-xs">{formErrors.accountId}</p>
                        )}
                      </div>
                    )}
                  </div>

                  {/* General Error */}
                  {(!selectedFields.merchantNamePattern &&
                    !selectedFields.thresholdAmount &&
                    !selectedFields.fromSubCategoryId &&
                    !selectedFields.accountId) && (
                    <div className={`p-3 rounded-lg border ${
                      darkMode 
                        ? 'border-red-700/50 bg-red-900/30 text-red-300' 
                        : 'border-red-200 bg-red-50 text-red-700'
                    }`}>
                      <p className="text-sm">
                        At least one of Merchants, Amount, Source Category, or Account must be selected
                      </p>
                    </div>
                  )}

                  {/* Cascade Flag */}
                  <div className="pt-8">
                    <label className="flex items-center gap-3">
                      <input
                        type="checkbox"
                        className={`w-5 h-5 rounded transition-all duration-200 ${
                          darkMode 
                            ? 'text-gray-600 bg-gray-700 border-gray-600 focus:ring-gray-500' 
                            : 'text-[#8bc34a] bg-white border-gray-300 focus:ring-[#8bc34a]'
                        }`}
                        checked={newBudgetRule.cascadeFlag || false}
                        onChange={(e) => dispatch(updateNewRule({ cascadeFlag: e.target.checked }))}
                      />
                      <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        Run rules for past transactions
                      </span>
                    </label>
                  </div>
                </div>
              </div>

              {/* Right Panel - Apply Actions */}
              <div className={`w-1/2 p-6 overflow-y-auto relative ${
                darkMode ? 'bg-gray-800' : 'bg-white'
              }`}>
                {/* Loading Overlay */}
                {loading && (
                  <div className={`absolute inset-0 bg-opacity-75 flex items-center justify-center z-10 ${
                    darkMode ? 'bg-gray-800' : 'bg-white'
                  }`}>
                    <div className="flex items-center gap-3">
                      <div className={`w-8 h-8 border-4 rounded-full animate-spin ${
                        darkMode ? 'border-gray-600 border-t-gray-400' : 'border-gray-300 border-t-gray-600'
                      }`}></div>
                      <span className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                        Saving...
                      </span>
                    </div>
                  </div>
                )}

                <h3 className={`text-lg font-semibold mb-6 ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                  Apply Actions
                </h3>

                {/* Error Message */}
                {error && successMessage !== 'Rule deleted successfully' && (
                  <div className={`mb-4 p-3 rounded-lg flex items-center justify-between ${
                    darkMode 
                      ? 'bg-red-900/30 border border-red-700/50 text-red-300' 
                      : 'bg-red-50 border border-red-200 text-red-700'
                  }`}>
                    <span className="text-sm">{error}</span>
                    <button 
                      onClick={() => dispatch(clearMessages())} 
                      className={`ml-2 hover:opacity-75 ${darkMode ? 'text-red-300' : 'text-red-700'}`}>
                      ×
                    </button>
                  </div>
                )}

                {/* Success Message */}
                {successMessage && successMessage !== 'Rule deleted successfully' && (
                  <div className={`mb-4 p-3 rounded-lg flex items-center justify-between ${
                    darkMode 
                      ? 'bg-green-900/30 border border-green-700/50 text-green-300' 
                      : 'bg-green-50 border border-green-200 text-green-700'
                  }`}>
                    <span className="text-sm">{successMessage}</span>
                    <button 
                      onClick={() => dispatch(clearMessages())} 
                      className={`ml-2 hover:opacity-75 ${darkMode ? 'text-green-300' : 'text-green-700'}`}>
                      ×
                    </button>
                  </div>
                )}

                <div className="space-y-6">
                  {/* Rename Merchant */}
                  <div className={`p-4 rounded-xl border ${
                    darkMode 
                      ? 'border-gray-600 bg-gray-700/50' 
                      : 'border-gray-200 bg-gray-50'
                  }`}>
                    <label className="flex items-center gap-3 mb-3">
                      <input
                        type="checkbox"
                        className={`w-5 h-5 rounded transition-all duration-200 ${
                          darkMode 
                            ? 'text-gray-600 bg-gray-700 border-gray-600 focus:ring-gray-500' 
                            : 'text-[#8bc34a] bg-white border-gray-300 focus:ring-[#8bc34a]'
                        }`}
                        checked={selectedFields.renamedMerchant}
                        onChange={() => {
                          handleFieldToggle('renamedMerchant');
                          if (!selectedFields.renamedMerchant) {
                            setFormErrors((prev) => ({ ...prev, renamedMerchant: '' }));
                          }
                        }}
                      />
                      <span className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                        Rename Merchant
                      </span>
                    </label>
                    
                    {selectedFields.renamedMerchant && (
                      <div className="space-y-2">
                        <input
                          type="text"
                          className={`w-full px-3 py-2 rounded-lg text-sm transition-all duration-200 ${
                            darkMode 
                              ? 'bg-gray-600 border-gray-500 text-gray-200 focus:border-gray-400 focus:ring-gray-400' 
                              : 'bg-white border-gray-300 text-gray-900 focus:border-[#8bc34a] focus:ring-[#8bc34a]'
                          } ${formErrors.renamedMerchant ? 'border-red-500' : ''}`}
                          value={newBudgetRule.renamedMerchant || ''}
                          onChange={(e) => {
                            const value = e.target.value;
                            if (!value.trim()) {
                              setFormErrors((prev) => ({ ...prev, renamedMerchant: 'Renamed merchant is required' }));
                            } else {
                              setFormErrors((prev) => ({ ...prev, renamedMerchant: '' }));
                            }
                            handleRuleInputChange('renamedMerchant')(e);
                          }}
                          placeholder="Enter renamed merchant"
                        />
                        {formErrors.renamedMerchant && (
                          <p className="text-red-500 text-xs">{formErrors.renamedMerchant}</p>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Target Subcategory */}
                  <div className={`p-4 rounded-xl border ${
                    darkMode 
                      ? 'border-gray-600 bg-gray-700/50' 
                      : 'border-gray-200 bg-gray-50'
                  }`}>
                    <label className={`block font-medium mb-3 ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                      Target Subcategory *
                    </label>
                    <select
                      className={`w-full px-3 py-2 rounded-lg text-sm transition-all duration-200 ${
                        darkMode 
                          ? 'bg-gray-600 border-gray-500 text-gray-200 focus:border-gray-400 focus:ring-gray-400' 
                          : 'bg-white border-gray-300 text-gray-900 focus:border-[#8bc34a] focus:ring-[#8bc34a]'
                      } ${formErrors.toSubCategoryId ? 'border-red-500' : ''}`}
                      value={`${newBudgetRule.toCustomSubCategoryId || newBudgetRule.toSubCategoryId || ''}|${newBudgetRule.toCustomSubCategoryId ? 'custom' : 'standard'}`}
                      onChange={handleCategoryChange('toSubCategoryId')}
                      required
                    >
                      <option value="" disabled>Select target subcategory</option>
                      {subcategories && subcategories.length > 0 ? (
                        subcategories.map((subcat) => (
                          <option key={`${subcat.subCategoryId}-${subcat.type}`} value={`${subcat.subCategoryId}|${subcat.type}`}>
                            {subcat.subCategoryName} ({subcat.type})
                          </option>
                        ))
                      ) : (
                        <option value="" disabled>No subcategories available</option>
                      )}
                    </select>
                    {formErrors.toSubCategoryId && (
                      <p className="text-red-500 text-xs mt-2">{formErrors.toSubCategoryId}</p>
                    )}
                  </div>

                  {/* Hide Transactions */}
                  <div className={`p-4 rounded-xl border ${
                    darkMode 
                      ? 'border-gray-600 bg-gray-700/50' 
                      : 'border-gray-200 bg-gray-50'
                  }`}>
                    <label className="flex items-center gap-3">
                      <input
                        type="checkbox"
                        className={`w-5 h-5 rounded transition-all duration-200 ${
                          darkMode 
                            ? 'text-gray-600 bg-gray-700 border-gray-600 focus:ring-gray-500' 
                            : 'text-[#8bc34a] bg-white border-gray-300 focus:ring-[#8bc34a]'
                        }`}
                        checked={newBudgetRule.hideTransactionsFlag || false}
                        onChange={(e) => dispatch(updateNewRule({ hideTransactionsFlag: e.target.checked }))}
                      />
                      <span className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                        Hide transactions
                      </span>
                    </label>
                  </div>

                  {/* Add Tags */}
                  <div className={`p-4 rounded-xl border ${
                    darkMode 
                      ? 'border-gray-600 bg-gray-700/50' 
                      : 'border-gray-200 bg-gray-50'
                  }`}>
                    <label className={`block font-medium mb-3 ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                      Add Tags
                    </label>
                    <input
                      type="text"
                      className={`w-full px-3 py-2 rounded-lg text-sm transition-all duration-200 ${
                        darkMode 
                          ? 'bg-gray-600 border-gray-500 text-gray-200 focus:border-gray-400 focus:ring-gray-400' 
                          : 'bg-white border-gray-300 text-gray-900 focus:border-[#8bc34a] focus:ring-[#8bc34a]'
                      }`}
                      value={newBudgetRule.tags || ''}
                      onChange={handleRuleInputChange('tags')}
                      placeholder="Enter tags"
                    />
                  </div>

                  {/* Link to Goal */}
                  <div className={`p-4 rounded-xl border ${
                    darkMode 
                      ? 'border-gray-600 bg-gray-700/50' 
                      : 'border-gray-200 bg-gray-50'
                  }`}>
                    <label className={`block font-medium mb-3 ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                      Link to Goal
                    </label>
                    <input
                      type="text"
                      className={`w-full px-3 py-2 rounded-lg text-sm transition-all duration-200 ${
                        darkMode 
                          ? 'bg-gray-600 border-gray-500 text-gray-200 focus:border-gray-400 focus:ring-gray-400' 
                          : 'bg-white border-gray-300 text-gray-900 focus:border-[#8bc34a] focus:ring-[#8bc34a]'
                      }`}
                      value={newBudgetRule.goal || ''}
                      onChange={handleRuleInputChange('goal')}
                      placeholder="Enter goal"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Modal Footer */}
            <div className={`px-6 py-4 border-t flex justify-center ${
              darkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
            } rounded-b-2xl`}>
              <button
                className={`px-8 py-3 rounded-xl font-semibold text-white transition-all duration-200 transform hover:scale-105 shadow-lg ${
                  darkMode 
                    ? 'bg-gray-700 hover:bg-gray-600 shadow-gray-800/25' 
                    : 'bg-[#8bc34a] hover:bg-[#6ec122] shadow-green-500/25'
                }`}
                onClick={handleSaveRule}
                disabled={loading}
              >
                {editingRule ? 'Update Rule' : 'Save Rule'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BudgetRulePage;