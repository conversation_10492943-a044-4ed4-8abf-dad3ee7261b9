import React, { useEffect, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getCurrentUserId } from '../../../utils/AuthUtil';
import { ThumbsUp, ThumbsDown, Copy } from 'lucide-react';
import ChatbotVisualizer from './ChatbotVisualizer';
import { logEvent } from '../../../utils/EventLogger';
import PaymentLoader from '../../load/PaymentLoader'; // Import PaymentLoader
// import { LoadingProvider, useLoading } from '../../context/LoadingContext'; // Import LoadingContext
import { themeClasses } from '../../../utils/tailwindUtils'; // Adjust path as needed
import { queryRequest } from '../../../../../logic/redux/chatbotSlice';
import { useCacheStatus } from '../../../../../logic/hooks/useCacheStatus';

const Chatbot = ({ darkMode }) => {
  const dispatch = useDispatch();
  const userId = getCurrentUserId();
  const chatbot = useSelector(state => state.chatbot);

  // Use cache status for chatbot history
  const cacheStatus = useCacheStatus('chatbotHistory');

  const [chatHistory, setChatHistory] = useState([]);
  const [newQuery, setNewQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [copiedIndex, setCopiedIndex] = useState(null);
  const [activeVisualization, setActiveVisualization] = useState(null);
  const [isVisualizerOpen, setIsVisualizerOpen] = useState(false);
  const chatContainerRef = useRef(null);
  const chatEndRef = useRef(null);
  const [isScrolledToBottom, setIsScrolledToBottom] = useState(true);

  useEffect(() => {
    // Use cached data if available
    if (cacheStatus.isLoaded && cacheStatus.data?.length >= 0) {
      console.log('✅ Using cached chatbot history in Chatbot component');
      const flatHistory = cacheStatus.data.flatMap(log => {
        const parsed = tryParseJSON(log.response);
        return [
          { type: 'user', text: log.userQuery },
          { type: 'ai',
            text: parsed?.summary || log.response,
            visualization: parsed?.visualization,
            chatId: log.id,
            isSaved: log.isSaved,
          }
        ];
      });
      setChatHistory(flatHistory);
    }
  }, [cacheStatus.isLoaded, cacheStatus.data]);

  // Listen for Redux state changes
  useEffect(() => {
    if (chatbot.history && chatbot.history.length > 0) {
      const flatHistory = chatbot.history.flatMap(log => {
        const parsed = tryParseJSON(log.response);
        return [
          { type: 'user', text: log.userQuery },
          { type: 'ai',
            text: parsed?.summary || log.response,
            visualization: parsed?.visualization,
            chatId: log.id,
            isSaved: log.isSaved,
          }
        ];
      });
      setChatHistory(flatHistory);
    }
    setIsLoading(chatbot.loading);
  }, [chatbot.history, chatbot.loading]);

  // Listen for query state changes
  useEffect(() => {
    setIsLoading(chatbot.querying || chatbot.loading);
  }, [chatbot.querying, chatbot.loading]);

  // Handle visualization when history updates after a query
  useEffect(() => {
    if (chatHistory.length > 0) {
      const lastMessage = chatHistory[chatHistory.length - 1];
      if (lastMessage.type === 'ai' && lastMessage.visualization) {
        if (lastMessage.visualization.type &&
            lastMessage.visualization.type.toLowerCase() !== 'none'
          ) {
          setActiveVisualization({
            ...lastMessage.visualization,
            chatId: lastMessage.chatId,
            isSaved: lastMessage.isSaved,
          });
          setIsVisualizerOpen(true);
        } else {
          setActiveVisualization(null);
          setIsVisualizerOpen(false);
        }
      }
    }
  }, [chatHistory]);

  const tryParseJSON = (text) => {
    try {
      const cleanText = text.replace(/^```json|```$/g, '').trim();
      const evaluatedText = cleanText.replace(
        /"value"\s*:\s*([0-9\.\s\+\-\*\/]+)/g,
        (_, expr) => `"value": ${eval(expr)}`
      );
      return JSON.parse(evaluatedText);
    } catch {
      return null;
    }
  };

  const sendQuery = () => {
    logEvent('Chatbot', 'sendQuery', { query: newQuery });
    if (!newQuery.trim()) return;

    const updatedHistory = [...chatHistory, { type: 'user', text: newQuery }];
    setChatHistory(updatedHistory);
    const queryText = newQuery;
    setNewQuery('');
    setIsLoading(true);

    // Use Redux for the query - this will handle the API call and response
    dispatch(queryRequest({ userId, userQuery: queryText }));
  };

  const handleChartSaved = (chatId) => {
    setChatHistory((prevHistory) =>
      prevHistory.map((msg) =>
        msg.type === 'ai' && msg.chatId === chatId
          ? { ...msg, isSaved: true }
          : msg
      )
    );
    // Update activeVisualization if it matches the saved chart
    if (activeVisualization?.chatId === chatId) {
      setActiveVisualization((prev) => ({ ...prev, isSaved: true }));
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') sendQuery();
  };

  const scrollToBottom = () => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [chatHistory, isLoading]);

  const handleScroll = () => {
    const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;
    setIsScrolledToBottom(scrollTop + clientHeight >= scrollHeight - 20);
  };

  const copyToClipboard = (text, index) => {
    navigator.clipboard.writeText(text);
    setCopiedIndex(index);
    setTimeout(() => setCopiedIndex(null), 2000);
  };

  // Show PaymentLoader when isLoading is true
  if (isLoading) {
    return (
      <div className={`min-h-screen w-full p-5 flex flex-col justify-center items-center ${themeClasses.container(darkMode)}`}>
        <PaymentLoader darkMode={darkMode} />
        {/* <p className={`mt-2 text-sm ${themeClasses.loadingText(darkMode)}`}>
          Loading chatbot response...
        </p> */}
      </div>
    );
  }

  return (
    <div className={`flex mt-10 p-4 h-full w-full transition-all duration-300 ease-in-out ${themeClasses.calendarGrid(darkMode)}`}>
      <style>
        {`
          @keyframes bounce-dot {
            0%, 80%, 100% {
              transform: scale(0);
            }
            40% {
              transform: scale(1);
            }
          }
        `}
      </style>
      <div className={`flex flex-col ${themeClasses.cardContainer(darkMode)} w-full p-4 h-full overflow-hidden transition-all duration-300 ease-in-out shadow-md`}>
        <div
          className="flex-1 overflow-y-auto space-y-4 mb-4 scrollbar-hide"
          style={{ WebkitOverflowScrolling: 'touch', 'scrollbar-width': 'none', '::-webkit-scrollbar': 'display: none' }}
          ref={chatContainerRef}
          onScroll={handleScroll}
        >
          {chatHistory.map((msg, index) => (
            <div key={index} className={`flex flex-col ${msg.type === 'user' ? 'items-start' : 'items-end'}`}>
              <div
                className={`inline-block max-w-full px-4 py-2 rounded-lg ${msg.type === 'user' ? themeClasses.chatUserMessage(darkMode) : themeClasses.chatAIMessage(darkMode)} ml-2`}
              >
                {msg.text}
                {msg.visualization?.type !== 'None' && msg.visualization?.type != null && (
                  <div
                    className={`mt-2 underline ${themeClasses.link(darkMode)} cursor-pointer`}
                    onClick={() => {
                      setActiveVisualization({
                        ...msg.visualization,
                        chatId: msg.chatId,
                        isSaved: msg.isSaved,
                      });
                      setIsVisualizerOpen(true);
                    }}
                  >
                    {msg.visualization?.title || 'chart'}
                  </div>
                )}
              </div>
              {msg.type === 'ai' && (
                <div className={`flex space-x-2 mt-1 mr-2`}>
                  <ThumbsUp className={`w-4 h-4 cursor-pointer ${themeClasses.iconThumbsUp(darkMode)}`} />
                  <ThumbsDown className={`w-4 h-4 cursor-pointer ${themeClasses.iconThumbsDown(darkMode)}`} />
                  <Copy
                    className={`w-4 h-4 cursor-pointer ${themeClasses.iconCopy(darkMode)}`}
                    onClick={() => copyToClipboard(msg.text, index)}
                  />
                  {copiedIndex === index && (
                    <span className={`absolute -top-6 left-1/2 -translate-x-1/2 ${themeClasses.copiedTooltip(darkMode)} text-xs px-2 py-0.5 rounded shadow`}>
                      Copied!
                    </span>
                  )}
                </div>
              )}
            </div>
          ))}
          <div ref={chatEndRef} />
        </div>

        {!isScrolledToBottom && (
          <div className="relative">
            <button
              onClick={scrollToBottom}
              className={`absolute bottom-3 left-1/2 transform -translate-x-1/2 ${themeClasses.scrollButton(darkMode)} text-sm px-3 py-1 rounded-md shadow transition`}
            >
              ↓
            </button>
          </div>
        )}

        <div className="flex items-center">
          <input
            type="text"
            value={newQuery}
            onChange={(e) => setNewQuery(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask a question..."
            className={`flex-1 border ${themeClasses.inputField(darkMode)} rounded-md px-4 py-2 mr-2 focus:outline-none focus:ring-2 ${themeClasses.inputFocusRing(darkMode)}`}
          />
          <button
            onClick={sendQuery}
            disabled={isLoading}
            className={`${themeClasses.reconcileButton(darkMode)} px-4 py-2 rounded-md disabled:opacity-50 transition`}
          >
            Send
          </button>
        </div>
      </div>

      {/* Right Visualization Area */}
      <div
        className={`flex flex-col ${themeClasses.visualizationPanel(darkMode)} shadow-md p-4 h-full overflow-auto transition-all duration-300 ease-in-out ${
          isVisualizerOpen ? 'w-1/2 opacity-100' : 'w-0 opacity-0 pointer-events-none'
        }`}
        style={{ WebkitOverflowScrolling: 'touch' }}
      >
        <div className="flex justify-between items-center mb-2">
          <button
            onClick={() => setIsVisualizerOpen(false)}
            className={`text-sm ${themeClasses.closeButton(darkMode)} px-2 py-1 rounded transition`}
          >
            ✕
          </button>
        </div>
        {activeVisualization ? (
          <ChatbotVisualizer visualization={activeVisualization} darkMode={darkMode}
           userId={userId}
           onChartSaved={handleChartSaved}
          />
        ) : (
          <div className={`${themeClasses.emptyVisualization(darkMode)} text-sm`}>No chart selected</div>
        )}
      </div>
    </div>
  );
};

export default Chatbot;