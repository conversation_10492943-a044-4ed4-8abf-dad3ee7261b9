import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Responsive<PERSON><PERSON><PERSON>, <PERSON>, Tooltip } from 'recharts';

const COLORS = {
  primary: '#7fe029',
  primaryLight: '#eaf7e0',
  primaryDark: '#5db01e',
  white: '#ffffff',
  lightGray: '#f8f9fa',
  textPrimary: '#333333',
  textSecondary: '#666666',
  success: '#2ecc71',
  danger: '#e74c3c',
  shadow: 'rgba(127, 224, 41, 0.15)',
};

const INVESTMENT_COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#0088FE', '#00C49F', '#FF8042'];

const InvestmentCard = () => {
  const investmentData = [
    { name: 'Stocks', value: 25000 },
    { name: 'Bonds', value: 15000 },
    { name: 'Real Estate', value: 40000 },
    { name: 'Crypto', value: 5000 },
    { name: 'Cash', value: 10000 },
  ];

  const totalInvestment = investmentData.reduce((sum, item) => sum + item.value, 0);
  const performance = { monthly: 2.4, yearly: 8.7 };
  const [period, setPeriod] = useState('all');

  return (
    <div className="h-full flex flex-col">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold" style={{ color: COLORS.textPrimary }}>Investment Portfolio</h2>
        <div className="flex rounded-lg p-1" style={{ backgroundColor: COLORS.primaryLight }}>
          {['month', 'year', 'all'].map(p => (
            <button
              key={p}
              className="py-1 px-3 rounded-md text-xs font-medium transition-all duration-200"
              style={{
                backgroundColor: period === p ? COLORS.primary : 'transparent',
                color: period === p ? COLORS.white : COLORS.textSecondary
              }}
              onClick={() => setPeriod(p)}
            >
              {p.charAt(0).toUpperCase() + p.slice(1)}
            </button>
          ))}
        </div>
      </div>

      <div className="flex justify-between mb-4">
        <div>
          <div className="text-sm" style={{ color: COLORS.textSecondary }}>Total Portfolio</div>
          <div className="text-2xl font-bold" style={{ color: COLORS.textPrimary }}>
            ${totalInvestment.toLocaleString()}
          </div>
        </div>
        <div>
          <div className="text-sm" style={{ color: COLORS.textSecondary }}>Performance</div>
          <div className="flex items-center">
            <span className="text-2xl font-bold" style={{ color: COLORS.success }}>
              +{period === 'month' ? performance.monthly : performance.yearly}%
            </span>
            <span className="ml-1 text-xs" style={{ color: COLORS.textSecondary }}>
              {period === 'month' ? '1M' : period === 'year' ? '1Y' : 'All'}
            </span>
          </div>
        </div>
      </div>

      <ResponsiveContainer width="100%" height={200}>
        <PieChart>
          <Pie
            data={investmentData}
            cx="50%"
            cy="50%"
            labelLine={false}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {investmentData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={INVESTMENT_COLORS[index % INVESTMENT_COLORS.length]} />
            ))}
          </Pie>
          <Tooltip
            formatter={(value) => [`$${value.toLocaleString()}`, 'Amount']}
            contentStyle={{
              backgroundColor: COLORS.white,
              borderColor: COLORS.primaryLight,
              borderRadius: '8px'
            }}
          />
          <Legend
            layout="horizontal"
            verticalAlign="bottom"
            align="center"
            wrapperStyle={{ fontSize: '12px' }}
          />
        </PieChart>
      </ResponsiveContainer>

      <div className="mt-2 overflow-auto">
        {investmentData.map((asset, index) => (
          <div key={index} className="flex justify-between items-center py-2">
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: INVESTMENT_COLORS[index % INVESTMENT_COLORS.length] }}></div>
              <span className="text-sm" style={{ color: COLORS.textPrimary }}>{asset.name}</span>
            </div>
            <div className="flex items-center">
              <span className="text-sm font-medium" style={{ color: COLORS.textPrimary }}>
                ${asset.value.toLocaleString()}
              </span>
              <span className="text-xs ml-2" style={{ color: COLORS.textSecondary }}>
                ({Math.round((asset.value / totalInvestment) * 100)}%)
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default InvestmentCard;
