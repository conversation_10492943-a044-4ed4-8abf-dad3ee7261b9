// authUtils.js - Authentication utility functions

/**
 * Gets the current authenticated user ID from Redux state
 * @param {Object} state - The Redux state object
 * @returns {string|null} The user ID or null if not found
 */
export const getCurrentUserId = (state) => {
    const userId = state?.auth?.user?.id;
    if (!userId) {
      console.warn('No user ID found in auth state');
      return null;
    }
    return userId;
  };
  
  /**
   * Checks if user is currently authenticated
   * @param {Object} state - The Redux state object
   * @returns {boolean} True if user is authenticated, false otherwise
   */
  export const isUserAuthenticated = (state) => {
    return !!state?.auth?.isUserAuthenticated && !!state?.auth?.user?.id;
  };