import { ofType } from 'redux-observable';
import { from, of } from 'rxjs';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchIconsStart,
  fetchIconsSuccess,
  fetchIconsFailure
} from '../redux/bankIconSlice';

// Epic for fetching all icons
export const bankIconEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchIconsStart.type),
    mergeMap(() => {
      const cache = state$.value.cache;

      // Check if data is available in cache first
      if (cache?.iconsLoaded && cache?.icons?.length > 0) {
        console.log('✅ Using cached icons data');
        return of(fetchIconsSuccess(cache.icons));
      }

      return from(axiosInstance.get('/pennypal/api/icons/list')).pipe(
        map((response) => fetchIconsSuccess(response.data)),
        catchError((error) => of(fetchIconsFailure(error.message)))
      );
    })
  );
export default bankIconEpic;