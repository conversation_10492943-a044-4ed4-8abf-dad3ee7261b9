{"private": true, "name": "my-monorepo", "workspaces": ["packages/*"], "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-oauth/google": "^0.12.2", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@tailwindcss/vite": "^4.0.8", "canvg": "^4.0.3", "classnames": "^2.5.1", "dom-to-image-more": "^3.6.0", "html2canvas": "^1.4.1", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "react-phone-input-2": "^2.15.1", "tailwindcss": "^4.0.8", "xlsx": "^0.18.5"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@vitest/ui": "^3.2.3", "jsdom": "^26.1.0", "vitest": "^3.2.3"}, "scripts": {"test": "npm run test --workspace=web", "test:all": "npm run test --workspaces", "test:ui": "npm run test:ui --workspace=web", "test:coverage": "npm run test:coverage --workspace=web", "test:coverage:ui": "npm run test:coverage:ui --workspace=web"}}