// logoutUtils.js - Comprehensive logout utility functions

import { clearCacheOnLogout } from './cacheUtils';
import { signOut } from '../redux/authSlice';

/**
 * Comprehensive logout function that handles all cleanup
 * Use this function for consistent logout behavior across the app
 * 
 * @param {Function} dispatch - Redux dispatch function
 * @param {Function} navigate - React Router navigate function (optional)
 * @param {Object} options - Additional options
 * @param {boolean} options.forceReload - Whether to force page reload (default: true)
 * @param {boolean} options.clearStorage - Whether to clear localStorage/sessionStorage (default: true)
 * @param {boolean} options.clearCookies - Whether to clear all cookies (default: true)
 * @param {string} options.redirectTo - Where to redirect after logout (default: '/login')
 */
export const performLogout = async (dispatch, navigate = null, options = {}) => {
  const {
    forceReload = true,
    clearStorage = true,
    clearCookies = true,
    redirectTo = '/login'
  } = options;

  try {
    console.log('🚪 Starting comprehensive logout process...');

    // Step 1: Clear all cache data
    console.log('🗑️ Clearing cache data...');
    clearCacheOnLogout(dispatch);

    // Step 2: Dispatch Redux signOut action
    console.log('📤 Dispatching signOut action...');
    dispatch(signOut());

    // Step 3: Clear browser storage
    if (clearStorage) {
      console.log('🧹 Clearing browser storage...');
      try {
        localStorage.clear();
        sessionStorage.clear();
        console.log('✅ Browser storage cleared');
      } catch (error) {
        console.warn('⚠️ Could not clear browser storage:', error);
      }
    }

    // Step 4: Clear cookies
    if (clearCookies) {
      console.log('🍪 Clearing cookies...');
      try {
        document.cookie.split(";").forEach((c) => {
          document.cookie = c
            .replace(/^ +/, "")
            .replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
        });
        console.log('✅ Cookies cleared');
      } catch (error) {
        console.warn('⚠️ Could not clear cookies:', error);
      }
    }

    // Step 5: Navigation
    if (navigate) {
      console.log(`🧭 Navigating to ${redirectTo}...`);
      navigate(redirectTo, { replace: true });
    }

    // Step 6: Force reload if requested
    if (forceReload) {
      console.log('🔄 Force reloading page for clean state...');
      setTimeout(() => {
        window.location.reload();
      }, 100); // Small delay to ensure navigation completes
    }

    console.log('✅ Logout process completed successfully');

  } catch (error) {
    console.error('❌ Error during logout process:', error);
    
    // Fallback: force redirect to login
    if (navigate) {
      navigate(redirectTo, { replace: true });
    } else {
      window.location.href = redirectTo;
    }
    
    if (forceReload) {
      window.location.reload();
    }
  }
};

/**
 * Quick logout function for emergency situations
 * Clears everything and forces redirect
 */
export const emergencyLogout = () => {
  console.log('🚨 Emergency logout initiated...');
  
  try {
    // Clear storage
    localStorage.clear();
    sessionStorage.clear();
    
    // Clear cookies
    document.cookie.split(";").forEach((c) => {
      document.cookie = c
        .replace(/^ +/, "")
        .replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
    });
  } catch (error) {
    console.error('Error during emergency logout:', error);
  }
  
  // Force redirect
  window.location.href = '/login';
};

/**
 * React hook for logout functionality
 * Provides logout functions that can be used in components
 */
export const useLogout = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  return {
    logout: (options) => performLogout(dispatch, navigate, options),
    emergencyLogout: () => emergencyLogout()
  };
};

// Re-export for convenience
export { clearCacheOnLogout } from './cacheUtils';
