@keyframes fadeInPage {
    from {
    opacity: 0;
    transform: translateY(10px);
    }
    to {
    opacity: 1;
    transform: translateY(0);
    }
}

.animate-fadeInPage {
    animation: fadeInPage 0.5s ease-out;
}

.spinning {
    animation: spin 2s linear infinite;
}

@keyframes spin {
    from {
    transform: rotate(0deg);
    }
    to {
    transform: rotate(360deg);
    }
}

/* Ensure tables are responsive */
@media (max-width: 768px) {
    .table-responsive {
    overflow-x: auto;
    }
    
    .table-responsive table {
    min-width: 600px;
    }
}

/* Improve drag and drop visual feedback */
.react-beautiful-dnd-dragging {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Enhanced hover effects */
.group:hover .drag-handle {
    opacity: 1;
}

.drag-handle {
    opacity: 0;
    transition: opacity 0.2s ease;
}