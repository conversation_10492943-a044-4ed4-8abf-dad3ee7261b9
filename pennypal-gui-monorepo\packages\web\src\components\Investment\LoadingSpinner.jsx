import React from 'react';

/**
 * Displays a loading spinner with optional message
 * @param {Object} props - Component props
 * @param {string} [props.message] - Optional loading message
 * @param {string} [props.className] - Additional CSS classes
 * @returns {JSX.Element} Loading spinner component
 */
const LoadingSpinner = ({ message = 'Loading...', className = '' }) => {
  return (
    <div className={`d-flex flex-column align-items-center justify-content-center py-4 ${className}`}>
      <div className="spinner-border text-primary mb-2" role="status">
        <span className="visually-hidden">Loading...</span>
      </div>
      {message && <p className="text-center">{message}</p>}
    </div>
  );
};

export default LoadingSpinner;