import { combineEpics, ofType } from 'redux-observable';
import { of, from } from 'rxjs';
import { catchError, map, mergeMap, withLatestFrom } from 'rxjs/operators';

// Import the configured axiosInstance instead of using axios directly
import { axiosInstance } from '../api/axiosConfig';

import {
  fetchRecurringContributionsRequest,
  fetchRecurringContributionsSuccess,
  fetchRecurringContributionsFailure,
  fetchGoalRecurringContributionsRequest,
  fetchGoalRecurringContributionsSuccess,
  fetchGoalRecurringContributionsFailure,
  setupRecurringContributionRequest,
  setupRecurringContributionSuccess,
  setupRecurringContributionFailure,
  updateRecurringContributionRequest,
  updateRecurringContributionSuccess,
  updateRecurringContributionFailure,
  deleteRecurringContributionRequest,
  deleteRecurringContributionSuccess,
  deleteRecurringContributionFailure,
  triggerContributionRequest,
  triggerContributionSuccess,
  triggerContributionFailure
} from '../redux/recurringSlice';

// Define API path without base URL since axiosInstance already has it
const API_PATH = '/pennypal/api/recurring-contributions';

// Epic to fetch all recurring contributions
const fetchRecurringContributionsEpic = (action$) => action$.pipe(
  ofType(fetchRecurringContributionsRequest.type),
  mergeMap(() => {
    return from(axiosInstance.get(API_PATH)).pipe(
      map(response => fetchRecurringContributionsSuccess(response.data)),
      catchError(error => {
        console.error('Error fetching recurring contributions:', error);
        return of(fetchRecurringContributionsFailure(
          error.response?.data?.message || 'Failed to load recurring contributions'
        ));
      })
    );
  })
);

// Epic to fetch recurring contributions for a specific goal
const fetchGoalRecurringContributionsEpic = (action$) => action$.pipe(
  ofType(fetchGoalRecurringContributionsRequest.type),
  mergeMap(action => {
    const goalId = action.payload;
    return from(axiosInstance.get(`${API_PATH}/goal/${goalId}`)).pipe(
      map(response => fetchGoalRecurringContributionsSuccess({ 
        goalId, 
        contributions: response.data 
      })),
      catchError(error => {
        console.error(`Error fetching recurring contributions for goal ${goalId}:`, error);
        return of(fetchGoalRecurringContributionsFailure(
          error.response?.data?.message || 'Failed to load recurring contributions for this goal'
        ));
      })
    );
  })
);

// Epic to set up a new recurring contribution
const setupRecurringContributionEpic = (action$) => action$.pipe(
  ofType(setupRecurringContributionRequest.type),
  mergeMap(action => {
    const contributionData = action.payload;
    return from(axiosInstance.post(API_PATH, contributionData)).pipe(
      map(response => setupRecurringContributionSuccess(response.data)),
      catchError(error => {
        console.error('Error setting up recurring contribution:', error);
        return of(setupRecurringContributionFailure(
          error.response?.data?.message || 'Failed to set up recurring contribution'
        ));
      })
    );
  })
);

// Epic to update an existing recurring contribution
const updateRecurringContributionEpic = (action$) => action$.pipe(
  ofType(updateRecurringContributionRequest.type),
  mergeMap(action => {
    const { contributionId, updateData } = action.payload;
    return from(axiosInstance.put(`${API_PATH}/${contributionId}`, updateData)).pipe(
      map(response => updateRecurringContributionSuccess(response.data)),
      catchError(error => {
        console.error(`Error updating recurring contribution ${contributionId}:`, error);
        return of(updateRecurringContributionFailure(
          error.response?.data?.message || 'Failed to update recurring contribution'
        ));
      })
    );
  })
);

// Epic to delete a recurring contribution
const deleteRecurringContributionEpic = (action$) => action$.pipe(
  ofType(deleteRecurringContributionRequest.type),
  mergeMap(action => {
    const { contributionId, goalId } = action.payload;
    return from(axiosInstance.delete(`${API_PATH}/${contributionId}`)).pipe(
      map(() => deleteRecurringContributionSuccess({ contributionId, goalId })),
      catchError(error => {
        console.error(`Error deleting recurring contribution ${contributionId}:`, error);
        return of(deleteRecurringContributionFailure(
          error.response?.data?.message || 'Failed to delete recurring contribution'
        ));
      })
    );
  })
);

// Epic to manually trigger a contribution
const triggerContributionEpic = (action$) => action$.pipe(
  ofType(triggerContributionRequest.type),
  mergeMap(action => {
    const contributionId = action.payload;
    return from(axiosInstance.post(`${API_PATH}/${contributionId}/trigger`, {})).pipe(
      map(response => triggerContributionSuccess(response.data)),
      catchError(error => {
        console.error(`Error triggering contribution ${contributionId}:`, error);
        return of(triggerContributionFailure(
          error.response?.data?.message || 'Failed to process contribution'
        ));
      })
    );
  })
);

// Combine all epics
const recurringEpics = combineEpics(
  fetchRecurringContributionsEpic,
  fetchGoalRecurringContributionsEpic,
  setupRecurringContributionEpic,
  updateRecurringContributionEpic,
  deleteRecurringContributionEpic,
  triggerContributionEpic
);

export default recurringEpics;