import React from 'react';
import { formatCurrency, formatPercentage } from '../../../../logic/utils/formatters';
import AllocationChart from './AllocationChart';
import PerformanceChart from './PerformanceChart';

const InvestmentPerformance = ({ performance }) => {
  const {
    totalValue,
    totalCostBasis,
    totalGain,
    totalGainPercent,
    dailyChange,
    dailyChangePercent,
    weeklyChange,
    weeklyChangePercent,
    monthlyChange,
    monthlyChangePercent,
    allocation,
    performanceByType
  } = performance;
  
  // Currency code - assuming USD for simplicity
  const currencyCode = 'USD';
  
  return (
    <div className="investment-performance">
      <div className="card mb-4">
        <div className="card-header">
          <h3>Portfolio Summary</h3>
        </div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-4">
              <div className="metric">
                <h4>Total Value</h4>
                <p className="display-6">{formatCurrency(totalValue, currencyCode)}</p>
              </div>
            </div>
            <div className="col-md-4">
              <div className="metric">
                <h4>Total Gain/Loss</h4>
                <p className={`display-6 ${totalGain >= 0 ? "text-success" : "text-danger"}`}>
                  {formatCurrency(totalGain, currencyCode)}
                  <small className="text-muted ms-2">({formatPercentage(totalGainPercent)})</small>
                </p>
              </div>
            </div>
            <div className="col-md-4">
              <div className="metric">
                <h4>Daily Change</h4>
                <p className={`display-6 ${dailyChange >= 0 ? "text-success" : "text-danger"}`}>
                  {formatCurrency(dailyChange, currencyCode)}
                  <small className="text-muted ms-2">({formatPercentage(dailyChangePercent)})</small>
                </p>
              </div>
            </div>
          </div>
          
          <div className="row mt-4">
            <div className="col-md-6">
              <div className="metric">
                <h4>Weekly Change</h4>
                <p className={`h5 ${weeklyChange >= 0 ? "text-success" : "text-danger"}`}>
                  {formatCurrency(weeklyChange, currencyCode)}
                  <small className="text-muted ms-2">({formatPercentage(weeklyChangePercent)})</small>
                </p>
              </div>
            </div>
            <div className="col-md-6">
              <div className="metric">
                <h4>Monthly Change</h4>
                <p className={`h5 ${monthlyChange >= 0 ? "text-success" : "text-danger"}`}>
                  {formatCurrency(monthlyChange, currencyCode)}
                  <small className="text-muted ms-2">({formatPercentage(monthlyChangePercent)})</small>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="row">
        <div className="col-md-6">
          <div className="card mb-4">
            <div className="card-header">
              <h3>Portfolio Allocation</h3>
            </div>
            <div className="card-body">
              <AllocationChart allocation={allocation} />
            </div>
          </div>
        </div>
        
        <div className="col-md-6">
          <div className="card mb-4">
            <div className="card-header">
              <h3>Performance by Type</h3>
            </div>
            <div className="card-body">
              <div className="table-responsive">
                <table className="table table-sm">
                  <thead>
                    <tr>
                      <th>Security Type</th>
                      <th>Value</th>
                      <th>Gain/Loss</th>
                    </tr>
                  </thead>
                  <tbody>
                    {Object.entries(performanceByType).map(([type, data]) => (
                      <tr key={type}>
                        <td>{type}</td>
                        <td>{formatCurrency(data.value, currencyCode)}</td>
                        <td className={data.gain >= 0 ? "text-success" : "text-danger"}>
                          {formatCurrency(data.gain, currencyCode)} ({formatPercentage(data.gainPercent)})
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvestmentPerformance;