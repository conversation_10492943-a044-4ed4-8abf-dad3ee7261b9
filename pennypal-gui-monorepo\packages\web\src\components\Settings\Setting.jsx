import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import FamilySection from './FamilySection';

const Setting = () => {
  const [activeTab, setActiveTab] = useState('profile');
  const user = useSelector((state) => state.auth.user);
  const dispatch = useDispatch();

  return (
    <div className="settings-container p-4">
      <h1 className="text-2xl font-bold mb-6">Settings</h1>
      
      <div className="settings-layout flex">
        {/* Left sidebar with tabs */}
        <div className="settings-sidebar w-1/4 pr-4">
          <ul className="settings-tabs">
            <li className={`settings-tab p-3 cursor-pointer ${activeTab === 'profile' ? 'bg-gray-100 font-bold' : ''}`}
                onClick={() => setActiveTab('profile')}>
              Profile Settings
            </li>
            <li className={`settings-tab p-3 cursor-pointer ${activeTab === 'family' ? 'bg-gray-100 font-bold' : ''}`}
                onClick={() => setActiveTab('family')}>
              Family Management
            </li>
            <li className={`settings-tab p-3 cursor-pointer ${activeTab === 'notifications' ? 'bg-gray-100 font-bold' : ''}`}
                onClick={() => setActiveTab('notifications')}>
              Notifications
            </li>
            <li className={`settings-tab p-3 cursor-pointer ${activeTab === 'security' ? 'bg-gray-100 font-bold' : ''}`}
                onClick={() => setActiveTab('security')}>
              Security
            </li>
            <li className={`settings-tab p-3 cursor-pointer ${activeTab === 'preferences' ? 'bg-gray-100 font-bold' : ''}`}
                onClick={() => setActiveTab('preferences')}>
              Preferences
            </li>
          </ul>
        </div>
        
        {/* Right content area */}
        <div className="settings-content w-3/4 pl-6 border-l">
          {activeTab === 'profile' && (
            <div className="profile-settings">
              <h2 className="text-xl font-semibold mb-4">Profile Settings</h2>
              <form className="space-y-4">
                <div className="form-group">
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                  <input 
                    type="text" 
                    id="name" 
                    className="w-full p-2 border rounded" 
                    defaultValue={user?.name || ''}
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                  <input 
                    type="email" 
                    id="email" 
                    className="w-full p-2 border rounded" 
                    defaultValue={user?.email || ''}
                    disabled
                  />
                  <p className="text-xs text-gray-500 mt-1">Email address cannot be changed</p>
                </div>
                <div className="form-group">
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                  <input 
                    type="tel" 
                    id="phone" 
                    className="w-full p-2 border rounded" 
                    defaultValue={user?.phoneNumber || ''}
                  />
                </div>
                <div className="form-group pt-4">
                  <button type="submit" className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                    Save Changes
                  </button>
                </div>
              </form>
            </div>
          )}
          
          {activeTab === 'family' && (
            <FamilySection />
          )}
          
          {activeTab === 'notifications' && (
            <div className="notifications-settings">
              <h2 className="text-xl font-semibold mb-4">Notification Settings</h2>
              <div className="notification-options space-y-4">
                <div className="notification-option flex items-center justify-between p-3 border-b">
                  <div>
                    <h3 className="font-medium">Budget Alerts</h3>
                    <p className="text-sm text-gray-600">Get notified when you're approaching budget limits</p>
                  </div>
                  <label className="switch">
                    <input type="checkbox" defaultChecked />
                    <span className="slider round"></span>
                  </label>
                </div>
                <div className="notification-option flex items-center justify-between p-3 border-b">
                  <div>
                    <h3 className="font-medium">Large Transactions</h3>
                    <p className="text-sm text-gray-600">Get notified of unusually large transactions</p>
                  </div>
                  <label className="switch">
                    <input type="checkbox" defaultChecked />
                    <span className="slider round"></span>
                  </label>
                </div>
                <div className="notification-option flex items-center justify-between p-3 border-b">
                  <div>
                    <h3 className="font-medium">Monthly Reports</h3>
                    <p className="text-sm text-gray-600">Receive monthly spending reports via email</p>
                  </div>
                  <label className="switch">
                    <input type="checkbox" defaultChecked />
                    <span className="slider round"></span>
                  </label>
                </div>
                <div className="notification-option flex items-center justify-between p-3">
                  <div>
                    <h3 className="font-medium">Investment Updates</h3>
                    <p className="text-sm text-gray-600">Get notified about significant changes in investments</p>
                  </div>
                  <label className="switch">
                    <input type="checkbox" />
                    <span className="slider round"></span>
                  </label>
                </div>
              </div>
            </div>
          )}
          
          {activeTab === 'security' && (
            <div className="security-settings">
              <h2 className="text-xl font-semibold mb-4">Security Settings</h2>
              <div className="password-section mb-6">
                <h3 className="text-lg font-medium mb-2">Change Password</h3>
                <form className="space-y-4">
                  <div className="form-group">
                    <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700 mb-1">Current Password</label>
                    <input type="password" id="currentPassword" className="w-full p-2 border rounded" />
                  </div>
                  <div className="form-group">
                    <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 mb-1">New Password</label>
                    <input type="password" id="newPassword" className="w-full p-2 border rounded" />
                  </div>
                  <div className="form-group">
                    <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">Confirm New Password</label>
                    <input type="password" id="confirmPassword" className="w-full p-2 border rounded" />
                  </div>
                  <div className="form-group pt-2">
                    <button type="submit" className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                      Update Password
                    </button>
                  </div>
                </form>
              </div>
              <div className="two-factor-section">
                <h3 className="text-lg font-medium mb-2">Two-Factor Authentication</h3>
                <div className="flex items-center justify-between p-3 border rounded">
                  <div>
                    <p className="font-medium">Enable Two-Factor Authentication</p>
                    <p className="text-sm text-gray-600">Add an extra layer of security to your account</p>
                  </div>
                  <button className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600">
                    Setup
                  </button>
                </div>
              </div>
            </div>
          )}
          
          {activeTab === 'preferences' && (
            <div className="preferences-settings">
              <h2 className="text-xl font-semibold mb-4">Preferences</h2>
              <div className="preference-section mb-6">
                <h3 className="text-lg font-medium mb-3">Display Settings</h3>
                <div className="form-group mb-4">
                  <label htmlFor="currency" className="block text-sm font-medium text-gray-700 mb-1">Default Currency</label>
                  <select id="currency" className="w-full p-2 border rounded">
                    <option value="USD">USD - US Dollar</option>
                    <option value="EUR">EUR - Euro</option>
                    <option value="GBP">GBP - British Pound</option>
                    <option value="CAD">CAD - Canadian Dollar</option>
                    <option value="AUD">AUD - Australian Dollar</option>
                  </select>
                </div>
                <div className="form-group mb-4">
                  <label htmlFor="dateFormat" className="block text-sm font-medium text-gray-700 mb-1">Date Format</label>
                  <select id="dateFormat" className="w-full p-2 border rounded">
                    <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                    <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                    <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                  </select>
                </div>
                <div className="form-group">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Theme</label>
                  <div className="flex space-x-4">
                    <div className="theme-option">
                      <input type="radio" id="lightTheme" name="theme" value="light" className="sr-only" defaultChecked />
                      <label htmlFor="lightTheme" className="flex flex-col items-center cursor-pointer">
                        <div className="w-16 h-16 bg-white border rounded shadow-sm mb-2"></div>
                        <span>Light</span>
                      </label>
                    </div>
                    <div className="theme-option">
                      <input type="radio" id="darkTheme" name="theme" value="dark" className="sr-only" />
                      <label htmlFor="darkTheme" className="flex flex-col items-center cursor-pointer">
                        <div className="w-16 h-16 bg-gray-800 border rounded shadow-sm mb-2"></div>
                        <span>Dark</span>
                      </label>
                    </div>
                    <div className="theme-option">
                      <input type="radio" id="systemTheme" name="theme" value="system" className="sr-only" />
                      <label htmlFor="systemTheme" className="flex flex-col items-center cursor-pointer">
                        <div className="w-16 h-16 bg-gradient-to-r from-white to-gray-800 border rounded shadow-sm mb-2"></div>
                        <span>System</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              <div className="form-group pt-4">
                <button type="submit" className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                  Save Preferences
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Setting;