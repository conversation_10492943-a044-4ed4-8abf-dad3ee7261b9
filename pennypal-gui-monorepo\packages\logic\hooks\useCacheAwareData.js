// useCacheAwareData.js - Custom hook for cache-aware data fetching

import { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { getCurrentUserId } from '../../web/src/utils/AuthUtil';

/**
 * Custom hook that provides cache-aware data fetching
 * Automatically updates component state when cache is populated
 * 
 * @param {Object} options - Configuration options
 * @param {string} options.cacheKey - The cache key to monitor (e.g., 'userAccounts', 'budgetData')
 * @param {Function} options.fetchAction - Action to dispatch if data not cached
 * @param {Function} options.selector - Selector to get data from Redux state
 * @param {Object} options.params - Parameters for the fetch action
 * @param {boolean} options.autoFetch - Whether to automatically fetch on mount (default: true)
 * @returns {Object} - { data, loading, error, refetch }
 */
export const useCacheAwareData = ({
  cacheKey,
  fetchAction,
  selector,
  params = {},
  autoFetch = true
}) => {
  const dispatch = useDispatch();
  const userId = getCurrentUserId();
  const cache = useSelector(state => state.cache);
  const componentData = useSelector(selector);
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Get cache state for the specific key
  const cacheLoaded = cache?.[`${cacheKey}Loaded`];
  const cacheLoading = cache?.[`${cacheKey}Loading`];
  const cacheError = cache?.[`${cacheKey}Error`];
  const cacheData = cache?.[cacheKey];
  const cacheParams = cache?.[`${cacheKey}Params`];

  // Check if cache is valid for current user/params
  const isCacheValid = cacheLoaded && 
                      cacheParams?.userId == userId &&
                      (!params.additionalParams || 
                       Object.keys(params.additionalParams || {}).every(
                         key => cacheParams[key] == params.additionalParams[key]
                       ));

  // Fetch function
  const fetchData = () => {
    if (!userId) {
      setError('User not authenticated');
      return;
    }

    if (isCacheValid) {
      console.log(`✅ Using cached ${cacheKey} data`);
      return;
    }

    console.log(`🔄 ${cacheKey} not cached, dispatching fetch action`);
    setLoading(true);
    setError(null);
    
    dispatch(fetchAction({ userId, ...params.additionalParams }));
  };

  // Auto-fetch on mount if enabled
  useEffect(() => {
    if (autoFetch && userId) {
      fetchData();
    }
  }, [userId, autoFetch]);

  // Update loading state based on cache loading
  useEffect(() => {
    setLoading(cacheLoading || false);
  }, [cacheLoading]);

  // Update error state based on cache error
  useEffect(() => {
    setError(cacheError || null);
  }, [cacheError]);

  // Return the appropriate data source
  const data = isCacheValid ? cacheData : componentData;

  return {
    data,
    loading,
    error,
    refetch: fetchData,
    isCached: isCacheValid,
    cacheLoading,
    cacheError
  };
};

/**
 * Hook specifically for account data
 */
export const useAccountData = () => {
  return useCacheAwareData({
    cacheKey: 'userAccounts',
    fetchAction: (params) => ({ type: 'cache/fetchUserAccountsStart', payload: params }),
    selector: state => state.accountsDashboard?.accounts || []
  });
};

/**
 * Hook specifically for budget data
 */
export const useBudgetData = () => {
  return useCacheAwareData({
    cacheKey: 'budgetData',
    fetchAction: (params) => ({ type: 'cache/fetchBudgetDataStart', payload: params }),
    selector: state => state.spendingDashboard?.monthlyExpenses || []
  });
};

/**
 * Hook specifically for transaction data
 */
export const useTransactionData = () => {
  return useCacheAwareData({
    cacheKey: 'transactions',
    fetchAction: (params) => ({ type: 'cache/fetchTransactionsStart', payload: params }),
    selector: state => state.transactions?.transactions || []
  });
};

/**
 * Hook specifically for recurring transaction data
 */
export const useRecurringTransactionData = () => {
  return useCacheAwareData({
    cacheKey: 'recurringTransactions',
    fetchAction: (params) => ({ type: 'cache/fetchRecurringTransactionsStart', payload: params }),
    selector: state => state.recurringTransactions?.transactions || []
  });
};

/**
 * Hook for payment subscription data
 */
export const usePaymentSubscriptionData = () => {
  return useCacheAwareData({
    cacheKey: 'paymentSubscription',
    fetchAction: (params) => ({ type: 'cache/fetchPaymentSubscriptionStart', payload: params }),
    selector: state => state.stripeDashboard?.subscriptionInfo || null
  });
};

/**
 * Hook for payment methods data
 */
export const usePaymentMethodsData = () => {
  return useCacheAwareData({
    cacheKey: 'paymentMethods',
    fetchAction: (params) => ({ type: 'cache/fetchPaymentMethodsStart', payload: params }),
    selector: state => state.stripeDashboard?.paymentMethods || []
  });
};

/**
 * Generic hook for any cache key
 */
export const useGenericCacheData = (cacheKey, fetchActionType, selector) => {
  return useCacheAwareData({
    cacheKey,
    fetchAction: (params) => ({ type: fetchActionType, payload: params }),
    selector
  });
};

export default useCacheAwareData;
