import { useMemo, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import StatCard from './StatCard';
import { 
  fetchAccountTypeDeltaData,
  selectCurrentQuarterAccountTypes, // ADD THIS
  selectDeltaLoading,
  selectDeltaError,
  selectDataType,
  selectDeltaData
} from '../../../../../../logic/redux/deltaSlice';
import { selectTimePeriod } from '../../../../../../logic/redux/accountChartSlice';
import {
  faSackDollar,
  faChartLine,
  faCreditCard,
  faCoins
} from "@fortawesome/free-solid-svg-icons";

const CARD_CONFIG = [
  {
    key: 'cash',
    label: 'Cash',
    variations: ['depository', 'cash', 'checking', 'savings', 'bank', 'cash_management'],
    icon: faCoins
  },
  {
    key: 'credit',
    label: 'Credit',
    variations: ['credit', 'creditcard', 'credit_card', 'credit card'],
    icon: faCreditCard
  },
  {
    key: 'investment',
    label: 'Investment',
    variations: ['investment', 'investmenttaxdeferred', 'roth'],
    icon: faChartLine
  },
  {
    key: 'loan',
    label: 'Loan',
    variations: ['loan', 'mortgage', 'liability'],
    icon: faSackDollar
  }
];

const StatCardGroupView = ({ darkMode, currentTheme, accounts = [] }) => {
  const dispatch = useDispatch();
  const deltaAccountTypes = useSelector(selectCurrentQuarterAccountTypes) || []; // ADD THIS
  const deltaLoading = useSelector(selectDeltaLoading);
  const deltaError = useSelector(selectDeltaError);
  const selectedTimePeriod = useSelector(selectTimePeriod);

  // Fetch data on mount and when time period changes
  useEffect(() => {
    console.log('Dispatching fetchAccountTypeDeltaData (timePeriod change) with payload:', { timePeriod: selectedTimePeriod });
    dispatch(fetchAccountTypeDeltaData({ timePeriod: selectedTimePeriod }));
  }, [selectedTimePeriod, dispatch]);

  // Map API accountTypes to a lookup object for fast access
  const accountTypeMap = useMemo(() => {
    const map = {};
    if (Array.isArray(deltaAccountTypes)) {
      deltaAccountTypes.forEach((item) => {
        const type = (item.accountType || '').toLowerCase();
        map[type] = item;
      });
    }
    return map;
  }, [deltaAccountTypes]);

  // Current quarter for display
  const currentQuarter = useMemo(() => {
    if (deltaAccountTypes.length > 0 && deltaAccountTypes[0].quarter) {
      return deltaAccountTypes[0].quarter;
    }
    return null;
  }, [deltaAccountTypes]);

  // Helper to get card data for each type
  const getCardData = (config) => {
    // For investment, sum all investment types
    if (config.key === 'investment') {
      let totalBalance = 0, totalDelta = 0, totalPast = 0, totalCount = 0;
      config.variations.forEach((variation) => {
        const data = accountTypeMap[variation];
        if (data) {
          totalBalance += data.totalCurrentBalance || 0;
          totalDelta += data.totalDeltaAmount || 0;
          totalPast += data.totalPastBalance || 0;
          totalCount += data.accountCount || 0;
        }
      });
      let percent = totalPast !== 0 ? (totalDelta / totalPast) * 100 : 0;
      if (!Number.isFinite(percent)) percent = 0;
      return {
        value: totalBalance,
        change: percent,
        icon: config.icon,
        accountCount: totalCount
      };
    }
    // For other types, just use the first matching variation
    for (const variation of config.variations) {
      const data = accountTypeMap[variation];
      if (data) {
        let percent = data.totalPastBalance !== 0
          ? (data.totalDeltaAmount / data.totalPastBalance) * 100
          : 0;
        if (!Number.isFinite(percent)) percent = 0;
        return {
          value: data.totalCurrentBalance || 0,
          change: percent,
          icon: config.icon,
          accountCount: data.accountCount || 0
        };
      }
    }
    // Fallback: show zero if no data
    return { value: 0, change: 0, icon: config.icon, accountCount: 0 };
  };

  // Loading state
  if (deltaLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {CARD_CONFIG.map((card) => (
          <StatCard
            key={card.key}
            label={card.label}
            value={0}
            change={0}
            darkMode={darkMode}
            icon={card.icon}
            currentTheme={currentTheme}
            loading={true}
          />
        ))}
      </div>
    );
  }

  // Error state
  if (deltaError) {
    return <div className="text-red-500">Error loading account type data.</div>;
  }

  // Render four cards
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {/* {currentQuarter && (
        <div className="mb-2 text-xs text-gray-500">
          Showing data for <b>{currentQuarter}</b>
        </div>
      )} */}
      {CARD_CONFIG.map((card) => {
        const data = getCardData(card);
        return (
          <StatCard
            key={card.key}
            label={card.label}
            value={data.value}
            change={data.change}
            darkMode={darkMode}
            icon={card.icon}
            currentTheme={currentTheme}
            accountCount={data.accountCount}
          />
        );
      })}
    </div>
  );
};

export default StatCardGroupView;