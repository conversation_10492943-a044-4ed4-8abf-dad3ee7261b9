import { combineEpics, ofType } from 'redux-observable';
import { from, of } from 'rxjs';
import { catchError, map, mergeMap, switchMap } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchGoalsRequest,
  fetchGoalsSuccess,
  fetchGoalsFailure,
  fetchGoalRequest,
  fetchGoalSuccess,
  fetchGoalFailure,
  createGoalRequest,
  createGoalSuccess,
  createGoalFailure,
  updateGoalRequest,
  updateGoalSuccess,
  updateGoalFailure,
  contributeToGoalRequest,
  contributeToGoalSuccess,
  contributeToGoalFailure,
  fetchMonthlyGoalsRequest,
  fetchMonthlyGoalsSuccess,
  fetchMonthlyGoalsFailure,
  fetchGoalSummaryRequest,
  fetchGoalSummarySuccess,
  fetchGoalSummaryFailure,
  createNextGoalRequest,
  createNextGoalSuccess,
  createNextGoalFailure
} from '../redux/goalSlice';

// Fetch all goals
const fetchGoalsEpic = (action$) => action$.pipe(
  ofType(fetchGoalsRequest.type),
  switchMap(() => {
    console.log('Fetching all goals');
    return from(axiosInstance.get('/pennypal/api/goals')).pipe(
      map(response => {
        console.log('Goals fetched successfully:', response.data);
        return fetchGoalsSuccess(response.data);
      }),
      catchError(error => {
        console.error('Error fetching goals:', error);
        return of(fetchGoalsFailure(error.response?.data?.message || 'Failed to fetch goals'));
      })
    );
  })
);

// Fetch single goal
const fetchGoalEpic = (action$) => action$.pipe(
  ofType(fetchGoalRequest.type),
  mergeMap(action => {
    const goalId = action.payload;
    console.log(`Fetching goal with ID: ${goalId}`);
    return from(axiosInstance.get(`/pennypal/api/goals/${goalId}`)).pipe(
      map(response => {
        console.log('Goal fetched successfully:', response.data);
        return fetchGoalSuccess(response.data);
      }),
      catchError(error => {
        console.error(`Error fetching goal ${goalId}:`, error);
        return of(fetchGoalFailure(error.response?.data?.message || 'Failed to fetch goal'));
      })
    );
  })
);

// Create goal
const createGoalEpic = (action$) => action$.pipe(
  ofType(createGoalRequest.type),
  mergeMap(action => {
    const goalData = action.payload;
    console.log('Creating new goal:', goalData);
    return from(axiosInstance.post('/pennypal/api/goals', goalData)).pipe(
      map(response => {
        console.log('Goal created successfully:', response.data);
        return createGoalSuccess(response.data);
      }),
      catchError(error => {
        console.error('Error creating goal:', error);
        return of(createGoalFailure(error.response?.data?.message || 'Failed to create goal'));
      })
    );
  })
);

// Update goal
const updateGoalEpic = (action$) => action$.pipe(
  ofType(updateGoalRequest.type),
  mergeMap(action => {
    const { goalId, goalData } = action.payload;
    console.log(`Updating goal ${goalId}:`, goalData);
    return from(axiosInstance.put(`/pennypal/api/goals/update/${goalId}`, goalData)).pipe(
      map(response => {
        console.log('Goal updated successfully:', response.data);
        return updateGoalSuccess(response.data);
      }),
      catchError(error => {
        console.error(`Error updating goal ${goalId}:`, error);
        return of(updateGoalFailure(error.response?.data?.message || 'Failed to update goal'));
      })
    );
  })
);

// Contribute to goal
const contributeToGoalEpic = (action$) => action$.pipe(
  ofType(contributeToGoalRequest.type),
  mergeMap(action => {
    const contributionData = action.payload;
    console.log('Contributing to goal:', contributionData);
    return from(axiosInstance.post('/pennypal/api/goals/contribute', contributionData)).pipe(
      map(response => {
        console.log('Contribution added successfully:', response.data);
        return contributeToGoalSuccess(response.data);
      }),
      catchError(error => {
        console.error('Error contributing to goal:', error);
        return of(contributeToGoalFailure(error.response?.data?.message || 'Failed to add contribution'));
      })
    );
  })
);

// Fetch goals for month
const fetchMonthlyGoalsEpic = (action$) => action$.pipe(
  ofType(fetchMonthlyGoalsRequest.type),
  mergeMap(action => {
    const { year, month } = action.payload;
    console.log(`Fetching goals for ${month}/${year}`);
    return from(axiosInstance.get(`/pennypal/api/goals/month/${year}/${month}`)).pipe(
      map(response => {
        console.log('Monthly goals fetched successfully:', response.data);
        return fetchMonthlyGoalsSuccess(response.data);
      }),
      catchError(error => {
        console.error(`Error fetching goals for ${month}/${year}:`, error);
        return of(fetchMonthlyGoalsFailure(error.response?.data?.message || 'Failed to fetch monthly goals'));
      })
    );
  })
);

// Fetch goals summary
const fetchGoalSummaryEpic = (action$) => action$.pipe(
  ofType(fetchGoalSummaryRequest.type),
  switchMap(() => {
    console.log('Fetching goals summary');
    return from(axiosInstance.get('/pennypal/api/goals/summary')).pipe(
      map(response => {
        console.log('Goal summary fetched successfully:', response.data);
        return fetchGoalSummarySuccess(response.data);
      }),
      catchError(error => {
        console.error('Error fetching goal summary:', error);
        return of(fetchGoalSummaryFailure(error.response?.data?.message || 'Failed to fetch goal summary'));
      })
    );
  })
);

// Create next goal based on previous one
const createNextGoalEpic = (action$) => action$.pipe(
  ofType(createNextGoalRequest.type),
  mergeMap(action => {
    const { previousGoalId, goalData } = action.payload;
    console.log(`Creating next goal based on goal ${previousGoalId}:`, goalData);
    return from(axiosInstance.post(`/pennypal/api/goals/next/${previousGoalId}`, goalData)).pipe(
      map(response => {
        console.log('Next goal created successfully:', response.data);
        return createNextGoalSuccess(response.data);
      }),
      catchError(error => {
        console.error(`Error creating next goal based on ${previousGoalId}:`, error);
        return of(createNextGoalFailure(error.response?.data?.message || 'Failed to create next goal'));
      })
    );
  })
);

export const goalEpics = combineEpics(
  fetchGoalsEpic,
  fetchGoalEpic,
  createGoalEpic,
  updateGoalEpic,
  contributeToGoalEpic,
  fetchMonthlyGoalsEpic,
  fetchGoalSummaryEpic,
  createNextGoalEpic
);
export default goalEpics;
