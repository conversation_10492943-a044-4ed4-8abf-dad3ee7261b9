import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { axiosInstance } from '../api/axiosConfig';
import {  createAction } from '@reduxjs/toolkit';

export const changePasswordStart   = createAction('accountManagement/changePasswordStart');
export const changePasswordSuccess = createAction('accountManagement/changePasswordSuccess');
export const changePasswordFailure = createAction('accountManagement/changePasswordFailure');
export const deleteAccountStart    = createAction('accountManagement/deleteAccountStart');
export const deleteAccountSuccess  = createAction('accountManagement/deleteAccountSuccess');
export const deleteAccountFailure  = createAction('accountManagement/deleteAccountFailure');
export const checkDeletionStatusStart   = createAction('accountManagement/checkDeletionStatusStart');
export const checkDeletionStatusSuccess = createAction('accountManagement/checkDeletionStatusSuccess');
export const checkDeletionStatusFailure = createAction('accountManagement/checkDeletionStatusFailure');

const initialState = {
  // Change password state
  changePasswordLoading: false,
  changePasswordError: null,
  changePasswordSuccess: false,
  
  // Delete account state
  deleteAccountLoading: false,
  deleteAccountError: null,
  deleteAccountSuccess: false,
  
  // Deletion status state
  deletionStatusLoading: false,
  deletionStatusError: null,
  deletionStatus: null,
};

// **UPDATED ENDPOINTS** - Replace these with your actual API endpoints
const API_ENDPOINTS = {
  CHANGE_PASSWORD: '/pennypal/api/v1/accounts/change-password',
  DELETE_ACCOUNT: '/pennypal/api/v1/accounts/delete-permanently',
  DELETION_STATUS: '/pennypal/api/v1/accounts/deletion-status'
};

// Updated thunk action to change password using createAsyncThunk
export const changePassword = createAsyncThunk(
  'accountManagement/changePassword',
  async (changePasswordDto, { rejectWithValue }) => {
    try {
      console.log('Making change password request to:', API_ENDPOINTS.CHANGE_PASSWORD);
      console.log('Request payload:', changePasswordDto);
      
      const response = await axiosInstance.post(API_ENDPOINTS.CHANGE_PASSWORD, changePasswordDto);
      console.log("Change password response:", response.data);
      
      return response.data;
    } catch (error) {
      console.error("Failed to change password:", error);
      console.error("Error details:", {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        url: error.config?.url
      });
      
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error || 
                          error.message || 
                          'Failed to change password';
      
      return rejectWithValue(errorMessage);
    }
  }
);

// Updated thunk action to delete account permanently
export const deleteAccountPermanently = createAsyncThunk(
  'accountManagement/deleteAccount',
  async (deleteAccountDto, { rejectWithValue }) => {
    try {
      console.log('Making delete account request to:', API_ENDPOINTS.DELETE_ACCOUNT);
      console.log('Request payload:', deleteAccountDto);
      
      const response = await axiosInstance.post(API_ENDPOINTS.DELETE_ACCOUNT, deleteAccountDto);
      console.log("Delete account response:", response.data);
      
      return response.data;
    } catch (error) {
      console.error("Failed to delete account:", error);
      console.error("Error details:", {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        url: error.config?.url
      });
      
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error || 
                          error.message || 
                          'Failed to delete account';
      
      return rejectWithValue(errorMessage);
    }
  }
);

// Updated thunk action to check deletion status
export const checkDeletionStatus = createAsyncThunk(
  'accountManagement/checkDeletionStatus',
  async (emailId, { rejectWithValue }) => {
    try {
      const url = `${API_ENDPOINTS.DELETION_STATUS}?emailId=${encodeURIComponent(emailId)}`;
      console.log('Making deletion status request to:', url);
      
      const response = await axiosInstance.get(url);
      console.log("Deletion status response:", response.data);
      
      return response.data.data;
    } catch (error) {
      console.error("Failed to check deletion status:", error);
      console.error("Error details:", {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        url: error.config?.url
      });
      
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error || 
                          error.message || 
                          'Failed to check deletion status';
      
      return rejectWithValue(errorMessage);
    }
  }
);

const accountManagementSlice = createSlice({
  name: 'accountManagement',
  initialState,
  reducers: {
    // Clear states
    clearChangePasswordState(state) {
      state.changePasswordLoading = false;
      state.changePasswordError = null;
      state.changePasswordSuccess = false;
    },
    clearDeleteAccountState(state) {
      state.deleteAccountLoading = false;
      state.deleteAccountError = null;
      state.deleteAccountSuccess = false;
    },
    clearDeletionStatus(state) {
      state.deletionStatus = null;
      state.deletionStatusLoading = false;
      state.deletionStatusError = null;
    },changePasswordFailure(state, action) {
  state.changePasswordError = action.payload;
  state.changePasswordSuccess = false;
  state.changePasswordLoading = false;
}

  },
  extraReducers: (builder) => {
    // Change password cases
    builder
      .addCase(changePassword.pending, (state) => {
        state.changePasswordLoading = true;
        state.changePasswordError = null;
        state.changePasswordSuccess = false;
      })
      .addCase(changePassword.fulfilled, (state, action) => {
        state.changePasswordLoading = false;
        state.changePasswordError = null;
        state.changePasswordSuccess = true;
      })
      .addCase(changePassword.rejected, (state, action) => {
        state.changePasswordLoading = false;
        state.changePasswordError = action.payload;
        state.changePasswordSuccess = false;
      })
      // Delete account cases
      .addCase(deleteAccountPermanently.pending, (state) => {
        state.deleteAccountLoading = true;
        state.deleteAccountError = null;
        state.deleteAccountSuccess = false;
      })
      .addCase(deleteAccountPermanently.fulfilled, (state, action) => {
        state.deleteAccountLoading = false;
        state.deleteAccountError = null;
        state.deleteAccountSuccess = true;
      })
      .addCase(deleteAccountPermanently.rejected, (state, action) => {
        state.deleteAccountLoading = false;
        state.deleteAccountError = action.payload;
        state.deleteAccountSuccess = false;
      })
      // Check deletion status cases
      .addCase(checkDeletionStatus.pending, (state) => {
        state.deletionStatusLoading = true;
        state.deletionStatusError = null;
      })
      .addCase(checkDeletionStatus.fulfilled, (state, action) => {
        state.deletionStatusLoading = false;
        state.deletionStatusError = null;
        state.deletionStatus = action.payload;
      })
      .addCase(checkDeletionStatus.rejected, (state, action) => {
        state.deletionStatusLoading = false;
        state.deletionStatusError = action.payload;
      });
  }
});

export const {
  clearChangePasswordState,
  clearDeleteAccountState,
  clearDeletionStatus 
} = accountManagementSlice.actions;

export default accountManagementSlice.reducer;