import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  receiptItems: [],
  loading: false,
  error: null,
   itemSummary: [],     // NEW
  summaryLoading: false,
  summaryError: null,
};

const receiptItemsSlice = createSlice({
  name: 'receiptItems',
  initialState,
  reducers: {
    fetchReceiptItemsRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchReceiptItemsSuccess: (state, action) => {
      state.loading = false;
      state.receiptItems = action.payload;
      state.error = null;
    },
    fetchReceiptItemsFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
      state.receiptItems = [];
    },
    fetchItemSummaryRequest: (state) => {
      state.summaryLoading = true;
      state.summaryError = null;
    },
    fetchItemSummarySuccess: (state, action) => {
      state.summaryLoading = false;
      state.itemSummary = action.payload;
    },
    fetchItemSummaryFailure: (state, action) => {
      state.summaryLoading = false;
      state.summaryError = action.payload;
    },
  },
});

export const {
  fetchReceiptItemsRequest,
  fetchReceiptItemsSuccess,
  fetchReceiptItemsFailure,
  fetchItemSummaryRequest,
  fetchItemSummarySuccess,
  fetchItemSummaryFailure,
} = receiptItemsSlice.actions;

export default receiptItemsSlice.reducer;