// epics/accountBalanceEpic.js
import { ofType } from 'redux-observable';
import { switchMap, catchError, map } from 'rxjs/operators';
import { of, from } from 'rxjs';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchAccountBalanceMonthlyDeltas,
  fetchAccountBalanceMonthlyDeltasSuccess,
  fetchAccountBalanceMonthlyDeltasFailure,
  fetchAccountBalanceMonthlyDeltasByAccountId,
  fetchAccountBalanceMonthlyDeltasByAccountIdSuccess,
  fetchAccountBalanceMonthlyDeltasByAccountIdFailure
} from '../redux/accountDeltaSlice';

import { getCurrentUserId } from '../../web/src/utils/AuthUtil'; // adjust path accordingly

// Epic for fetching monthly deltas
export const fetchAccountBalanceMonthlyDeltasEpic = (action$) =>
  action$.pipe(
    ofType(fetchAccountBalanceMonthlyDeltas.type),
    switchMap(action => {
      const userId = getCurrentUserId();
      if (!userId) {
        return of(fetchAccountBalanceMonthlyDeltasFailure('User not authenticated'));
      }

      const { year, month } = action.payload;

      return from(
        axiosInstance.get(`/api/v1/account/balances/delta/monthly/${userId}/${year}/${month}`)
      ).pipe(
        map(response => fetchAccountBalanceMonthlyDeltasSuccess(response.data)),
        catchError(error => {
          console.error('Error fetching monthly deltas:', error);
          return of(fetchAccountBalanceMonthlyDeltasFailure(
            error.response?.data?.message || error.message || 'Failed to fetch monthly deltas'
          ));
        })
      );
    }),
    catchError(error => {
      console.error('Epic error:', error);
      return of(fetchAccountBalanceMonthlyDeltasFailure('Epic processing error'));
    })
  );

// Epic for fetching monthly deltas by account ID
export const fetchAccountBalanceMonthlyDeltasByAccountIdEpic = (action$) =>
  action$.pipe(
    ofType(fetchAccountBalanceMonthlyDeltasByAccountId.type),
    switchMap(action => {
      const userId = getCurrentUserId();
      if (!userId) {
        return of(fetchAccountBalanceMonthlyDeltasByAccountIdFailure('User not authenticated'));
      }

      const { year, month } = action.payload;

      return from(
        axiosInstance.get(`/api/v1/account/balances/delta/monthly/accountid/${userId}/${year}/${month}`)
      ).pipe(
        map(response => fetchAccountBalanceMonthlyDeltasByAccountIdSuccess(response.data)),
        catchError(error => {
          console.error('Error fetching monthly deltas by account ID:', error);
          return of(fetchAccountBalanceMonthlyDeltasByAccountIdFailure(
            error.response?.data?.message || error.message || 'Failed to fetch monthly deltas by account ID'
          ));
        })
      );
    }),
    catchError(error => {
      console.error('Epic error:', error);
      return of(fetchAccountBalanceMonthlyDeltasByAccountIdFailure('Epic processing error'));
    })
  );