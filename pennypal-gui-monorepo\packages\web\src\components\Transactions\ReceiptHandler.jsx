import React, { useEffect, useRef,useState ,useCallback} from 'react';
import { useSelector, useDispatch ,} from 'react-redux';
import './TransactionPage.css';
import { logEvent } from '../../utils/EventLogger';
import { getCurrentUserId } from '../../utils/AuthUtil';

import CloseIcon from '@mui/icons-material/Close';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import ReceiptIcon from '@mui/icons-material/Receipt';
import ReceiptLongIcon from '@mui/icons-material/ReceiptLong';
import ListAltIcon from '@mui/icons-material/ListAlt';
// import CloseIcon from '@mui/icons-material/Close';
import DeleteIcon from '@mui/icons-material/Delete';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import Divider from '@mui/material/Divider';

import {
  Dialog, DialogActions, DialogContent, DialogTitle, Button, IconButton, InputAdornment,FormControl,InputLabel,
  LinearProgress, CircularProgress, TextField, Table, TableHead,Tooltip,  Select,Grid,Paper,ListSubheader ,
  TableBody, TableRow, TableCell, Tabs, Tab, MenuItem, Radio,Box,Typography,FormControlLabel,Checkbox
} from '@mui/material';

// Import actions from slices
import {
  fetchTransactionsStart,
  setSelectedTransaction,
  setOpenModal,
  toggleCardVisibility,
  setSearchName,
  setStartDate,
  setEndDate,
  setSearchDate,
  setSelectedDateRange,
  toggleDateFilter,
  setSortOrder,
  toggleSelectTransaction,
  toggleSelectAll,
  toggleAddTransactionModal,
  updateNewTransaction,
  resetNewTransaction,
  applyFilters,
  addTransactionRequest,
  fetchTransactionSummaryStart ,
  setCustomSearchText,
  selectAllTransactions,
  deselectAllTransactions,
  setSelectedCategory,
   setPage,
     fetchCategoryMonthlyExpensesStart, 
  setOpenCategoryModal ,
  setSelectedSubCategoryId,
  fetchAllIconsStart,
  clearSubCategoryIcon,
  
  // setSelectedCategoryId

} from '../../../../logic/redux/transactionSlice';

import { fetchAccountsRequest } from '../../../../logic/redux/transactionSlice';

import {
  setCurrentTab,
  setReceiptUploadModal,
  // setSelectedFile,
  setErrorMessage,
  setShowError,
  // setFileMetadata,
  setBlinkError,
  setIsReceiptModalOpen,
  setSelectedReceipt,
  // setSelectedTransaction,
  setEditingField,
  setEditedValue,
  setEditedItemIndex,
  setSelectedDate,
  setUploadProgress,
  setIsUploading,
  setIsProcessing,
  // setIsPopupVisible,
  // setReceiptNewTransaction,
  uploadReceiptRequest,
  saveReceiptRequest,
  addNewTransaction,
  fetchReceiptTransactionIdsRequest,
  fetchReceiptDetailsRequest,
  // addTransactionRequest,
  updateReceiptField,
  setJsonResponse, 
  setIsMatchingTransactionAdd,
  
} from '../../../../logic/redux/receiptSlice';




const ReceiptHandler = ({ darkMode,isEmbedded = false, onClose, onSave }) => {
  // Redux state selectors
  const dispatch = useDispatch();

   
  // Transactions state
  const transactions = useSelector(state => state.transactions.transactions);
   const page = useSelector(state => state.transactions.page);
const pageSize = useSelector(state => state.transactions.pageSize);
const totalElements = useSelector(state => state.transactions.totalElements);
const totalPages = useSelector(state => state.transactions.totalPages);
  const [selectedCategoryId, setSelectedCategoryId] = useState(null);
const [rows, setRows] = useState([]);
const [showHidden, setShowHidden] = useState(false);
const summary = useSelector(state => state.transactions.summary);
const accounts = useSelector((state) => state.transactions.accounts); // same slice
const loadingAccounts = useSelector((state) => state.transactions.loadingAccounts);
const categoryId = useSelector((state) => state.transactions.selectedCategoryId);
const hiddenTransactions = useSelector(state => state.transactions.hiddenTransactions);
  const selectedTransaction = useSelector(state => state.transactions.selectedTransaction);
  const openModal = useSelector(state => state.transactions.openModal);
  const isCardVisible = useSelector(state => state.transactions.isCardVisible);
  const searchName = useSelector(state => state.transactions.searchName);
  const searchDate = useSelector(state => state.transactions.searchDate);
  const sortOrder = useSelector(state => state.transactions.sortOrder);
  const dateFilterOpen = useSelector(state => state.transactions.dateFilterOpen);
  const selectedDateRange = useSelector(state => state.transactions.selectedDateRange);
  const selectedTransactions = useSelector(state => state.transactions.selectedTransactions);
  const allChecked = useSelector(state => state.transactions.allChecked);
  const isAddTransactionOpen = useSelector(state => state.transactions.isAddTransactionOpen);
   const newTransaction = useSelector(state => state.transactions.newTransaction);
   const hideFromBudgetSuccess = useSelector(state => state.transactions.hideFromBudgetSuccess);
  const hideFromBudgetError = useSelector(state => state.transactions.hideFromBudgetError);
 const [expandedRows, setExpandedRows] = useState({});
  const reconciledTransactionsById = useSelector(state => state.transactions.reconciledTransactionsById);
  const [expandedTransactionIds, setExpandedTransactionIds] = useState([]);
const [customFilterOpen, setCustomFilterOpen] = useState(false);
const customText = useSelector((state) => state.transactions.customSearchText);

const [showEmbeddedReceiptUpload, setShowEmbeddedReceiptUpload] = useState(false);
  // Receipts state
   const {
    errorMessage,
    jsonResponse,
    currentTab,
    receiptUploadModal,
    showError,
    blinkError,
    isReceiptModalOpen,
    selectedReceipt,
    // selectedFile,
    fileMetadata,
    // selectedTransaction,
    uploadPopupWidth,
    editingField,
    editedValue,
    editedItemIndex,
    selectedDate,
    uploadProgress,
    isUploading,
    isProcessing,
    isPopupVisible,
    
    //  receiptNewTransaction,
    receiptTransactionIds,
  } = useSelector((state) => state.receipts);
  const receiptNewTransaction = useSelector((state) => state.receipts.receiptNewTransaction);

    // Replace with actual data source
const [selectedFile, setSelectedFile] = useState([]);
  const isMatchingTransactionAdd = useSelector(state => state.receipts.isMatchingTransactionAdd);

 
const handleFileChange = (event) => {
  const files = Array.from(event.target.files);
  setSelectedFile(files);
  dispatch(setErrorMessage('')); // Clear error message
  dispatch(setShowError(false));

   logEvent('ReceiptHandler', 'FilesSelected', {
    fileCount: files.length,
    fileNames: files.map(f => f.name),
    fileTypes: files.map(f => f.type)
  });
};

const handleDrop = (event) => {
  event.preventDefault();
  const files = Array.from(event.dataTransfer.files);
  setSelectedFile(files);
  dispatch(setErrorMessage('')); // Clear error message
  dispatch(setShowError(false));

   logEvent('ReceiptHandler', 'FilesDropped', {
    fileCount: files.length,
    fileNames: files.map(f => f.name)
  });
};

const handleFileRemove = (index) => {
  logEvent('ReceiptHandler', 'FileRemoved', {
    fileName: selectedFile[index].name,
    remainingFiles: selectedFile.length - 1
  });

  setSelectedFile((prevFiles) => prevFiles.filter((_, i) => i !== index));
};

  // Open receipt modal

  // Close receipt modal
 const closeReceiptModal = () => {
  logEvent('ReceiptHandler', 'ModalClosed', {
    hadJsonResponse: !!jsonResponse,
    currentTab,
    selectedTransaction
  });
  console.log('closeReceiptModal called'); // Debug log
  dispatch(setReceiptUploadModal(false));
  dispatch(setSelectedTransaction(null));
  dispatch(setJsonResponse({})); // Reset jsonResponse
  dispatch(setIsUploading(false));
  dispatch(setIsProcessing(false));
  dispatch(setUploadProgress(0));
  setSelectedFile([]); // Clear local state
  dispatch(setErrorMessage('')); // Clear any error messages
  dispatch(setShowError(false));
};

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    logEvent('ReceiptHandler', 'TabChanged', {
    fromTab: currentTab,
    toTab: newValue
  });
    dispatch(setCurrentTab(newValue));
  };

  // Handle radio change
  const handleRadioChange = (transactionId) => {
    dispatch(setSelectedTransaction(
      selectedTransaction === transactionId ? null : transactionId
    ));
  };

  // Handle double click on fields
  const handleDoubleClick = (field, index) => {
     logEvent('ReceiptHandler', 'FieldEditStarted', {
    field,
    index,
    currentValue: jsonResponse?.Items?.[index]?.[field] || ''
  });
    dispatch(setEditingField(field));
    dispatch(setEditedItemIndex(index));
    dispatch(setEditedValue(jsonResponse?.Items?.[index]?.[field] || ''));
  };

  const handleFieldDoubleClick = (field) => {
    dispatch(setEditingField(field));
    dispatch(setEditedItemIndex(null));
    dispatch(setEditedValue(jsonResponse?.[field] || ''));
  };

  // Handle date change
  const handleDateChange = (date) => {
    dispatch(setSelectedDate(date));
    dispatch(setEditedValue(date.toISOString().split('T')[0]));
  };

  // Handle field blur
  const handleBlur = () => {
     logEvent('ReceiptHandler', 'FieldEditCompleted', {
    field: editingField,
    index: editedItemIndex,
    newValue: editedValue,
    oldValue: editedItemIndex !== null 
      ? jsonResponse?.Items?.[editedItemIndex]?.[editingField] 
      : jsonResponse?.[editingField]
  });
    if (editedItemIndex !== null) {
      dispatch(
        updateReceiptField({
          field: editingField,
          value: editedValue,
          itemIndex: editedItemIndex,
        })
      );
    } else {
      dispatch(
        updateReceiptField({
          field: editingField,
          value: editedValue,
          itemIndex: null,
        })
      );
    }
    dispatch(setEditingField(null));
    dispatch(setEditedItemIndex(null));
  };

  // Handle field change
  const handleChange = (e) => {
    dispatch(setEditedValue(e.target.value));
  };

  // Handle receipt upload
const handleUpload = () => {
  if (selectedFile.length === 0) return;

  logEvent('ReceiptHandler', 'UploadStarted', {
    fileCount: selectedFile.length,
    fileNames: selectedFile.map(f => f.name)
  });
  console.log('handleUpload selectedFile:', selectedFile); // Debug log
  dispatch(setIsUploading(true));
  dispatch(setUploadProgress(0));

  let progress = 0;
  const interval = setInterval(() => {
    progress += 10;
    if (progress >= 100) {
      clearInterval(interval);
      dispatch(setUploadProgress(100));
      dispatch(setIsUploading(false));
      dispatch(setIsProcessing(true));
    } else {
      dispatch(setUploadProgress(progress));
    }
  }, 500);

  const formData = new FormData();
  selectedFile.forEach((file) => formData.append('file', file));
  console.log('handleUpload dispatching uploadReceiptRequest'); // Debug log
  dispatch(uploadReceiptRequest(formData));
};

useEffect(() => {
  console.log('useEffect jsonResponse:', JSON.stringify(jsonResponse, null, 2)); // Detailed debug log
  if (jsonResponse && Object.keys(jsonResponse).length > 0) {
    setTimeout(() => {
      console.log('Opening receiptUploadModal with jsonResponse:', JSON.stringify(jsonResponse, null, 2)); // Debug log
      dispatch(setReceiptUploadModal(true));
      dispatch(setCurrentTab('receipt'));
      dispatch(setIsProcessing(false));
      setSelectedFile([]);
    }, 700);
  }
}, [jsonResponse, dispatch]);
  // Handle save receipt
  // In your component
  const { saveSuccess, saveError } = useSelector((state) => state.receipts);
  
useEffect(() => {
  if (saveSuccess) {
    alert('Receipt saved successfully!');
    closeReceiptModal();
  }
}, [saveSuccess]);

useEffect(() => {
  if (saveError) {
    alert(`Error saving receipt: ${saveError}`);
  }
}, [saveError]);
const handleSave = () => {
   logEvent('ReceiptHandler', 'SaveInitiated', {
    hasSelectedTransaction: !!selectedTransaction,
    merchant: jsonResponse.MerchantName,
    totalAmount: jsonResponse.Total
  });
  console.log('handleSave - Selected Transaction:', selectedTransaction);
  console.log('handleSave - Matching Transactions:', JSON.stringify(jsonResponse.matchingTransactions, null, 2));

  const filePath = jsonResponse.savedFilePath || jsonResponse.filePath;
  if (!filePath) {
    dispatch(setErrorMessage('Receipt file path is missing.'));
    dispatch(setShowError(true));
    return;
  }

  const updatedJsonResponse = {
    ...jsonResponse,
    savedFilePath: filePath,
    docType: jsonResponse.docType || 'receipt',
    docName: jsonResponse.docName || 'Uploaded Receipt',
    category: jsonResponse.category || 'Uncategorized',
    qrData: jsonResponse.qrData || '',
    scannedCopyPath: jsonResponse.scannedCopyPath || '',
    merchantName: jsonResponse.MerchantName || 'Unknown',
    merchantAddress: jsonResponse.MerchantAddress || 'Unknown',
    merchantPhoneNumber: jsonResponse.MerchantPhoneNumber || 'Unknown',
    transactionDate: jsonResponse.TransactionDate || '',
    transactionTime: jsonResponse.TransactionTime || '',
    Items: (jsonResponse.Items || []).map((item) => ({
      Name: item.Name || 'Unknown',
      Price: String(item.Price || '0.00'),
      TotalPrice: String(item.TotalPrice || '0.00'),
      Quantity: Number(item.Quantity || 1),
      Category: item.Category || 'Uncategorized',
    })),
    subtotal: String(jsonResponse.subtotal || jsonResponse.Subtotal || '0.00'),
    tax: String(jsonResponse.tax || jsonResponse.Tax || '0.00'),
    total: String(jsonResponse.total || jsonResponse.Total || '0.00'),
    matchingTransactions: jsonResponse.matchingTransactions || [],
    transactionId: selectedTransaction ? String(selectedTransaction) : null,
    userId: jsonResponse.userId || getCurrentUserId(),
  };

  if (editingField && editedItemIndex !== null) {
    updatedJsonResponse.Items = updatedJsonResponse.Items.map((item, index) =>
      index === editedItemIndex ? { ...item, [editingField]: editedValue } : item
    );
  } else if (editingField) {
    updatedJsonResponse[editingField] = editedValue;
  }

  console.log('handleSave - updatedJsonResponse:', JSON.stringify(updatedJsonResponse, null, 2));

  dispatch(
    saveReceiptRequest({
      jsonResponse: updatedJsonResponse,
      selectedTransaction: selectedTransaction,
    })
  );

  dispatch(setEditingField(null));
  dispatch(setEditedItemIndex(null));
  dispatch(setEditedValue(null));
};
 useEffect(() => {
    dispatch(fetchReceiptTransactionIdsRequest());
  }, [dispatch]);
  // Handle receipt click
  const handleReceiptClick = (transactionId) => {
     logEvent('ReceiptHandler', 'TransactionSelected', {
    transactionId,
    fromTab: currentTab
  });
    dispatch(fetchReceiptDetailsRequest(transactionId));
  };
   const handleCloseReceiptModal = () => {
    dispatch(setIsReceiptModalOpen(false));
  };

  // Add this function to your component

  // Render editable field
  const renderEditableField = (field, index) => {
    if (editingField === field && editedItemIndex === index) {
      return (
        <input
          type="text"
          value={editedValue || ''}
          onChange={handleChange}
          onBlur={handleBlur}
          autoFocus
          style={{ width: '100%', padding: '5px', border: '1px solid #ccc', borderRadius: '4px' }}
        />
      );
    }
    const value = jsonResponse?.Items?.[index]?.[field] || '-';
      if (typeof value === 'number') {
    return value.toFixed(2);
  }
    return value.replace(/[()$]/g, '');
  };

  // Render editable summary field
  const renderEditableSummaryField = (field) => {
    if (editingField === field) {
      return (
        <input
          type="text"
          value={editedValue || ''}
          onChange={handleChange}
          onBlur={handleBlur}
          autoFocus
          style={{ width: '100%', padding: '5px', border: '1px solid #ccc', borderRadius: '4px' }}
        />
      );
    }
    const value = jsonResponse?.[field] || '-';
     if (typeof value === 'number') {
    return value.toFixed(2);
  }
    return value.replace(/[()$]/g, '');
  };
 


  const handleCloseAddTransaction = () => {
    dispatch(toggleAddTransactionModal(false));
  };

const handletranssave = () => {
  let formattedDate = null;

  if (newTransaction.date) {
    const fullDate = new Date(newTransaction.date);
    if (!isNaN(fullDate)) {
      formattedDate = fullDate.toISOString();
    } else {
      console.warn('Invalid date format:', newTransaction.date);
    }
  }

  const transactionData = {
    transactionId: `temp-${Date.now()}`,
    transactionDate: formattedDate,
    description: newTransaction.description,
    category: newTransaction.category,
    account: newTransaction.account,
    transactionAmount: newTransaction.amount ? parseFloat(newTransaction.amount) : 0,
      userId: getCurrentUserId(),
  };

  dispatch(addTransactionRequest({ transactionData }));
};


const handleShowPopup = () => {
  dispatch(setSelectedTransaction({
    date: new Date().toISOString().split('T')[0], // Set default date to today
    name: '',
    subcategory: '',
    bank: '',
    amount: '',
    tax: '',
    notes: '',
    tag: '',
    hideFromBudget: false,
    hidden: false,
  }));
dispatch(setIsMatchingTransactionAdd(true));
  dispatch(setOpenModal(true));
}

 const handleOpenAddTransaction = () => {
   dispatch(setSelectedTransaction({
     date: new Date().toISOString().split('T')[0],
     name: '',
     subcategory: '',
     bank: '',
     amount: '',
     tax: '',
     notes: '',
     tag: '',
     hideFromBudget: false,
     hidden: false,
   }));
   dispatch(setIsMatchingTransactionAdd(false));
   dispatch(setOpenModal(true));
 };
 
  
const content = (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: isEmbedded ? '100%' : 'auto',
        overflow: 'hidden',
      }}
    >
      {jsonResponse && Object.keys(jsonResponse).length > 0 ? (
        <>
          {!isEmbedded && (
            <Box
              sx={{
                backgroundColor: '#8BC34A',
                padding: '16px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                borderRadius: '4px 4px 0 0',
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <ReceiptIcon sx={{ marginRight: '8px', color: '#333' }} />
                <Typography variant="h6" sx={{ color: '#333', fontWeight: 'bold' }}>
                  Receipt
                </Typography>
              </Box>
              <IconButton onClick={closeReceiptModal} sx={{ color: '#333' }}>
                <CloseIcon />
              </IconButton>
            </Box>
          )}

          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs
              value={currentTab}
              onChange={(e, newValue) => dispatch(setCurrentTab(newValue))}
              aria-label="receipt tabs"
              sx={{
                '& .MuiTabs-indicator': {
                  backgroundColor: '#8BC34A',
                },
              }}
            >
              <Tab
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <ReceiptIcon sx={{ mr: 1, fontSize: '18px' }} />
                    <span>Current Receipt</span>
                  </Box>
                }
                value="receipt"
                sx={{
                  fontFamily: 'Roboto, sans-serif',
                  '&.Mui-selected': {
                    color: '#8BC34A',
                    fontWeight: 'bold',
                  },
                }}
              />
              <Tab
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <ListAltIcon sx={{ mr: 1, fontSize: '18px' }} />
                    <span>Transaction History</span>
                  </Box>
                }
                value="matchingTransaction"
                sx={{
                  fontFamily: 'Roboto, sans-serif',
                  '&.Mui-selected': {
                    color: '#8BC34A',
                    fontWeight: 'bold',
                  },
                }}
              />
            </Tabs>
          </Box>

          {currentTab === 'receipt' && (
            <Box
              sx={{
                padding: '16px',
                flex: 1,
                overflowY: 'auto',
                display: 'flex',
                flexDirection: 'column',
                width: '100%',
              }}
            >
              <Box
                sx={{
                  textAlign: 'center',
                  marginBottom: '16px',
                  padding: '16px',
                  borderBottom: '1px solid #8BC34A',
                  borderTop: '1px solid #8BC34A',
                  backgroundColor: '#f6fbf3',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '90%',
                }}
              >
                <Typography variant="h5" sx={{ fontWeight: 'bold', textAlign: 'center', width: '100%' }}>
                  {jsonResponse?.MerchantName || ' '}
                </Typography>
                <Typography variant="body1" sx={{ textAlign: 'center', width: '100%' }}>
                  {jsonResponse?.MerchantAddress || ' '}
                </Typography>
                <Typography variant="body2" sx={{ textAlign: 'center', width: '100%' }}>
                  {jsonResponse?.MerchantPhoneNumber || ' '}
                </Typography>
              </Box>

              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <Grid container spacing={2} sx={{ marginBottom: '24px', justifyContent: 'center', width: '100%' }}>
                  <Grid item xs={6} sx={{ textAlign: 'center', display: 'flex', justifyContent: 'center' }}>
                    <Paper
                      sx={{
                        p: 2,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: '#f0f7e6',
                        width: '100%',
                        textAlign: 'center',
                      }}
                    >
                      <CalendarTodayIcon sx={{ mr: 1, color: '#8BC34A' }} />
                      <Box
                        sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}
                        onDoubleClick={() => handleFieldDoubleClick('TransactionDate')}
                      >
                        <Typography variant="caption" color="text.secondary">
                          Date
                        </Typography>
                        {/* Your existing DatePicker logic */}
                      </Box>
                    </Paper>
                  </Grid>
                  <Grid item xs={6} sx={{ textAlign: 'center', display: 'flex', justifyContent: 'center' }}>
                    <Paper
                      sx={{
                        p: 2,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: '#f0f7e6',
                        width: '100%',
                        textAlign: 'center',
                      }}
                    >
                      <AccessTimeIcon sx={{ mr: 1, color: '#8BC34A' }} />
                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                        <Typography variant="caption" color="text.secondary">
                          Time
                        </Typography>
                        <Typography variant="body1">{jsonResponse?.TransactionTime || '-'}</Typography>
                      </Box>
                    </Paper>
                  </Grid>
                </Grid>
              </LocalizationProvider>

              <Box sx={{ flex: 1, width: '100%' }}>
                <Paper
                  elevation={3}
                  sx={{
                    mb: 3,
                    border: '1px solid #e0e0e0',
                    borderRadius: '8px',
                    overflow: 'hidden',
                  }}
                >
                  <Table sx={{ minWidth: '100%' }}>
                    <TableHead>
                      <TableRow sx={{ backgroundColor: '#8BC34A' }}>
                        <TableCell sx={{ color: '#333', fontWeight: 'bold' }}>Item</TableCell>
                        <TableCell sx={{ color: '#333', fontWeight: 'bold' }}>Qty</TableCell>
                        <TableCell sx={{ color: '#333', fontWeight: 'bold' }}>Price</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {jsonResponse?.Items && jsonResponse.Items.length > 0 ? (
                        jsonResponse.Items.map((item, index) => (
                          <TableRow
                            key={index}
                            sx={{
                              '&:hover': { backgroundColor: '#f5f5f5' },
                            }}
                          >
                            <TableCell sx={{ cursor: 'pointer' }} onDoubleClick={() => handleDoubleClick('Name', index)}>
                              {renderEditableField('Name', index)}
                            </TableCell>
                            <TableCell
                              sx={{ cursor: 'pointer' }}
                              onDoubleClick={() => handleDoubleClick('Quantity', index)}
                            >
                              {renderEditableField('Quantity', index)}
                            </TableCell>
                            <TableCell
                              sx={{ cursor: 'pointer' }}
                              onDoubleClick={() => handleDoubleClick('TotalPrice', index)}
                            >
                              {renderEditableField('TotalPrice', index)}
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={4} sx={{ textAlign: 'center', py: 2 }}>
                            No items found
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </Paper>

                <Paper sx={{ p: 2, backgroundColor: '#f9f9f9' }}>
                  <Grid container>
                    <Grid item xs={6}></Grid>
                    <Grid item xs={6}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body1">Subtotal:</Typography>
                        <Typography
                          variant="body1"
                          sx={{ cursor: 'pointer', textAlign: 'right', minWidth: '100px' }}
                          onDoubleClick={() => handleFieldDoubleClick('Subtotal')}
                        >
                          {renderEditableSummaryField('Subtotal')}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body1">Tax:</Typography>
                        <Typography
                          variant="body1"
                          sx={{ cursor: 'pointer', textAlign: 'right', minWidth: '100px' }}
                          onDoubleClick={() => handleFieldDoubleClick('Tax')}
                        >
                          {renderEditableSummaryField('Tax')}
                        </Typography>
                      </Box>
                      <Divider sx={{ my: 1 }} />
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                          Total:
                        </Typography>
                        <Typography
                          variant="body1"
                          sx={{ fontWeight: 'bold', cursor: 'pointer', textAlign: 'right', minWidth: '450px' }}
                          onDoubleClick={() => handleFieldDoubleClick('Total')}
                        >
                          {renderEditableSummaryField('Total')}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </Paper>
              </Box>

              <Box sx={{ mt: 'auto', marginBottom: '40px', display: 'flex', justifyContent: 'center', width: '100%' }}>
                <Button
                  onClick={handleSave}
                  color="primary"
                  variant="contained"
                  disabled={isUploading || isProcessing}
                  sx={{
                    fontFamily: 'Roboto, sans-serif',
                    mt: 2,
                    backgroundColor: '#8BC34A',
                    '&:hover': {
                      backgroundColor: '#7CB342',
                    },
                    width: '100%',
                    maxWidth: '100%',
                    borderRadius: '4px',
                    textAlign: 'center',
                  }}
                  fullWidth
                >
                  Submit
                </Button>
              </Box>
            </Box>
          )}

          {currentTab === 'matchingTransaction' && (
            <Box sx={{ padding: '16px', flex: 1, overflowY: 'auto' }}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Matching Transactions
              </Typography>
              <Box sx={{ flex: 1, overflow: 'auto', mb: 2 }}>
                <Paper sx={{ overflow: 'auto' }}>
                  <Table>
                    <TableHead>
                      <TableRow sx={{ backgroundColor: '#8BC34A' }}>
                        <TableCell sx={{ color: '#333', fontWeight: 'bold' }}></TableCell>
                        <TableCell sx={{ color: '#333', fontWeight: 'bold' }}>Date</TableCell>
                        <TableCell sx={{ color: '#333', fontWeight: 'bold' }}>Transaction Name</TableCell>
                        <TableCell sx={{ color: '#333', fontWeight: 'bold' }}>Category</TableCell>
                        <TableCell sx={{ color: '#333', fontWeight: 'bold' }}>Account</TableCell>
                        <TableCell sx={{ color: '#333', fontWeight: 'bold' }}>Amount</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {jsonResponse?.matchingTransactions?.filter(tx => tx).length > 0 ? (
                        jsonResponse.matchingTransactions
                          .filter(tx => tx)
                          .map((transaction, index) => (
                            <TableRow
                              key={index}
                              sx={{
                                backgroundColor: selectedTransaction === transaction.transactionId ? '#f0f7f0' : 'transparent',
                                '&:hover': { backgroundColor: '#f5f5f5' },
                              }}
                            >
                              <TableCell>
                                <Radio
                                  checked={selectedTransaction === transaction.transactionId}
                                  onChange={() => dispatch(setSelectedTransaction(transaction.transactionId))}
                                  color="primary"
                                  sx={{
                                    color: '#8BC34A',
                                    '&.Mui-checked': { color: '#8BC34A' },
                                  }}
                                />
                              </TableCell>
                              <TableCell>
                                {transaction.transactionDate ?
                                  new Date(transaction.transactionDate).toLocaleDateString() : '-'}
                              </TableCell>
                              <TableCell>{transaction.description || '-'}</TableCell>
                              <TableCell>{transaction.category || '-'}</TableCell>
                              <TableCell>
                                {transaction.account ||
                                 accounts.find(a => a.accountId === transaction.accountId)?.accountName || '-'}
                              </TableCell>
                              <TableCell>
                                ${transaction.transactionAmount?.toFixed(2) || '0.00'}
                              </TableCell>
                            </TableRow>
                          ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={6} sx={{ textAlign: 'center', padding: '16px' }}>
                            No matching transactions found
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </Paper>
              </Box>
              <Box sx={{ mt: 'auto', marginBottom: '40px' }}>
                <Button
                  onClick={handleShowPopup}
                  color="primary"
                  variant="contained"
                  sx={{
                    fontFamily: 'Roboto, sans-serif',
                    mt: 2,
                    backgroundColor: '#8BC34A',
                    '&:hover': { backgroundColor: '#7CB342' },
                    width: '100%',
                  }}
                  fullWidth
                >
                  Add Transaction
                </Button>
              </Box>
            </Box>
          )}
        </>
      ) : (
        <Box sx={{ padding: '16px', flex: 1, display: 'flex', flexDirection: 'column' }}>
          {!isEmbedded && <Typography variant="h6" sx={{ textAlign: 'center', mb: 2 }}>Upload Receipt</Typography>}
          <Box sx={{ display: 'flex', gap: '10px', flex: 1 }}>
            <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Box
                onDrop={handleDrop}
                onDragOver={(e) => e.preventDefault()}
                onClick={() => document.getElementById('fileInput').click()}
                sx={{
                  padding: '20px',
                  textAlign: 'center',
                  marginBottom: '10px',
                  cursor: 'pointer',
                  borderRadius: '8px',
                  maxWidth: '400px',
                  backgroundColor: '#6aa84f',
                  border: '2px dashed white',
                  color: 'white',
                  fontWeight: 'bold',
                }}
              >
                <Typography>Click to upload or Drag and Drop</Typography>
              </Box>
              {isUploading && (
                <Box sx={{ width: '100%', maxWidth: '300px', marginTop: '10px' }}>
                  <LinearProgress
                    variant="determinate"
                    value={uploadProgress}
                    sx={{
                      height: '10px',
                      borderRadius: '5px',
                      backgroundColor: '#e0e0e0',
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: '#4caf50',
                      },
                    }}
                  />
                  <Typography sx={{ textAlign: 'center', marginTop: '5px', color: '#4caf50' }}>
                    Uploading... {uploadProgress}%
                  </Typography>
                </Box>
              )}
              {uploadProgress === 100 && !isUploading && !isProcessing && (
                <Typography sx={{ color: 'green', textAlign: 'center' }}>
                  Receipt uploaded successfully!
                </Typography>
              )}
              {isProcessing && (
                <Box sx={{ textAlign: 'center', marginTop: '10px', color: 'green' }}>
                  <Typography>Processing receipt...</Typography>
                  <CircularProgress size={24} sx={{ marginTop: '10px' }} />
                </Box>
              )}
              <Box sx={{ width: '100%', maxWidth: '300px' }}>
                {selectedFile && selectedFile.length > 0 ? (
                  selectedFile.map((file, index) => (
                    <Box
                      key={index}
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        background: 'white',
                        borderRadius: '20px',
                        padding: '10px',
                        marginBottom: '5px',
                        border: '1px solid #ccc',
                        boxShadow: '2px 2px 5px rgba(0,0,0,0.1)',
                      }}
                    >
                      <Typography>{file.name}</Typography>
                      <IconButton size="small" onClick={() => handleFileRemove(index)}>
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  ))
                ) : null}
              </Box>
              <input
                type="file"
                style={{ display: 'none' }}
                id="fileInput"
                multiple
                onChange={handleFileChange}
              />
            </Box>
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                borderLeft: '2px solid #aaa',
              }}
            >
              <img src="/images/qr-code.png" alt="QR Code" style={{ width: '120px', height: '120px' }} />
            </Box>
          </Box>
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between' }}>
            <Button onClick={closeReceiptModal} color="secondary">
              Cancel
            </Button>
            <Button
              onClick={handleUpload}
              color="primary"
              variant="contained"
              disabled={!selectedFile || selectedFile.length === 0}
            >
              Upload
            </Button>
          </Box>
        </Box>
      )}
    </Box>
  );

  return isEmbedded ? content : (
    <Dialog
      open={receiptUploadModal}
      onClose={closeReceiptModal}
      sx={{
        '& .MuiDialog-paper': {
          width: jsonResponse && Object.keys(jsonResponse).length > 0
            ? { xs: '95vw', sm: '85vw', md: '70vw', lg: '60vw', xl: '50vw' }
            : { xs: '90vw', sm: '500px', md: '600px' },
          maxWidth: jsonResponse && Object.keys(jsonResponse).length > 0 ? '700px' : '650px',
          height: jsonResponse && Object.keys(jsonResponse).length > 0 ? '85vh' : 'auto',
          maxHeight: '90vh',
          fontFamily: 'Roboto, sans-serif',
          padding: '10px',
          boxSizing: 'border-box',
          position: 'relative',
          overflow: 'visible',
          backgroundColor: 'white',
          display: 'flex',
          flexDirection: 'column',
          transition: 'width 0.3s ease',
          clipPath: `polygon(
            0% 0%, 100% 0%, 100% 95%, 95% 98%, 90% 95%, 85% 98%, 80% 95%, 75% 98%, 70% 95%, 65% 98%,
            60% 95%, 55% 98%, 50% 95%, 45% 98%, 40% 95%, 35% 98%, 30% 95%, 25% 98%, 20% 95%, 15% 98%,
            10% 95%, 5% 98%, 0% 95%)`,
        },
        '& .MuiBackdrop-root': {
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
        },
      }}
    >
      {content}
    </Dialog>
  );
};

export default ReceiptHandler;
