// import React, { useState } from 'react';
// import { FaRobot } from 'react-icons/fa';
// import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
// import { faTimes } from '@fortawesome/free-solid-svg-icons';

// const CashflowAIChat = ({
//   darkMode,
//   currentTheme,
//   setShowAIChat,
//   showAIChat,
//   month,
//   year,
//   setMonth,
//   setYear
// }) => {
//   const [chatHistory, setChatHistory] = useState([]);
//   const [query, setQuery] = useState('');
//   const [isLoading, setIsLoading] = useState(false);

//   const handleQuerySend = () => {
//     if (!query.trim()) return;
//     const updated = [...chatHistory, { type: 'user', text: query }];
//     setChatHistory(updated);
//     setQuery('');
//     setIsLoading(true);

//     setTimeout(() => {
//       let lower = query.toLowerCase();
//       let newMonth = month;
//       let newYear = year;

//       if (lower.includes('next month')) {
//         if (month === 12) {
//           newMonth = 1;
//           newYear = year + 1;
//         } else {
//           newMonth += 1;
//         }
//       } else if (lower.includes('previous month') || lower.includes('last month')) {
//         if (month === 1) {
//           newMonth = 12;
//           newYear = year - 1;
//         } else {
//           newMonth -= 1;
//         }
//       } else if (/\b(january|february|march|april|may|june|july|august|september|october|november|december)\b/.test(lower)) {
//         const monthMap = {
//           january: 1, february: 2, march: 3, april: 4, may: 5, june: 6,
//           july: 7, august: 8, september: 9, october: 10, november: 11, december: 12
//         };
//         const found = Object.keys(monthMap).find(m => lower.includes(m));
//         if (found) newMonth = monthMap[found];
//       }

//       const yearMatch = lower.match(/\b(20\d{2})\b/);
//       if (yearMatch) newYear = parseInt(yearMatch[1], 10);

//       setMonth(newMonth);
//       setYear(newYear);

//       setChatHistory([...updated, { type: 'ai', text: `Okay, showing data for ${newMonth}/${newYear}` }]);
//       setIsLoading(false);
//     }, 1000);
//   };

//   return (
//     <div
//       className={`fixed top-0 right-0 h-full w-96 border-l shadow-2xl z-50 transform transition-transform duration-300 ease-in-out ${showAIChat ? 'translate-x-0' : 'translate-x-full'}`}
//       style={{ backgroundColor: currentTheme.cardBg, borderColor: currentTheme.border }}
//     >
//       <div className="p-4 border-b flex items-center justify-between" style={{ backgroundColor: currentTheme.cardBg }}>
//         <div className="flex items-center space-x-3">
//           <div className="p-2 rounded-xl border" style={{ backgroundColor: currentTheme.accent + '20' }}>
//             <FaRobot className="text-lg" style={{ color: currentTheme.primary }} />
//           </div>
//           <div>
//             <h3 className="text-lg font-bold">AI Assistant</h3>
//             <p className="text-xs text-gray-500">Ask about your cashflow</p>
//           </div>
//         </div>
//         <button onClick={() => setShowAIChat(false)} className="p-2 hover:bg-gray-200 rounded-full">
//           <FontAwesomeIcon icon={faTimes} />
//         </button>
//       </div>

//       <div className="p-4 space-y-4 overflow-y-auto" style={{ height: 'calc(100vh - 160px)' }}>
//         {chatHistory.map((msg, idx) => (
//           <div
//             key={idx}
//             className={`p-3 rounded-lg w-fit max-w-xs ${msg.type === 'user' ? 'bg-blue-100 text-left' : 'bg-green-100 text-right'} text-sm`}
//           >
//             {msg.text}
//           </div>
//         ))}
//         {isLoading && <p className="text-sm text-gray-400">AI is thinking...</p>}
//       </div>

//       <div className="p-4 border-t flex items-center space-x-2">
//         <input
//           className="flex-1 border rounded px-3 py-2 text-sm"
//           placeholder="Ask something..."
//           value={query}
//           onChange={(e) => setQuery(e.target.value)}
//           onKeyDown={(e) => e.key === 'Enter' && handleQuerySend()}
//         />
//         <button
//           onClick={handleQuerySend}
//           className="px-4 py-2 text-sm rounded text-white"
//           style={{ backgroundColor: currentTheme.primary }}
//         >
//           Send
//         </button>
//       </div>
//     </div>
//   );
// };

// export default CashflowAIChat;
import React, { useState, useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { FaRobot } from 'react-icons/fa';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faArrowUp, faThumbsUp, faThumbsDown, faCopy } from '@fortawesome/free-solid-svg-icons';
import { axiosInstance } from '../../../../../../packages/logic/api/axiosConfig';
import { getCurrentUserId } from '@pp-web/utils/AuthUtil';
import { logEvent } from '@pp-web/utils/EventLogger';
import { fetchHistoryRequest } from '../../../../../../packages/logic/redux/chatbotSlice';

const CashflowAIChat = ({
  darkMode,
  currentTheme,
  setShowAIChat,
  showAIChat,
  month,
  year,
  setMonth,
  setYear
}) => {
  const dispatch = useDispatch();
  const [chatHistory, setChatHistory] = useState([]);
  const [query, setQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [copiedIndex, setCopiedIndex] = useState(null);
  const chatContainerRef = useRef(null);
  const chatEndRef = useRef(null);
  const [isScrolledToBottom, setIsScrolledToBottom] = useState(true);
  const userId = getCurrentUserId();
  const cache = useSelector(state => state.cache);

  useEffect(() => {
    // Use cached data if available, otherwise fetch
    if (cache?.chatbotHistoryLoaded && cache?.chatbotHistory?.length >= 0 &&
        cache?.chatbotHistoryParams?.userId === userId) {
      const flatHistory = cache.chatbotHistory.flatMap(log => {
        const parsed = tryParseJSON(log.response);
        return [
          { type: 'user', text: log.userQuery },
          { type: 'ai', text: parsed?.summary || log.response, chatId: log.id }
        ];
      });
      setChatHistory(flatHistory);
    } else {
      dispatch(fetchHistoryRequest({ userId }));
    }
  }, [dispatch, userId, cache?.chatbotHistoryLoaded]);

  // Listen for Redux state changes to update local state
  useEffect(() => {
    if (cache?.chatbotHistoryLoaded && cache?.chatbotHistory) {
      const flatHistory = cache.chatbotHistory.flatMap(log => {
        const parsed = tryParseJSON(log.response);
        return [
          { type: 'user', text: log.userQuery },
          { type: 'ai', text: parsed?.summary || log.response, chatId: log.id }
        ];
      });
      setChatHistory(flatHistory);
      setIsLoading(false);
    }
  }, [cache?.chatbotHistory, cache?.chatbotHistoryLoaded]);

  const tryParseJSON = (text) => {
    try {
      const cleanText = text.replace(/^```json|```$/g, '').trim();
      const evaluatedText = cleanText.replace(
        /"value"\s*:\s*([0-9\.\s\+\-\*\/]+)/g,
        (_, expr) => `"value": ${eval(expr)}`
      );
      return JSON.parse(evaluatedText);
    } catch {
      return null;
    }
  };

  const handleQuerySend = async () => {
    logEvent('CashflowAIChat', 'sendQuery', { query });
    if (!query.trim()) return;

    const updatedHistory = [...chatHistory, { type: 'user', text: query }];
    setChatHistory(updatedHistory);
    setQuery('');
    setIsLoading(true);

    try {
      const res = await axiosInstance.post('pennypal/api/v1/chatbot/query', {
        user_id: userId,
        user_query: query,
      });

      const parsed = tryParseJSON(res.data.response);
      const aiMessage = {
        type: 'ai',
        text: parsed?.summary || res.data.response,
        chatId: res.data.chat_id,
      };

      setChatHistory([...updatedHistory, aiMessage]);

      // Parse query for month and year
      const lowerQuery = query.toLowerCase();
      let newMonth = month;
      let newYear = year;

      if (lowerQuery.includes('next month')) {
        if (month === 12) {
          newMonth = 1;
          newYear = year + 1;
        } else {
          newMonth += 1;
        }
      } else if (lowerQuery.includes('previous month') || lowerQuery.includes('last month')) {
        if (month === 1) {
          newMonth = 12;
          newYear = year - 1;
        } else {
          newMonth -= 1;
        }
      } else if (/\b(january|february|march|april|may|june|july|august|september|october|november|december)\b/.test(lowerQuery)) {
        const monthMap = {
          january: 1, february: 2, march: 3, april: 4, may: 5, june: 6,
          july: 7, august: 8, september: 9, october: 10, november: 11, december: 12
        };
        const found = Object.keys(monthMap).find(m => lowerQuery.includes(m));
        if (found) newMonth = monthMap[found];
      }

      // Parse query for year
      if (lowerQuery.includes('next year')) {
        newYear += 1;
      } else if (lowerQuery.includes('previous year') || lowerQuery.includes('last year')) {
        newYear -= 1;
      } else if (lowerQuery.includes('current year')) {
        newYear = year;
      } else if (lowerQuery.includes('this year')) {
        newYear = year;
      } else {
        const yearMatch = lowerQuery.match(/\b(20\d{2})\b/);
        if (yearMatch) newYear = parseInt(yearMatch[1], 10);
      }

      if (newMonth !== month || newYear !== year) {
        setMonth(newMonth);
        setYear(newYear);
        setChatHistory(prev => [...prev, { type: 'ai', text: `Okay, showing data for ${newMonth}/${newYear}` }]);
      }
    } catch (err) {
      setChatHistory([
        ...updatedHistory,
        { type: 'ai', text: '⚠️ Error getting response from AI.' }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') handleQuerySend();
  };

  const scrollToBottom = () => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [chatHistory, isLoading]);

  const handleScroll = () => {
    const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;
    setIsScrolledToBottom(scrollTop + clientHeight >= scrollHeight - 20);
  };

  const copyToClipboard = (text, index) => {
    navigator.clipboard.writeText(text);
    setCopiedIndex(index);
    setTimeout(() => setCopiedIndex(null), 2000);
  };

  return (
    <div
      className={`fixed top-0 right-0 h-full w-96 border-l shadow-2xl z-50 transform transition-transform duration-300 ease-in-out ${showAIChat ? 'translate-x-0' : 'translate-x-full'}`}
      style={{ backgroundColor: currentTheme.cardBg, borderColor: currentTheme.border }}
    >
      <style>
        {`
          @keyframes bounce-dot {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
          }
          .dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            margin: 0 2px;
            background-color: ${currentTheme.primary};
            border-radius: 50%;
            animation: bounce-dot 1.4s infinite ease-in-out both;
          }
          .dot:nth-child(1) { animation-delay: -0.32s; }
          .dot:nth-child(2) { animation-delay: -0.16s; }
          .dot:nth-child(3) { animation-delay: 0s; }
        `}
      </style>

      <div className="p-4 border-b flex items-center justify-between" style={{ backgroundColor: currentTheme.cardBg, borderColor: currentTheme.border }}>
        <div className="flex items-center space-x-3">
          <div className="p-2 rounded-xl border" style={{ backgroundColor: currentTheme.accent + '20' }}>
            <FaRobot className="text-lg" style={{ color: currentTheme.primary }} />
          </div>
          <div>
            <h3 className="text-lg font-bold">AI Assistant</h3>
            <p className="text-xs text-gray-500">Ask about your cashflow</p>
          </div>
        </div>
        <button onClick={() => setShowAIChat(false)} className="p-2 hover:bg-gray-200 rounded-full">
          <FontAwesomeIcon icon={faTimes} />
        </button>
      </div>

      <div
        className="p-4 space-y-4 overflow-y-auto scrollbar-hide"
        style={{ height: 'calc(100vh - 160px)', WebkitOverflowScrolling: 'touch', 'scrollbar-width': 'none', '::-webkit-scrollbar': { display: 'none' } }}
        ref={chatContainerRef}
        onScroll={handleScroll}
      >
        <div className="flex items-start space-x-3">
          <div className="p-2 rounded-full" style={{ backgroundColor: `${currentTheme.primary}20` }}>
            <FaRobot className="text-sm" style={{ color: currentTheme.primary }} />
          </div>
          <div className="flex-1 p-3 rounded-xl max-w-xs" style={{ backgroundColor: darkMode ? `${currentTheme.primary}20` : `${currentTheme.primary}10`, color: currentTheme.text }}>
            <p className="text-sm">Hi! I'm your AI financial assistant. I can help you analyze your cashflow and answer questions about your finances.</p>
          </div>
        </div>

        {chatHistory.map((msg, index) => (
          <div key={index} className={`flex flex-col ${msg.type === 'user' ? 'items-start' : 'items-end'}`}>
            <div className={`inline-block max-w-full px-4 py-2 rounded-lg ${msg.type === 'user' ? 'bg-blue-100 text-blue-900' : 'bg-green-100 text-green-900'} ml-2`}>
              {msg.text}
            </div>
            {msg.type === 'ai' && (
              <div className="flex space-x-2 mt-1 mr-2 items-center relative">
                <FontAwesomeIcon icon={faThumbsUp} className={`w-4 h-4 cursor-pointer ${darkMode ? 'text-slate-400' : 'text-slate-600'}`} />
                <FontAwesomeIcon icon={faThumbsDown} className={`w-4 h-4 cursor-pointer ${darkMode ? 'text-slate-400' : 'text-slate-600'}`} />
                <FontAwesomeIcon icon={faCopy} className={`w-4 h-4 cursor-pointer ${darkMode ? 'text-slate-400' : 'text-slate-600'}`} onClick={() => copyToClipboard(msg.text, index)} />
                {copiedIndex === index && (
                  <span className="absolute -top-6 left-1/2 -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-0.5 rounded shadow-md">Copied!</span>
                )}
              </div>
            )}
          </div>
        ))}
        {isLoading && (
          <div className="flex flex-col items-end">
            <div className="inline-block max-w-full px-4 py-2 rounded-lg bg-green-100 text-green-900 ml-2">
              <span className="dot"></span>
              <span className="dot"></span>
              <span className="dot"></span>
            </div>
          </div>
        )}
        <div ref={chatEndRef} />
      </div>

      {!isScrolledToBottom && (
        <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 z-10">
          <button onClick={scrollToBottom} className="bg-gray-800 text-white text-sm px-3 py-1 rounded-md shadow-lg transition hover:bg-gray-700">↓</button>
        </div>
      )}

      <div className="p-4 border-t flex items-center space-x-2" style={{ backgroundColor: currentTheme.cardBg, borderColor: currentTheme.border }}>
        <input
          className="flex-1 border rounded-xl px-4 py-3 text-sm focus:outline-none focus:ring-2 transition-all duration-200"
          placeholder="Ask about your cashflow..."
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={handleKeyPress}
          style={{ backgroundColor: darkMode ? currentTheme.bg : currentTheme.cardBg, color: currentTheme.text, borderColor: currentTheme.border }}
          onFocus={(e) => e.target.style.borderColor = currentTheme.primary}
          onBlur={(e) => e.target.style.borderColor = currentTheme.border}
        />
        <button
          onClick={handleQuerySend}
          disabled={isLoading}
          className="p-3 rounded-xl text-white transition-all duration-200 font-semibold shadow-lg"
          style={{ backgroundColor: currentTheme.primary }}
        >
          <FontAwesomeIcon icon={faArrowUp} />
        </button>
      </div>
    </div>
  );
};

export default CashflowAIChat;