import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

export default function PennyLogoDrawing() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-8">
      {/* Main Drawing Animation */}
      <div className="text-center mb-12">
        {/* <h1 className="text-4xl font-bold text-gray-800 mb-8">PP Hand-Drawn Loading</h1> */}
        
        {/* SVG Logo Drawing */}
        <div className="relative w-32 h-32 mx-auto mb-8">
          <svg 
            width="128" 
            height="128" 
            viewBox="0 0 192 192"
            className="absolute inset-0"
          >
            {/* First P - Electric Lime */}
            <motion.g>
              <motion.path
                d="M 58 60 L 58 130"
                fill="none"
                stroke="#AAFF00"
                strokeWidth="8"
                strokeLinecap="round"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{
                  duration: 0.8,
                  repeat: Infinity,
                  repeatType: "loop",
                  ease: "easeInOut",
                  delay: 0
                }}
              />
              <motion.path
                d="M 58 60 L 93 60 L 113 60 113 70 L 113 78 L 113 88 93 88 L 58 88"
                fill="none"
                stroke="#AAFF00"
                strokeWidth="8"
                strokeLinecap="round"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{
                  duration: 1.2,
                  repeat: Infinity,
                  repeatType: "loop",
                  ease: "easeInOut",
                  delay: 0.3
                }}
              />
            </motion.g>
            
            {/* Second P - Lime Green */}
            <motion.g>
              <motion.path
                d="M 134 130 L 134 60"
                fill="none"
                stroke="#32CD32"
                strokeWidth="8"
                strokeLinecap="round"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{
                  duration: 0.8,
                  repeat: Infinity,
                  repeatType: "loop",
                  ease: "easeInOut",
                  delay: 0.1
                }}
              />
              <motion.path
                d="M 134 130 L 99 130 L 79 130 79 120 L 79 112 L 79 102 99 102 L 134 102"
                fill="none"
                stroke="#32CD32"
                strokeWidth="8"
                strokeLinecap="round"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{
                  duration: 1.2,
                  repeat: Infinity,
                  repeatType: "loop",
                  ease: "easeInOut",
                  delay: 0.4
                }}
              />
            </motion.g>
          </svg>
        </div>
      </div>
    </div>
  );
}
