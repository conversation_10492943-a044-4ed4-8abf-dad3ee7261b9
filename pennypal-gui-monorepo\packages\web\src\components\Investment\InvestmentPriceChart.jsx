import React, { useEffect, useState } from 'react';
import { axiosInstance } from '../../../../logic/api/axiosConfig';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import { formatCurrency } from '../../../../logic/utils/formatters';

const InvestmentPriceChart = ({ investmentId }) => {
  const [priceData, setPriceData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [timeRange, setTimeRange] = useState('1M'); // Default to 1 month
  
  useEffect(() => {
    const fetchPriceHistory = async () => {
      try {
        setLoading(true);
        // Determine date range based on selected timeRange
        const today = new Date();
        let startDate = new Date();
        
        switch(timeRange) {
          case '1W':
            startDate.setDate(today.getDate() - 7);
            break;
          case '1M':
            startDate.setMonth(today.getMonth() - 1);
            break;
          case '3M':
            startDate.setMonth(today.getMonth() - 3);
            break;
          case '6M':
            startDate.setMonth(today.getMonth() - 6);
            break;
          case '1Y':
            startDate.setFullYear(today.getFullYear() - 1);
            break;
          case 'ALL':
            startDate = new Date(0); // Beginning of time (or your data)
            break;
          default:
            startDate.setMonth(today.getMonth() - 1);
        }
        
        // Format dates for API
        const formattedStartDate = startDate.toISOString();
        const formattedEndDate = today.toISOString();
        
        // Fetch the data
        // Note: This endpoint needs to be implemented on the backend
        const response = await axiosInstance.get(`/api/investments/${investmentId}/history`, {
          params: {
            startDate: formattedStartDate,
            endDate: formattedEndDate
          }
        });
        
        // Process the data for the chart
        const chartData = response.data.map(item => ({
          date: new Date(item.timestamp).toLocaleDateString(),
          price: item.price,
          value: item.value
        }));
        
        setPriceData(chartData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching price history:', err);
        setError('Failed to load price history data');
        setLoading(false);
      }
    };
    
    if (investmentId) {
      fetchPriceHistory();
    }
  }, [investmentId, timeRange]);
  
  // Simulating data until backend endpoint is available
  useEffect(() => {
    if (loading && !priceData.length) {
      // Generate sample data for demonstration
      const today = new Date();
      const sampleData = [];
      
      for (let i = 30; i >= 0; i--) {
        const date = new Date();
        date.setDate(today.getDate() - i);
        
        // Generate random price fluctuations
        const basePrice = 100;
        const randomFactor = 0.1; // 10% max variation
        const variation = basePrice * randomFactor * (Math.random() - 0.5);
        const price = basePrice + variation;
        
        sampleData.push({
          date: date.toLocaleDateString(),
          price: price,
          value: price * 10 // Assuming 10 shares
        });
      }
      
      setPriceData(sampleData);
      setLoading(false);
    }
  }, [loading, priceData.length]);
  
  if (loading) {
    return <div>Loading price history...</div>;
  }
  
  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }
  
  const handleTimeRangeChange = (range) => {
    setTimeRange(range);
  };
  
  return (
    <div>
      <div className="btn-group mb-3" role="group">
        <button 
          type="button" 
          className={`btn btn-outline-primary ${timeRange === '1W' ? 'active' : ''}`}
          onClick={() => handleTimeRangeChange('1W')}
        >
          1W
        </button>
        <button 
          type="button" 
          className={`btn btn-outline-primary ${timeRange === '1M' ? 'active' : ''}`}
          onClick={() => handleTimeRangeChange('1M')}
        >
          1M
        </button>
        <button 
          type="button" 
          className={`btn btn-outline-primary ${timeRange === '3M' ? 'active' : ''}`}
          onClick={() => handleTimeRangeChange('3M')}
        >
          3M
        </button>
        <button 
          type="button" 
          className={`btn btn-outline-primary ${timeRange === '6M' ? 'active' : ''}`}
          onClick={() => handleTimeRangeChange('6M')}
        >
          6M
        </button>
        <button 
          type="button" 
          className={`btn btn-outline-primary ${timeRange === '1Y' ? 'active' : ''}`}
          onClick={() => handleTimeRangeChange('1Y')}
        >
          1Y
        </button>
        <button 
          type="button" 
          className={`btn btn-outline-primary ${timeRange === 'ALL' ? 'active' : ''}`}
          onClick={() => handleTimeRangeChange('ALL')}
        >
          ALL
        </button>
      </div>
      
      <ResponsiveContainer width="100%" height={400}>
        <LineChart
          data={priceData}
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="date" 
            tickFormatter={(tick) => {
              // Format date for display
              return tick;
            }}
          />
          <YAxis 
            yAxisId="left"
            domain={['auto', 'auto']}
            tickFormatter={(value) => formatCurrency(value, 'USD', 0)} 
          />
          <YAxis 
            yAxisId="right"
            orientation="right"
            domain={['auto', 'auto']}
            tickFormatter={(value) => formatCurrency(value, 'USD', 0)} 
          />
          <Tooltip 
            formatter={(value, name) => {
              if (name === 'price') {
                return [formatCurrency(value, 'USD'), 'Price'];
              }
              return [formatCurrency(value, 'USD'), 'Total Value'];
            }}
          />
          <Legend />
          <Line 
            yAxisId="left"
            type="monotone" 
            dataKey="price" 
            stroke="#8884d8" 
            activeDot={{ r: 8 }} 
            name="Price"
          />
          <Line 
            yAxisId="right"
            type="monotone" 
            dataKey="value" 
            stroke="#82ca9d" 
            name="Total Value"
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default InvestmentPriceChart;