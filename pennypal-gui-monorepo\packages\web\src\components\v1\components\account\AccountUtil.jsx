// Category icons mapping
import {
  faLink,
  faSync,
  faPlus,
  faRedo,
  faTimes,
  faClock,
  faUniversity,
  faExclamationTriangle, 
  faArrowUp, 
  faArrowDown, 
  faEquals,  
  faSpinner,
  faChartLine,
  faCreditCard,
  faCoins,
  faLandmark,
  faSackDollar,
  faRobot,
  faExchangeAlt
} from "@fortawesome/free-solid-svg-icons";

export const maskAccountNumber = (accountMask) => {
  if (!accountMask) return "N/A";
  const visibleDigits = accountMask.slice(-4);
  return "••••••" + visibleDigits;
};

export const ALL_CATEGORIES = [
  "Cash",
  "CreditCards",
  "LoanAccounts",
  "Investments",
];


export const getCategoryIcon = (boxId) => {
  switch (boxId) {
    case "Cash":
      return faExchangeAlt;
    case "CreditCards":
      return faCreditCard;
    case "LoanAccounts":
      return faLandmark;
    case "Investments":
      return faChartLine;
    default:
      return faSackDollar;
  }
};



export  const formatCategoryName = (boxId) => {
  switch (boxId) {
    case "Cash":
      return "Cash Accounts";
    case "CreditCards":
      return "Credit Cards";
    case "LoanAccounts":
      return "Loan Accounts";
    case "Investments":
      return "Investment Accounts";
    default:
      return boxId;
  }
};

 
// Utility function to format last sync time
export const formatLastSyncTime = (lastSyncTimeStr) => {
  if (!lastSyncTimeStr) return "Never synced";
  
  const now = new Date();
  const lastSyncTime = new Date(lastSyncTimeStr);
  const diffMs = now - lastSyncTime;
  
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffMins < 1) return "Just now";
  if (diffMins < 60) return `${diffMins} min${diffMins !== 1 ? 's' : ''} ago`;
  if (diffHours < 24) return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
  if (diffDays < 30) return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
  
  return lastSyncTime.toLocaleDateString();
};

