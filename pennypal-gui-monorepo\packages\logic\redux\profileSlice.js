import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  user: null,
  
  // fetch state
  fetchLoading: false,
  fetchError: null,
  userLoaded: false,
  
  // update state
  updateLoading: false,
  updateError: null,
  updateSuccess: false, // Added to track successful updates
};

const profileSlice = createSlice({
  name: 'profile',
  initialState,
  reducers: {
    /* ─────────────── FETCH USER ─────────────── */
    fetchUserStart(state) {
      state.fetchLoading = true;
      state.fetchError = null;
      state.userLoaded = false;
    },
    fetchUserSuccess(state, action) {
      state.user = action.payload;
      state.fetchLoading = false;
      state.fetchError = null;
      state.userLoaded = true;
    },
    fetchUserFailure(state, action) {
      state.fetchLoading = false;
      state.fetchError = action.payload;
      state.userLoaded = true;
      state.user = null;
    },

    /* ─────────────── UPDATE USER ────────────── */
    updateUserStart(state) {
      state.updateLoading = true;
      state.updateError = null;
      state.updateSuccess = false;
    },
    updateUserSuccess(state, action) {
      state.user = action.payload;
      state.updateLoading = false;
      state.updateError = null;
      state.updateSuccess = true;
    },
    updateUserFailure(state, action) {
      state.updateLoading = false;
      state.updateError = action.payload;
      state.updateSuccess = false;
    },

    /* ─────────────── UTILITY ACTIONS ───────────── */
    clearFetchError(state) {
      state.fetchError = null;
    },
    clearUpdateError(state) {
      state.updateError = null;
    },
    clearUpdateSuccess(state) {
      state.updateSuccess = false;
    },
    clearProfile(state) {
      return initialState;
    },
    
    // Reset all loading states
    resetLoadingStates(state) {
      state.fetchLoading = false;
      state.updateLoading = false;
    }
  },
});

export const {
  fetchUserStart,
  fetchUserSuccess,
  fetchUserFailure,
  updateUserStart,
  updateUserSuccess,
  updateUserFailure,
  clearFetchError,
  clearUpdateError,
  clearUpdateSuccess,
  clearProfile,
  resetLoadingStates,
} = profileSlice.actions;

// Selectors
export const selectUser = (state) => state.profile.user;
export const selectFetchLoading = (state) => state.profile.fetchLoading;
export const selectFetchError = (state) => state.profile.fetchError;
export const selectUserLoaded = (state) => state.profile.userLoaded;
export const selectUpdateLoading = (state) => state.profile.updateLoading;
export const selectUpdateError = (state) => state.profile.updateError;
export const selectUpdateSuccess = (state) => state.profile.updateSuccess;

export default profileSlice.reducer;