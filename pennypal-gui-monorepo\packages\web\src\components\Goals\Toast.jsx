import React, { useEffect, useState } from 'react';
import { X, CheckCircle, AlertCircle, Info } from 'lucide-react';

export const Toast = ({ message, type = 'success', duration = 3000, onClose }) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      handleClose();
    }, duration);

    return () => clearTimeout(timer);
  }, [duration]);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => {
      onClose && onClose();
    }, 300); // Allow animation to complete
  };

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="text-white" size={20} />;
      case 'error':
        return <AlertCircle className="text-white" size={20} />;
      case 'info':
        return <Info className="text-white" size={20} />;
      default:
        return <Info className="text-white" size={20} />;
    }
  };

  const getBackgroundColor = () => {
    switch (type) {
      case 'success':
        return 'bg-green-600';
      case 'error':
        return 'bg-red-600';
      case 'info':
        return 'bg-blue-600';
      default:
        return 'bg-blue-600';
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50 flex items-center transition-opacity duration-300">
      <div className={`${getBackgroundColor()} text-white px-4 py-3 rounded-lg shadow-lg flex items-center max-w-md`}>
        <div className="mr-3">{getIcon()}</div>
        <div className="flex-1 mr-2">{message}</div>
        <button
          onClick={handleClose}
          className="text-white hover:text-gray-200 focus:outline-none"
          aria-label="Close"
        >
          <X size={20} />
        </button>
      </div>
    </div>
  );
};