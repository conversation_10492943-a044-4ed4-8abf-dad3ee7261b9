import Cookies from 'js-cookie';
import { jwtDecode } from 'jwt-decode';

export const TOKEN_COOKIE_NAME = 'pennypal_jwt_token';
export const USER_PERMISSION_COOKIE_NAME = 'pennypal_user_permissions';

export const getAuthToken = () => {
  return Cookies.get(TOKEN_COOKIE_NAME);
};

export const getPermissions = () => {
  const permissionsStr = Cookies.get(USER_PERMISSION_COOKIE_NAME);
  console.log("******");
  console.log(permissionsStr)
  if (permissionsStr) {
    console.log("^^^^^^^^");
    try {
      return JSON.parse(permissionsStr);
    } catch (error) {
      console.error('Error parsing permissions from cookie:', error);
      return null;
    }
  }

  const token = getAuthToken();
  if (!token) return [];

  try {
    const decodedToken = jwtDecode(token);
    const permissions = decodedToken.permissions;
    setPermissionsCookie(permissions);
    return permissions;
  } catch (error) {
    console.error('Error decoding JWT token:', error);
    return [];
  }
};


export const getPagePermissions = (page) => {
  const permissionsStr = Cookies.get(USER_PERMISSION_COOKIE_NAME);
  if (permissionsStr) {
    try {
      const permissions = JSON.parse(permissionsStr);
      if (Array.isArray(permissions)) {
        return permissions
          .filter(p => typeof p === 'object' && p !== null)
          .filter(p => p.page === page)
          .map(p => p.action);
      }
      if (permissions && typeof permissions === 'object') {
        if (permissions[page]) {
          return Array.isArray(permissions[page]) 
            ? permissions[page] 
            : [permissions[page]];
        }
      }
    } catch (error) {
      console.error('Error parsing permissions from cookie:', error);
    }
  }

  const token = getAuthToken();
  if (!token) return [];

  try {
    const decodedToken = jwtDecode(token);
    const permissions = decodedToken.permissions;
    setPermissionsCookie(permissions);
    if (Array.isArray(permissions)) {
      return permissions.filter(p => p.startsWith(page));
    }
    return Object.keys(permissions).filter(p => p.startsWith(page));
  } catch (error) {
    console.error('Error decoding JWT token:', error);
    return [];
  }
};

export const setPermissionsCookie = (permissions) => {
  Cookies.set(USER_PERMISSION_COOKIE_NAME, JSON.stringify(permissions), { 
    expires: 7,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict'
  });
};