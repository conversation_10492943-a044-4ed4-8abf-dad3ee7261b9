import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Popover,
  Box,
  Typography,
  CircularProgress,
  IconButton,
  Fade,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { logEvent } from '../../utils/EventLogger';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import { Line } from 'react-chartjs-2';
import { setOpenCategoryModal } from '../../../../logic/redux/transactionSlice';
import {
  Chart as ChartJS,
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';

ChartJS.register(
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale,
  Title,
  Tooltip,
  Legend,
  Filler
);

const CategoryChartMiniPopup = ({ darkMode, categoryId }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const open = useSelector((state) => state.transactions.openCategoryModal);
  const category = useSelector((state) => state.transactions.selectedCategory);
  const subCategoryId = useSelector((state) => state.transactions.selectedSubCategoryId);
  const data = useSelector((state) => state.transactions.categoryMonthlyExpenses);
  const loading = useSelector((state) => state.transactions.loadingCategoryExpenses);

  const handleClose = () => {
    logEvent('CategoryChartMiniPopup', 'ModalClosed', {
      categoryId,
      categoryName: category,
      subCategoryId
    });
    dispatch(setOpenCategoryModal(false));
  };
     const handleShowDetails = () => {
    if (categoryId && !isNaN(parseInt(categoryId))) {
      logEvent('CategoryChartMiniPopup', 'ViewDetailsClicked', {
        categoryId,
        categoryName: category,
        subCategoryId
      });
      navigate(`/dashboard/category-details/${categoryId}${subCategoryId ? `/${subCategoryId}` : ''}`);
      dispatch(setOpenCategoryModal(false));
    }
  };
  const themeColors = {
    primary: '#84cc16',
    primaryLight: '#a3e635',
    primaryDark: '#65a30d',
    background: darkMode ? '#1f2937' : '#ffffff',
    text: darkMode ? '#f3f4f6' : '#374151',
    textSecondary: darkMode ? '#9ca3af' : '#6b7280',
    border: darkMode ? '#4b5563' : '#e5e7eb',
    shadow: darkMode ? 'rgba(132, 204, 22, 0.1)' : 'rgba(132, 204, 22, 0.15)',
  };

  const chartData = {
    labels: data.map((entry) => entry.month),
    datasets: [
      {
        label: `${category || 'Category'}${subCategoryId ? ' (Sub)' : ''}`,
        data: data.map((entry) => entry.total),
        fill: true,
        backgroundColor: (context) => {
          const ctx = context.chart.ctx;
          const gradient = ctx.createLinearGradient(0, 0, 0, 150);
          gradient.addColorStop(0, darkMode ? 'rgba(132, 204, 22, 0.3)' : 'rgba(132, 204, 22, 0.2)');
          gradient.addColorStop(1, 'rgba(132, 204, 22, 0.02)');
          return gradient;
        },
        borderColor: themeColors.primary,
        borderWidth: 2,
        pointBackgroundColor: themeColors.background,
        pointBorderColor: themeColors.primary,
        pointBorderWidth: 2,
        pointRadius: 3,
        pointHoverRadius: 5,
        pointHoverBackgroundColor: themeColors.primaryLight,
        pointHoverBorderColor: themeColors.primaryDark,
        tension: 0.4,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false },
      tooltip: {
        backgroundColor: themeColors.background,
        titleColor: themeColors.text,
        bodyColor: themeColors.textSecondary,
        borderColor: themeColors.border,
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: false,
        padding: 8,
        titleFont: { size: 11, weight: 'bold' },
        bodyFont: { size: 10 },
        callbacks: {
          label: function (context) {
            return `$${context.parsed.y.toLocaleString()}`;
          },
        },
      },
    },
    scales: {
      x: {
        grid: { display: false },
        border: { display: false },
        ticks: {
          color: themeColors.textSecondary,
          font: { size: 9 },
          padding: 5,
          maxTicksLimit: 6,
        },
      },
      y: {
        grid: { display: false },
        border: { display: false },
        ticks: {
          color: themeColors.textSecondary,
          font: { size: 9 },
          padding: 8,
          maxTicksLimit: 4,
          callback: function (value) {
            return '$' + (value >= 1000 ? (value / 1000).toFixed(0) + 'k' : value);
          },
        },
      },
    },
    interaction: {
      intersect: false,
      mode: 'index',
    },
    elements: {
      point: { hoverRadius: 5 },
    },
    layout: {
      padding: { top: 5, right: 5, bottom: 5, left: 5 },
    },
  };

  return (
    <Popover
      open={open}
      onClose={handleClose}
      anchorReference="none"
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
      PaperProps={{
        elevation: 12,
        sx: {
          borderRadius: 3,
          border: `1px solid ${themeColors.border}`,
          overflow: 'hidden',
          background: themeColors.background,
          boxShadow: `0 12px 32px ${themeColors.shadow}, 0 4px 12px rgba(0, 0, 0, 0.08)`,
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
        },
      }}
      TransitionComponent={Fade}
      transitionDuration={200}
    >
      <Box sx={{ width: 320, maxWidth: '90vw' }}>
        <Box
          sx={{
            background: `linear-gradient(135deg, ${themeColors.primary} 0%, ${themeColors.primaryLight} 100%)`,
            padding: '12px 16px',
            position: 'relative',
          }}
        >
          <IconButton
            onClick={handleClose}
            size="small"
            sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              color: 'white',
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              width: 24,
              height: 24,
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.3)',
              },
            }}
          >
            <CloseIcon sx={{ fontSize: 16 }} />
          </IconButton>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, color: 'white', pr: 4 }}>
            <TrendingUpIcon sx={{ fontSize: 20 }} />
            <Box>
              <Typography variant="subtitle2" sx={{ fontWeight: 'bold', lineHeight: 1.2 }}>
                {category || 'Category'}
              </Typography>
              <Typography variant="caption" sx={{ opacity: 0.9, fontSize: '0.7rem' }}>
                Monthly Trends{subCategoryId ? ' (Sub)' : ''}
              </Typography>
            </Box>
          </Box>
        </Box>
        <Box sx={{ padding: '16px 12px' }}>
          {loading ? (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: 140,
                flexDirection: 'column',
                gap: 1,
              }}
            >
              <CircularProgress size={24} sx={{ color: themeColors.primary }} />
              <Typography variant="caption" sx={{ color: themeColors.textSecondary }}>
                Loading...
              </Typography>
            </Box>
          ) : (
            <Box
              sx={{
                height: 140,
                background: themeColors.background,
                borderRadius: 2,
                padding: 1,
              }}
            >
              <Line data={chartData} options={chartOptions} />
            </Box>
          )}
        </Box>
        {!loading && data.length > 0 && (
          <Box
            sx={{
              borderTop: `1px solid ${themeColors.border}`,
              background: darkMode ? '#374151' : '#fafafa',
            }}
          >
            <Box
              sx={{
                padding: '8px 16px',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <Typography variant="caption" sx={{ color: themeColors.textSecondary }}>
                {data.length} months
              </Typography>
              <Typography variant="caption" sx={{ color: themeColors.primary, fontWeight: 'bold' }}>
                Latest: ${data[data.length - 1]?.total.toLocaleString() || '0'}
              </Typography>
            </Box>
            <Box
              sx={{
                padding: '8px 16px',
                paddingTop: 0,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                borderTop: `1px solid ${themeColors.border}`,
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  color: themeColors.primary,
                  fontWeight: 'bold',
                  cursor: 'pointer',
                  textDecoration: 'underline',
                  '&:hover': {
                    color: themeColors.primaryDark,
                    textDecoration: 'none',
                  },
                  transition: 'all 0.2s ease',
                }}
             onClick={() => {
    if (categoryId && !isNaN(parseInt(categoryId))) {
      navigate(`/dashboard/category-details/${categoryId}${subCategoryId ? `/${subCategoryId}` : ''}`);
      dispatch(setOpenCategoryModal(false)); // close modal if needed
    }
  }}
              >
                Show Details →
              </Typography>
            </Box>
          </Box>
        )}
      </Box>
    </Popover>
  );
};

export default CategoryChartMiniPopup;