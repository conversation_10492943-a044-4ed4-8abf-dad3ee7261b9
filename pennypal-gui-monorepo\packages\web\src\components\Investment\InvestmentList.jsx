import React from 'react';
import { Link } from 'react-router-dom';
import { formatCurrency, formatPercentage } from '../../../../logic/utils/formatters';

const InvestmentList = ({ investments }) => {
  return (
    <div className="investment-list">
      <h2>Your Investments</h2>
      <div className="table-responsive">
        <table className="table table-striped">
          <thead>
            <tr>
              <th>Security</th>
              <th>Ticker</th>
              <th>Type</th>
              <th>Quantity</th>
              <th>Current Price</th>
              <th>Value</th>
              <th>Today's Change</th>
              <th>Total Gain/Loss</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {investments.map(investment => (
              <tr key={investment.id}>
                <td>{investment.securityName}</td>
                <td>{investment.ticker}</td>
                <td>{investment.securityType}</td>
                <td>{investment.quantity?.toFixed(4)}</td>
                <td>{formatCurrency(investment.currentPrice, investment.currencyCode)}</td>
                <td>{formatCurrency(investment.value, investment.currencyCode)}</td>
                <td className={investment.todayChangePercent >= 0 ? "text-success" : "text-danger"}>
                  {formatCurrency(investment.todayChange, investment.currencyCode)}
                  {' '}({formatPercentage(investment.todayChangePercent)})
                </td>
                <td className={investment.totalGainPercent >= 0 ? "text-success" : "text-danger"}>
                  {formatCurrency(investment.totalGain, investment.currencyCode)}
                  {' '}({formatPercentage(investment.totalGainPercent)})
                </td>
                <td>
                  <Link to={`/investments/${investment.id}`} className="btn btn-sm btn-primary">Details</Link>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default InvestmentList;