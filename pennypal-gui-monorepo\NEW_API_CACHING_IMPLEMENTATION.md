# New API Caching Implementation

## 🎯 Overview

This document outlines the implementation of caching for the new APIs as requested:

### Transaction-Related APIs (Invalidate on transaction change)
- `pennypal/api/v1/transaction/summary/user/34`
- `pennypal/api/v1/transaction/hidden/user/34`
- `pennypal/api/v1/reconcile/all`

### Receipt-Related APIs (Invalidate on receipt change)
- `pennypal/api/receipts/getReceiptTransactionIds`
- `pennypal/api/v1/receipt-items/all`
- `pennypal/api/v1/receipt/summary`
- `pennypal/api/receipts/user/34`

### Chatbot API (Invalidate on new user query)
- `pennypal/api/v1/chatbot/history/34`

## 🔧 Implementation Details

### 1. Cache State (cacheSlice.js)

Added new state properties for each API:

```javascript
// Transaction summary
transactionSummary: null,
transactionSummaryLoaded: false,
transactionSummaryLoading: false,
transactionSummaryError: null,
transactionSummaryParams: { userId: null },

// Hidden transactions
hiddenTransactions: [],
hiddenTransactionsLoaded: false,
hiddenTransactionsLoading: false,
hiddenTransactionsError: null,
hiddenTransactionsParams: { userId: null },

// Reconcile data
reconcileData: [],
reconcileDataLoaded: false,
reconcileDataLoading: false,
reconcileDataError: null,

// Receipt transaction IDs
receiptTransactionIds: [],
receiptTransactionIdsLoaded: false,
receiptTransactionIdsLoading: false,
receiptTransactionIdsError: null,

// Receipt items
receiptItems: [],
receiptItemsLoaded: false,
receiptItemsLoading: false,
receiptItemsError: null,

// Receipt summary
receiptSummary: null,
receiptSummaryLoaded: false,
receiptSummaryLoading: false,
receiptSummaryError: null,

// User receipts
userReceipts: [],
userReceiptsLoaded: false,
userReceiptsLoading: false,
userReceiptsError: null,
userReceiptsParams: { userId: null },

// Chatbot history
chatbotHistory: [],
chatbotHistoryLoaded: false,
chatbotHistoryLoading: false,
chatbotHistoryError: null,
chatbotHistoryParams: { userId: null }
```

### 2. Cache Actions

Added start/success/failure actions for each API:
- `fetchTransactionSummaryStart/Success/Failure`
- `fetchHiddenTransactionsStart/Success/Failure`
- `fetchReconcileDataStart/Success/Failure`
- `fetchReceiptTransactionIdsStart/Success/Failure`
- `fetchReceiptItemsStart/Success/Failure`
- `fetchReceiptSummaryStart/Success/Failure`
- `fetchUserReceiptsStart/Success/Failure`
- `fetchChatbotHistoryStart/Success/Failure`

Added new invalidation actions:
- `invalidateReceiptCache()` - Clears all receipt-related cache
- `invalidateChatbotCache()` - Clears chatbot history cache

### 3. Cache Epics (cacheEpic.js)

Implemented epics for each new API with proper caching logic:
- Only fetch if data is not already loaded
- Handle user ID requirements for user-specific APIs
- Proper error handling and logging
- Added to login initialization process

### 4. Cache Invalidation (cacheInvalidationEpic.js)

#### Transaction-Related Cache Invalidation
The transaction-related APIs are automatically invalidated when:
- Transactions are added/modified/deleted
- Accounts are synced
- Any transaction-related action occurs

These APIs are included in `invalidateAllTransactionRelatedCache()`:
- Transaction summary
- Hidden transactions  
- Reconcile data

#### Receipt Cache Invalidation
Added `invalidateOnReceiptChangeEpic` that listens for:
- `receipts/uploadReceiptSuccess`
- `receipts/saveReceiptSuccess`
- `receipts/addNewTransaction`

#### Chatbot Cache Invalidation
Added `invalidateOnChatbotQueryEpic` that listens for:
- `chatbot/querySuccess` (to be implemented)
- `chatbot/newQuery` (to be implemented)

### 5. Cache Utilities (cacheUtils.js)

Added new utility functions:
- `invalidateReceipts(dispatch)` - Manual receipt cache invalidation
- `invalidateChatbot(dispatch)` - Manual chatbot cache invalidation

Updated `useCacheInvalidation()` hook to include new functions.

## 🔄 Automatic Behavior

### On Login
All new APIs are automatically cached when user logs in:
```javascript
of(fetchTransactionSummaryStart({ userId })).pipe(delay(500)),
of(fetchHiddenTransactionsStart({ userId })).pipe(delay(500)),
of(fetchReconcileDataStart()).pipe(delay(500)),
of(fetchReceiptTransactionIdsStart()).pipe(delay(500)),
of(fetchReceiptItemsStart()).pipe(delay(500)),
of(fetchReceiptSummaryStart()).pipe(delay(500)),
of(fetchUserReceiptsStart({ userId })).pipe(delay(500)),
of(fetchChatbotHistoryStart({ userId })).pipe(delay(500))
```

### On Data Changes
- **Transaction changes** → Invalidates transaction summary, hidden transactions, reconcile data
- **Receipt changes** → Invalidates all receipt-related APIs
- **Chatbot queries** → Invalidates chatbot history

### Automatic Refetch
After invalidation, the system automatically refetches the invalidated data.

## 🛠️ Usage Examples

### Manual Cache Invalidation
```javascript
import { useCacheInvalidation } from '../../../../logic/utils/cacheUtils';

const MyComponent = () => {
  const { invalidateReceipts, invalidateChatbot } = useCacheInvalidation();
  
  const handleReceiptUpload = async () => {
    // Upload receipt logic
    invalidateReceipts(); // Manually invalidate if needed
  };
  
  const handleNewChatQuery = async () => {
    // Chat query logic
    invalidateChatbot(); // Manually invalidate if needed
  };
};
```

### Accessing Cached Data
```javascript
import { useSelector } from 'react-redux';

const MyComponent = () => {
  const {
    transactionSummary,
    transactionSummaryLoaded,
    transactionSummaryLoading,
    hiddenTransactions,
    receiptItems,
    chatbotHistory
  } = useSelector(state => state.cache);
  
  if (transactionSummaryLoading) return <Loading />;
  if (transactionSummaryLoaded) return <TransactionSummary data={transactionSummary} />;
};
```

## ⚠️ Notes

1. **Chatbot Actions**: The chatbot invalidation triggers (`chatbot/querySuccess`, `chatbot/newQuery`) need to be implemented when chatbot queries are made.

2. **API Endpoints**: All API endpoints are implemented as specified in the requirements.

3. **User ID Dependency**: APIs requiring user ID will only be cached when user is authenticated.

4. **Error Handling**: All epics include proper error handling and logging.

5. **Performance**: APIs are only fetched once on login and when invalidated, improving performance.

## 🚀 Benefits

- **Improved Performance**: Data is cached and only fetched when needed
- **Automatic Invalidation**: Cache is automatically updated when data changes
- **Consistent Pattern**: Follows the same pattern as existing cache implementation
- **Manual Control**: Utilities available for manual cache management
- **Error Resilience**: Proper error handling and fallbacks
