// deltaEpic.js
import { ofType } from 'redux-observable';
import { from, of } from 'rxjs';
import { mergeMap, map, catchError } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchDeltaData,
  fetchDeltaDataSuccess,
  fetchDeltaDataFailure,
  fetchAccountTypeDeltaData,
  fetchAccountTypeDeltaDataSuccess,
  fetchAccountTypeDeltaDataFailure
} from '../redux/deltaSlice';
import { getCurrentUserId } from '../../web/src/utils/AuthUtil';

export const fetchDeltaDataEpic = (action$) => action$.pipe(
  ofType(fetchDeltaData.type),
  mergeMap(action => {
    console.log('🔄 [EPIC] fetchDeltaDataEpic triggered with action:', action);

    let { timePeriod } = action.payload; // <-- use let, not const
    if (timePeriod === 'quarterly-aggregate') timePeriod = 'quarterly-rolling';

    const userId = getCurrentUserId();
    
    console.log('📋 [EPIC] Delta data request details:', {
      timePeriod,
      userId,
      actionPayload: action.payload
    });
    
    if (!userId) {
      console.error('❌ [EPIC] User not authenticated for delta data');
      return of(fetchDeltaDataFailure({
        message: 'User not authenticated',
        status: 401,
        detail: 'JWT missing or invalid'
      }));
    }

    // Map time periods to API endpoints
    const deltaEndpointMapping = {
      'one-month': '1month',
      'three-month': '3months',
      'half-year': '6months', 
      'yearly': '1year',
      'ytd': 'ytd',
      'quarterly-rolling': 'quarterly-rolling'
    };

    const endpoint = deltaEndpointMapping[timePeriod];
    
    console.log('🔗 [EPIC] Endpoint mapping:', {
      timePeriod,
      endpoint,
      availableEndpoints: Object.keys(deltaEndpointMapping)
    });
    
    if (!endpoint) {
      console.error('❌ [EPIC] Invalid time period for delta calculation:', timePeriod);
      return of(fetchDeltaDataFailure({
        message: 'Invalid time period for delta calculation',
        status: 400,
        detail: `Time period ${timePeriod} not supported for delta`
      }));
    }

    const apiUrl = `/pennypal/api/v1/account/balances/delta/${endpoint}/accountid/${userId}`;
    console.log('🌐 [EPIC] Making API call to:', apiUrl);

    return from(axiosInstance.get(apiUrl)).pipe(
      map(response => {
        console.log('✅ [EPIC] Delta data API response received:', {
          status: response.status,
          dataKeys: Object.keys(response.data || {}),
          dataPreview: response.data
        });
        
        const processedData = processDeltaData(response.data, timePeriod);
        console.log('🔄 [EPIC] Processed delta data:', {
          comparisonType: processedData.comparisonType,
          accountsCount: processedData.accounts?.length || 0,
          accountTypesCount: processedData.accountTypes?.length || 0,
          summaryTotalBalance: processedData.summary?.totalCurrentBalance || 0
        });
        
        return fetchDeltaDataSuccess(processedData);
      }),
      catchError(error => {
        console.error('❌ [EPIC] Delta data API error:', {
          message: error.message,
          status: error.response?.status,
          statusText: error.response?.statusText,
          responseData: error.response?.data,
          url: apiUrl
        });
        
        const errorData = {
          message: error.response?.data?.message || 'Failed to fetch delta data',
          status: error.response?.status || 500,
          detail: error.response?.data?.detail || error.message
        };
        return of(fetchDeltaDataFailure(errorData));
      })
    );
  })
);

export const fetchAccountTypeDeltaDataEpic = (action$) => action$.pipe(
  ofType(fetchAccountTypeDeltaData.type),
  mergeMap((action) => {
  let { timePeriod } = action.payload; // <-- use let
  if (timePeriod === 'quarterly-aggregate') timePeriod = 'quarterly-rolling';
    const userId = getCurrentUserId();

    // Map time periods to API endpoints
    const endpointMapping = {
      'one-month': '1month',
      'three-month': '3months',
      'half-year': '6months',
      'yearly': '1year',
      'ytd': 'ytd',
       'quarterly-rolling': 'quarterly-rolling'
    };
    const endpoint = endpointMapping[timePeriod];
    if (!endpoint) {
      return of(fetchAccountTypeDeltaDataFailure({ message: 'Invalid time period', status: 400 }));
    }
 // Use account-type endpoint!
    const apiUrl = `/pennypal/api/v1/account/balances/delta/${endpoint}/account-type/${userId}`;

    return from(axiosInstance.get(apiUrl)).pipe(
      map(response => {
        console.log('Epic API response:', response.data);
        let processed = response.data;
        // Flatten quarterly-rolling account type data
        if (
          (processed.comparisonType === 'quarterly-rolling' || processed.quarters) &&
          processed.quarters
        ) {
          processed = {
            ...processed,
            accountTypes: flattenQuarterlyAccountTypes(processed.quarters)
          };
        }
        return fetchAccountTypeDeltaDataSuccess(processed);
      }),
      catchError(error => {
        return of(fetchAccountTypeDeltaDataFailure({
          message: error.response?.data?.message || 'Failed to fetch account type delta data',
          status: error.response?.status || 500,
          detail: error.response?.data?.detail || error.message
        }));
      })
    );
     })
);
// Helper function to process delta data
const processDeltaData = (responseData, timePeriod) => {
  console.log('🔄 [PROCESSOR] processDeltaData called:', {
    timePeriod,
    responseDataKeys: Object.keys(responseData || {}),
    responseDataType: typeof responseData,
    hasAccounts: !!(responseData?.accounts),
    accountsLength: responseData?.accounts?.length || 0
  });

  if (!responseData) {
    console.warn('⚠️ [PROCESSOR] No response data for delta processing');
    return {
      comparisonType: timePeriod,
      accounts: [],
      accountTypes: [],
      metadata: null,
      summary: {
        totalCurrentBalance: 0,
        totalPastBalance: 0,
        totalDeltaAmount: 0,
        totalDeltaPercentage: 0,
        overallTrend: 'neutral'
      }
    };
  }

  let accounts = [];
  let metadata = {
    comparisonType: responseData.comparisonType || timePeriod,
    currentPeriodDate: responseData.currentPeriodDate || '',
    pastPeriodDate: responseData.pastPeriodDate || '',
    description: responseData.description || ''
  };

  console.log('📊 [PROCESSOR] Processing metadata:', metadata);

  // Handle quarterly rolling data structure
  if (timePeriod === 'quarterly-rolling' && responseData.quarters) {
    console.log('📈 [PROCESSOR] Processing quarterly rolling data:', {
      quartersKeys: Object.keys(responseData.quarters),
      quartersCount: Object.keys(responseData.quarters).length
    });
    accounts = processQuarterlyRollingDeltas(responseData.quarters);
    metadata.quarterDetails = responseData.quarterDetails || {};
  } 
  // Handle YTD data structure
  else if (timePeriod === 'ytd') {
    console.log('📅 [PROCESSOR] Processing YTD data:', {
      hasAccounts: !!(responseData.accounts),
      accountsLength: responseData.accounts?.length || 0,
      ytdDays: responseData.ytdDays
    });
    accounts = responseData.accounts ? responseData.accounts.map(account => ({
      accountId: account.accountId || account.account_id,
      accountName: account.accountName || account.account_name,
      accountType: account.accountType || account.account_type,
      currentBalance: Number(account.currentBalance || account.current_balance || 0),
      pastBalance: Number(account.pastBalance || account.past_balance || 0),
      deltaAmount: Number(account.deltaAmount || account.delta_amount || 0),
      deltaPercentage: Number(account.deltaPercentage || account.delta_percentage || 0),
      trend: determineTrend(account.deltaAmount || account.delta_amount || 0),
      formattedDelta: formatDeltaDisplay(
        account.deltaAmount || account.delta_amount || 0,
        account.deltaPercentage || account.delta_percentage || 0
      )
    })) : [];
    
    // Add YTD specific metadata
    metadata.ytdDays = responseData.ytdDays || 0;
  } 
  // Handle regular period deltas (1month, 3month, 6month, 1year)
  else if (responseData.accounts) {
    console.log('📊 [PROCESSOR] Processing regular period data:', {
      accountsLength: responseData.accounts.length,
      sampleAccount: responseData.accounts[0]
    });
    accounts = responseData.accounts.map(account => ({
      accountId: account.accountId || account.account_id,
      accountName: account.accountName || account.account_name,
      accountType: account.accountType || account.account_type,
      currentBalance: Number(account.currentBalance || account.current_balance || 0),
      pastBalance: Number(account.pastBalance || account.past_balance || 0),
      deltaAmount: Number(account.deltaAmount || account.delta_amount || 0),
      deltaPercentage: Number(account.deltaPercentage || account.delta_percentage || 0),
      trend: determineTrend(account.deltaAmount || account.delta_amount || 0),
      formattedDelta: formatDeltaDisplay(
        account.deltaAmount || account.delta_amount || 0,
        account.deltaPercentage || account.delta_percentage || 0
      )
    }));
  }

  console.log('📊 [PROCESSOR] Processed accounts:', {
    accountsCount: accounts.length,
    accountTypes: accounts.map(a => a.accountType).filter(Boolean)
  });

  // Calculate summary statistics
  const summary = calculateSummary(accounts, timePeriod);
  console.log('📊 [PROCESSOR] Calculated summary:', summary);

  const result = {
    comparisonType: metadata.comparisonType,
    accounts,
    accountTypes: [],
    metadata,
    summary,
    lastUpdated: new Date().toISOString()
  };

  console.log('✅ [PROCESSOR] Final processed delta data:', {
    comparisonType: result.comparisonType,
    accountsCount: result.accounts.length,
    accountTypesCount: result.accountTypes.length,
    summaryBalance: result.summary.totalCurrentBalance
  });

  return result;
};


// Process quarterly rolling delta data
const processQuarterlyRollingDeltas = (quarterlyData) => {
  console.log('📈 [PROCESSOR] Processing quarterly rolling deltas:', {
    quarterlyDataKeys: Object.keys(quarterlyData),
    quarterlyDataType: typeof quarterlyData
  });
  
  const processedAccounts = [];
  
  Object.entries(quarterlyData).forEach(([accountId, quarters]) => {
    console.log(`📊 [PROCESSOR] Processing account ${accountId}:`, {
      quartersType: typeof quarters,
      quartersLength: Array.isArray(quarters) ? quarters.length : 'not array'
    });
    
    if (Array.isArray(quarters)) {
      quarters.forEach(quarter => {
        processedAccounts.push({
          accountId: quarter.accountId || accountId,
          accountName: quarter.accountName || quarter.account_name || `Account ${accountId}`,
          accountType: quarter.accountType || quarter.account_type || 'unknown',
          quarter: quarter.quarter,
          currentQuarterBalance: Number(quarter.currentQuarterBalance || quarter.current_quarter_balance || 0),
          pastQuarterBalance: Number(quarter.pastQuarterBalance || quarter.past_quarter_balance || 0),
          deltaAmount: Number(quarter.deltaAmount || quarter.delta_amount || 0),
          deltaPercentage: Number(quarter.deltaPercentage || quarter.delta_percentage || 0),
          trend: determineTrend(quarter.deltaAmount || quarter.delta_amount || 0),
          formattedDelta: formatDeltaDisplay(
            quarter.deltaAmount || quarter.delta_amount || 0,
            quarter.deltaPercentage || quarter.delta_percentage || 0
          )
        });
      });
    }
  });

  console.log('✅ [PROCESSOR] Processed quarterly accounts:', processedAccounts.length);
  return processedAccounts;
};

const processQuarterlyRollingAccountTypeDeltas = (quarterlyData) => {
  console.log('📈 [PROCESSOR] Processing quarterly rolling account type deltas:', {
    quarterlyDataKeys: Object.keys(quarterlyData),
    quarterlyDataType: typeof quarterlyData
  });
  
  const processedAccountTypes = [];
  
  Object.entries(quarterlyData).forEach(([quarter, accountTypes]) => {
    console.log(`📊 [PROCESSOR] Processing quarter ${quarter}:`, {
      accountTypesType: typeof accountTypes,
      accountTypesLength: Array.isArray(accountTypes) ? accountTypes.length : 'not array'
    });
    
    if (Array.isArray(accountTypes)) {
      accountTypes.forEach(accountType => {
        processedAccountTypes.push({
          accountType: accountType.accountType || accountType.account_type,
          quarter: quarter,
          accountCount: Number(accountType.accountCount || accountType.account_count || 0),
          currentQuarterBalance: Number(accountType.currentQuarterBalance || accountType.current_quarter_balance || 0),
          pastQuarterBalance: Number(accountType.pastQuarterBalance || accountType.past_quarter_balance || 0),
          deltaAmount: Number(accountType.deltaAmount || accountType.delta_amount || 0),
          deltaPercentage: Number(accountType.deltaPercentage || accountType.delta_percentage || 0),
          trend: determineTrend(accountType.deltaAmount || accountType.delta_amount || 0),
          formattedDelta: formatDeltaDisplay(
            accountType.deltaAmount || accountType.delta_amount || 0,
            accountType.deltaPercentage || accountType.delta_percentage || 0
          )
        });
      });
    }
  });

  console.log('✅ [PROCESSOR] Processed quarterly account types:', processedAccountTypes.length);
  return processedAccountTypes;
};

// Determine trend direction
const determineTrend = (deltaAmount) => {
  if (deltaAmount > 0) return 'increase';
  if (deltaAmount < 0) return 'decrease';
  return 'neutral';
};

// Format delta display with proper signs and indicators
const formatDeltaDisplay = (deltaAmount, deltaPercentage) => {
  const amount = Math.abs(deltaAmount);
  const percentage = Math.abs(deltaPercentage);
  const sign = deltaAmount >= 0 ? '+' : '-';
  const arrow = deltaAmount > 0 ? '↗' : deltaAmount < 0 ? '↘' : '→';
  
  return {
    amount: `${sign}$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
    percentage: `${sign}${percentage.toFixed(2)}%`,
    arrow,
    trend: determineTrend(deltaAmount),
    rawAmount: deltaAmount,
    rawPercentage: deltaPercentage
  };
};

// Calculate summary statistics
const calculateSummary = (accounts, timePeriod) => {
  console.log('📊 [CALCULATOR] Calculating summary for accounts:', {
    accountsCount: accounts?.length || 0,
    timePeriod
  });

  if (!accounts || accounts.length === 0) {
    return {
      totalCurrentBalance: 0,
      totalPastBalance: 0,
      totalDeltaAmount: 0,
      totalDeltaPercentage: 0,
      overallTrend: 'neutral',
      accountCount: 0
    };
  }

  let totalCurrentBalance, totalPastBalance;

  // Handle quarterly rolling differently
  if (timePeriod === 'quarterly-rolling') {
    totalCurrentBalance = accounts.reduce((sum, acc) => 
      sum + (acc.currentQuarterBalance || 0), 0);
    totalPastBalance = accounts.reduce((sum, acc) => 
      sum + (acc.pastQuarterBalance || 0), 0);
  } else {
    totalCurrentBalance = accounts.reduce((sum, acc) => 
      sum + (acc.currentBalance || 0), 0);
    totalPastBalance = accounts.reduce((sum, acc) => 
      sum + (acc.pastBalance || 0), 0);
  }

  const totalDeltaAmount = totalCurrentBalance - totalPastBalance;
  const totalDeltaPercentage = totalPastBalance !== 0 
    ? (totalDeltaAmount / totalPastBalance) * 100 
    : 0;

  const summary = {
    totalCurrentBalance,
    totalPastBalance,
    totalDeltaAmount,
    totalDeltaPercentage,
    overallTrend: determineTrend(totalDeltaAmount),
    accountCount: accounts.length,
    formattedSummary: formatDeltaDisplay(totalDeltaAmount, totalDeltaPercentage)
  };

  console.log('✅ [CALCULATOR] Calculated summary:', summary);
  return summary;
};

// Calculate summary statistics for account types
const calculateAccountTypeSummary = (accountTypes, timePeriod) => {
  console.log('📊 [CALCULATOR] Calculating account type summary:', {
    accountTypesCount: accountTypes?.length || 0,
    timePeriod
  });

  if (!accountTypes || accountTypes.length === 0) {
    return {
      totalCurrentBalance: 0,
      totalPastBalance: 0,
      totalDeltaAmount: 0,
      totalDeltaPercentage: 0,
      overallTrend: 'neutral',
      accountTypeCount: 0,
      totalAccountCount: 0
    };
  }

  let totalCurrentBalance, totalPastBalance;

  if (timePeriod === 'quarterly-rolling') {
    totalCurrentBalance = accountTypes.reduce((sum, acc) => 
      sum + (acc.currentQuarterBalance || 0), 0);
    totalPastBalance = accountTypes.reduce((sum, acc) => 
      sum + (acc.pastQuarterBalance || 0), 0);
  } else {
    totalCurrentBalance = accountTypes.reduce((sum, acc) => 
      sum + (acc.currentBalance || 0), 0);
    totalPastBalance = accountTypes.reduce((sum, acc) => 
      sum + (acc.pastBalance || 0), 0);
  }

  const totalDeltaAmount = totalCurrentBalance - totalPastBalance;
  const totalDeltaPercentage = totalPastBalance !== 0 
    ? (totalDeltaAmount / totalPastBalance) * 100 
    : 0;

  const totalAccountCount = accountTypes.reduce((sum, acc) => 
    sum + (acc.accountCount || 0), 0);

  const summary = {
    totalCurrentBalance,
    totalPastBalance,
    totalDeltaAmount,
    totalDeltaPercentage,
    overallTrend: determineTrend(totalDeltaAmount),
    accountTypeCount: accountTypes.length,
    totalAccountCount,
    formattedSummary: formatDeltaDisplay(totalDeltaAmount, totalDeltaPercentage)
  };

  console.log('✅ [CALCULATOR] Calculated account type summary:', summary);
  return summary;
};

// Example function to flatten quarters
function flattenQuarterlyAccountTypes(quartersObj) {
  if (!quartersObj || typeof quartersObj !== 'object') return [];
  return Object.entries(quartersObj).flatMap(([quarter, arr]) =>
    Array.isArray(arr) ? arr.map(item => ({ ...item, quarter })) : []
  );
}

export default [fetchDeltaDataEpic, fetchAccountTypeDeltaDataEpic];