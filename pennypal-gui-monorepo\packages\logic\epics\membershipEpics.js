import { combineEpics, ofType } from 'redux-observable';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { from, of } from 'rxjs';
import {
  inviteFamilyMemberRequest,
  inviteFamilyMemberSuccess,
  inviteFamilyMemberFailure,
  validateInviteLinkRequest,
  validateInviteLinkSuccess,
  validateInviteLinkFailure,
  completeSignupRequest,
  completeSignupSuccess,
  completeSignupFailure,
  getFamilyMembersRequest,
  getFamilyMembersSuccess,
  getFamilyMembersFailure,
  revokeFamilyMemberRequest,
  revokeFamilyMemberSuccess,
  revokeFamilyMemberFailure
} from '../redux/memberSlice';
import { axiosInstance } from '../api/axiosConfig';

// Epic for inviting a family member
// Epic for inviting a family member
const inviteFamilyMemberEpic = (action$) => action$.pipe(
  ofType(inviteFamilyMemberRequest.type),
  mergeMap(action => {
    // Map FULL_ACCESS to WRITE before sending to backend
    const mappedPayload = {
      ...action.payload,
      permissionType: action.payload.permissionType === 'FULL_ACCESS' 
        ? 'WRITE' 
        : action.payload.permissionType
    };

    return from(
      axiosInstance.post('/pennypal/api/membership/invite', mappedPayload)
    ).pipe(
      map(response => {
        // Handle successful response (HTTP 200)
        return inviteFamilyMemberSuccess(response.data);
      }),
      catchError(error => {
        console.error('Error inviting family member:', error);
        
        // Extract error message from response
        let errorMessage = 'Failed to send invite';
        
        if (error.response?.data) {
          if (typeof error.response.data === 'string') {
            errorMessage = error.response.data;
          } else if (error.response.data.message) {
            errorMessage = error.response.data.message;
          }
        }
        
        return of(inviteFamilyMemberFailure({
          message: errorMessage,
          status: error.response?.status,
          statusCode: error.response?.status
        }));
      })
    );
  })
);

// Epic for validating an invite link
const validateInviteLinkEpic = (action$) => action$.pipe(
  ofType(validateInviteLinkRequest.type),
  mergeMap(action => from(
    axiosInstance.get(`/pennypal/api/membership/validate-invite?token=${action.payload}`)
  ).pipe(
    map(response => {
      // Handle successful response (HTTP 200)
      return validateInviteLinkSuccess(response.data);
    }),
    catchError(error => {
      console.error('Error validating invite link:', error);
      
      // Extract error message from response
      let errorMessage = 'Invalid or expired invite link';
      
      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message;
        }
      }
      
      return of(validateInviteLinkFailure({
        message: errorMessage,
        status: error.response?.status,
        statusCode: error.response?.status
      }));
    })
  ))
);

// Epic for completing a signup process
const completeSignupEpic = (action$) => action$.pipe(
  ofType(completeSignupRequest.type),
  mergeMap(action => from(
    axiosInstance.post(
      `/pennypal/api/membership/complete-signup?token=${action.payload.token}`,
      action.payload.signupDetails
    )
  ).pipe(
    map(response => {
      // Handle successful response (HTTP 200)
      return completeSignupSuccess(response.data);
    }),
    catchError(error => {
      console.error('Error completing signup:', error);
      
      // Extract error message from response
      let errorMessage = 'Failed to complete signup';
      
      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message;
        }
      }
      
      return of(completeSignupFailure({
        message: errorMessage,
        status: error.response?.status,
        statusCode: error.response?.status
      }));
    })
  ))
);

// Epic for fetching family members
const getFamilyMembersEpic = (action$) => action$.pipe(
  ofType(getFamilyMembersRequest.type),
  mergeMap(() => from(
    axiosInstance.get('/pennypal/api/membership/family-members')
  ).pipe(
    map(response => {
      // Handle successful response (HTTP 200) - expect array of family members
      return getFamilyMembersSuccess(response.data);
    }),
    catchError(error => {
      console.error('Error fetching family members:', error);
      
      // Extract error message from response
      let errorMessage = 'Failed to fetch family members';
      
      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message;
        }
      }
      
      return of(getFamilyMembersFailure({
        message: errorMessage,
        status: error.response?.status,
        statusCode: error.response?.status
      }));
    })
  ))
);

// Epic for revoking a family member's access
const revokeFamilyMemberEpic = (action$) => action$.pipe(
  ofType(revokeFamilyMemberRequest.type),
  mergeMap(action => from(
    axiosInstance.delete(`/pennypal/api/membership/revoke/${action.payload}`)
  ).pipe(
    map(response => {
      // Handle successful response (HTTP 200)
      return revokeFamilyMemberSuccess({
        ...response.data,
        relationshipId: action.payload
      });
    }),
    catchError(error => {
      console.error('Error revoking family member access:', error);
      
      // Extract error message from response
      let errorMessage = 'Failed to revoke family member access';
      
      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message;
        }
      }
      
      return of(revokeFamilyMemberFailure({
        message: errorMessage,
        status: error.response?.status,
        statusCode: error.response?.status,
        relationshipId: action.payload
      }));
    })
  ))
);

// Combine all epics
const membershipEpics = combineEpics(
  inviteFamilyMemberEpic,
  validateInviteLinkEpic,
  completeSignupEpic,
  getFamilyMembersEpic,
  revokeFamilyMemberEpic
);

export default membershipEpics;