import React, { useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON><PERSON><PERSON><PERSON>, Toolt<PERSON>, ResponsiveContainer } from 'recharts';
// import { logEvent } from '../../utils/EventLogger'; // Adjust path as needed

const AccountMiniChart = ({ accountId, data }) => {
  useEffect(() => {
    // Log component load with metadata
    // logEvent('AccountMiniChart', 'load', {
    //   accountId,
    //   dataPoints: data?.length || 0,
    // });
  }, [accountId, data]);

  // Log if no data is available
  if (!data || !Array.isArray(data) || data.length === 0) {
    // logEvent('AccountMiniChart', 'no_data', { accountId });

    return (
      <div className="flex items-center justify-center h-full">
        <span className="text-gray-400 text-sm">No balance data</span>
      </div>
    );
  }
let chartData = data.map(item => ({
  date: new Date(item.groupEndDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
  rawDate: new Date(item.groupEndDate),
  balance: parseFloat(item.aggregatedBalance)
}));

if (chartData.length === 0) {
  // No data: add 3 dummy points ending today
  const today = new Date();
  const prevDate1 = new Date(today);
  prevDate1.setDate(today.getDate() - 2);

  const prevDate2 = new Date(today);
  prevDate2.setDate(today.getDate() - 1);

  chartData = [
    {
      date: prevDate1.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      balance: 0
    },
    {
      date: prevDate2.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      balance: 0
    },
    {
      date: today.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      balance: 0
    }
  ];
} else if (chartData.length === 1) {
  const realPoint = chartData[0];
  const realDate = realPoint.rawDate;

  const prevDate1 = new Date(realDate);
  prevDate1.setDate(realDate.getDate() - 2);

  const prevDate2 = new Date(realDate);
  prevDate2.setDate(realDate.getDate() - 1);

  chartData = [
    {
      date: prevDate1.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      balance: 0
    },
    {
      date: prevDate2.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      balance: 0
    },
    {
      date: realDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      balance: realPoint.balance
    }
  ];
} else if (chartData.length === 2) {
  const firstPoint = chartData[0];
  const firstDate = firstPoint.rawDate;

  const prevDate = new Date(firstDate);
  prevDate.setDate(firstDate.getDate() - 1);

  chartData = [
    {
      date: prevDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      balance: 0
    },
    ...chartData.map(({ rawDate, ...rest }) => rest)
  ];
} else {
  // For 3 or more points, just remove rawDate
  chartData = chartData.map(({ rawDate, ...rest }) => rest);
}




  // Custom tooltip to display formatted dollar amounts
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      // Log each tooltip activation (hover on a data point)
      // logEvent('AccountMiniChart', 'tooltip_hover', {
      //   accountId,
      //   label,
      //   value: payload[0].value
      // });

      return (
        <div className="bg-white p-2 border border-gray-200 shadow-sm rounded-md">
          <p className="text-xs font-medium">{label}</p>
          <p className="text-xs text-blue-600">{`$${payload[0].value.toLocaleString(undefined, { 
            minimumFractionDigits: 2, 
            maximumFractionDigits: 2 
          })}`}</p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="h-full w-full">
      <ResponsiveContainer width="100%" height={50}>
        <LineChart data={chartData} margin={{ top: 5, right: 5, bottom: 5, left: 5 }}>
          <XAxis 
            dataKey="date"
            tick={{ fontSize: 9 }}
            tickLine={false}
            axisLine={false}
            interval="preserveStartEnd" // Show first and last labels
          />
          <YAxis 
            hide 
            domain={['dataMin - 100', 'dataMax + 100']} // Add some padding to the chart
          />
          <Tooltip content={<CustomTooltip />} />
          <Line 
            type="monotone"
            dataKey="balance"
            stroke="#4F46E5"
            strokeWidth={2}
            dot={{ r: 2, fill: "#4F46E5", stroke: "white", strokeWidth: 1 }}
            activeDot={{ r: 4, fill: "#4F46E5", stroke: "white", strokeWidth: 2 }}
            isAnimationActive={false} // Disable animation for better performance
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default AccountMiniChart;
