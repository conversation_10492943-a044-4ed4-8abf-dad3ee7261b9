import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  fetchInvestmentByIdRequest, 
  fetchInvestmentPerformanceRequest,
  selectInvestment,
  selectPerformance,
  selectLoading,
  selectError,
  clearInvestment
} from '../../../../logic/redux/investmentSlice';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import { formatCurrency, formatPercentage, formatDate } from '../../../../logic/utils/formatters';

const Investment = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const investment = useSelector(selectInvestment);
  const performance = useSelector(selectPerformance);
  const loading = useSelector(selectLoading);
  const error = useSelector(selectError);
  
  const [activeTab, setActiveTab] = useState('overview');
  const [timeframe, setTimeframe] = useState('1M'); // Default timeframe
  
  // Fetch investment details on component mount
  useEffect(() => {
    if (id) {
      dispatch(fetchInvestmentByIdRequest(id));
      
      // Clean up on unmount
      return () => {
        dispatch(clearInvestment());
      };
    }
  }, [dispatch, id]);
  
  // Fetch performance data when investment is loaded
  useEffect(() => {
    if (investment?.userId) {
      dispatch(fetchInvestmentPerformanceRequest(investment.userId));
    }
  }, [dispatch, investment?.userId]);
  
  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="p-6 bg-red-100 border border-red-400 rounded-md">
        <h3 className="text-xl font-semibold text-red-700">Error</h3>
        <p className="text-red-600">{error}</p>
        <button 
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          onClick={() => navigate(-1)}
        >
          Go Back
        </button>
      </div>
    );
  }
  
  if (!investment) {
    return (
      <div className="p-6">
        <h3 className="text-xl">Investment not found</h3>
        <button 
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          onClick={() => navigate('/investments')}
        >
          Back to Investments
        </button>
      </div>
    );
  }
  
  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };
  
  const handleTimeframeChange = (newTimeframe) => {
    setTimeframe(newTimeframe);
  };
  
  // Filter chart data based on selected timeframe
  const getTimeframeData = () => {
    if (!performance?.historicalData) return [];
    
    const now = new Date();
    let cutoffDate;
    
    switch (timeframe) {
      case '1W':
        cutoffDate = new Date(now.setDate(now.getDate() - 7));
        break;
      case '1M':
        cutoffDate = new Date(now.setMonth(now.getMonth() - 1));
        break;
      case '3M':
        cutoffDate = new Date(now.setMonth(now.getMonth() - 3));
        break;
      case '1Y':
        cutoffDate = new Date(now.setFullYear(now.getFullYear() - 1));
        break;
      case 'ALL':
      default:
        return performance.historicalData;
    }
    
    return performance.historicalData.filter(item => new Date(item.date) >= cutoffDate);
  };
  
  // Calculate some additional metrics
  const totalReturn = investment.value - investment.costBasis;
  const totalReturnPercentage = (totalReturn / investment.costBasis) * 100;
  
  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header Section */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">{investment.securityName}</h1>
          <p className="text-gray-600">{investment.securityType} • {investment.ticker}</p>
        </div>
        <div className="text-right">
          <p className="text-3xl font-bold">{formatCurrency(investment.value)}</p>
          <div className={`text-lg ${totalReturn >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            {totalReturn >= 0 ? '+' : ''}{formatCurrency(totalReturn)} ({formatPercentage(totalReturnPercentage/100)})
          </div>
        </div>
      </div>
      
      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="flex space-x-8">
          <button
            className={`py-4 px-1 border-b-2 font-medium text-sm ${activeTab === 'overview' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
            onClick={() => handleTabChange('overview')}
          >
            Overview
          </button>
          <button
            className={`py-4 px-1 border-b-2 font-medium text-sm ${activeTab === 'performance' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
            onClick={() => handleTabChange('performance')}
          >
            Performance
          </button>
          <button
            className={`py-4 px-1 border-b-2 font-medium text-sm ${activeTab === 'transactions' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
            onClick={() => handleTabChange('transactions')}
          >
            Transactions
          </button>
        </nav>
      </div>
      
      {/* Tab Content */}
      <div className="mb-8">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Chart Section */}
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Performance</h2>
                <div className="flex space-x-2">
                  {['1W', '1M', '3M', '1Y', 'ALL'].map((option) => (
                    <button
                      key={option}
                      className={`px-3 py-1 text-sm rounded ${timeframe === option ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                      onClick={() => handleTimeframeChange(option)}
                    >
                      {option}
                    </button>
                  ))}
                </div>
              </div>
              
              <div className="h-64">
                {performance?.historicalData ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={getTimeframeData()}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" tickFormatter={(tick) => formatDate(tick, 'short')} />
                      <YAxis domain={['auto', 'auto']} />
                      <Tooltip formatter={(value) => formatCurrency(value)} labelFormatter={(label) => formatDate(label)} />
                      <Legend />
                      <Line type="monotone" dataKey="value" stroke="#3B82F6" activeDot={{ r: 8 }} name="Value" />
                    </LineChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex justify-center items-center h-full">
                    <p>Performance data not available</p>
                  </div>
                )}
              </div>
            </div>
            
            {/* Investment Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow">
                <h2 className="text-xl font-semibold mb-4">Investment Details</h2>
                <div className="space-y-3">
                  <div className="flex justify-between border-b pb-2">
                    <span className="text-gray-600">Initial Investment</span>
                    <span className="font-medium">{formatCurrency(investment.costBasis)}</span>
                  </div>
                  <div className="flex justify-between border-b pb-2">
                    <span className="text-gray-600">Current Value</span>
                    <span className="font-medium">{formatCurrency(investment.value)}</span>
                  </div>
                  <div className="flex justify-between border-b pb-2">
                    <span className="text-gray-600">Total Return</span>
                    <span className={`font-medium ${totalReturn >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {totalReturn >= 0 ? '+' : ''}{formatCurrency(totalReturn)} ({formatPercentage(totalReturnPercentage/100)})
                    </span>
                  </div>
                  <div className="flex justify-between border-b pb-2">
                    <span className="text-gray-600">Last Updated</span>
                    <span className="font-medium">{formatDate(investment.lastUpdated)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Account ID</span>
                    <span className="font-medium">{investment.accountId}</span>
                  </div>
                </div>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow">
                <h2 className="text-xl font-semibold mb-4">Asset Information</h2>
                <div className="space-y-3">
                  <div className="flex justify-between border-b pb-2">
                    <span className="text-gray-600">Type</span>
                    <span className="font-medium">{investment.securityType}</span>
                  </div>
                  <div className="flex justify-between border-b pb-2">
                    <span className="text-gray-600">Ticker</span>
                    <span className="font-medium">{investment.ticker}</span>
                  </div>
                  <div className="flex justify-between border-b pb-2">
                    <span className="text-gray-600">Current Price</span>
                    <span className="font-medium">{formatCurrency(investment.currentPrice)}</span>
                  </div>
                  <div className="flex justify-between border-b pb-2">
                    <span className="text-gray-600">Quantity</span>
                    <span className="font-medium">{investment.quantity}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Currency</span>
                    <span className="font-medium">{investment.currencyCode}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {activeTab === 'performance' && (
          <div className="space-y-6">
            {/* Performance Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {performance && (
                <>
                  <div className="bg-white p-4 rounded-lg shadow">
                    <h3 className="text-gray-600 text-sm uppercase">Total Gain</h3>
                    <p className={`text-2xl font-bold ${performance.totalGain >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatCurrency(performance.totalGain)}
                    </p>
                  </div>
                  <div className="bg-white p-4 rounded-lg shadow">
                    <h3 className="text-gray-600 text-sm uppercase">Total Gain Percentage</h3>
                    <p className={`text-2xl font-bold ${performance.totalGainPercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatPercentage(performance.totalGainPercent/100)}
                    </p>
                  </div>
                  <div className="bg-white p-4 rounded-lg shadow">
                    <h3 className="text-gray-600 text-sm uppercase">Daily Change</h3>
                    <p className={`text-2xl font-bold ${performance.dailyChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatCurrency(performance.dailyChange)} ({formatPercentage(performance.dailyChangePercent/100)})
                    </p>
                  </div>
                </>
              )}
            </div>
            
            {/* Performance Chart */}
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Historical Performance</h2>
                <div className="flex space-x-2">
                  {['1W', '1M', '3M', '1Y', 'ALL'].map((option) => (
                    <button
                      key={option}
                      className={`px-3 py-1 text-sm rounded ${timeframe === option ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                      onClick={() => handleTimeframeChange(option)}
                    >
                      {option}
                    </button>
                  ))}
                </div>
              </div>
              
              <div className="h-80">
                {performance?.historicalData ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={getTimeframeData()}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" tickFormatter={(tick) => formatDate(tick, 'short')} />
                      <YAxis domain={['auto', 'auto']} />
                      <Tooltip formatter={(value) => formatCurrency(value)} labelFormatter={(label) => formatDate(label)} />
                      <Legend />
                      <Line type="monotone" dataKey="value" stroke="#3B82F6" activeDot={{ r: 8 }} name="Value" />
                      {performance.benchmark && (
                        <Line type="monotone" dataKey="benchmark" stroke="#10B981" activeDot={{ r: 6 }} name="Benchmark" />
                      )}
                    </LineChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex justify-center items-center h-full">
                    <p>Performance data not available</p>
                  </div>
                )}
              </div>
            </div>
            
            {/* Performance Table */}
            <div className="bg-white p-6 rounded-lg shadow">
              <h2 className="text-xl font-semibold mb-4">Performance by Period</h2>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Return</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Change</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {performance ? (
                      <>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Daily</td>
                          <td className={`px-6 py-4 whitespace-nowrap text-sm ${performance.dailyChangePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {formatPercentage(performance.dailyChangePercent/100)}
                          </td>
                          <td className={`px-6 py-4 whitespace-nowrap text-sm ${performance.dailyChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {formatCurrency(performance.dailyChange)}
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Weekly</td>
                          <td className={`px-6 py-4 whitespace-nowrap text-sm ${performance.weeklyChangePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {formatPercentage(performance.weeklyChangePercent/100)}
                          </td>
                          <td className={`px-6 py-4 whitespace-nowrap text-sm ${performance.weeklyChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {formatCurrency(performance.weeklyChange)}
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Monthly</td>
                          <td className={`px-6 py-4 whitespace-nowrap text-sm ${performance.monthlyChangePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {formatPercentage(performance.monthlyChangePercent/100)}
                          </td>
                          <td className={`px-6 py-4 whitespace-nowrap text-sm ${performance.monthlyChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {formatCurrency(performance.monthlyChange)}
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Total</td>
                          <td className={`px-6 py-4 whitespace-nowrap text-sm ${performance.totalGainPercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {formatPercentage(performance.totalGainPercent/100)}
                          </td>
                          <td className={`px-6 py-4 whitespace-nowrap text-sm ${performance.totalGain >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {formatCurrency(performance.totalGain)}
                          </td>
                        </tr>
                      </>
                    ) : (
                      <tr>
                        <td colSpan="3" className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                          No performance data available
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
        
        {activeTab === 'transactions' && (
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Transaction History</h2>
            {investment.transactions && investment.transactions.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Shares</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {investment.transactions.map((transaction) => (
                      <tr key={transaction.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatDate(transaction.date)}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            transaction.type === 'Buy' ? 'bg-green-100 text-green-800' : 
                            transaction.type === 'Sell' ? 'bg-red-100 text-red-800' : 
                            'bg-blue-100 text-blue-800'
                          }`}>
                            {transaction.type}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(transaction.amount)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {transaction.shares}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {transaction.price ? formatCurrency(transaction.price) : '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            transaction.status === 'Completed' ? 'bg-green-100 text-green-800' : 
                            transaction.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' : 
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {transaction.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="text-gray-500">No transaction history available for this investment.</p>
            )}
          </div>
        )}
      </div>
      
      {/* Actions Section */}
      <div className="flex justify-end space-x-4">
        <button 
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
          onClick={() => navigate('/investments')}
        >
          Back to Investments
        </button>
        <button 
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          onClick={() => navigate(`/investments/${id}/manage`)}
        >
          Manage Investment
        </button>
      </div>
    </div>
  );
};

export default Investment;
// // src/pages/Investment.jsx
// import React from "react";

// const Investment = () => {
//   const investmentOptions = [
//     {
//       title: "Stocks",
//       description: "Invest in individual companies and ETFs for long-term growth.",
//       icon: "📈",
//     },
//     {
//       title: "Real Estate",
//       description: "Diversify your portfolio with residential and commercial properties.",
//       icon: "🏡",
//     },
//     {
//       title: "Crypto",
//       description: "Explore digital assets like Bitcoin, Ethereum, and more.",
//       icon: "🪙",
//     },
//   ];

//   return (
//     <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 text-gray-800">
//       {/* Hero Section */}
//       <section className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-24 px-6 text-center">
//         <h1 className="text-5xl md:text-6xl font-extrabold leading-tight mb-6 drop-shadow-md">
//           Smart Investing Starts Here
//         </h1>
//         <p className="text-xl md:text-2xl max-w-3xl mx-auto opacity-90">
//           Build wealth and achieve financial freedom with expert-guided investments.
//         </p>
//       </section>

//       {/* Investment Options */}
//       <section className="py-20 px-6 md:px-16">
//         <h2 className="text-4xl font-bold text-center mb-16 text-gray-800">
//           Explore Investment Options
//         </h2>
//         <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-10">
//           {investmentOptions.map((option, index) => (
//             <div
//               key={index}
//               className="bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-shadow p-8 text-center transform hover:-translate-y-1 duration-300"
//             >
//               <div className="text-5xl mb-6">{option.icon}</div>
//               <h3 className="text-2xl font-semibold mb-3">{option.title}</h3>
//               <p className="text-gray-600 leading-relaxed">{option.description}</p>
//             </div>
//           ))}
//         </div>
//       </section>

//       {/* Call to Action */}
//       <section className="bg-indigo-700 text-white text-center py-24 px-6">
//         <h2 className="text-4xl font-bold mb-4">Ready to Invest?</h2>
//         <p className="text-lg md:text-xl max-w-xl mx-auto mb-8">
//           Start your investment journey today with our expert tools and dedicated support.
//         </p>
//         <button className="bg-white text-indigo-700 font-bold py-4 px-10 rounded-full shadow-md hover:bg-gray-100 transition">
//           Get Started
//         </button>
//       </section>
//     </div>
//   );
// };

// export default Investment;