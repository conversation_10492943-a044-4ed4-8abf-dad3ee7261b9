import { ofType } from 'redux-observable';
import { from, of } from 'rxjs';
import { switchMap, catchError, map, mergeMap } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import { getCurrentUserId } from '../../web/src/utils/AuthUtil';
import {
  fetchContactsRequest,
  fetchContactsSuccess,
  fetchContactsFailure,
  fetchSplitTransactionsRequest,
  fetchSplitTransactionsSuccess,
  fetchSplitTransactionsFailure,
  submitSplitRequest,
  submitSplitSuccess,
  submitSplitFailure,
} from '../redux/splitTransactionSlice';

// Fetch contacts epic
export const fetchContactsEpic = (action$) =>
  action$.pipe(
    ofType(fetchContactsRequest.type),
    switchMap((action) => {
      const userId = action.payload || getCurrentUserId();
      return from(axiosInstance.get(`/pennypal/api/contacts?userId=${userId}`)).pipe(
        map((response) => fetchContactsSuccess(response.data)),
        catchError((error) => of(fetchContactsFailure(error.response?.data || 'Failed to fetch contacts')))
      )}
    )
  );

// Fetch split transactions epic
export const fetchSplitTransactionsEpic = (action$) =>
  action$.pipe(
    ofType(fetchSplitTransactionsRequest.type),
    switchMap((action) => {
      const userId = action.payload || getCurrentUserId();
      return from(axiosInstance.get(`/pennypal/api/split-transactions/user/${userId}`)).pipe(
        map((response) => fetchSplitTransactionsSuccess(response.data)),
        catchError((error) => of(fetchSplitTransactionsFailure(error.response?.data || 'Failed to fetch split transactions')))
      )
    })
  );

// Submit split request epic
export const submitSplitEpic = (action$) =>
  action$.pipe(
    ofType(submitSplitRequest.type),
    switchMap((action) => {
      const { splitRequests } = action.payload;
      return from(axiosInstance.post('/pennypal/api/split-transactions/add', splitRequests)).pipe(
       mergeMap((response) => [
          submitSplitSuccess(),
          fetchSplitTransactionsRequest(getCurrentUserId()), // no longer depends on hardcoded ID
        ]),
        catchError((error) => of(submitSplitFailure(error.response?.data || 'Failed to submit split request')))
      )
    })
  );