import { ofType, combineEpics } from 'redux-observable';
import { of, from } from 'rxjs';
import { switchMap, catchError, filter } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchReconcileRequest,
  fetchReconcileSuccess,
  fetchReconcileFailure,
  reconcileTransactionsRequest,
  reconcileTransactionsSuccess,
  reconcileTransactionsFailure,
} from '../redux/reconcileSlice';

export const fetchReconcileEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchReconcileRequest.type),
    switchMap(() => {
      try {
        const cache = state$.value?.cache;
        // Check if reconcile data is already cached
        if (cache?.reconcileDataLoaded && cache?.reconcileData?.length > 0) {
          console.log('✅ Using cached reconcile data in reconcile epic');
          return of(fetchReconcileSuccess(cache.reconcileData));
        }
        console.log('🔄 Reconcile data not cached, making API call');

        // Make API call if not cached
        return from(axiosInstance.get('/pennypal/api/v1/reconcile/all')).pipe(
          switchMap((response) => of(fetchReconcileSuccess(response.data))),
          catchError((error) => {
            console.error('Fetch reconcile error:', error);
            return of(fetchReconcileFailure(error.response?.data || 'Failed to fetch reconciled transactions'));
          })
        );
      } catch (error) {
        console.error('Error checking cache state in reconcile epic:', error);
        // Make API call on error
        return from(axiosInstance.get('/pennypal/api/v1/reconcile/all')).pipe(
          switchMap((response) => of(fetchReconcileSuccess(response.data))),
          catchError((error) => {
            console.error('Fetch reconcile error:', error);
            return of(fetchReconcileFailure(error.response?.data || 'Failed to fetch reconciled transactions'));
          })
        );
      }
    })
  );

// Epic to handle reconciling transactions
export const reconcileTransactionsEpic = (action$) =>
  action$.pipe(
    ofType(reconcileTransactionsRequest.type),
    switchMap(() => {
      console.log('🔄 Starting reconcile transactions process');

      // Make API call to reconcile transactions
      return from(axiosInstance.post('/pennypal/api/v1/reconcile/process')).pipe(
        switchMap((response) => {
          console.log('✅ Reconcile transactions completed successfully');
          return of(reconcileTransactionsSuccess(response.data));
        }),
        catchError((error) => {
          console.error('❌ Failed to reconcile transactions:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to reconcile transactions';
          return of(reconcileTransactionsFailure(errorMessage));
        })
      );
    })
  );

// Combined reconcile epic
export const reconcileEpic = combineEpics(
  fetchReconcileEpic,
  reconcileTransactionsEpic
);

export default reconcileEpic;