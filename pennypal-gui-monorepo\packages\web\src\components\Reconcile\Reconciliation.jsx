import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { logEvent } from '../../utils/EventLogger';
import { CheckSquare, Square, Eye, EyeOff, Filter, ChevronDown, ChevronUp, Calendar, CreditCard, Trash2, RefreshCw } from 'lucide-react';
import { fetchReconcileRequest, reconcileTransactionsRequest } from '../../../../logic/redux/reconcileSlice';
// import { AnimatePresence, motion } from 'framer-motion';
import PaymentLoader from '../load/PaymentLoader';
import { themeClasses } from '../../utils/tailwindUtils'; // Adjust path as needed

function groupTransactions(transactions) {
  const groups = {};
  
  transactions.forEach(transaction => {
    const key = transaction.reconcileId;
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(transaction);
  });
  
  return Object.values(groups);
}

export default function TransactionPage({ darkMode }) {
  // Log page load
  useEffect(() => {
    logEvent('TransactionPage', 'PageLoad', {});
  }, []);
  return (
    <>
      <style jsx global>{`
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        // body {
        //   font-family: 'Inter', sans-serif;
        //   background-color: ${darkMode ? '#111827' : '#f9fafb'};
        //   background-size: 20px 20px;
        // }
      `}</style>
      <TransactionPageContent darkMode={darkMode} />
    </>
  );
}

function TransactionPageContent({ darkMode }) {
  const dispatch = useDispatch();
  const { transactions, loading, error, reconciling } = useSelector((state) => state.reconcile);
  const [selectedTransactions, setSelectedTransactions] = useState([]);
  const [expandedGroups, setExpandedGroups] = useState({});
  const [hiddenTransactions, setHiddenTransactions] = useState([]);

  useEffect(() => {
    dispatch(fetchReconcileRequest());
  }, [dispatch]);
  
  const transactionGroups = groupTransactions(transactions);
  
  const toggleTransaction = (transactionId) => {
logEvent('TransactionPage', 'ToggleTransaction', {
      transactionId,
      wasSelected: selectedTransactions.includes(transactionId)
    });

    setSelectedTransactions(prev => 
      prev.includes(transactionId) 
        ? prev.filter(id => id !== transactionId)
        : [...prev, transactionId]
    );
  };
  
  const toggleGroup = (groupIndex) => {
     const isExpanding = !expandedGroups[groupIndex];
    logEvent('TransactionPage', isExpanding ? 'ExpandGroup' : 'CollapseGroup', {
      groupIndex,
      groupId: transactionGroups[groupIndex][0]?.reconcileId
    });

    setExpandedGroups(prev => ({
      ...prev,
      [groupIndex]: !prev[groupIndex]
    }));
  };
  
  const toggleSelectAllInGroup = (groupTransactions) => {
    const groupIds = groupTransactions.map(t => t.id);
    const allSelected = groupIds.every(id => selectedTransactions.includes(id));
    
     logEvent('TransactionPage', allSelected ? 'DeselectAllInGroup' : 'SelectAllInGroup', {
      groupId: groupTransactions[0]?.reconcileId,
      transactionCount: groupTransactions.length
    });

    if (allSelected) {
      setSelectedTransactions(prev => prev.filter(id => !groupIds.includes(id)));
    } else {
      setSelectedTransactions(prev => [...new Set([...prev, ...groupIds])]);
    }
  };
  
  const hideTransactions = (groupTransactions) => {
    const groupIds = groupTransactions.map(t => t.id);
    logEvent('TransactionPage', 'HideTransactions', {
      groupId: groupTransactions[0]?.reconcileId,
      transactionCount: groupIds.length
    });
    
    setHiddenTransactions(prev => [...prev, ...groupIds]);
    setSelectedTransactions(prev => prev.filter(id => !groupIds.includes(id)));
  };
  
  const formatAmount = (amount) => {
    const absAmount = Math.abs(amount);
    return `${amount < 0 ? '-' : ''}$${absAmount.toFixed(2)}`;
  };
  
  const getAmountColor = (amount) => {
    return amount < 0 ? 'text-rose-600' : 'text-lime-600';
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleReconcile = () => {
    dispatch(reconcileTransactionsRequest())
      .then(() => {
        dispatch(fetchReconcileRequest());
      });
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <PaymentLoader darkMode={darkMode} />
        {/* <p className={`mt-4 ${themeClasses.loadingText(darkMode)}`}>Loading transactions...</p> */}
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className={`border-l-4 p-6 rounded-md max-w-lg ${themeClasses.error(darkMode)}`}>
          <div className="flex items-center">
            <svg className={`h-6 w-6 ${themeClasses.errorText(darkMode)} mr-3`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <p className={`font-medium ${themeClasses.errorText(darkMode)}`}>Error: {error}</p>
          </div>
          <button
            className={`mt-4 px-4 py-2 rounded-md transition-colors ${themeClasses.actionButton(darkMode)}`}
            onClick={() => dispatch(fetchReconcileRequest())}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`p-6 w-full min-h-screen ${themeClasses.container(darkMode)}`}>
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className={`flex text-2xl items-center ${themeClasses.container(darkMode)}`}>
            Reconciled Transactions
          </h1>
          <p className={`mt-1 ${themeClasses.loadingText(darkMode)}`}>
            Manage and review your reconciled transactions
          </p>
        </div>
        <div className="flex space-x-2">
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm ${themeClasses.badgeDate(darkMode)}`}>
            <Calendar size={14} className="mr-1" />
            {new Date().toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}
          </span>
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm ${themeClasses.badgeGroup(darkMode)}`}>
            <CreditCard size={14} className="mr-1" />
            {transactionGroups.length} Groups
          </span>
         
        </div>
      </div>
      
      {/* Filters & Actions Bar */}
      <div className={`rounded-xl shadow-sm p-4 mb-6 flex justify-between items-center border ${themeClasses.cardContainer(darkMode)} ${themeClasses.border(darkMode)}`}>
        <div className="flex items-center space-x-3">
          <button className={`flex items-center px-4 py-2 rounded-lg shadow-sm text-sm transition-colors ${themeClasses.filterButton(darkMode)}`}>
            <Filter size={16} className={`mr-2 ${themeClasses.icon(darkMode)}`} />
            Filter
            <ChevronDown size={16} className={`ml-2 ${themeClasses.icon(darkMode)}`} />
          </button>
          
          {selectedTransactions.length > 0 && (
            <span className={`text-sm px-3 py-1 rounded-lg font-medium border ${themeClasses.badgeSelected(darkMode)}`}>
              {selectedTransactions.length} selected
            </span>
          )}
        </div>
        
        <div className="flex items-center space-x-3">
          {selectedTransactions.length > 0 && (
            <button className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors border ${themeClasses.deleteButton(darkMode)}`}>
              <Trash2 size={16} className="mr-2" />
              Delete
            </button>
          )}

          <button
            className={`flex items-center px-4 py-2 rounded-lg text-sm transition-colors ${
              selectedTransactions.length > 0 ? themeClasses.reconcileButton(darkMode) : themeClasses.actionButtonDisabled(darkMode)
            }`}
            disabled={selectedTransactions.length === 0}
            onClick={() => {
              setHiddenTransactions(prev => [...prev, ...selectedTransactions]);
              setSelectedTransactions([]);
            }}
          >
            <EyeOff size={16} className="mr-2" />
            Hide Selected
          </button>
           <button
            onClick={handleReconcile}
            disabled={reconciling}
            className={`inline-flex items-center px-4 py-2 rounded-lg text-sm transition-colors ${
              reconciling ? themeClasses.reconcileButtonDisabled(darkMode) : themeClasses.reconcileButton(darkMode)
            }`}
          >
            {reconciling ? (
              <>
                <RefreshCw size={14} className="mr-1 animate-spin" />
                Reconciling...
              </>
            ) : (
              <>
                <RefreshCw size={14} className="mr-1" />
                Reconcile Now
              </>
            )}
          </button>
        </div>
      </div>
      
      {/* Transaction Groups */}
      <div className="space-y-5">
        {transactionGroups.map((group, groupIndex) => {
          if (group.every(t => hiddenTransactions.includes(t.id))) {
            return null;
          }
          
          const expanded = expandedGroups[groupIndex] || false;
          const groupAmount = Math.abs(group[0].amount);
          const merchant = group[0].description;
          
          // Get all unique dates in the group
          const groupDates = [...new Set(group.map(t => formatDate(t.transactionDate)))].join(" & ");

          const allInGroupSelected = group.every(t =>
            selectedTransactions.includes(t.id) || hiddenTransactions.includes(t.id)
          );
          
          return (
            <div key={groupIndex} className={`border rounded-xl shadow-sm hover:shadow transition-shadow duration-200 ${themeClasses.cardContainer(darkMode)} ${themeClasses.border(darkMode)}`}>
              {/* Group Header */}
              <div className={`flex justify-between items-center p-5 cursor-pointer rounded-t-xl ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'}`}
                onClick={() => toggleGroup(groupIndex)}>
                <div className="flex items-center">
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleSelectAllInGroup(group);
                    }}
                    className={`${themeClasses.icon(darkMode)} ${themeClasses.iconHover(darkMode)} transition-colors mr-4`}
                  >
                    {allInGroupSelected ? (
                      <CheckSquare size={22} className={`${darkMode ? 'text-lime-400' : 'text-lime-600'}`} />
                    ) : (
                      <Square size={22} />
                    )}
                  </button>
                  
                  <div>
                    <h3 className={`font-semibold text-lg ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>{merchant}</h3>
                    <p className={`text-sm flex items-center mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      <Calendar size={14} className={`${darkMode ? 'text-gray-500' : 'text-gray-400'} mr-1`} />
                      {groupDates}
                      <>
                        <span className="mx-2"></span>
                        <span className={`text-xs py-1 px-2 rounded-full ${darkMode ? 'bg-purple-900 text-purple-300' : 'bg-purple-50 text-purple-700'}`}>
                          {group[0].category}
                        </span>
                      </>

                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <span className={`font-semibold text-lg ${getAmountColor(group[0].amount)}`}>
                    {formatAmount(groupAmount)}
                  </span>
                  
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      hideTransactions(group);
                    }}
                    className={`px-3 py-1 text-sm rounded-md transition-colors ${darkMode ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                  >
                    <EyeOff size={14} className="inline mr-1" />
                    Hide
                  </button>
                  
                  {expanded ? (
                    <ChevronUp size={20} className={`${darkMode ? 'text-gray-500' : 'text-gray-400'}`} />
                  ) : (
                    <ChevronDown size={20} className={`${darkMode ? 'text-gray-500' : 'text-gray-400'}`} />
                  )}
                </div>
              </div>
              
              {/* Expanded Group Content */}
              {expanded && (
                <div className={`border-t px-5 py-3 rounded-b-xl ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-100'}`}>
                  {group.map(transaction => {
                    if (hiddenTransactions.includes(transaction.id)) {
                      return null;
                    }

                    return (
                      <div key={transaction.id} className={`flex justify-between items-center py-4 px-3 border-b last:border-0 rounded-lg my-1 ${darkMode ? 'border-gray-600 hover:bg-gray-600' : 'border-gray-100 hover:bg-white'}`}>
                        <div className="flex items-center">
                          <button
                            onClick={() => toggleTransaction(transaction.id)}
                            className={`${darkMode ? 'text-gray-500 hover:text-lime-400' : 'text-gray-400 hover:text-lime-600'} transition-colors mr-4`}
                          >
                            {selectedTransactions.includes(transaction.id) ? (
                              <CheckSquare size={18} className={`${darkMode ? 'text-lime-400' : 'text-lime-600'}`} />
                            ) : (
                              <Square size={18} />
                            )}
                          </button>

                          <div>
                            <p className={`text-sm font-medium ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>{transaction.description}</p>
                            <div className="flex items-center mt-1">
                              <span className={`text-xs py-1 px-2 rounded-full font-medium ${themeClasses.badgeTransaction(darkMode)}`}>
                                ID: {transaction.reconcileId}
                              </span>
                              {transaction.category && (
                                <span className={`text-xs py-1 px-2 rounded-full font-medium ml-2 ${themeClasses.badgeCategory(darkMode)}`}>
                                  {transaction.category}
                                </span>
                              )}
                              <span className={`text-xs py-1 px-2 rounded-full ${themeClasses.badgeDateTransaction(darkMode)}`}>
                                {formatDate(transaction.transactionDate)}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center">
                          <span className={`text-sm font-medium ${transaction.amount < 0 ? themeClasses.amountCredit(darkMode) : themeClasses.amountDebit(darkMode)}`}>
                            {formatAmount(transaction.amount)}
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          );
        })}
      </div>
      
      {/* Hidden Transactions Toggle */}
      {hiddenTransactions.length > 0 && (
        <div className="mt-8 text-center">
          <button
            className={`inline-flex items-center px-4 py-2 rounded-lg transition-colors ${themeClasses.actionButton(darkMode)}`}
            onClick={() => setHiddenTransactions([])}
          >
            <Eye size={16} className="mr-2" />
            Show hidden transactions ({hiddenTransactions.length})
          </button>
        </div>
      )}
    </div>
  );
}