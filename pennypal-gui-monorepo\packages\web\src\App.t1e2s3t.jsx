/*import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'

// Simple test to verify Vitest setup
describe('Vitest Setup', () => {
  it('basic math works', () => {
    expect(1 + 1).toBe(2)
  })

  it('jest-dom matchers work', () => {
    const div = document.createElement('div')
    div.textContent = 'Hello World'
    document.body.appendChild(div)
    
    expect(div).toBeInTheDocument()
  })

  it('can render a basic component', () => {
    const TestComponent = () => <div>Test Component</div>
    
    render(<TestComponent />)
    expect(screen.getByText('Test Component')).toBeInTheDocument()
  })
})*/
