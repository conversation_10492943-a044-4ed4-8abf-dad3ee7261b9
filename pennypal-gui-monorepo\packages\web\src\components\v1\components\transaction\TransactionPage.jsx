import React, { useEffect, useRef, useState, useCallback } from "react";
import { useSelector, useDispatch } from "react-redux";
import { DataGrid } from "@mui/x-data-grid";
//import './TransactionPage.css';

import { VisibilityOff } from "@mui/icons-material";

import Visibility from "@mui/icons-material/Visibility";

import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";
import SplitTransactionPopup from "./SplitTransactionPopup";
import { LocalizationProvider, DatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";

// import CloseIcon from '@mui/icons-material/Close';

import AccessTimeIcon from "@mui/icons-material/AccessTime";
import Divider from "@mui/material/Divider";
import ReceiptModal from "./ReceiptModal";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Button,
  IconButton,
  InputAdornment,
  FormControl,
  InputLabel,
  LinearProgress,
  CircularProgress,
  TextField,
  Table,
  TableHead,
  Tooltip,
  Select,
  Grid2,
  Paper,
  ListSubheader,
  TableBody,
  TableRow,
  TableCell,
  Tabs,
  Tab,
  MenuItem,
  Radio,
  Box,
  Typography,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import { FaChevronDown } from "react-icons/fa";

import receipt from "@pp-web/assets/receipt.png";
import split from "@pp-web/assets/split 1.png";
import BankIcon from "@pp-web/components/v1/components/account/BankIcon";

// Transaction name cell (merchant/category icon)
export const TransactionNameCell = ({ name, icon }) => {
  // Use the transaction name as fallback when icon prop is null
  const iconName = icon || name;

  return (
    <div className="flex items-center gap-2 w-full min-w-0">
      <BankIcon
        institutionName={iconName}
        accountType="merchant" // <-- Use merchant for transaction/merchant icons
        size={32}
        sizeClass="md"
        className="transaction-icon flex-shrink-0"
      />
      <span className="text-base font-medium truncate flex-1 min-w-0">
        {name}
      </span>
    </div>
  );
};

// Account cell (bank icon)
export const AccountCell = ({ bankName }) => {
  return (
    <div className="flex items-center gap-2 w-full min-w-0">
      <BankIcon
        institutionName={bankName}
        accountType="bank" // <-- Use bank for account icons
        size={28}
        sizeClass="md"
        className="account-icon flex-shrink-0"
      />
      <span className="text-base font-medium truncate flex-1 min-w-0">
        {bankName}
      </span>
    </div>
  );
};

export const getTransactionColumns = (
  toggleTransactionModal,
  handleReceiptClick
) => [
  {
    field: "date",
    headerName: "Date",
    flex: 1,
    minWidth: 120,
    headerClassName: "data-grid-header",
    renderCell: (params) => {
      return params.row.isGroupHeader ? (
        <strong className="text-base">{params.value}</strong>
      ) : (
        params.value
      );
    },
  },
  {
    field: "name",
    headerName: "Transaction Name",
    flex: 2,
    minWidth: 180,
    headerClassName: "data-grid-header",
    renderCell: (params) => {
      if (params.row.isGroupHeader) return params.value;

      return (
        <div
          className="cursor-pointer w-full"
          onClick={() => toggleTransactionModal(params.row)}
        >
          <TransactionNameCell
            name={params.value}
            icon={params.row.icon}
            onClick={() => toggleTransactionModal(params.row)}
          />
        </div>
      );
    },
  },
  {
    field: "category",
    headerName: "Category",
    flex: 1.5,
    minWidth: 150,
    headerClassName: "data-grid-header",
  },
  {
    field: "bank",
    headerName: "Account",
    flex: 1.5,
    minWidth: 120,
    headerClassName: "data-grid-header",
    renderCell: (params) => {
      if (params.row.isGroupHeader) return null;

      return <AccountCell bankName={params.value} />;
    },
  },
  {
    field: "amount",
    headerName: "Price",
    flex: 1,
    minWidth: 100,
    headerClassName: "data-grid-header",
    type: "number",
    renderCell: (params) => {
      return params.row.isGroupHeader ? (
        <strong>{params.value}</strong>
      ) : (
        params.value
      );
    },
  },
  {
    field: "receipts",
    headerName: (
      <div className="flex items-center justify-center w-full">
        <span>Receipts</span>
      </div>
    ),
    flex: 1,
    minWidth: 120,
    headerClassName: "data-grid-header",
    renderCell: (params) => {
      if (params.row.isGroupHeader) return null;

      return (
        <div className="flex items-center justify-center w-full h-full">
          <img
            src={receipt}
            alt="Receipt"
            className="w-5 h-5 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              handleReceiptClick(params.row.transaction_id);
            }}
          />
          <img
            src={split}
            alt="Split Icon"
            className="w-5 h-5 ml-5 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              handleSplitIconClick(params.row);
            }}
          />
        </div>
      );
    },
  },
];

const TransactionPage = ({ darkMode }) => {
  // Redux state selectors
  const dispatch = useDispatch();

  //TODO: replceIt
  const closeReceiptModal = () => {};

  const [selectedFile, setSelectedFile] = useState([]);

  const [splitModalOpen, setSplitModalOpen] = useState(false);
  const [selectedSplitTransaction, setSelectedSplitTransaction] =
    useState(null);

  const handleSplitIconClick = useCallback(
    (transaction) => {
      setSelectedSplitTransaction(transaction);
      setSplitModalOpen(true);
      // Close any other open modals if needed
      dispatch(setOpenModal(false));
    },
    [dispatch]
  );

  const handleCloseSplitModal = useCallback(() => {
    setSplitModalOpen(false);
    setSelectedSplitTransaction(null);
  }, []);

  const handleSubmitSplit = (splitData) => {
    console.log("Split request submitted:", splitData);
    // The actual submission is now handled by the epic
  };
  // Transactions state
  const transactions = useSelector((state) => state.transactions.transactions);
  const page = useSelector((state) => state.transactions.page);
  const pageSize = useSelector((state) => state.transactions.pageSize);
  const totalElements = useSelector(
    (state) => state.transactions.totalElements
  );
  const totalPages = useSelector((state) => state.transactions.totalPages);

  const [rows, setRows] = useState([]);
  const [showHidden, setShowHidden] = useState(false);
  const summary = useSelector((state) => state.transactions.summary);
  const accounts = useSelector((state) => state.transactions.accounts); // same slice

  const hiddenTransactions = useSelector(
    (state) => state.transactions.hiddenTransactions
  );
  const selectedTransaction = useSelector(
    (state) => state.transactions.selectedTransaction
  );
  const openModal = useSelector((state) => state.transactions.openModal);
  const isCardVisible = useSelector(
    (state) => state.transactions.isCardVisible
  );
  const searchName = useSelector((state) => state.transactions.searchName);
  const searchDate = useSelector((state) => state.transactions.searchDate);
  const sortOrder = useSelector((state) => state.transactions.sortOrder);

  const selectedTransactions = useSelector(
    (state) => state.transactions.selectedTransactions
  );
  const allChecked = useSelector((state) => state.transactions.allChecked);
  const isAddTransactionOpen = useSelector(
    (state) => state.transactions.isAddTransactionOpen
  );
  const newTransaction = useSelector(
    (state) => state.transactions.newTransaction
  );
  const hideFromBudgetSuccess = useSelector(
    (state) => state.transactions.hideFromBudgetSuccess
  );
  const hideFromBudgetError = useSelector(
    (state) => state.transactions.hideFromBudgetError
  );
  const [expandedRows, setExpandedRows] = useState({});
  const reconciledTransactionsById = useSelector(
    (state) => state.transactions.reconciledTransactionsById
  );
  const [expandedTransactionIds, setExpandedTransactionIds] = useState([]);
  const [customFilterOpen, setCustomFilterOpen] = useState(false);

  const handleHideTransaction = (transactionId) => {
    if (!transactionId) return;
    dispatch({
      type: "transactions/hideTransactions",
      payload: [transactionId],
    });
    closeTransactionModal();
  };
  const user = useSelector((state) => state.auth.user);

  useEffect(() => {
    if (user && user.id) {
      dispatch(fetchAccountsRequest({ userId: user.id }));
    }
  }, [dispatch, user]);

  //pagination
  // const [page, setPage] = useState(0);
  // const [pageSize, setPageSize] = useState(150); //  adjust this  value

  // Calculate pagination
  // Custom Pagination component
  const CustomPagination = () => {
    const handlePageChange = (newPage) => {
      console.log("Changing page to:", newPage);
      dispatch(setPage(newPage));
    };

    const getVisiblePages = () => {
      const visiblePages = [];
      const maxVisiblePages = 5;

      if (totalPages <= maxVisiblePages) {
        for (let i = 0; i < totalPages; i++) {
          visiblePages.push(i);
        }
      } else {
        const startPage = Math.max(
          0,
          Math.min(page - 2, totalPages - maxVisiblePages)
        );
        const endPage = Math.min(totalPages, startPage + maxVisiblePages);
        for (let i = startPage; i < endPage; i++) {
          visiblePages.push(i);
        }
      }
      return visiblePages;
    };

    if (totalPages <= 1) return null;

    return (
      <div className="flex items-center space-x-1">
        <button
          onClick={() => handlePageChange(page - 1)}
          disabled={page === 0}
          className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
            page === 0
              ? "text-gray-400 cursor-not-allowed"
              : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
          }`}
        >
          Previous
        </button>
        {getVisiblePages().map((pageNum) => (
          <button
            key={pageNum}
            onClick={() => handlePageChange(pageNum)}
            className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
              page === pageNum
                ? "bg-lime-500 text-white shadow-sm"
                : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
            }`}
          >
            {pageNum + 1}
          </button>
        ))}
        <button
          onClick={() => handlePageChange(page + 1)}
          disabled={page === totalPages - 1}
          className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
            page === totalPages - 1
              ? "text-gray-400 cursor-not-allowed"
              : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
          }`}
        >
          Next
        </button>
        <span className="text-sm text-gray-500 ml-4">
          Page {page + 1} of {totalPages} ({totalElements} total)
        </span>
      </div>
    );
  };

  // Receipts state
  const {
    errorMessage,
    jsonResponse,
    currentTab,
    receiptUploadModal,
    showError,
    blinkError,
    isReceiptModalOpen,
    selectedReceipt,
    // selectedFile,
    fileMetadata,
    // selectedTransaction,
    uploadPopupWidth,
    editingField,
    editedValue,
    editedItemIndex,
    selectedDate,
    uploadProgress,
    isUploading,
    isProcessing,
    isPopupVisible,

    //  receiptNewTransaction,
    receiptTransactionIds,
  } = useSelector((state) => state.receipts);
  const receiptNewTransaction = useSelector(
    (state) => state.receipts.receiptNewTransaction
  );

  const { addTransactionSuccess, addTransactionError } = useSelector(
    (state) => state.receipts
  );
  useEffect(() => {
    if (addTransactionSuccess) {
      alert("Transaction added successfully!");
      dispatch({
        type: "receipts/resetAddTransactionStatus", // Add a new action to reset status
        payload: { addTransactionSuccess: false, addTransactionError: null },
      });
    }
  }, [addTransactionSuccess, dispatch]);

  useEffect(() => {
    if (addTransactionError) {
      alert(`Error adding transaction: ${addTransactionError}`);
      dispatch({
        type: "receipts/resetAddTransactionStatus",
        payload: { addTransactionSuccess: false, addTransactionError: null },
      });
    }
  }, [addTransactionError, dispatch]);

  // useEffect(() => {
  //     if (hideFromBudgetSuccess) {
  //       alert('Transactions hidden from budget successfully!');
  //       dispatch(resetHideFromBudgetStatus());
  //     }
  //   }, [hideFromBudgetSuccess, dispatch]);

  //   useEffect(() => {
  //     if (hideFromBudgetError) {
  //       alert(`Error hiding transactions from budget: ${hideFromBudgetError}`);
  //       dispatch(resetHideFromBudgetStatus());
  //     }
  //   }, [hideFromBudgetError, dispatch]);
  const { saveSuccess, saveError } = useSelector((state) => state.receipts);
  const [sortModel, setSortModel] = useState([]);

  const handleSortModelChange = (newSortModel) => {
    setSortModel(newSortModel);
  };

  // Update sortComparator to use sortModel
  const getSortDirection = (field) => {
    const sortItem = sortModel.find((model) => model.field === field);
    return sortItem ? sortItem.sort : "asc";
  };

  // Fetch transactions on component mount
  useEffect(() => {
    dispatch(fetchTransactionsStart());
  }, [dispatch, page, pageSize]);

  useEffect(() => {
    console.log("Dispatching fetchTransactionSummaryStart");
    dispatch(fetchTransactionSummaryStart());
  }, [dispatch]);

  useEffect(() => {
    dispatch({ type: "transactions/fetchHiddenTransactions" });
  }, [dispatch]);
  // Event handlers
  // const handleTabChange = (event, newValue) => {
  //   dispatch(setCurrentTab(newValue));
  // };

  const handleSelectAll = () => {
    dispatch(toggleSelectAll());
  };

  const handleCheckboxChange = (transactionId) => {
    dispatch(toggleSelectTransaction(transactionId));
  };

  const toggleTransactionModal = (transaction) => {
    dispatch(setSelectedTransaction(transaction));
    dispatch(setOpenModal(true));
  };

  const closeTransactionModal = () => {
    dispatch(setOpenModal(false));
    dispatch(setSelectedTransaction(null));
  };

  const isMatchingTransactionAdd = useSelector(
    (state) => state.receipts.isMatchingTransactionAdd
  );

  //for trans detail popup

  const formatDateForInput = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    dispatch(setCurrentTab(newValue));
  };

  // Handle radio change
  const handleRadioChange = (transactionId) => {
    dispatch(
      setSelectedTransaction(
        selectedTransaction === transactionId ? null : transactionId
      )
    );
  };

  // Handle double click on fields
  const handleDoubleClick = (field, index) => {
    dispatch(setEditingField(field));
    dispatch(setEditedItemIndex(index));
    dispatch(setEditedValue(jsonResponse?.Items?.[index]?.[field] || ""));
  };

  const handleFieldDoubleClick = (field) => {
    dispatch(setEditingField(field));
    dispatch(setEditedItemIndex(null));
    dispatch(setEditedValue(jsonResponse?.[field] || ""));
  };

  // Handle date change
  const handleDateChange = (date) => {
    dispatch(setSelectedDate(date));
    dispatch(setEditedValue(date.toISOString().split("T")[0]));
  };

  useEffect(() => {
    console.log(
      "useEffect jsonResponse:",
      JSON.stringify(jsonResponse, null, 2)
    ); // Detailed debug log
    if (jsonResponse && Object.keys(jsonResponse).length > 0) {
      setTimeout(() => {
        console.log(
          "Opening receiptUploadModal with jsonResponse:",
          JSON.stringify(jsonResponse, null, 2)
        ); // Debug log
        dispatch(setReceiptUploadModal(true));
        dispatch(setCurrentTab("receipt"));
        dispatch(setIsProcessing(false));
        setSelectedFile([]);
      }, 700);
    }
  }, [jsonResponse, dispatch]);

  // Handle save receipt
  // In your component
  useEffect(() => {
    if (saveSuccess) {
      alert("Receipt saved successfully!");
      closeReceiptModal();
    }
  }, [saveSuccess]);

  useEffect(() => {
    if (saveError) {
      alert(`Error saving receipt: ${saveError}`);
    }
  }, [saveError]);
  const handleSave = () => {
    console.log("handleSave - Selected Transaction:", selectedTransaction);
    console.log(
      "handleSave - Matching Transactions:",
      JSON.stringify(jsonResponse.matchingTransactions, null, 2)
    );

    const filePath = jsonResponse.savedFilePath || jsonResponse.filePath;
    if (!filePath) {
      dispatch(setErrorMessage("Receipt file path is missing."));
      dispatch(setShowError(true));
      return;
    }

    const updatedJsonResponse = {
      ...jsonResponse,
      savedFilePath: filePath,
      docType: jsonResponse.docType || "receipt",
      docName: jsonResponse.docName || "Uploaded Receipt",
      category: jsonResponse.category || "Uncategorized",
      qrData: jsonResponse.qrData || "",
      scannedCopyPath: jsonResponse.scannedCopyPath || "",
      merchantName: jsonResponse.MerchantName || "Unknown",
      merchantAddress: jsonResponse.MerchantAddress || "Unknown",
      merchantPhoneNumber: jsonResponse.MerchantPhoneNumber || "Unknown",
      transactionDate: jsonResponse.TransactionDate || "",
      transactionTime: jsonResponse.TransactionTime || "",
      Items: (jsonResponse.Items || []).map((item) => ({
        Name: item.Name || "Unknown",
        Price: String(item.Price || "0.00"),
        TotalPrice: String(item.TotalPrice || "0.00"),
        Quantity: Number(item.Quantity || 1),
        Category: item.Category || "Uncategorized",
      })),
      subtotal: String(
        jsonResponse.subtotal || jsonResponse.Subtotal || "0.00"
      ),
      tax: String(jsonResponse.tax || jsonResponse.Tax || "0.00"),
      total: String(jsonResponse.total || jsonResponse.Total || "0.00"),
      matchingTransactions: jsonResponse.matchingTransactions || [],
      transactionId: selectedTransaction ? String(selectedTransaction) : null,
      userId: jsonResponse.userId || getCurrentUserId(),
    };

    if (editingField && editedItemIndex !== null) {
      updatedJsonResponse.Items = updatedJsonResponse.Items.map((item, index) =>
        index === editedItemIndex
          ? { ...item, [editingField]: editedValue }
          : item
      );
    } else if (editingField) {
      updatedJsonResponse[editingField] = editedValue;
    }

    console.log(
      "handleSave - updatedJsonResponse:",
      JSON.stringify(updatedJsonResponse, null, 2)
    );

    dispatch(
      saveReceiptRequest({
        jsonResponse: updatedJsonResponse,
        selectedTransaction: selectedTransaction,
      })
    );

    dispatch(setEditingField(null));
    dispatch(setEditedItemIndex(null));
    dispatch(setEditedValue(null));
  };
  useEffect(() => {
    dispatch(fetchReceiptTransactionIdsRequest());
  }, [dispatch]);
  // Handle receipt click
  const handleReceiptClick = (transactionId) => {
    dispatch(fetchReceiptDetailsRequest(transactionId));
  };
  const handleCloseReceiptModal = () => {
    dispatch(setIsReceiptModalOpen(false));
  };

  // Add this function to your component
  const handleShowPopup = () => {
    dispatch(
      setSelectedTransaction({
        date: new Date().toISOString().split("T")[0], // Set default date to today
        name: "",
        subcategory: "",
        bank: "",
        amount: "",
        tax: "",
        notes: "",
        tag: "",
        hideFromBudget: false,
        hidden: false,
      })
    );
    dispatch(setIsMatchingTransactionAdd(true));
    dispatch(setOpenModal(true));
  };
  // Handle new transaction save
  const handleSaveNewTransaction = () => {
    if (
      !receiptNewTransaction.date ||
      !receiptNewTransaction.description ||
      !receiptNewTransaction.subcategory ||
      !receiptNewTransaction.account ||
      !receiptNewTransaction.amount
    ) {
      dispatch(setErrorMessage("Please fill in all required fields."));
      dispatch(setShowError(true));
      return;
    }

    try {
      const fullDate = new Date(receiptNewTransaction.date);
      if (isNaN(fullDate)) {
        dispatch(setErrorMessage("Invalid date format."));
        dispatch(setShowError(true));
        return;
      }
      const formattedDate = fullDate.toISOString();

      const newTransactionData = {
        transactionId: `temp-${Date.now()}`, // Temp ID for UI
        transactionDate: formattedDate,
        description: receiptNewTransaction.description,
        category: receiptNewTransaction.subcategory,
        categoryId: 5, // Replace with valid ID for the category
        transactionAmount: parseFloat(receiptNewTransaction.amount) || 0,
        budgetId: 1, // Replace with valid ID
        userId: getCurrentUserId(),
        accountId: Number(receiptNewTransaction.account),
        account:
          accounts.find(
            (a) => a.accountId === Number(receiptNewTransaction.account)
          )?.accountName || "",
      };

      console.log(
        "handleSaveNewTransaction - newTransactionData:",
        JSON.stringify(newTransactionData, null, 2)
      );

      dispatch(addNewTransaction(newTransactionData));

      // Reset form
      dispatch(
        setReceiptNewTransaction({
          date: "",
          description: "",
          subcategory: "",
          account: "",
          amount: "",
        })
      );
    } catch (error) {
      console.error("Error adding transaction:", error);
      dispatch(setErrorMessage("Failed to add transaction"));
      dispatch(setShowError(true));
    }
  };

  // const handleReceiptClick = (transactionId) => {
  //   dispatch(fetchReceiptDetailsRequest(transactionId));
  // };

  const handleCloseAddTransaction = () => {
    dispatch(toggleAddTransactionModal(false));
  };

  const handletranssave = () => {
    let formattedDate = null;

    if (newTransaction.date) {
      const fullDate = new Date(newTransaction.date);
      if (!isNaN(fullDate)) {
        formattedDate = fullDate.toISOString();
      } else {
        console.warn("Invalid date format:", newTransaction.date);
      }
    }

    const transactionData = {
      transactionId: `temp-${Date.now()}`,
      transactionDate: formattedDate,
      description: newTransaction.description,
      category: newTransaction.category,
      account: newTransaction.account,
      transactionAmount: newTransaction.amount
        ? parseFloat(newTransaction.amount)
        : 0,
      userId: getCurrentUserId(),
    };

    dispatch(addTransactionRequest({ transactionData }));
  };

  // Helper functions
  const getCategoryEmoji = (category) => {
    if (Array.isArray(category)) {
      category = category.join(" ").toLowerCase();
    } else if (typeof category === "string") {
      category = category.toLowerCase();
    } else {
      return "❓";
    }

    const categoryEmojis = {
      taxi: "🚗",
      shopping: "🛍️",
      "credit card": "💳",
      "airlines and aviation services": "✈️",
      restaurants: "🍹",
      payroll: "💸",
      payment: "💰",
      "sporting goods": "🏏",
      debit: "💳",
      deposit: "🏧",
      entertainment: "🎬",
      "gyms and fitness centers": "💪",
    };

    return categoryEmojis[category.toLowerCase()] || "❓";
  };

  // Group transactions by date
  const handleRowClick = (transactionId) => {
    console.log("1. Row clicked - transactionId:", transactionId);

    setExpandedRows((prev) => {
      const newState = { ...prev, [transactionId]: !prev[transactionId] };
      console.log("2. Expanded rows state:", newState);
      return newState;
    });

    const transaction = rows.find(
      (row) => row.transaction_id === transactionId
    );
    console.log("3. Found transaction:", transaction);

    if (!reconciledTransactionsById[transaction.reconcileId]) {
      console.log("Dispatching to fetch reconciled transactions");
      dispatch({
        type: "transactions/getReconciledTransactions",
        payload: transaction.reconcileId,
      });
    } else {
      console.log("4. No reconcileId found for transaction");
    }
  };

  useEffect(() => {
    if (!Array.isArray(transactions)) return;

    const buildRows = () => {
      console.log("Building rows with state:", {
        transactions,
        reconciledTransactionsById,
        expandedRows,
      });

      console.log(
        "Transactions data:",
        transactions.map((t) => ({
          id: t.id,
          description: t.description,
          reconcileId: t.reconcileId,
        }))
      );
      const groupedTransactions = transactions.reduce((acc, transaction) => {
        const transactionDate = new Date(
          transaction.transactionDate
        ).toLocaleDateString();
        if (!acc[transactionDate]) {
          acc[transactionDate] = { totalAmount: 0, transactions: [] };
        }
        acc[transactionDate].transactions.push(transaction);
        acc[transactionDate].totalAmount += transaction.transactionAmount || 0;
        return acc;
      }, {});

      let rowsArray = [];
      let idCounter = 1;

      Object.keys(groupedTransactions)
        .sort((a, b) => new Date(b) - new Date(a))
        .forEach((date) => {
          rowsArray.push({
            id: `header-${date}`,
            date,
            isGroupHeader: true,
            amount: groupedTransactions[date].totalAmount?.toFixed(2) || "0.00",
            name: "", // Ensure name is set for group header
            category: "",
            bank: "",
          });

          groupedTransactions[date].transactions.forEach((transaction) => {
            const isExpanded = expandedRows[transaction.id];
            rowsArray.push({
              id: idCounter++,
              transaction_id: transaction.id,
              date,
              name: transaction.description,
              category: `${getCategoryEmoji(transaction.category)} ${
                transaction.category
              }`,
              amount: transaction.transactionAmount?.toFixed(2) || "N/A",
              bank: transaction.accountName || "Unknown Bank",
              receipts: transaction.receiptUrl,
              reconcileId: transaction.reconcileId,
              isExpanded,
            });

            if (
              isExpanded &&
              transaction.reconcileId &&
              reconciledTransactionsById[transaction.reconcileId]
            ) {
              console.log(
                "Adding reconciled rows for:",
                transaction.reconcileId
              );
              console.log(
                "Reconciled transactions:",
                reconciledTransactionsById[transaction.reconcileId]
              );
              const originalAmount = transaction.transactionAmount;

              reconciledTransactionsById[transaction.reconcileId].forEach(
                (match, index) => {
                  if (
                    match.id !== transaction.id &&
                    originalAmount * match.amount < 0 // ensures opposite sign
                  ) {
                    rowsArray.push({
                      id: `reconcile-${transaction.id}-${index}`,
                      isReconciledDetail: true,
                      date: new Date(
                        match.transactionDate
                      ).toLocaleDateString(),
                      name: match.description,
                      category: match.category,
                      amount: match.amount?.toFixed(2) || "N/A", // Use match.amount and add null check
                      bank: match.accountName || "Unknown",
                      parentId: transaction.id,
                    });
                  }
                }
              );
            }
          });
        });

      console.log("Final rows built:", rowsArray);
      return rowsArray;
    };

    setRows(buildRows());
  }, [transactions, reconciledTransactionsById, expandedRows]);

  const hiddenColumns = [
    {
      field: "transactionDate",
      headerName: "Date",
      width: 300,
      // minWidth: 120,
      headerClassName: "lime-header",
      renderCell: (params) => (
        <span className="text-gray-700 text-sm">{params.value}</span>
      ),
    },
    {
      field: "description",
      headerName: "Description",
      width: 320,
      minWidth: 180,
      flex: 1,
      maxWidth: 300,
      headerClassName: "lime-header",
      renderCell: (params) => {
        const handleNameClick = (e) => {
          e.stopPropagation();
          dispatch(setSelectedTransaction(params.row));
          dispatch(setOpenModal(true));
        };

        return (
          <div onClick={handleNameClick} className="cursor-pointer w-full">
            <TransactionNameCell name={params.value} icon={params.row.icon} />
          </div>
        );
      },
    },
    {
      field: "category",
      headerName: "Category",
      width: 250,
      // minWidth: 120,
      headerClassName: "lime-header",
      renderCell: (params) => (
        <span className="text-gray-700 text-sm">{params.value}</span>
      ),
    },
    {
      field: "accountName",
      headerName: "Account",
      width: 300,
      // minWidth: 150,
      headerClassName: "lime-header",
      renderCell: (params) => <AccountCell bankName={params.value} />,
    },
    {
      field: "transactionAmount",
      headerName: "Amount",
      width: 220,
      // minWidth: 120,
      headerClassName: "lime-header",
      align: "right",
      headerAlign: "right",
      renderCell: (params) => (
        <span className="text-gray-700 text-sm font-medium">
          {params.value}
        </span>
      ),
    },
  ];

  const headerBaseClass = "font-[Architects Daughter] text-lg";
  const headerBgClass = darkMode
    ? "bg-gray-800 text-white"
    : "bg-white text-black";

  // Define DataGrid columns
  const columns = [
    {
      field: "checkbox",
      headerName: "",
      width: 60,
      minWidth: 60,
      maxWidth: 60,
      sortable: false,
      filterable: false,
      disableColumnMenu: true,
      renderHeader: (params) => {
        const selectableRows = rows.filter(
          (row) =>
            !row.isGroupHeader && !row.isReconciledDetail && row.transaction_id
        );

        const allSelected =
          selectableRows.length > 0 &&
          selectableRows.every((row) =>
            selectedTransactions.includes(row.transaction_id)
          );

        const someSelected = selectableRows.some((row) =>
          selectedTransactions.includes(row.transaction_id)
        );

        return (
          <div className="flex items-center justify-center h-full">
            <input
              type="checkbox"
              checked={allSelected}
              ref={(el) => {
                if (el) el.indeterminate = !allSelected && someSelected;
              }}
              onChange={(e) => {
                const isChecked = e.target.checked;
                const transactionIds = selectableRows.map(
                  (row) => row.transaction_id
                );

                if (isChecked) {
                  dispatch(selectAllTransactions(transactionIds));
                } else {
                  dispatch(deselectAllTransactions(transactionIds));
                }
              }}
              className="h-4 w-4 text-lime-500 border-gray-300 rounded focus:ring-lime-500"
              onClick={(e) => e.stopPropagation()}
            />
          </div>
        );
      },
      renderCell: (params) => {
        if (
          params.row.isGroupHeader ||
          params.row.isReconciledDetail ||
          !params.row.transaction_id
        )
          return null;
        const isSelected = selectedTransactions.includes(
          params.row.transaction_id
        );
        return (
          <div
            className={`${
              isSelected ? "opacity-100" : "opacity-0 group-hover:opacity-100"
            } transition-opacity checkbox-cell`}
          >
            <input
              type="checkbox"
              checked={isSelected}
              onChange={() =>
                dispatch(toggleSelectTransaction(params.row.transaction_id))
              }
              className="h-4 w-4 text-lime-500 border-gray-300 rounded focus:ring-lime-500"
              onClick={(e) => e.stopPropagation()}
            />
          </div>
        );
      },
    },
    {
      field: "date",
      headerName: "Date",
      width: 180,
      minWidth: 150,
      maxWidth: 180,
      headerClassName: "font-mono text-lg text-lime-700",
      renderCell: (params) =>
        params.row.isGroupHeader ? (
          <strong className="text-base text-lime-700">{params.value}</strong>
        ) : (
          <span
            className={
              params.row.isReconciledDetail ? "text-gray-500 pl-4" : ""
            }
          >
            {params.value}
          </span>
        ),
    },
    {
      field: "name",
      headerName: "Description",
      // width: 300,
      minWidth: 200,
      flex: 1,
      maxWidth: 300,
      headerClassName: "font-mono text-lg text-lime-700",
      renderCell: (params) => {
        if (params.row.isGroupHeader) {
          return null;
        }
        if (params.row.isReconciledDetail) {
          return (
            <div className="pl-8 text-gray-500 italic">
              <TransactionNameCell name={params.value} icon={params.row.icon} />
            </div>
          );
        }
        const hasReconcile = params.row.reconcileId;
        const isLoading =
          hasReconcile &&
          expandedRows[params.row.transaction_id] &&
          !reconciledTransactionsById[params.row.reconcileId];

        const handleArrowClick = (e) => {
          e.stopPropagation(); // Prevent row click
          handleRowClick(params.row.transaction_id); // Trigger expansion
        };

        const handleNameClick = (e) => {
          e.stopPropagation(); // Prevent row click
          dispatch(setSelectedTransaction(params.row));
          dispatch(setOpenModal(true));
        };

        return (
          <Tooltip
            title={
              hasReconcile ? (
                <div className="p-2">
                  <span className="text-white">
                    Click the arrow to view reconciled transactions
                  </span>
                </div>
              ) : (
                <span>No reconciled transactions available</span>
              )
            }
            placement="top"
            arrow
            disableInteractive={!hasReconcile}
          >
            <div className="flex items-center space-x-2">
              {hasReconcile ? (
                <div
                  onClick={handleArrowClick}
                  className="cursor-pointer text-lime-600"
                >
                  {isLoading ? (
                    <CircularProgress size={20} className="text-lime-500" />
                  ) : expandedRows[params.row.transaction_id] ? (
                    "▼"
                  ) : (
                    "▶"
                  )}
                </div>
              ) : (
                <div className="w-5" />
              )}
              <div onClick={handleNameClick} className="cursor-pointer">
                <TransactionNameCell
                  name={params.value}
                  icon={params.row.icon}
                />
              </div>
            </div>
          </Tooltip>
        );
      },
      sortComparator: (v1, v2, param1, param2) => {
        const row1 = param1.api.getRow(param1.id);
        const row2 = param2.api.getRow(param2.id);

        const dateComparison = new Date(row2.date) - new Date(row1.date);
        if (row1.date !== row2.date) {
          return dateComparison;
        }

        if (row1.isGroupHeader && !row2.isGroupHeader) return -1;
        if (!row1.isGroupHeader && row2.isGroupHeader) return 1;
        if (row1.isGroupHeader && row2.isGroupHeader) return 0;

        if (row1.isReconciledDetail || row2.isReconciledDetail) {
          if (row1.isReconciledDetail && !row2.isReconciledDetail) return 1;
          if (!row1.isReconciledDetail && row2.isReconciledDetail) return -1;
          if (
            row1.isReconciledDetail &&
            row2.isReconciledDetail &&
            row1.parentId === row2.parentId
          ) {
            return (v1 || "").localeCompare(v2 || "");
          }
          return 0;
        }

        return (v1 || "").localeCompare(v2 || "");
      },
    },
    {
      field: "category",
      headerName: "Category",
      width: 200,
      minWidth: 150,
      maxWidth: 200,
      headerClassName: "font-mono text-lg text-lime-700",
      renderCell: (params) => (
        <span
          className={params.row.isReconciledDetail ? "text-gray-500 pl-4" : ""}
        >
          {params.value}
        </span>
      ),
      sortComparator: (v1, v2, param1, param2) => {
        const row1 = param1.api.getRow(param1.id);
        const row2 = param2.api.getRow(param2.id);

        const dateComparison = new Date(row2.date) - new Date(row1.date);
        if (row1.date !== row2.date) {
          return dateComparison;
        }

        if (row1.isGroupHeader && !row2.isGroupHeader) return -1;
        if (!row1.isGroupHeader && row2.isGroupHeader) return 1;
        if (row1.isGroupHeader && row2.isGroupHeader) return 0;

        if (row1.isReconciledDetail || row2.isReconciledDetail) {
          if (row1.isReconciledDetail && !row2.isReconciledDetail) return 1;
          if (!row1.isReconciledDetail && row2.isReconciledDetail) return -1;
          if (
            row1.isReconciledDetail &&
            row2.isReconciledDetail &&
            row1.parentId === row2.parentId
          ) {
            return (v1 || "").localeCompare(v2 || "");
          }
          return 0;
        }

        return (v1 || "").localeCompare(v2 || "");
      },
    },
    {
      field: "bank",
      headerName: "Account",
      width: 200,
      minWidth: 150,
      maxWidth: 200,
      headerClassName: "font-mono text-lg text-lime-700",
      renderCell: (params) => {
        if (params.row.isGroupHeader) return null;
        return <AccountCell bankName={params.value} />;
      },
      sortComparator: (v1, v2, param1, param2) => {
        const row1 = param1.api.getRow(param1.id);
        const row2 = param2.api.getRow(param2.id);

        const dateComparison = new Date(row2.date) - new Date(row1.date);
        if (row1.date !== row2.date) {
          return dateComparison;
        }

        if (row1.isGroupHeader && !row2.isGroupHeader) return -1;
        if (!row1.isGroupHeader && row2.isGroupHeader) return 1;
        if (row1.isGroupHeader && row2.isGroupHeader) return 0;

        if (row1.isReconciledDetail || row2.isReconciledDetail) {
          if (row1.isReconciledDetail && !row2.isReconciledDetail) return 1;
          if (!row1.isReconciledDetail && row2.isReconciledDetail) return -1;
          if (
            row1.isReconciledDetail &&
            row2.isReconciledDetail &&
            row1.parentId === row2.parentId
          ) {
            return (v1 || "").localeCompare(v2 || "");
          }
          return 0;
        }

        return (v1 || "").localeCompare(v2 || "");
      },
    },
    {
      field: "amount",
      headerName: "Price",
      width: 140,
      minWidth: 100,
      maxWidth: 140,
      headerClassName: "font-mono text-lg text-lime-700",
      type: "number",
      renderCell: (params) =>
        params.row.isGroupHeader ? (
          <strong className="text-lime-700">{params.value}</strong>
        ) : (
          <span
            className={
              params.row.isReconciledDetail ? "text-gray-500 pl-4" : ""
            }
          >
            {params.value}
          </span>
        ),
      sortComparator: (v1, v2, param1, param2) => {
        const row1 = param1.api.getRow(param1.id);
        const row2 = param2.api.getRow(param2.id);

        const dateComparison = new Date(row2.date) - new Date(row1.date);
        if (row1.date !== row2.date) {
          return dateComparison;
        }

        if (row1.isGroupHeader && !row2.isGroupHeader) return -1;
        if (!row1.isGroupHeader && row2.isGroupHeader) return 1;
        if (row1.isGroupHeader && row2.isGroupHeader) return 0;

        if (row1.isReconciledDetail || row2.isReconciledDetail) {
          if (row1.isReconciledDetail && !row2.isReconciledDetail) return 1;
          if (!row1.isReconciledDetail && row2.isReconciledDetail) return -1;
          if (
            row1.isReconciledDetail &&
            row2.isReconciledDetail &&
            row1.parentId === row2.parentId
          ) {
            return parseFloat(v1 || 0) - parseFloat(v2 || 0);
          }
          return 0;
        }

        return parseFloat(v1 || 0) - parseFloat(v2 || 0);
      },
    },
    {
      field: "receipts",
      headerName: "Receipts",
      width: 140,
      minWidth: 120,
      maxWidth: 140,
      headerClassName: "font-mono text-lg text-lime-700",
      renderCell: (params) => {
        if (params.row.isGroupHeader) return null;
        const receiptImageUrl = params.value || receipt;
        const splitIconUrl = split;
        const hasReceipt = receiptTransactionIds.includes(
          params.row.transaction_id
        );
        return (
          <div className="flex items-center justify-center h-full">
            <Tooltip title={hasReceipt ? "" : "No receipt available"}>
              <img
                src={receiptImageUrl}
                alt="Receipt"
                style={{
                  width: "20px",
                  height: "20px",
                  cursor: hasReceipt ? "pointer" : "not-allowed",
                  opacity: hasReceipt ? 1 : 0.5,
                }}
                onClick={() => {
                  if (hasReceipt) {
                    handleReceiptClick(params.row.transaction_id);
                  }
                }}
              />
            </Tooltip>
            <img
              src={split}
              alt="Split Icon"
              className="w-5 h-5 ml-3 cursor-pointer"
              onClick={(e) => {
                e.stopPropagation(); // Prevent row click
                handleSplitIconClick(params.row);
              }}
            />
          </div>
        );
      },
    },
  ];

  // Update renderCardLayout function to accept darkMode
  const renderCardLayout = (data, idPrefix, darkMode) => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
      {data.map((item, index) => {
        const IconComponent = item.icon;
        return (
          <div
            key={`${idPrefix}-${index}`}
            className={`
                group relative overflow-hidden rounded-xl shadow-lg hover:shadow-xl
                transform hover:-translate-y-2 transition-all duration-300 ease-out cursor-pointer
                border border-lime-200/50 hover:border-lime-300 w-full
                ${
                  darkMode
                    ? `${item.darkBgColor} hover:bg-opacity-40 backdrop-blur-lg`
                    : `${item.bgColor} hover:bg-opacity-80`
                }
              `}
          >
            {/* Top Decorative Bar */}
            <div
              className={`
                h-1 w-full bg-gradient-to-r ${item.color}
                transform scale-x-0 group-hover:scale-x-100 
                transition-transform duration-300 origin-left
              `}
            ></div>

            <div className="p-4 relative z-10">
              {/* Header with Icon and Label on Left */}
              <div className="flex items-center mb-4">
                <div
                  className={`
                    p-2 rounded-lg bg-gradient-to-br ${item.color}
                    shadow-lg transform group-hover:rotate-6 group-hover:scale-105
                    transition-all duration-300 mr-3
                  `}
                >
                  <IconComponent className="text-white text-lg" />
                </div>

                {/* Label next to icon */}
                <label
                  className={`
                    text-sm font-bold uppercase tracking-wide
                    ${darkMode ? "text-gray-300" : "text-gray-600"}
                    group-hover:text-lime-600 transition-colors duration-300
                  `}
                >
                  {item.label}
                </label>

                {/* Fixed Dot - moved to far right */}
                <div className="ml-auto">
                  <div
                    className={`
                      w-2 h-2 rounded-full bg-gradient-to-r ${item.color}
                    `}
                  ></div>
                </div>
              </div>

              {/* Value - Centered */}
              <div className="text-center">
                <p
                  className={`
                    text-2xl font-bold tracking-tight
                    ${darkMode ? "text-white" : "text-gray-900"}
                    group-hover:text-lime-700 transition-colors duration-300
                  `}
                >
                  {item.value}
                </p>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );

  <ReceiptModal />;
  return (
    <div
      className={`
        p-8 min-h-screen w-full font-roboto relative overflow-hidden
        ${
          darkMode
            ? "bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900"
            : "bg-gradient-to-br from-lime-50 via-white to-lime-100"
        }
      `}
    >
      {/* Fixed Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 rounded-full bg-lime-300 opacity-10"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 rounded-full bg-lime-400 opacity-5"></div>
      </div>

      {/* Header */}
      <div className="relative z-10 mb-8">
        <h1
          className={`
            flex items-center text-2xl font-bold tracking-tight
            ${darkMode ? "text-white" : "text-gray-900"}
          `}
        >
          Transactions
          <FaChevronDown
            className={`
                ml-4 cursor-pointer transition-all duration-300 ease-in-out
                hover:scale-110 active:scale-95
                ${isCardVisible ? "rotate-180" : "rotate-0"}
                ${
                  darkMode
                    ? "text-gray-300 hover:text-white"
                    : "text-gray-600 hover:text-gray-900"
                }
              `}
            onClick={() => dispatch(toggleCardVisibility())}
          />
        </h1>
      </div>

      {/* Cards Container */}
      {isCardVisible && (
        <TransactionStatCard darkMode={darkMode} summary={summary} />
      )}

      {isReceiptModalOpen && (
        <ReceiptModal
          receiptData={selectedReceipt}
          onClose={handleCloseReceiptModal}
        />
      )}

      <SplitTransactionPopup
        open={splitModalOpen}
        onClose={handleCloseSplitModal}
        transaction={selectedSplitTransaction}
        onSubmitSplit={handleSubmitSplit}
      />

      {/* Filters */}
      <TransactionControls
        selectedTransactions={selectedTransactions}
        isProcessing={isProcessing}
        setReceiptUploadModal={setReceiptUploadModal}
      />
      {/* DataGrid */}
      <div className="w-full max-w-7xl mx-auto overflow-x-auto -ml-4">
        <div
          className={`
              w-full font-bold p-2.5 mt-5
              ${darkMode ? "bg-gray-800 text-white" : "bg-white text-black"}
              rounded-t-xl cursor-pointer border-t border-l border-r
              ${darkMode ? "border-gray-600" : "border-gray-300"}
              shadow-[0_-4px_6px_0_rgba(0,0,0,0.1),0_6px_8px_-4px_rgba(0,0,0,0.1)]
            `}
        >
          <DataGrid
            rows={rows}
            disableColumnResize
            pagination={false}
            columns={columns}
            hideFooter
            disableColumnSelector
            autoHeight
            sortingMode="client"
            sortModel={sortModel}
            onSortModelChange={handleSortModelChange}
            disableSelectionOnClick
            getRowId={(row) => row.id || row.transaction_id}
            sx={{
              "& .MuiDataGrid-cell": {
                border: "none",
                padding: "8px 16px",
              },
              "& .MuiDataGrid-columnHeaders": {
                border: "none",
                backgroundColor: "rgba(132, 204, 22, 0.1)",
                borderRadius: "4px",
                position: "sticky",
                top: 0,
                zIndex: 10,
              },
              "& .MuiDataGrid-root": {
                border: "none",
                width: "100%",
                maxWidth: "100%",
              },
              border: "none",
              "& .MuiDataGrid-row": {
                "&:hover": {
                  backgroundColor: "rgba(132, 204, 22, 0.05)",
                },
                cursor: "pointer",
                borderBottom: "1px solid rgba(224, 224, 224, 0.4)",
              },
              "& .MuiDataGrid-row .checkbox-cell": {
                opacity: 0,
                transition: "opacity 0.2s ease",
              },
              "& .MuiDataGrid-row:hover .checkbox-cell": {
                opacity: 1,
              },
              "& .MuiDataGrid-row .checkbox-selected": {
                opacity: "1 !important",
              },
              "& .reconciled-detail-row": {
                backgroundColor: "rgba(249, 250, 251, 0.9)",
                fontStyle: "italic",
              },
              "& .group-header-row": {
                backgroundColor: "rgba(132, 204, 22, 0.08)",
                fontWeight: "bold",
              },
            }}
            getRowClassName={(params) => {
              if (!params.row) return "";
              if (params.row.isReconciledDetail) return "reconciled-detail-row";
              if (params.row.isGroupHeader) return "group-header-row";
              const baseClass = "group";
              const isSelected = selectedTransactions.includes(
                params.row.transaction_id
              );
              return `${baseClass} ${isSelected ? "checkbox-selected" : ""}`;
            }}
            onRowClick={(params) => {
              if (!params.row) return;
              if (!params.row.isGroupHeader && !params.row.isReconciledDetail) {
                handleRowClick(params.row.transaction_id);
              }
            }}
            getRowHeight={(params) => {
              if (!params.row) return 52;
              if (params.row.isGroupHeader) return 56;
              if (params.row.isReconciledDetail) return 40;
              return 52;
            }}
          />
        </div>

        {showHidden && hiddenTransactions.length > 0 && (
          <div className="mt-6 w-full">
            <div className="flex items-center mb-3">
              <h3 className="text-lg font-semibold text-gray-800">
                Hidden Transactions
              </h3>
              <span className="ml-2 text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                {hiddenTransactions.length}
              </span>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
              <DataGrid
                rows={hiddenTransactions}
                // disableColumnResize
                columns={hiddenColumns}
                hideFooter
                autoHeight
                disableRowSelectionOnClick
                getRowId={(row) => row.id}
                sx={{
                  width: "100%",
                  minHeight: 300,
                  "& .MuiDataGrid-root": {
                    border: "none",
                    fontFamily: "inherit",
                  },
                  "& .MuiDataGrid-main": {
                    border: "none",
                  },
                  "& .MuiDataGrid-cell": {
                    border: "none",
                    padding: "12px 16px",
                    fontSize: "14px",
                    "&:focus": {
                      outline: "none",
                    },
                  },
                  "& .MuiDataGrid-columnHeaders": {
                    border: "none",
                    backgroundColor: "rgba(105, 226, 13, 0.1)",
                    borderBottom: "2px solid rgba(105, 226, 13, 0.3)",
                  },
                  "& .lime-header": {
                    backgroundColor: "rgba(105, 226, 13, 0.1)",
                    fontWeight: "600",
                    fontSize: "14px",
                    color: "#374151",
                  },
                  "& .MuiDataGrid-row": {
                    "&:hover": {
                      backgroundColor: "rgba(105, 226, 13, 0.05)",
                    },
                    "&:nth-of-type(even)": {
                      backgroundColor: "#fafafa",
                    },
                  },
                  "& .MuiDataGrid-virtualScroller": {
                    overflow: "auto",
                  },
                  "& .MuiDataGrid-footerContainer": {
                    display: "none",
                  },
                }}
              />
            </div>
          </div>
        )}
      </div>
      {/* Pagination and Show Hidden button container */}
      <div className="flex justify-between items-center mt-4">
        {/* Show Hidden button on the left */}
        <div className="flex justify-start">
          <Tooltip
            title={
              showHidden
                ? "Hide hidden transactions"
                : "Show hidden transactions"
            }
          >
            <button
              onClick={() => setShowHidden((prev) => !prev)}
              className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center transition-colors duration-200 shadow-sm border border-gray-200"
            >
              {showHidden ? (
                <VisibilityOff fontSize="small" />
              ) : (
                <Visibility fontSize="small" />
              )}
              <span className="ml-2 text-sm font-medium">
                {showHidden ? "Hide" : "Show"} Hidden (
                {hiddenTransactions?.length || 0})
              </span>
            </button>
          </Tooltip>
        </div>

        {/* Pagination on the right */}
        <div className="flex justify-end">
          <CustomPagination />
        </div>
      </div>

      {/* Transaction Details Modal */}
      {selectedTransaction && (
        <TransactionDetailsModal
          closeTransactionModal={closeTransactionModal}
          selectedTransaction={selectedTransaction}
        />
      )}

      {/* Receipt Upload/Edit Modal */}
      <ParsedReceiptDisplayModal
        closeReceiptModal={closeReceiptModal}
        selectedTransaction={selectedTransaction}
        selectedFile={selectedFile}
        setSelectedFile={setSelectedFile}
      />
    </div>
  );
};

export default TransactionPage;
