import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  investments: [],
  performanceMetrics: null,
  investmentAccounts: [],
  portfolioDiversity: null,
  portfolioOptimization: null,
  loading: false,
  error: null,
  success: false,
  message: ''
};

export const investmentSlice = createSlice({
  name: 'mock_investments',
  initialState,
  reducers: {
    // Request actions
    fetchInvestmentsRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchPerformanceRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchInvestmentAccountsRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchPortfolioDiversityRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchPortfolioOptimizationRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    // syncPlaidInvestmentsRequest: (state) => {
    //   state.loading = true;
    //   state.error = null;
    // },
    
    // Success actions
    fetchInvestmentsSuccess: (state, action) => {
      state.loading = false;
      state.investments = action.payload;
      state.success = true;
    },
    fetchPerformanceSuccess: (state, action) => {
      state.loading = false;
      state.performanceMetrics = action.payload;
      state.success = true;
    },
    fetchInvestmentAccountsSuccess: (state, action) => {
      state.loading = false;
      state.investmentAccounts = action.payload;
      state.success = true;
    },
    fetchPortfolioDiversitySuccess: (state, action) => {
      state.loading = false;
      state.portfolioDiversity = action.payload;
      state.success = true;
    },
    fetchPortfolioOptimizationSuccess: (state, action) => {
      state.loading = false;
      state.portfolioOptimization = action.payload;
      state.success = true;
    },
    // syncPlaidInvestmentsSuccess: (state, action) => {
    //   state.loading = false;
    //   state.success = true;
    //   state.message = 'Investment accounts synced successfully';
    // },
    
    // Failure actions
    investmentActionFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
      state.success = false;
    },
    
    // Reset states
    resetInvestmentStatus: (state) => {
      state.loading = false;
      state.success = false;
      state.error = null;
      state.message = '';
    }
  }
});

export const {
  fetchInvestmentsRequest,
  fetchPerformanceRequest,
  fetchInvestmentAccountsRequest,
  fetchPortfolioDiversityRequest,
  fetchPortfolioOptimizationRequest,
  // syncPlaidInvestmentsRequest,
  fetchInvestmentsSuccess,
  fetchPerformanceSuccess,
  fetchInvestmentAccountsSuccess,
  fetchPortfolioDiversitySuccess,
  fetchPortfolioOptimizationSuccess,
  // syncPlaidInvestmentsSuccess,
  investmentActionFailure,
  resetInvestmentStatus
} = investmentSlice.actions;

export default investmentSlice.reducer;