import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

// Stat Card Component
const StatCard = ({ label, value, change, darkMode, icon, currentTheme }) => {
  const isPositive = change >= 0;

  const getCardClasses = () => {
    let classes = `backdrop-blur-sm border transition-all duration-300 min-w-0 overflow-hidden
      ${currentTheme.layout?.animations !== false ? 'hover:shadow-2xl hover:scale-105' : ''}
      flex flex-col justify-between h-full`;
    if (currentTheme.layout?.cardStyle === 'flat') {
      classes += ` shadow-none`;
    } else if (currentTheme.layout?.cardStyle === 'outlined') {
      classes += ` shadow-none border-2`;
    } else {
      classes += ` shadow-lg`;
    }
    return classes;
  };

  const getCardStyle = () => {
    const style = {
      backgroundColor: darkMode ? currentTheme.colors.darkCardBg : currentTheme.colors.cardBg,
      borderColor: darkMode ? currentTheme.colors.darkBorder : currentTheme.colors.border,
      borderRadius:
        currentTheme.layout?.borderRadius === 'square'
          ? '6px'
          : currentTheme.layout?.borderRadius === 'pill'
          ? '24px'
          : '12px',
    };
    if (currentTheme.layout?.gradients !== false) {
      style.background = darkMode
        ? `linear-gradient(135deg, ${currentTheme.colors.darkCardBg}, ${currentTheme.colors.darkCardBg}dd)`
        : `linear-gradient(135deg, ${currentTheme.colors.cardBg}, ${currentTheme.colors.primary}05)`;
    }
    if (currentTheme.layout?.shadows === false) {
      style.boxShadow = 'none';
    }
    if (currentTheme.layout?.backdropBlur === false) {
      style.backdropFilter = 'none';
    }
    return style;
  };

  return (
    <div className={getCardClasses() + " p-4 sm:p-5"} style={getCardStyle()}>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0 mb-2">
        <div
          className="p-2 flex-shrink-0"
          style={{
            backgroundColor: `${currentTheme.colors.primary}20`,
            borderRadius:
              currentTheme.layout?.borderRadius === 'square'
                ? '4px'
                : currentTheme.layout?.borderRadius === 'pill'
                ? '50%'
                : '8px',
          }}
        >
          <FontAwesomeIcon icon={icon} className="text-lg" style={{ color: currentTheme.colors.primary }} />
        </div>
        <div
          className="flex items-center space-x-1 px-2 py-1 text-xs font-medium"
          style={{
            color: isPositive ? currentTheme.colors.primary : '#ef4444',
            backgroundColor: isPositive
              ? darkMode
                ? `${currentTheme.colors.primary}20`
                : `${currentTheme.colors.primary}10`
              : darkMode
              ? 'rgba(239, 68, 68, 0.2)'
              : 'rgba(239, 68, 68, 0.1)',
            borderRadius:
              currentTheme.layout?.borderRadius === 'square'
                ? '4px'
                : currentTheme.layout?.borderRadius === 'pill'
                ? '50px'
                : '8px',
          }}
        >
          <FontAwesomeIcon icon={isPositive ? "arrow-up" : "arrow-down"} className="text-xs" />
          <span>{Math.abs(change).toFixed(1)}%</span>
        </div>
      </div>
      <div
        className="text-xl sm:text-2xl font-bold truncate mb-1"
        style={{ color: darkMode ? currentTheme.colors.darkText : currentTheme.colors.text }}
      >
        ${value.toLocaleString(undefined, { maximumFractionDigits: 0 })}
      </div>
      <div
        className="text-sm"
        style={{ color: darkMode ? currentTheme.colors.darkText + '80' : currentTheme.colors.text + '80' }}
      >
        {label}
      </div>
    </div>
  );
};

export default StatCard;