import Cookies from 'js-cookie';
import { jwtDecode } from 'jwt-decode';

export const TOKEN_COOKIE_NAME = 'pennypal_jwt_token';
export const USER_ID_COOKIE_NAME = 'pennypal_user_id';

export const getAuthToken = () => {
  return Cookies.get(TOKEN_COOKIE_NAME);
};

export const setUserIdCookie = (userId) => {
  Cookies.set(USER_ID_COOKIE_NAME, userId.toString(), { 
    expires: 7,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict'
  });
};

export const getCurrentUserId = () => {
  const userId = Cookies.get(USER_ID_COOKIE_NAME);
  if (userId) return userId;

  const token = getAuthToken();
  if (!token) return 0;
  
  try {
    const decodedToken = jwtDecode(token);
    const userId = decodedToken.userId;
    setUserIdCookie(userId);
    // return decodedToken.userId || 0;
    return userId;
  } catch (error) {
    console.error('Error decoding JWT token:', error);
    return 0;
  }
};