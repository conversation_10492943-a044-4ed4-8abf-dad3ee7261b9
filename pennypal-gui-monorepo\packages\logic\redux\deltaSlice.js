// deltaSlice.js
import { createSlice, createAction } from '@reduxjs/toolkit';

// Actions for epics - Account-level delta data
export const fetchDeltaData = createAction('delta/fetchDeltaData');
export const fetchDeltaDataSuccess = createAction('delta/fetchDeltaDataSuccess');
export const fetchDeltaDataFailure = createAction('delta/fetchDeltaDataFailure');

// Actions for epics - Account Type-level delta data
export const fetchAccountTypeDeltaData = createAction('delta/fetchAccountTypeDeltaData');
export const fetchAccountTypeDeltaDataSuccess = createAction('delta/fetchAccountTypeDeltaDataSuccess');
export const fetchAccountTypeDeltaDataFailure = createAction('delta/fetchAccountTypeDeltaDataFailure');

const initialState = {
  deltaData: {
    comparisonType: null,
    accounts: [],
    accountTypes: [],
    metadata: null,
    summary: {
      totalCurrentBalance: 0,
      totalPastBalance: 0,
      totalDeltaAmount: 0,
      totalDeltaPercentage: 0,
      overallTrend: 'neutral',
      accountCount: 0
    },
    accountTypes : '',
    lastUpdated: null
  },
  loading: false,
  error: null,
  selectedTimePeriod: null,
  dataType: 'account', 
  // UI enhancement flags
  uiState: {
    showDeltaIndicators: false,
    deltaTransition: 'idle', // 'loading', 'transitioning', 'idle'
    highlightTrends: true,
    compactView: false,
    groupByQuarter: false // For quarterly rolling view
  }
};

const deltaSlice = createSlice({
  name: 'delta',
  initialState,
  reducers: {
    setSelectedTimePeriod: (state, action) => {
      state.selectedTimePeriod = action.payload;
      if (state.deltaData.accounts.length > 0) {
        state.loading = true;
        state.uiState.deltaTransition = 'loading';
      }
    },
    setDataType: (state, action) => {
      state.dataType = action.payload;
      state.uiState.deltaTransition = 'loading';
    },
    toggleDeltaIndicators: (state) => {
      state.uiState.showDeltaIndicators = !state.uiState.showDeltaIndicators;
    },
    
    toggleCompactView: (state) => {
      state.uiState.compactView = !state.uiState.compactView;
    },
    
    toggleGroupByQuarter: (state) => {
      state.uiState.groupByQuarter = !state.uiState.groupByQuarter;
    },
    
    setDeltaTransition: (state, action) => {
      state.uiState.deltaTransition = action.payload;
    },
    
    clearDeltaData: (state) => {
      state.deltaData = initialState.deltaData;
      state.error = null;
      state.uiState.deltaTransition = 'idle';
    },
    fetchAccountTypeDeltaDataSuccess: (state, action) => {
      // Merge accountTypes and metadata, keep accounts
      state.deltaData = {
        ...state.deltaData,
        ...action.payload,
        accounts: state.deltaData.accounts // preserve accounts!
      };
      state.loading = false;
      state.error = null;
    },
  },
  
  extraReducers: (builder) => {
    builder
      .addCase(fetchDeltaData, (state, action) => {
        state.loading = true;
        state.error = null;
        state.selectedTimePeriod = action.payload.timePeriod;
        state.dataType = 'account';
        state.uiState.deltaTransition = 'loading';
      })
      
      .addCase(fetchDeltaDataSuccess, (state, action) => {
        state.loading = false;
        state.deltaData = {
          ...state.deltaData,
          ...action.payload,
          accountTypes: [] // Clear account type data when loading account data
        };
        state.error = null;
        
        // Update UI state
        state.uiState = {
          ...state.uiState,
          deltaTransition: 'idle',
          showDeltaIndicators: action.payload.accounts.length > 0,
          groupByQuarter: action.payload.comparisonType === 'quarterly-rolling'
        };
      })
      
      .addCase(fetchDeltaDataFailure, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.deltaData = initialState.deltaData;
        
        state.uiState = {
          ...state.uiState,
          deltaTransition: 'idle',
          showDeltaIndicators: false,
          groupByQuarter: false
        };
      })
      
      // Account type aggregated delta data handlers
      .addCase(fetchAccountTypeDeltaData, (state, action) => {
        state.loading = true;
        state.error = null;
        state.selectedTimePeriod = action.payload.timePeriod;
        state.dataType = 'account-type';
        state.uiState.deltaTransition = 'loading';
      })
      .addCase(fetchAccountTypeDeltaDataSuccess, (state, action) => {
        // Merge accountTypes and metadata, keep accounts
        state.deltaData = {
          ...state.deltaData,
          ...action.payload,
          accounts: state.deltaData.accounts // preserve accounts!
        };
        state.loading = false;
        state.error = null;
      })
      .addCase(fetchAccountTypeDeltaDataFailure, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.deltaData = initialState.deltaData;
        
        state.uiState = {
          ...state.uiState,
          deltaTransition: 'idle',
          showDeltaIndicators: false,
          groupByQuarter: false
        };
      });
  }
});

// Export actions
export const {
  setSelectedTimePeriod,
  setDataType,
  toggleDeltaIndicators,
  toggleCompactView,
  toggleGroupByQuarter,
  setDeltaTransition,
  clearDeltaData
} = deltaSlice.actions;

// Basic selectors
export const selectDeltaData = (state) => state.delta.deltaData;
export const selectDeltaLoading = (state) => state.delta.loading;
export const selectDeltaError = (state) => state.delta.error;
export const selectDeltaUIState = (state) => state.delta.uiState;
export const selectDeltaSummary = (state) => state.delta.deltaData.summary;
export const selectDeltaAccounts = (state) => state.delta.deltaData.accounts;
export const selectDeltaAccountTypes = (state) =>
  state.delta?.deltaData?.accountTypes || [];
export const selectSelectedTimePeriod = (state) => state.delta.selectedTimePeriod;
export const selectDataType = (state) => state.delta.dataType;

// Advanced selectors
export const selectAccountsByTrend = (state) => {
  const accounts = state.delta.deltaData.accounts;
  return {
    increasing: accounts.filter(acc => acc.trend === 'increase'),
    decreasing: accounts.filter(acc => acc.trend === 'decrease'),
    neutral: accounts.filter(acc => acc.trend === 'neutral')
  };
};

export const selectTopPerformers = (state, limit = 5) => {
  const accounts = state.delta.deltaData.accounts;
  return [...accounts]
    .sort((a, b) => b.deltaPercentage - a.deltaPercentage)
    .slice(0, limit);
};

export const selectBottomPerformers = (state, limit = 5) => {
  const accounts = state.delta.deltaData.accounts;
  return [...accounts]
    .sort((a, b) => a.deltaPercentage - b.deltaPercentage)
    .slice(0, limit);
};

export const selectAccountTypesByTrend = (state) => {
  const accountTypes = state.delta.deltaData.accountTypes;
  return {
    increasing: accountTypes.filter(acc => acc.trend === 'increase'),
    decreasing: accountTypes.filter(acc => acc.trend === 'decrease'),
    neutral: accountTypes.filter(acc => acc.trend === 'neutral')
  };
};

export const selectTopPerformingAccountTypes = (state, limit = 5) => {
  const accountTypes = state.delta.deltaData.accountTypes;
  return [...accountTypes]
    .sort((a, b) => b.deltaPercentage - a.deltaPercentage)
    .slice(0, limit);
};

// New selectors for quarterly rolling data
export const selectQuarterlyAccountsGrouped = (state) => {
  const accounts = state.delta.deltaData.accounts;
  const comparisonType = state.delta.deltaData.comparisonType;
  
  if (comparisonType !== 'quarterly-rolling') {
    return {};
  }
  
  return accounts.reduce((grouped, account) => {
    const quarter = account.quarter || 'Unknown';
    if (!grouped[quarter]) {
      grouped[quarter] = [];
    }
    grouped[quarter].push(account);
    return grouped;
  }, {});
};

export const selectQuarterlyAccountTypesGrouped = (state) => {
  const accountTypes = state.delta.deltaData.accountTypes;
  const comparisonType = state.delta.deltaData.comparisonType;
  
  if (comparisonType !== 'quarterly-rolling') {
    return {};
  }
  
  return accountTypes.reduce((grouped, accountType) => {
    const quarter = accountType.quarter || 'Unknown';
    if (!grouped[quarter]) {
      grouped[quarter] = [];
    }
    grouped[quarter].push(accountType);
    return grouped;
  }, {});
};

export const selectYTDMetadata = (state) => {
  const metadata = state.delta.deltaData.metadata;
  if (state.delta.selectedTimePeriod === 'ytd' && metadata) {
    return {
      ytdDays: metadata.ytdDays,
      currentPeriodDate: metadata.currentPeriodDate,
      pastPeriodDate: metadata.pastPeriodDate,
      description: metadata.description
    };
  }
  return null;
};

export const selectMonthlyMetadata = (state) => {
  const metadata = state.delta.deltaData.metadata;
  if (state.delta.selectedTimePeriod === 'one-month' && metadata) {
    return {
      currentPeriodDate: metadata.currentPeriodDate,
      pastPeriodDate: metadata.pastPeriodDate,
      description: metadata.description
    };
  }
  return null;
};

// Selector for account performance ranking
export const selectAccountPerformanceRanking = (state) => {
  const accounts = state.delta.deltaData.accounts;
  
  return accounts
    .map((account, index) => ({
      ...account,
      rank: index + 1
    }))
    .sort((a, b) => b.deltaPercentage - a.deltaPercentage)
    .map((account, index) => ({
      ...account,
      performanceRank: index + 1
    }));
};

// Selector for filtering accounts by balance threshold
export const selectAccountsByBalanceThreshold = (state, minBalance = 0) => {
  const accounts = state.delta.deltaData.accounts;
  return accounts.filter(account => {
    const currentBalance = account.currentBalance || account.currentQuarterBalance || 0;
    return currentBalance >= minBalance;
  });
};

// Selector for account type distribution
export const selectAccountTypeDistribution = (state) => {
  const accounts = state.delta.deltaData.accounts;
  
  return accounts.reduce((distribution, account) => {
    const type = account.accountType || 'unknown';
    if (!distribution[type]) {
      distribution[type] = {
        count: 0,
        totalCurrentBalance: 0,
        totalDeltaAmount: 0,
        accounts: []
      };
    }
    
    distribution[type].count++;
    distribution[type].totalCurrentBalance += (account.currentBalance || account.currentQuarterBalance || 0);
    distribution[type].totalDeltaAmount += (account.deltaAmount || 0);
    distribution[type].accounts.push(account);
    
    return distribution;
  }, {});
};

// Utility selector for time period display names
export const selectTimePeriodDisplayName = (state) => {
  const timePeriod = state.delta.selectedTimePeriod;
  const displayNames = {
    'one-month': '1 Month',
    'three-month': '3 Months',
    'half-year': '6 Months',
    'yearly': '1 Year',
    'ytd': 'Year to Date',
    'quarterly-rolling': 'Quarterly Rolling'
  };
  
  return displayNames[timePeriod] || timePeriod;
};

// Selector: Get account type deltas for the selected quarter (for quarterly-rolling)
export const selectCurrentQuarterAccountTypes = (state) => {
  const { deltaData } = state.delta;
  const accountTypes = deltaData.accountTypes;

  if (!Array.isArray(accountTypes) || accountTypes.length === 0) return [];

  // Only for quarterly-rolling
  if (deltaData.comparisonType === 'quarterly-rolling') {
    // Group by quarter and get their end dates
    const quarterGroups = {};
    accountTypes.forEach(at => {
      if (!quarterGroups[at.quarter]) quarterGroups[at.quarter] = [];
      quarterGroups[at.quarter].push(at);
    });

    // Find the latest fully completed quarter (end date < today)
    const today = new Date();
    let latestQuarter = null;
    let latestEndDate = null;

    Object.values(quarterGroups).forEach(group => {
      // Assume all items in group have the same currentPeriodDate
      const period = group[0].currentPeriodDate;
      if (period) {
        const endStr = period.split(' to ')[1];
        const endDate = new Date(endStr);
        if (endDate < today && (!latestEndDate || endDate > latestEndDate)) {
          latestEndDate = endDate;
          latestQuarter = group[0].quarter;
        }
      }
    });

    if (latestQuarter) {
      return accountTypes.filter(at => at.quarter === latestQuarter);
    }
    // Fallback: return empty
    return [];
  }

  // Non-quarterly: just return all
  return accountTypes;
};

export default deltaSlice.reducer;