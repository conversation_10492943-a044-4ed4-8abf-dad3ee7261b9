import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import { 
  ArrowRight, 
  Shield, 
  // futures,
  Smartphone, 
  TrendingUp, 
  Target, 
  CreditCard, 
  BarChart3, 
  Users, 
  Star,
  Check,
  Play,
  DollarSign,
  PieChart,
  Calendar,
  Repeat,
  Eye,
  Zap,
  Wallet,
  ChevronDown // Add ChevronDown for dropdown indicator
} from 'lucide-react';


/*
import moneyImage from '../../../assets/money-image.jpg'; // Adjust the path as needed
import moneyImage1 from '../../../assets/money-image1.jpg'; // Adjust the path as needed
import moneyImage2 from '../../../assets/money-image2.jpg'; // Adjust the path as needed
import moneyImage3 from '../../../assets/money-image3.jpg'; // Adjust the path as needed
import ai from '../../../assets/ai.jpg'; // Adjust the path as needed
import Apps from '../../../assets/Apps.jpg'; // Adjust the path as needed
import budget from '../../../assets/budget.jpg'; // Adjust the path as needed
import Investments from '../../../assets/Investments.jpg'; // Adjust the path as needed
import networth from '../../../assets/networth.jpg'; // Adjust the path as needed
import Security from '../../../assets/Security.jpg'; // Adjust the path as needed
import goals from '../../../assets/goals.png'; // Adjust the path as needed
import accounts from '../../../assets/accounts.png'; // Adjust the path as needed
import budgets from '../../../assets/budget.png'; // Adjust the path as needed
import Transactions from '../../../assets/transactions.png'; // Adjust the path as needed
import networths from '../../../assets/networth.png'; // Adjust the path as needed
import fin from '../../../assets/fin.gif'; // Adjust the path as needed
import budget1 from '../../../assets/budget1.png'; // Adjust the path as needed
import income from '../../../assets/income.gif'; // Adjust the path as needed
import gif1 from '../../../assets/gif1.mp4'; // Adjust the path as needed
import gif2 from '../../../assets/gif2.mp4'; // Adjust the path as needed
import gif3 from '../../../assets/gif3.mp4'; // Adjust the path as needed
import gif4 from '../../../assets/gif4.mp4'; // Adjust the path as needed
import gif5 from '../../../assets/gif5.mp4'; // Adjust the path as needed
import gif6 from '../../../assets/gif.1.mp4'; // Adjust the path as needed
import gif7 from '../../../assets/gif.2.mp4'; // Adjust the path as needed
import gif8 from '../../../assets/gif.3.mp4'; // Adjust the path as needed
import gif9 from '../../../assets/gif.4.mp4'; // Adjust the path as needed
import gif10 from '../../../assets/gif.5.mp4'; // Adjust the path as needed
import gif11 from '../../../assets/gif.6.mp4'; // Adjust the path as needed
import gif12 from '../../../assets/gif.8.mp4'; // Adjust the path as needed
import gif13 from '../../../assets/gif.7.mp4'; // Adjust the path as needed 
import gif14 from '../../../assets/gif.9.mp4'; // Adjust the path as needed */

import gif14 from '../../../assets/fin.gif'; // Adjust the path as needed 
import pennypal from '../../../assets/logo_mini.png';


const Pennypal = ({ onClose }) => {
  // const [activeFeature, setActiveFeature] = useState(0);
  const [isVisible, setIsVisible] = useState({});
  const [openDropdown, setOpenDropdown] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          setIsVisible(prev => ({ ...prev, [entry.target.id]: true }));
        }
      });
    }, { threshold: 0.1 });

    document.querySelectorAll('[id^="section-"]').forEach((el) => {
      observer.observe(el);
    });

    return () => observer.disconnect();
  }, []);

  // Dropdown data for each navigation item
  const dropdownItems = {
    features: [
      { name: 'Net Worth Tracking', href: '#net-worth' },
      { name: 'Transaction Management', href: '#transactions' },
      { name: 'Subscription Tracking', href: '#subscriptions' },
      { name: 'Custom Reports', href: '#reports' },
      { name: 'Goal Setting', href: '#goals' },
      { name: 'Multi-Platform', href: '#multi-platform' },
    ],
    pricing: [
      { name: 'Pricing Plans', href: '#pricing-plans' },
      { name: 'Free Trial', href: '#free-trial' },
      { name: 'Compare Plans', href: '#compare-plans' },
    ],
    blog: [
      { name: 'Latest Posts', href: '#latest-posts' },
      { name: 'Financial Tips', href: '#financial-tips' },
      { name: 'User Stories', href: '#user-stories' },
    ],
    support: [
      { name: 'Help Center', href: '#help-center' },
      { name: 'Contact Us', href: '#contact' },
      { name: 'Community', href: '#community' },
      { name: 'FAQs', href: '#faqs' },
    ],
  };

  // Handle dropdown toggle
  const handleMouseEnter = (dropdown) => {
    setOpenDropdown(dropdown);
  };

  const handleMouseLeave = () => {
    setOpenDropdown(null);
  };

  // Function to open login page in a new window
  const handleLoginClick = () => {
    navigate('/'); // Navigate to root, where SignIn is shown
    onClose(); // Close Pennypal page (sets showPennyPalHomepage to false)
  };

  // Handle Get Started button click (optional: navigate to SignIn with signup form open)
  const handleGetStartedClick = () => {
    navigate('/'); // Navigate to root
    onClose();
    // Optionally, you can pass a prop to SignIn to show the signup form
  };

  const features = [
    {
      icon: <Eye className="w-8 h-8" />,
      title: "Net Worth Tracking",
      description: "All your accounts, in one place. Connect all your bank accounts, credit cards, loans, real estate, and investments to see your entire financial picture.",
      color: "from-blue-500 to-cyan-500"
    },
    {
      icon: <CreditCard className="w-8 h-8" />,
      title: "Transaction Management",
      description: "Every transaction in one clean, searchable list. Mark transactions as reviewed to stay on top of your spending and spot anything unexpected.",
      color: "from-purple-500 to-pink-500"
    },
    {
      icon: <Repeat className="w-8 h-8" />,
      title: "Subscription Tracking",
      description: "Automatically detect recurring subscriptions like streaming services, gyms, or app memberships. Never lose track of what you're paying for.",
      color: "from-green-500 to-emerald-500"
    },
    {
      icon: <BarChart3 className="w-8 h-8" />,
      title: "Custom Reports",
      description: "Visualize the flow of money with customizable charts. Track spending trends, income, or net worth over time with actionable insights.",
      color: "from-orange-500 to-red-500"
    },
    {
      icon: <Target className="w-8 h-8" />,
      title: "Goal Setting",
      description: "Create clear plans to save for what matters. Set targets, track progress, and stay motivated as you watch your savings grow.",
      color: "from-indigo-500 to-purple-500"
    },
    {
      icon: <Smartphone className="w-8 h-8" />,
      title: "Multi-Platform",
      description: "Works seamlessly across web, iOS, and Android. Check your finances anytime, anywhere with perfect synchronization.",
      color: "from-pink-500 to-rose-500"
    },
  // {
  //   icon: <Wallet className="w-8 h-8" />,
  //   title: "Budget Planning",
  //   description: "Easily create and manage budgets tailored to your needs. Stay on track with your spending and savings goals effortlessly.",
  //   color: "from-teal-500 to-blue-500"
  // },
  // {
  //   icon: <Zap className="w-8 h-8" />,
  //   title: "AI-Powered Insights",
  //   description: "Leverage AI to gain smart insights into your finances. Get personalized recommendations to optimize your spending and saving habits.",
  //   color: "from-yellow-500 to-orange-500"
  // }
];
  const testimonials = [
    {
      text: "I love everything about this app. Building a budget is easy, tracking spending is awesome, and everything I wanted in an app this can do! So easy and flexible.",
      source: "App Store Review",
      rating: 5
    },
    {
      text: "Because of this app, I feel like I am not winging it financially anymore and feel on top of my spending.",
      source: "App Store Review",
      rating: 5
    },
    {
      text: "This app does it all. It combines extremely granular monthly budgeting functionality with holistic, high-level wealth management tools better than any app out there.",
      source: "App Store Review",
      rating: 5
    },
    {
      text: "Finally a financial dashboard that allows for intuitive customization but also has a powerful set of pre-built functionality!",
      source: "App Store Review",
      rating: 5
    },
    {
      text: "Mint shutting down was a blessing in disguise. Pennypal is way better and the connections to institutions way more reliable than Mint.",
      source: "App Store Review",
      rating: 5
    },
    {
      text: "I have tried and used many budgeting apps — Mint, copilot, rocket money— and Pennypal is by far the best one. I love the customization and monthly overviews.",
      source: "App Store Review",
      rating: 5
    }
  ];

  const benefits = [
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Bank-Level Security",
      description: "Your data is protected with 256-bit encryption"
    },
    {
      icon: <Zap className="w-6 h-6" />,
      title: "Best-in-Class Connectivity",
      description: "Connect to 13,000+ financial institutions"
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: "Collaboration Built-In",
      description: "Share with your partner or advisor at no extra cost"
    },
    {
      icon: <PieChart className="w-6 h-6" />,
      title: "Automatic Categorization",
      description: "Smart AI categorizes your transactions automatically"
    }
  ];

  // Updated Key Features section with text, images, and new content
 const keyFeatures = [
  { 
    name: "Net Worth", 
    icon: <Eye className="w-5 h-5 text-[#8bc34a]" />, 
    description: "See your entire financial picture in one place by connecting bank accounts, credit cards, loans, and investments for a unified view. Monitor your assets and liabilities effortlessly with a clear overview. Stay updated with real-time syncing to ensure your net worth is always accurate. This feature helps you make informed financial decisions with confidence.", 
    // image: gif10,
    //       isVideo: true // Flag to indicate this is a video
 
  },
  { 
    name: "Transactions", 
    icon: <CreditCard className="w-5 h-5 text-[#8bc34a]" />, 
    description: "Track every transaction in a clean, searchable list, mark them as reviewed, and easily spot unexpected expenses to stay in control. Review your spending habits with detailed categorization. Get updated instantly with new transactions as they occur, ensuring you never miss a beat. Take control of your finances with ease and precision.", 
    // image: gif11,
    // isVideo: true // Flag to indicate this is a video

  },
  { 
    name: "Recurring", 
    icon: <Repeat className="w-5 h-5 text-[#8bc34a]" />, 
    description: "Never miss a subscription payment again with automatic detection of recurring charges like streaming services, gyms, or app memberships. Keep track of all your regular expenses in one place. Receive updated alerts to manage your subscriptions effectively and avoid unnecessary costs. Save money by identifying and canceling unused subscriptions.", 
    // image: gif8,
    //     isVideo: true // Flag to indicate this is a video
 
  },
  { 
    name: "Budget", 
    icon: <Wallet className="w-5 h-5 text-[#8bc34a]" />, 
    description: "Create and manage budgets tailored to your needs, ensuring you stay on track with spending and savings goals effortlessly. Set limits for different categories to control your expenses. Stay updated with budget progress notifications to meet your targets without stress. Achieve financial discipline with a clear plan for your money.", 
    // image: gif13,
    //   isVideo: true // Flag to indicate this is a video
 
  },
  { 
    name: "Goals", 
    icon: <Target className="w-5 h-5 text-[#8bc34a]" />, 
    description: "Set savings targets, track your progress, and stay motivated with clear plans to achieve what matters most to you. Whether it’s a vacation or a new car, plan with purpose. Get updated regularly on your goal milestones to keep pushing forward toward success. Celebrate every step as you get closer to your dreams.", 
    // image: goals 
  },
  { 
    name: "Reports", 
    icon: <BarChart3 className="w-5 h-5 text-[#8bc34a]" />, 
    description: "Visualize your finances with custom charts to track spending trends, income, or net worth over time with actionable insights. Analyze your financial patterns to make better decisions. Charts are updated automatically to reflect your latest financial data for accuracy. Gain a deeper understanding of your money with powerful reporting tools.", 
    // image: gif9,
    //         isVideo: true // Flag to indicate this is a video

  },
  { 
    name: "AI Insights", 
    icon: <Zap className="w-5 h-5 text-[#8bc34a]" />, 
    description: "Get personalized financial recommendations powered by AI to optimize your spending, saving, and investment habits effectively. Discover new ways to improve your financial health. Insights are updated daily to reflect your financial behavior and goals accurately. Make smarter decisions with cutting-edge technology at your fingertips.", 
    // image: ai 
  }
];
  // { 
  //   name: "Apps", 
  //   icon: <Smartphone className="w-5 h-5 text-[#8bc34a]" />, 
  //   description: "Access Pennypal on web, iOS, and Android, ensuring seamless synchronization to manage your finances anytime, anywhere. Enjoy a consistent experience across all your devices. Stay updated with the latest app features across all platforms to enhance your experience. Manage your money on the go with convenience and reliability.", 
  //   image: gif7 ,
  //               isVideo: true // Flag to indicate this is a video

  // },
  // { 
  //   name: "Investments", 
  //   icon: <TrendingUp className="w-5 h-5 text-[#8bc34a]" />, 
  //   description: "Track your investments alongside other finances, monitoring performance and growth to make informed decisions with ease. Keep an eye on your portfolio’s progress over time. Get updated with real-time market data to optimize your investment strategy effectively. Grow your wealth with insights tailored to your financial goals.", 
  //   image: Investments 
  // },
  // { 
  //   name: "Security", 
  //   icon: <Shield className="w-5 h-5 text-[#8bc34a]" />, 
  //   description: "Your data is protected with bank-level 256-bit encryption, ensuring your financial information stays secure and private. Trust in a platform designed with your safety in mind. Security protocols are updated regularly to safeguard your information against threats. Focus on your finances with peace of mind knowing your data is secure.", 
  //   image: Security 
  // },
  

  return (
    <div className="min-h-screen bg-white relative">
      {/* Header */}
      <header className="bg-white/95 backdrop-blur-lg border-b border-gray-100 sticky top-0 z-50">
        <div className=" mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-[#8bc34a] to-[#c5e1a5] rounded-xl flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-white" />
              </div>
              <h1 className="text-2xl font-bold text-gray-900">
                Pennypal
              </h1>
            </div>
            <nav className="hidden md:flex items-center space-x-5 justify-end">
              {/* Features Dropdown */}
              <div 
                className="relative"
                onMouseEnter={() => handleMouseEnter('features')}
                onMouseLeave={handleMouseLeave}
              >
                <a 
                  href="#features" 
                  className="text-gray-600 hover:text-[#8bc34a] transition-colors font-medium flex items-center space-x-1"
                >
                  <span>Features</span>
                  <ChevronDown 
                    className={`w-4 h-4 transition-transform duration-200 ${openDropdown === 'features' ? 'rotate-180' : ''}`} 
                  />
                </a>
                {openDropdown === 'features' && (
                  <div className="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 z-50">
                    {dropdownItems.features.map((item, index) => (
                      <a
                        key={index}
                        href={item.href}
                        className="block px-4 py-2 text-gray-700 hover:bg-green-50 hover:text-[#8bc34a] transition-colors"
                      >
                        {item.name}
                      </a>
                    ))}
                  </div>
                )}
              </div>

              {/* Pricing Dropdown */}
              <div 
                className="relative"
                onMouseEnter={() => handleMouseEnter('pricing')}
                onMouseLeave={handleMouseLeave}
              >
                <a 
                  href="#pricing" 
                  className="text-gray-600 hover:text-[#8bc34a] transition-colors font-medium flex items-center space-x-1"
                >
                  <span>Pricing</span>
                  <ChevronDown 
                    className={`w-4 h-4 transition-transform duration-200 ${openDropdown === 'pricing' ? 'rotate-180' : ''}`} 
                  />
                </a>
                {openDropdown === 'pricing' && (
                  <div className="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 z-50">
                    {dropdownItems.pricing.map((item, index) => (
                      <a
                        key={index}
                        href={item.href}
                        className="block px-4 py-2 text-gray-700 hover:bg-green-50 hover:text-[#8bc34a] transition-colors"
                      >
                        {item.name}
                      </a>
                    ))}
                  </div>
                )}
              </div>

              {/* Blog Dropdown */}
              <div 
                className="relative"
                onMouseEnter={() => handleMouseEnter('blog')}
                onMouseLeave={handleMouseLeave}
              >
                <a 
                  href="#blog" 
                  className="text-gray-600 hover:text-[#8bc34a] transition-colors font-medium flex items-center space-x-1"
                >
                  <span>Blog</span>
                  <ChevronDown 
                    className={`w-4 h-4 transition-transform duration-200 ${openDropdown === 'blog' ? 'rotate-180' : ''}`} 
                  />
                </a>
                {openDropdown === 'blog' && (
                  <div className="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 z-50">
                    {dropdownItems.blog.map((item, index) => (
                      <a
                        key={index}
                        href={item.href}
                        className="block px-4 py-2 text-gray-700 hover:bg-green-50 hover:text-[#8bc34a] transition-colors"
                      >
                        {item.name}
                      </a>
                    ))}
                  </div>
                )}
              </div>

              {/* Support Dropdown */}
              <div 
                className="relative"
                onMouseEnter={() => handleMouseEnter('support')}
                onMouseLeave={handleMouseLeave}
              >
                <a 
                  href="#support" 
                  className="text-gray-600 hover:text-[#8bc34a] transition-colors font-medium flex items-center space-x-1"
                >
                  <span>Support</span>
                  <ChevronDown 
                    className={`w-4 h-4 transition-transform duration-200 ${openDropdown === 'support' ? 'rotate-180' : ''}`} 
                  />
                </a>
                {openDropdown === 'support' && (
                  <div className="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 z-50">
                    {dropdownItems.support.map((item, index) => (
                      <a
                        key={index}
                        href={item.href}
                        className="block px-4 py-2 text-gray-700 hover:bg-green-50 hover:text-[#8bc34a] transition-colors"
                      >
                        {item.name}
                      </a>
                    ))}
                  </div>
                )}
              </div>

              <button 
                onClick={handleLoginClick}
                className="border-2 border-gray-300 text-gray-700 px-6 py-2 rounded-full font-semibold hover:border-[#8bc34a] hover:text-[#8bc34a] transition-all duration-300"
              >
                Login
              </button>
              <button
                onClick={handleGetStartedClick}
                className="text-white bg-[#8bc34a] px-6 py-2 rounded-full font-semibold hover:shadow-lg transition-all"
                // style={{ backgroundColor: '#c5e1a5' }}
              >
                Get Started
              </button>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="pt-20 pb-32 bg-gradient-to-br from-green-50 to-green-50">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center max-w-4xl mx-auto">
            <h2 className="text-6xl md:text-7xl font-bold mb-8 leading-tight">
              <span className="text-gray-900">The modern way to</span>
              <br />
              <span className="bg-gradient-to-r from-[#8bc34a] via-green-200 to-[#8bc34a] bg-clip-text text-transparent">
                manage money
              </span>
            </h2>
            <p className="text-xl md:text-2xl text-gray-600 mb-12 leading-relaxed">
              Pennypal will change the way you organize your financial life. 
              Track, budget, plan, and do more with your money - together.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
              <button className="bg-gradient-to-r from-[#8bc34a] to-[#8bc34a] text-white px-8 py-4 rounded-full text-lg font-semibold hover:shadow-xl hover:scale-105 transition-all duration-300 flex items-center space-x-2">
                <span>Get Started Free</span>
                <ArrowRight className="w-5 h-5" />
              </button>
              <button className="border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-full text-lg font-semibold hover:border-[#8bc34a] hover:text-[#8bc34a]  transition-all duration-300 flex items-center space-x-2">
                <Play className="w-5 h-5" />
                <span>Watch Demo</span>
              </button>
            </div>
            <p className="text-gray-500 mt-6">7-day free trial • No credit card required</p>
          </div>
        </div>
      </section>

      {/* What is Pennypal Section */}
      <section id="section-what" className={`py-20 transform transition-all duration-1000 ${isVisible['section-what'] ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h3 className="text-5xl font-bold text-gray-900 mb-6">
              Your home base for money clarity
            </h3>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Pennypal simplifies finances by bringing all your accounts together into one clear view. 
              Always know where your money is and where it's going, achieve your goals quicker, and 
              collaborate with your partner or professional at no extra cost.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <div 
                key={index}
                className="bg-gradient-to-r from-green-50 to-green-50 rounded-2xl p-8 hover:shadow-lg hover:scale-105 transition-all duration-itev300 border border-green-200"
              >
                <div className="bg-[#8bc34a] w-14 h-14 rounded-full flex items-center justify-center mb-6 text-white">
                  {benefit.icon}
                </div>
                <h4 className="text-xl font-bold text-gray-900 mb-3">{benefit.title}</h4>
                <p className="text-gray-600 leading-relaxed">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Updated Key Features Section */}
<section id="section-key-features" className={`py-20 bg-white transform transition-all duration-1000 ${isVisible['section-key-features'] ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
  <div className="ml-50 mr-50 px-6">
    <div className="text-center mb-16">
      <h3 className="text-5xl font-bold text-gray-900 mb-6">
        Key Features at a Glance
      </h3>
      <p className="text-xl text-gray-600 mb-12">
        Everything you need to manage your finances, all in one place.
      </p>
    </div>
    <div className="space-y-8">
      {keyFeatures.map((feature, index) => (
        <div
          key={index}
          className=" p-6 grid grid-cols-1 md:grid-cols-2 gap-6 items-center"
        >
          {/* Text Section */}
          <div className={`flex flex-col space-y-3 ${index % 2 === 0 ? '' : 'md:order-last'}`}>
            <div className="flex items-center space-x-3">
              {feature.icon}
              <h4 className="text-3xl font-bold text-gray-900">{feature.name}</h4>
            </div>
            <p className="text-2xl text-gray-600 leading-relaxed">{feature.description}</p>
          </div>
          {/* Image Section */}
          <div className={`flex justify-center ${index % 2 === 0 ? 'md:justify-end' : 'md:order-first md:justify-start'}`}>
            {feature.isVideo ? (
                    <video
                      src={feature.image}
                      autoPlay
                      loop
                      muted
                      playsInline
                      className="w-full max-w-md h-auto "
                      alt={`${feature.name} video`}
                    />
                  ) : (
                    <img
                      src={feature.image}
                      alt={`${feature.name} illustration`}
                      className="w-full max-w-md h-auto rounded-lg object-cover shadow-md"
                    />
                  )}
          </div>
        </div>
      ))}
    </div>
  </div>
</section>
      {/* Features Section */}
      <section id="section-features" className={`py-20 transition-all duration-1000 ${isVisible['section-features'] ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
        <div className="max-w-8xl mx-auto px-6">
          <div className="text-center mb-16">
            <h3 className="text-5xl font-bold text-gray-900 mb-6">
              Everything you need, all in one app
            </h3>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Connect your accounts and Pennypal will do the heavy lifting to categorize your finances. 
              From there, you can track, budget, collaborate, and set goals specific to you.
            </p>
          </div>

    <style>
      {`
        @keyframes scaleIn {
          from {
            transform: scale(0.8);
          }
          to {
            transform: scale(1);
          }
        }
        .animate-scale-in {
          animation: scaleIn 500ms ease-in-out forwards;
        }
      `}
    </style>

    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
      {/* Left Column: First four features */}
      <div className="space-y-8">
        {features.slice(0, 3).map((feature, index) => (
          <div 
            key={index}
            className={`bg-white rounded-3xl p-8 hover:shadow-xl transition-all duration-500 cursor-pointer `}
            // onClick={() => setActiveFeature(index)}
          >
            <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${feature.color} flex items-center justify-center mb-6 text-white`}>
              {feature.icon}
            </div>
            <h4 className="text-2xl font-bold text-gray-900 mb-4">{feature.title}</h4>
            <p className="text-gray-600 leading-relaxed mb-6">{feature.description}</p>
            <div className="flex items-center text-[#8bc34a] font-semibold">
              <span>Learn more</span>
              <ArrowRight className="w-5 h-5 ml-2" />
            </div>
          </div>
        ))}
      </div>

        {/* Center Column: Video Slideshow */}
      {/* <div className="flex flex-col items-center">
        {(() => {
          const videos = [
            { src: gif14, alt: "Financial overview video 1" },
            { src: gif2, alt: "Financial overview video 2" },
            { src: gif3, alt: "Financial overview video 3" },
            { src: gif4, alt: "Financial overview video 4" },
            { src: gif5, alt: "Financial overview video 5" }
          ];
          const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
          const [isAutoPlaying, setIsAutoPlaying] = useState(true);

          useEffect(() => {
            let interval;
            if (isAutoPlaying) {
              interval = setInterval(() => {
                setCurrentVideoIndex((prevIndex) => (prevIndex + 1) % videos.length);
              }, 4000); // Change video every 4 seconds (adjusted for video playback)
            }

            return () => clearInterval(interval); // Clean up interval on unmount or when isAutoPlaying changes
          }, [isAutoPlaying]);

          const handlePrevious = () => {
            setIsAutoPlaying(false); // Pause autoplay on manual navigation
            setCurrentVideoIndex((prevIndex) => (prevIndex - 1 + videos.length) % videos.length);
          };

          const handleNext = () => {
            setIsAutoPlaying(false); // Pause autoplay on manual navigation
            setCurrentVideoIndex((prevIndex) => (prevIndex + 1) % videos.length);
          };

          return (
            <div className="flex flex-col items-center">
              <video
                key={currentVideoIndex} 
                src={videos[currentVideoIndex].src}
                autoPlay
                loop
                muted
                playsInline
                className="w-[900px] h-[600px] rounded-2xl shadow-lg object-cover transition-opacity duration-500 ease-in-out animate-scale-in"
                style={{ opacity: 1 }}
                onLoadedData={(e) => e.target.style.opacity = 1}
                onError={(e) => e.target.style.opacity = 0}
                aria-label={videos[currentVideoIndex].alt}
              />
          
            </div>
          );
        })()}
      </div> */}
<div className="flex flex-col items-center">
        <video
          src={gif14}
          autoPlay
          loop
          muted
          playsInline
          className="w-[900px] h-[600px] rounded-2xl  object-cover transition-opacity duration-500 ease-in-out animate-scale-in"
          style={{ opacity: 1 }}
          onLoadedData={(e) => e.target.style.opacity = 1}
          onError={(e) => e.target.style.opacity = 0}
          aria-label="Financial overview video"
        />
      </div>
      {/* Right Column: Last four features */}
      <div className="space-y-8">
        {features.slice(3, 6).map((feature, index) => (
          <div 
            key={index + 3}
            className={`bg-white rounded-3xl p-8 hover:shadow-xl transition-all duration-500 cursor-pointer  `}
            // onClick={() => setActiveFeature(index + 3)}
          >
            <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${feature.color} flex items-center justify-center mb-6 text-white`}>
              {feature.icon}
            </div>
            <h4 className="text-2xl font-bold text-gray-900 mb-4">{feature.title}</h4>
            <p className="text-gray-600 leading-relaxed mb-6">{feature.description}</p>
            <div className="flex items-center text-[#8bc34a] font-semibold">
              <span>Learn more</span>
              <ArrowRight className="w-5 h-5 ml-2" />
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
</section>



      {/* Testimonials Section */}
      <section id="section-testimonials" className={`py-20 bg-gradient-to-r from-green-50 to-green-50 transform transition-all duration-1000 ${isVisible['section-testimonials'] ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h3 className="text-5xl font-bold text-gray-900 mb-6">
              What our members say
            </h3>
            <div className="flex items-center justify-center space-x-1 mb-4">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
              ))}
            </div>
            <p className="text-xl text-gray-600">Rated 4.8/5 on the App Store</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div 
                key={index}
                className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100"
              >
                <div className="flex items-center space-x-1 mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-700 leading-relaxed mb-4 italic">
                  "{testimonial.text}"
                </p>
                <p className="text-[#8bc34a] font-semibold text-sm">{testimonial.source}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Recognition Section */}
      {/* <section className="py-20 bg-gradient-to-r from-green-200 to-green-100 text-gray-900">
        <div className="max-w-6xl mx-auto px-6 text-center">
          <h3 className="text-4xl font-bold mb-12">Recognized by leading publications</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
              <p className="text-lg leading-relaxed italic mb-4">
                "Pennypal is the best budgeting app overall for 2024. It's the best for anyone 
                looking to cut expenses and increase savings—especially couples or families."
              </p>
              <p className="font-semibold">Financial Expert Review</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
              <p className="text-lg leading-relaxed italic mb-4">
                "Pennypal has become one of the most hyped-up Mint alternatives among Reddit users. 
                It provides everything you need to manage your personal finances."
              </p>
              <p className="font-semibold">Tech Publication</p>
            </div>
          </div>
        </div>
      </section> */}

      {/* Community Section */}
      <section id="section-community" className={`py-20 bg-white transform transition-all duration-1000 ${isVisible['section-community'] ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
        <div className="max-w-6xl mx-auto px-6">
          <div className="text-center mb-16">
            <h3 className="text-4xl font-bold text-gray-900 mb-6">Community resources</h3>
            <p className="text-xl text-gray-600">Connect with other Pennypal users and get expert insights</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="bg-gradient-to-r from-orange-500 to-red-500 w-16 h-16 rounded-full flex items-center justify-center mb-6">
                <Users className="w-8 h-8 text-white" />
              </div>
              <h4 className="text-2xl font-bold text-gray-900 mb-4">Reddit Community</h4>
              <p className="text-gray-600 mb-6 leading-relaxed">
                Join our growing community of 30,000+ Redditors. Ask questions and share feedback 
                on what we're building.
              </p>
              <button className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-6 py-3 rounded-full font-semibold hover:shadow-lg transition-all">
                r/pennypal
              </button>
            </div>

            <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="bg-gradient-to-r from-blue-500 to-cyan-500 w-16 h-16 rounded-full flex items-center justify-center mb-6">
                <BarChart3 className="w-8 h-8 text-white" />
              </div>
              <h4 className="text-2xl font-bold text-gray-900 mb-4">Our Blog</h4>
              <p className="text-gray-600 mb-6 leading-relaxed">
                We share expert insights and data-backed research that helps you with your money 
                questions whether big or small.
              </p>
              <button className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-6 py-3 rounded-full font-semibold hover:shadow-lg transition-all">
                Read Now
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="section-pricing" className={`py-20 transform transition-all duration-1000 ${isVisible['section-pricing'] ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h3 className="text-5xl font-bold text-gray-900 mb-6">
            Try Pennypal free for 7 days
          </h3>
          <p className="text-xl text-gray-600 mb-12 max-w-2xl mx-auto">
            See and manage your finances all in one place. No ads and best-in-class connectivity. 
            Collaborate with your partner or advisor at no extra cost.
          </p>

          <div className=" bg-gradient-to-br from-green-50 via-white to-green-50 rounded-3xl p-12 border-2 border-purple-100">
            <div className="mb-8">
              <div className="text-6xl font-bold bg-gradient-to-r from-[#8bc34a] to-[#8bc34a] bg-clip-text text-transparent mb-2">
                $14.99<span className="text-2xl text-gray-600">/month</span>
              </div>
              <p className="text-gray-600">Billed annually at $179.88/year</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10 text-left">
              {[
                "Connect unlimited accounts",
                "Automatic transaction categorization",
                "Custom budgets and goals",
                "Investment tracking",
                "Collaboration features",
                "Mobile apps (iOS & Android)",
                "Premium customer support",
                "Advanced reporting & analytics"
              ].map((feature, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                  <span className="text-gray-700">{feature}</span>
                </div>
              ))}
            </div>

            <button className="bg-gradient-to-r from-[#8bc34a] to-[#8bc34a] text-white px-12 py-4 rounded-full text-lg font-semibold hover:shadow-xl hover:scale-105 transition-all duration-300 mb-4">
              Start 7-Day Free Trial
            </button>
            <p className="text-gray-500 text-sm">No credit card required • Cancel anytime</p>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-gray-900 to-gray-800 text-white">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h3 className="text-4xl font-bold mb-6">
            Ready to take control of your finances?
          </h3>
          <p className="text-xl mb-10 opacity-90">
            Join thousands of users who have transformed their financial lives with Pennypal.
          </p>
          <button className="bg-gradient-to-r from-[#8bc34a] to-[#8bc34a] text-white px-10 py-4 rounded-full text-lg font-semibold hover:shadow-xl hover:scale-105 transition-all duration-300">
            Get Started Today
          </button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-r from-[#8bc34a] to-[#c5e1a5] rounded-xl flex items-center justify-center">
                  <DollarSign className="w-6 h-6 text-white" />
                </div>
                <span className="text-xl font-bold">Pennypal</span>
              </div>
              <p className="text-gray-400 leading-relaxed">
                The modern way to manage your money. Track, budget, plan, and achieve your financial goals.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold mb-6">Product</h4>
              <ul className="space-y-3 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Features</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Pricing</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Security</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Mobile Apps</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-6">Company</h4>
              <ul className="space-y-3 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">About</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Blog</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Careers</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Press</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-6">Support</h4>
              <ul className="space-y-3 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Help Center</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Community</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Privacy Policy</a></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400">
            <p>© 2025 Pennypal. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Pennypal;