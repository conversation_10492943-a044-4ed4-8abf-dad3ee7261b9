// import { createSlice } from '@reduxjs/toolkit';

// const initialState = {
//   view: 'monthly',
//   currentMonth: new Date().getMonth(),
//   currentYear: new Date().getFullYear(),
//   budgetData: [],
//   categories: [],
//   subcategories: [],
//   loading: true,
//   error: null,
//   networkError: null,
//   expandedCategories: {},
//   showZeroBudget: {},
//   showAllTables: true,
//   editingSubcategory: null,
//   editedBudget: { key: null, value: 0 },
//   lastCategoriesFetch: null,
//   lastSubcategoriesFetch: null,
// };

// // Helper Functions
// export const removeDuplicateSubcategoryNames = (data) => {
//   if (!Array.isArray(data)) {
//     console.warn('removeDuplicateSubcategoryNames received non-array data:', data);
//     return [];
//   }
  
//   return data.map((budgetDto) => {
//     const subs = budgetDto.category?.subCategories || [];
//     const seen = new Set();
//     const uniqueSubs = [];
//     for (const sub of subs) {
//       const name = (sub.subCategory || sub.customSubCategory || "").trim().toLowerCase();
//       if (!seen.has(name)) {
//         seen.add(name);
//         uniqueSubs.push(sub);
//       }
//     }
//     return {
//       ...budgetDto,
//       category: {
//         ...budgetDto.category,
//         subCategories: uniqueSubs,
//       },
//     };
//   });
// };

// export const formatCurrency = (value) => {
//   const numberValue = Number(value) || 0;
//   return numberValue.toLocaleString("en-US", {
//     minimumFractionDigits: 2,
//     maximumFractionDigits: 2,
//   });
// };

// export const getProgress = (spent, budget) => {
//   if (!budget || budget <= 0) return 0;
//   return Math.round((spent / budget) * 100);
// };

// export const calculateTotals = (budgetData) => {
//   let totalBudget = 0;
//   let totalActual = 0;
//   let totalRemaining = 0;

//   budgetData.forEach((category) => {
//     totalBudget += category.category?.allocated || 0;
//     totalActual += category.category?.actual || 0;
//     totalRemaining += category.category?.remaining || 0;

//     // Subcategories totals
//     category.category?.subCategories?.forEach((subcategory) => {
//       totalBudget += subcategory.budget || 0;
//       totalActual += subcategory.spent || 0;
//       totalRemaining += subcategory.remaining || 0;
//     });
//   });
//   return { totalBudget, totalActual, totalRemaining };
// };

// export const calculateCategoryTotal = (category, type) => {
//   if (!category) return 0;
  
//   let total = category.category?.[type] || 0;
  
//   // Add subcategory values
//   category.category?.subCategories?.forEach((subcategory) => {
//     if (type === 'actual') {
//       total += subcategory.spent || 0;
//     } else if (type === 'allocated') {
//       total += subcategory.budget || 0;
//     } else if (type === 'remaining') {
//       total += subcategory.remaining || 0;
//     }
//   });
  
//   return total;
// };

// export const prepareBudgetData = (data) => {
//   console.log("Inside prepareBudgetData");
//   console.log(data);
//   if (!Array.isArray(data)) {
//     console.warn('prepareBudgetData received non-array data:', data);
//     return [];
//   }
  
//   // First remove duplicates
//   const noDupSubs = removeDuplicateSubcategoryNames(data);
  
//   return noDupSubs.map((budgetDto) => {
//     // Ensure the category exists and trim its icon key
//     if (!budgetDto.category) {
//       budgetDto.category = {
//         category: "Other",
//         categoryIconKey: (budgetDto.icon || "FaMiscellaneous").trim(),
//         allocated: 0,
//         actual: 0,
//         remaining: 0,
//         subCategories: []
//       };
//     } else {
//       if (budgetDto.category.categoryIconKey && typeof budgetDto.category.categoryIconKey === "string") {
//         budgetDto.category.categoryIconKey = budgetDto.category.categoryIconKey.trim();
//       } else {
//         budgetDto.category.categoryIconKey = "FaMiscellaneous";
//       }
//     }

//     // Process subCategories and trim the icon key
//     if (Array.isArray(budgetDto.category?.subCategories)) {
//       budgetDto.category.subCategories = budgetDto.category.subCategories.map(
//         (subCategory, index) => {
//           if (!subCategory.subCategory) {
//             subCategory.subCategory = subCategory.customSubCategory;
//           }
//           // Use the subcategory icon key if provided and valid
//           if (subCategory.iconKey && typeof subCategory.iconKey === "string" && subCategory.iconKey.trim()) {
//             subCategory.iconKey = subCategory.iconKey.trim();
//           } else {
//             // If missing, fallback to the category's icon key if available
//             subCategory.iconKey = budgetDto.category?.categoryIconKey
//               ? budgetDto.category.categoryIconKey.trim()
//               : "FaMiscellaneous";
//           }
//           subCategory.icon_key = subCategory.iconKey;
//           // Create a compositeKey using the subcategory id and index
//           subCategory.compositeKey = `${subCategory.id}-${index}`;
//           // For custom subcategories, if id is 0, fall back to the parent budget's id
//           if ((!subCategory.id || subCategory.id === 0) && budgetDto.id) {
//             subCategory.id = budgetDto.id;
//           }
//           return subCategory;
//         }
//       );
//     }
//     return budgetDto;
//   });
// };

// const budgetSlice = createSlice({
//   name: 'budget',
//   initialState,
//   reducers: {
//     setBudgetData: (state, action) => {
//       state.budgetData = action.payload;
//       state.loading = false;
//       state.error = null;
//       state.networkError = null;
//     },
//     changeView: (state, action) => {
//       state.view = action.payload;
//     },
//     changeMonth: (state, action) => {
//       state.currentMonth += action.payload;
//       if (state.currentMonth < 0) {
//         state.currentMonth = 11;
//         state.currentYear -= 1;
//       } else if (state.currentMonth > 11) {
//         state.currentMonth = 0;
//         state.currentYear += 1;
//       }
//       state.loading = true;
//       state.error = null;
//       state.networkError = null;
//     },
//     setToToday: (state) => {
//       const today = new Date();
//       state.currentMonth = today.getMonth();
//       state.currentYear = today.getFullYear();
//       state.loading = true;
//       state.error = null;
//       state.networkError = null;
//     },
//     changeYear: (state, action) => {
//       state.currentYear += action.payload;
//       state.loading = true;
//       state.error = null;
//       state.networkError = null;
//     },
//     toggleAllTables: (state) => {
//       state.showAllTables = !state.showAllTables;
//     },
//     toggleCategory: (state, action) => {
//       const categoryId = action.payload;
//       state.expandedCategories = {
//         ...state.expandedCategories,
//         [categoryId]: !state.expandedCategories[categoryId]
//       };
//     },
//     toggleZeroBudget: (state, action) => {
//       const categoryId = action.payload;
//       state.showZeroBudget = {
//         ...state.showZeroBudget,
//         [categoryId]: !state.showZeroBudget[categoryId]
//       };
//     },
//     setEditingSubcategory: (state, action) => {
//       state.editingSubcategory = action.payload;
//     },
//     setEditedBudget: (state, action) => {
//       state.editedBudget = action.payload;
//     },
//     addNewCategory: (state, action) => {
//       state.budgetData.push(action.payload);
//     },
//     addNewSubcategory: (state, action) => {
//       const { categoryId, newSubcategory } = action.payload;
//       const category = state.budgetData.find(cat => cat.id === categoryId);
//       if (category && category.category) {
//         category.category.subCategories.push(newSubcategory);
//       }
//     },
//   addBudget: (state, action) => {
//       console.log('Adding budget to store:', action.payload);
      
//       // Find the category in the budget data using the correct categoryId
//       const categoryIndex = state.budgetData.findIndex(
//         cat => cat.id === action.payload.categoryId || cat.category?.id === action.payload.categoryId
//       );

//       console.log('Looking for category ID:', action.payload.categoryId);
//       console.log('Available categories:', state.budgetData.map(cat => ({ 
//         id: cat.id, 
//         categoryId: cat.category?.id,
//         name: cat.category?.category || cat.category?.name 
//       })));

//       if (categoryIndex >= 0) {
//         // Create the new subcategory object with all necessary properties
//         const newSubCategory = {
//           id: action.payload.customSubCategoryId || action.payload.id, // Use the custom subcategory ID from response
//           sub_category_id: action.payload.customSubCategoryId || action.payload.id,
//           budgetId: action.payload.id, // This is the budget table ID
//           subCategory: action.payload.customSubCategory,
//           customSubCategory: action.payload.customSubCategory,
//           budget: action.payload.allocated,
//           allocated: action.payload.allocated,
//           spent: action.payload.actual || 0,
//           actual: action.payload.actual || 0,
//           remaining: action.payload.allocated - (action.payload.actual || 0),
//           iconKey: action.payload.icon || action.payload.iconKey || "FaMiscellaneous",
//           icon_key: action.payload.icon || action.payload.iconKey || "FaMiscellaneous",
//           isRollover: action.payload.isRollover || false,
//           isExcluded: action.payload.isExcluded || false,
//           isDynamic: action.payload.isDynamic || false,
//           dynamicAllocated: action.payload.dynamicAllocated || 0,
//           compositeKey: `${action.payload.id}-${state.budgetData[categoryIndex].category.subCategories.length}`
//         };

//         console.log('New subcategory object:', newSubCategory);

//         // Ensure subCategories array exists
//         if (!state.budgetData[categoryIndex].category.subCategories) {
//           state.budgetData[categoryIndex].category.subCategories = [];
//         }

//         // Add the new subcategory to the existing category
//         state.budgetData[categoryIndex].category.subCategories.push(newSubCategory);
        
//         console.log('Updated category subcategories:', state.budgetData[categoryIndex].category.subCategories);
//       } else {
//         console.error('Category not found for ID:', action.payload.categoryId);
//         console.log('Available categories:', state.budgetData.map(cat => ({ 
//           id: cat.id, 
//           categoryId: cat.category?.id, 
//           name: cat.category?.category || cat.category?.name 
//         })));
//       }

//       // Clear any errors
//       state.error = null;
//       state.networkError = null;
//       state.loading = false;
//     },

//     updateBudget: (state, action) => {
//       const { categoryId, subcategoryId, newBudget } = action.payload;
      
//       const categoryIndex = state.budgetData.findIndex(
//         cat => cat.id === categoryId
//       );

//       if (categoryIndex >= 0) {
//         const subCategoryIndex = state.budgetData[categoryIndex].category.subCategories.findIndex(
//           sub => sub.id === subcategoryId || sub.compositeKey === subcategoryId
//         );

//         if (subCategoryIndex >= 0) {
//           const subcategory = state.budgetData[categoryIndex].category.subCategories[subCategoryIndex];
//           subcategory.budget = newBudget;
//           subcategory.remaining = newBudget - (subcategory.spent || 0);
//         }
//       }
      
//       // Reset editing state
//       state.editingSubcategory = null;
//       state.error = null;
//       state.networkError = null;
//     },
//     // New optimistic update actions for better UX
//     updateBudgetOptimistic: (state, action) => {
//       const { categoryId, subcategoryId, newBudget, originalBudget } = action.payload;
      
//       const categoryIndex = state.budgetData.findIndex(
//         cat => cat.id === categoryId
//       );

//       if (categoryIndex >= 0) {
//         const subCategoryIndex = state.budgetData[categoryIndex].category.subCategories.findIndex(
//           sub => sub.id === subcategoryId || sub.compositeKey === subcategoryId
//         );

//         if (subCategoryIndex >= 0) {
//           const subcategory = state.budgetData[categoryIndex].category.subCategories[subCategoryIndex];
//           subcategory.budget = newBudget;
//           subcategory.remaining = newBudget - (subcategory.spent || 0);
//           // Store original for potential rollback
//           subcategory._originalBudget = originalBudget;
//         }
//       }
//     },
//     revertBudgetOptimistic: (state, action) => {
//       const { categoryId, subcategoryId, originalBudget } = action.payload;
      
//       const categoryIndex = state.budgetData.findIndex(
//         cat => cat.id === categoryId
//       );

//       if (categoryIndex >= 0) {
//         const subCategoryIndex = state.budgetData[categoryIndex].category.subCategories.findIndex(
//           sub => sub.id === subcategoryId || sub.compositeKey === subcategoryId
//         );

//         if (subCategoryIndex >= 0) {
//           const subcategory = state.budgetData[categoryIndex].category.subCategories[subCategoryIndex];
//           subcategory.budget = originalBudget;
//           subcategory.remaining = originalBudget - (subcategory.spent || 0);
//           delete subcategory._originalBudget;
//         }
//       }
//     },
//     deleteSubcategory: (state, action) => {
//       const { categoryId, subcategoryId } = action.payload;
//       const category = state.budgetData.find(cat => cat.id === categoryId);
//       if (category && category.category) {
//         category.category.subCategories = category.category.subCategories.filter(
//           sub => sub.id !== subcategoryId && sub.compositeKey !== subcategoryId
//         );
//       }
//       state.error = null;
//       state.networkError = null;
//     },
//     setLoading: (state, action) => {
//       state.loading = action.payload;
//       if (action.payload) {
//         state.error = null;
//         state.networkError = null;
//       }
//     },
//     setError: (state, action) => {
//       state.error = action.payload;
//       state.loading = false;
//       state.networkError = null;
//     },
//     setNetworkError: (state, action) => {
//       state.networkError = action.payload;
//       state.error = null;
//       state.loading = false;
//     },
//     clearError: (state) => {
//       state.error = null;
//       state.networkError = null;
//     },
//     setCategoriesData: (state, action) => {
//       console.log('💾 Setting categories data:', action.payload);
//       state.categories = action.payload;
//       state.lastCategoriesFetch = Date.now();
//     },
//     setSubcategoriesData: (state, action) => {
//       console.log('💾 Setting subcategories data:', action.payload);
//       state.subcategories = action.payload;
//       state.lastSubcategoriesFetch = Date.now();
//     },
//     // New action for refreshing data (used by epics)
//     refreshBudgetData: (state) => {
//       state.loading = true;
//       state.error = null;
//       state.networkError = null;
//     },
//     // Bulk operations support
//     bulkUpdateBudgets: (state, action) => {
//       // This will be handled by the epic, just set loading state
//       state.loading = true;
//       state.error = null;
//       state.networkError = null;
//     },
//     // Actions for epic dispatching (these don't modify state directly)
//     saveBudget: (state, action) => {
//       // This action is handled by epic, no state changes needed here
//       // But we can set loading state for the specific item being updated
//       state.error = null;
//       state.networkError = null;
//     },
//     addBudgetItem: (state, action) => {
//       // This action is handled by epic, no state changes needed here
//       state.error = null;
//       state.networkError = null;
//     },
//     deleteSubcategoryBudget: (state, action) => {
//       // This action is handled by epic, no state changes needed here
//       state.error = null;
//       state.networkError = null;
//     },
//   },
// });

// export const {
//   setBudgetData,
//   changeView,
//   changeMonth,
//   setToToday,
//   changeYear,
//   toggleAllTables,
//   toggleCategory,
//   toggleZeroBudget,
//   setEditingSubcategory,
//   setEditedBudget,
//   addNewCategory,
//   addNewSubcategory,
//   addBudget,
//   updateBudget,
//   updateBudgetOptimistic,
//   revertBudgetOptimistic,
//   deleteSubcategory,
//   setLoading,
//   setError,
//   setNetworkError,
//   clearError,
//   setCategoriesData,
//   setSubcategoriesData,
//   refreshBudgetData,
//   bulkUpdateBudgets,
//   saveBudget,
//   addBudgetItem,
//   deleteSubcategoryBudget,
// } = budgetSlice.actions;

// export default budgetSlice.reducer;
import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  currentMonth: new Date().getMonth(),
  currentYear: new Date().getFullYear(),
  budgetData: [],
  categories: [],
  subcategories: [],
  loading: false,
  error: null,
  networkError: null,
  expandedCategories: {},
  showZeroBudget: {},
  showAllTables: true,
  editingSubcategory: null,
  editedBudget: { key: null, value: "0" },
};

const createDefaultExpandedState = (budgetData) =>
  budgetData.reduce((acc, category) => {
    acc[category.categoryId] = true;
    return acc;
  }, {});

export const formatCurrency = (value) => {
  const numberValue = Number(value) || 0;
  return numberValue.toLocaleString("en-US", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
};

export const getProgress = (spent, budget) =>
  !budget || budget <= 0 ? 0 : Math.round((spent / budget) * 100);

export const calculateTotals = (budgetData) => {
  let totalBudget = 0;
  let totalActual = 0;
  let totalRemaining = 0;

  budgetData.forEach((category) => {
    category.subcategories.forEach((sub) => {
      totalBudget += sub.allocated || 0;
      totalActual += sub.actual || 0;
      totalRemaining += sub.remaining || 0;
    });
  });

  return { totalBudget, totalActual, totalRemaining };
};

export const prepareBudgetData = (data) => {
  if (!Array.isArray(data)) {
    return [];
  }

  const groupedData = data.reduce((acc, item) => {
    const categoryId = item.categoryId || `cat-${item.id}`;
    if (!acc[categoryId]) {
      acc[categoryId] = {
        categoryId,
        categoryName: item.categoryName || "Other",
        icon: item.icon || "FaQuestionCircle",
        allocated: 0,
        actual: 0,
        remaining: 0,
        subcategories: [],
      };
    }

    acc[categoryId].allocated += item.allocated || 0;
    acc[categoryId].actual += item.actual || 0;
    acc[categoryId].remaining += item.remaining || 0;

    acc[categoryId].subcategories.push({
      id: item.id,
      subcategoryId: item.subcategoryId || null,
      subcategoryName: item.subcategoryName || item.customSubCategoryName || "N/A",
      customSubCategoryId: item.customSubCategoryId || null,
      customSubCategoryName: item.customSubCategoryName || null,
      allocated: item.allocated || 0,
      actual: item.actual || 0,
      remaining: item.remaining || 0,
      icon: item.icon || acc[categoryId].icon,
      compositeKey: `${item.id}-${categoryId}`,
      rollover: item.rollover || false,
    });

    return acc;
  }, {});

  return Object.values(groupedData);
};

const budgetSlice = createSlice({
  name: "budget",
  initialState,
  reducers: {
    setBudgetData: (state, action) => {
      state.budgetData = prepareBudgetData(action.payload);
      state.expandedCategories = createDefaultExpandedState(state.budgetData);
      state.loading = false;
      state.error = null;
    },
    setCategoriesData: (state, action) => {
      state.categories = action.payload || [];
      state.loading = false;
      state.error = null;
    },
    setSubcategoriesData: (state, action) => {
      state.subcategories = action.payload || [];
      state.loading = false;
      state.error = null;
    },
    changeMonth: (state, action) => {
      state.currentMonth += action.payload;
      if (state.currentMonth < 0) {
        state.currentMonth = 11;
        state.currentYear -= 1;
      } else if (state.currentMonth > 11) {
        state.currentMonth = 0;
        state.currentYear += 1;
      }
      state.loading = true;
    },
    setToToday: (state) => {
      const today = new Date();
      state.currentMonth = today.getMonth();
      state.currentYear = today.getFullYear();
      state.loading = true;
    },
    toggleAllTables: (state) => {
      state.showAllTables = !state.showAllTables;
      // state.expandedCategories = createDefaultExpandedState(state.budgetData);
    },
    toggleCategory: (state, action) => {
      state.expandedCategories[action.payload] = !state.expandedCategories[action.payload];
    },
    // APT-82 fix for expand/collapse not working
    setExpandedCategories: (state, action) => {
      state.expandedCategories = action.payload;
    },
    // End of APT-82 fix
    toggleZeroBudget: (state, action) => {
      state.showZeroBudget[action.payload] = !state.showZeroBudget[action.payload];
    },
    setEditingSubcategory: (state, action) => {
      state.editingSubcategory = action.payload;
    },
    setEditedBudget: (state, action) => {
      state.editedBudget = action.payload;
    },
    addBudget: (state, action) => {
      const {
        id,
        categoryId,
        // APT-126 fix for category/subcategory name not being displayed properly - to update redux state
        categoryName,
        subCategoryId,
        subcategoryName,
        customSubCategory,
        customSubCategoryId,
        allocated,
        icon,
        rollover,
      } = action.payload;
      let categoryIndex = state.budgetData.findIndex(
        (cat) => cat.categoryId === categoryId
      );

      // APT-126 fix for category/subcategory name not being displayed properly - to update redux state
      if (categoryIndex < 0) {
        // Create new category if it doesn't exist
        state.budgetData.push({
          categoryId,
          categoryName: categoryName || "Unknown",
          icon: icon || "FaQuestionCircle",
          allocated: 0,
          actual: 0,
          remaining: 0,
          subcategories: [],
        });
        categoryIndex = state.budgetData.length - 1;
      }

      const category = state.budgetData[categoryIndex];
      const existingSubcategoryIndex = category.subcategories.findIndex(
        (sub) =>
          (sub.subcategoryId && sub.subcategoryId === subCategoryId) ||
          (sub.customSubCategoryId && sub.customSubCategoryId === customSubCategoryId)
      );

      if (existingSubcategoryIndex >= 0) {
        // Update existing subcategory
        const subcategory = category.subcategories[existingSubcategoryIndex];
        const oldAllocated = subcategory.allocated || 0;
        subcategory.allocated = allocated || 0;
        subcategory.remaining = (allocated || 0) - (subcategory.actual || 0);
        subcategory.icon = icon || subcategory.icon || "FaQuestionCircle";
        subcategory.rollover = rollover || subcategory.rollover || false;
        category.allocated = category.allocated - oldAllocated + (allocated || 0);
        category.remaining = category.remaining - oldAllocated + (allocated || 0);
      } else {
        // End of APT-126 fix
        // Add new subcategory
        const newSubcategory = {
          id: id || `sub-${Date.now()}`,
          subcategoryId: subCategoryId || null,
          subcategoryName: subCategoryId
            ? subcategoryName
            : customSubCategory || "N/A",
          customSubCategoryId: customSubCategoryId || null,
          customSubCategoryName: customSubCategory || null,
          allocated: allocated || 0,
          actual: 0,
          remaining: allocated || 0,
          icon: icon || "FaQuestionCircle",
          compositeKey: `${id || Date.now()}-${categoryId}`,
          rollover: rollover || false,
        };
        category.subcategories.push(newSubcategory);
        category.allocated += allocated || 0;
        category.remaining += allocated || 0;
      }

      state.expandedCategories[categoryId] = true;
      state.error = null;
      state.loading = false;
    },
    updateBudget: (state, action) => {
      const { categoryId, subcategoryId, newBudget, isRollover } = action.payload;
      const categoryIndex = state.budgetData.findIndex(
        (cat) => cat.categoryId === categoryId
      );
      if (categoryIndex < 0) return;

      const subCategoryIndex = state.budgetData[categoryIndex].subcategories.findIndex(
        (sub) => sub.compositeKey === subcategoryId
      );
      if (subCategoryIndex < 0) return;

      const subcategory = state.budgetData[categoryIndex].subcategories[subCategoryIndex];
      const oldBudget = subcategory.allocated;
      subcategory.allocated = newBudget;
      subcategory.remaining = newBudget - (subcategory.actual || 0);
      state.budgetData[categoryIndex].allocated += newBudget - oldBudget;
      state.budgetData[categoryIndex].remaining += newBudget - oldBudget;
      // update rollover if it exists
      if (isRollover !== undefined) {
        subcategory.rollover = isRollover;
      }
      state.editingSubcategory = null;
      state.error = null;
    },
    deleteSubcategory: (state, action) => {
      const { categoryId, subcategoryId } = action.payload;
      const categoryIndex = state.budgetData.findIndex(
        (cat) => cat.categoryId === categoryId
      );
      if (categoryIndex < 0) return;

      const subCategoryIndex = state.budgetData[categoryIndex].subcategories.findIndex(
        (sub) => sub.compositeKey === subcategoryId
      );
      if (subCategoryIndex < 0) return;

      const subcategory = state.budgetData[categoryIndex].subcategories[subCategoryIndex];
      state.budgetData[categoryIndex].allocated -= subcategory.allocated || 0;
      state.budgetData[categoryIndex].remaining -= subcategory.remaining || 0;
      state.budgetData[categoryIndex].subcategories.splice(subCategoryIndex, 1);
      state.error = null;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
      if (action.payload) state.error = null;
    },
    setError: (state, action) => {
      state.error = action.payload;
      state.loading = false;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const {
  setBudgetData,
  setCategoriesData,
  setSubcategoriesData,
  changeMonth,
  setToToday,
  toggleAllTables,
  toggleCategory,
  setExpandedCategories,
  toggleZeroBudget,
  setEditingSubcategory,
  setEditedBudget,
  addBudget,
  updateBudget,
  deleteSubcategory,
  setLoading,
  setError,
  clearError,
} = budgetSlice.actions;

export default budgetSlice.reducer;