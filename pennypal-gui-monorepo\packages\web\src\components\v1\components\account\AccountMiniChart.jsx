// import React, { useEffect } from 'react';
// import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON><PERSON><PERSON><PERSON>, Toolt<PERSON>, ResponsiveContainer } from 'recharts';
// // import { logEvent } from '../../utils/EventLogger'; // Adjust path as needed

// const AccountMiniChart = ({ accountId, data }) => {
//   useEffect(() => {
//     // Log component load with metadata
//     // logEvent('AccountMiniChart', 'load', {
//     //   accountId,
//     //   dataPoints: data?.length || 0,
//     // });
//   }, [accountId, data]);

//   // Log if no data is available
//   if (!data || !Array.isArray(data) || data.length === 0) {
//     // logEvent('AccountMiniChart', 'no_data', { accountId });

//     return (
//       <div className="flex items-center justify-center h-full">
//         <span className="text-gray-400 text-sm">No balance data</span>
//       </div>
//     );
//   }
// let chartData = data.map(item => ({
//   date: new Date(item.groupEndDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
//   rawDate: new Date(item.groupEndDate),
//   balance: parseFloat(item.aggregatedBalance)
// }));

// if (chartData.length === 0) {
//   // No data: add 3 dummy points ending today
//   const today = new Date();
//   const prevDate1 = new Date(today);
//   prevDate1.setDate(today.getDate() - 2);

//   const prevDate2 = new Date(today);
//   prevDate2.setDate(today.getDate() - 1);

//   chartData = [
//     {
//       date: prevDate1.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
//       balance: 0
//     },
//     {
//       date: prevDate2.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
//       balance: 0
//     },
//     {
//       date: today.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
//       balance: 0
//     }
//   ];
// } else if (chartData.length === 1) {
//   const realPoint = chartData[0];
//   const realDate = realPoint.rawDate;

//   const prevDate1 = new Date(realDate);
//   prevDate1.setDate(realDate.getDate() - 2);

//   const prevDate2 = new Date(realDate);
//   prevDate2.setDate(realDate.getDate() - 1);

//   chartData = [
//     {
//       date: prevDate1.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
//       balance: 0
//     },
//     {
//       date: prevDate2.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
//       balance: 0
//     },
//     {
//       date: realDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
//       balance: realPoint.balance
//     }
//   ];
// } else if (chartData.length === 2) {
//   const firstPoint = chartData[0];
//   const firstDate = firstPoint.rawDate;

//   const prevDate = new Date(firstDate);
//   prevDate.setDate(firstDate.getDate() - 1);

//   chartData = [
//     {
//       date: prevDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
//       balance: 0
//     },
//     ...chartData.map(({ rawDate, ...rest }) => rest)
//   ];
// } else {
//   // For 3 or more points, just remove rawDate
//   chartData = chartData.map(({ rawDate, ...rest }) => rest);
// }




//   // Custom tooltip to display formatted dollar amounts
//   const CustomTooltip = ({ active, payload, label }) => {
//     if (active && payload && payload.length) {
//       // Log each tooltip activation (hover on a data point)
//       // logEvent('AccountMiniChart', 'tooltip_hover', {
//       //   accountId,
//       //   label,
//       //   value: payload[0].value
//       // });

//       return (
//         <div className="bg-white p-2 border border-gray-200 shadow-sm rounded-md">
//           <p className="text-xs font-medium">{label}</p>
//           <p className="text-xs text-blue-600">{`$${payload[0].value.toLocaleString(undefined, { 
//             minimumFractionDigits: 2, 
//             maximumFractionDigits: 2 
//           })}`}</p>
//         </div>
//       );
//     }
//     return null;
//   };

//   return (
//     <div className="h-full w-full">
//       <ResponsiveContainer width="100%" height={50}>
//         <LineChart data={chartData} margin={{ top: 5, right: 5, bottom: 5, left: 5 }}>
//           <XAxis 
//             dataKey="date"
//             tick={{ fontSize: 9 }}
//             tickLine={false}
//             axisLine={false}
//             interval="preserveStartEnd" // Show first and last labels
//           />
//           <YAxis 
//             hide 
//             domain={['dataMin - 100', 'dataMax + 100']} // Add some padding to the chart
//           />
//           <Tooltip content={<CustomTooltip />} />
//           <Line 
//             type="monotone"
//             dataKey="balance"
//             stroke="#4F46E5"
//             strokeWidth={2}
//             dot={{ r: 2, fill: "#4F46E5", stroke: "white", strokeWidth: 1 }}
//             activeDot={{ r: 4, fill: "#4F46E5", stroke: "white", strokeWidth: 2 }}
//             isAnimationActive={false} // Disable animation for better performance
//           />
//         </LineChart>
//       </ResponsiveContainer>
//     </div>
//   );
// };

// export default AccountMiniChart;
import React, { useEffect, useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowUp, faArrowDown } from '@fortawesome/free-solid-svg-icons';
// import { logEvent } from '../../utils/EventLogger'; // Adjust path as needed

const AccountMiniChart = ({ accountId, data, darkMode = false, currentTheme = { colors: { primary: '#4F46E5' } } }) => {
  const [tooltipData, setTooltipData] = useState(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    // Log component load with metadata
    // logEvent('AccountMiniChart', 'load', {
    //   accountId,
    //   dataPoints: data?.length || 0,
    // });
  }, [accountId, data]);

  // Handle data processing - REPLACED the "No balance data" return block
  let chartData;
  if (!data || !Array.isArray(data) || data.length === 0) {
    // logEvent('AccountMiniChart', 'no_data', { accountId });
    chartData = []; // Will be handled by the existing logic below
  } else {
    chartData = data.map(item => ({
      date: new Date(item.groupEndDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      rawDate: new Date(item.groupEndDate),
      balance: parseFloat(item.aggregatedBalance)
    }));
  }

  if (chartData.length === 0) {
    // No data: add 3 dummy points ending today
    const today = new Date();
    const prevDate1 = new Date(today);
    prevDate1.setDate(today.getDate() - 2);

    const prevDate2 = new Date(today);
    prevDate2.setDate(today.getDate() - 1);

    chartData = [
      {
        date: prevDate1.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        balance: 0
      },
      {
        date: prevDate2.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        balance: 0
      },
      {
        date: today.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        balance: 0
      }
    ];
  } else if (chartData.length === 1) {
    const realPoint = chartData[0];
    const realDate = realPoint.rawDate;

    const prevDate1 = new Date(realDate);
    prevDate1.setDate(realDate.getDate() - 2);

    const prevDate2 = new Date(realDate);
    prevDate2.setDate(realDate.getDate() - 1);

    chartData = [
      {
        date: prevDate1.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        balance: 0
      },
      {
        date: prevDate2.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        balance: 0
      },
      {
        date: realDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        balance: realPoint.balance
      }
    ];
  } else if (chartData.length === 2) {
    const firstPoint = chartData[0];
    const firstDate = firstPoint.rawDate;

    const prevDate = new Date(firstDate);
    prevDate.setDate(firstDate.getDate() - 1);

    chartData = [
      {
        date: prevDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        balance: 0
      },
      ...chartData.map(({ rawDate, ...rest }) => rest)
    ];
  } else {
    // For 3 or more points, just remove rawDate
    chartData = chartData.map(({ rawDate, ...rest }) => rest);
  }

  // Extract values for the chart design
  const values = chartData.map(d => d.balance);
  const maxValue = Math.max(...values);
  const minValue = Math.min(...values);
  const range = maxValue - minValue || 1;

  // Calculate trend
  const firstValue = values[0];
  const lastValue = values[values.length - 1];
  const trendUp = lastValue > firstValue;

  // Create smooth curve using cubic bezier
  const createSmoothPath = (points, closed = false) => {
    if (points.length < 2) return '';
    
    const smoothing = 0.2;
    
    // Helper function to get control points for smooth curves
    const getControlPoints = (p0, p1, p2) => {
      const dx = p2.x - p0.x;
      const dy = p2.y - p0.y;
      const d = Math.sqrt(dx * dx + dy * dy);
      const factor = smoothing * d;
      
      return {
        cp1x: p1.x - factor * (p2.x - p0.x) / d,
        cp1y: p1.y - factor * (p2.y - p0.y) / d,
        cp2x: p1.x + factor * (p2.x - p0.x) / d,
        cp2y: p1.y + factor * (p2.y - p0.y) / d
      };
    };

    let pathData = `M ${points[0].x} ${points[0].y}`;
    
    if (points.length === 2) {
      pathData += ` L ${points[1].x} ${points[1].y}`;
    } else {
      for (let i = 1; i < points.length; i++) {
        const p0 = points[i - 1] || points[i];
        const p1 = points[i];
        const p2 = points[i + 1] || points[i];
        
        if (i === 1) {
          // First curve
          const cp = getControlPoints(p0, p1, p2);
          pathData += ` Q ${cp.cp2x} ${cp.cp2y} ${p1.x} ${p1.y}`;
        } else if (i === points.length - 1) {
          // Last curve
          const cp = getControlPoints(points[i - 2], p0, p1);
          pathData += ` Q ${cp.cp2x} ${cp.cp2y} ${p1.x} ${p1.y}`;
        } else {
          // Middle curves
          const cp = getControlPoints(p0, p1, p2);
          pathData += ` Q ${cp.cp1x} ${cp.cp1y} ${p1.x} ${p1.y}`;
        }
      }
    }
    
    if (closed) {
      pathData += ` Z`;
    }
    
    return pathData;
  };

  // Create SVG path for area chart
  const createAreaPath = () => {
    const width = 100; // SVG viewBox width
    const height = 100; // SVG viewBox height
    const padding = 5;
    
    const points = values.map((value, index) => {
      const x = padding + (index / (values.length - 1)) * (width - 2 * padding);
      const y = height - padding - ((value - minValue) / range) * (height - 2 * padding);
      return { x, y };
    });

    // Create smooth line path
    const smoothLine = createSmoothPath(points);
    
    // Create area path by adding bottom points
    let areaPath = smoothLine;
    areaPath += ` L ${points[points.length - 1].x} ${height - padding}`;
    areaPath += ` L ${points[0].x} ${height - padding}`;
    areaPath += ` Z`;
    
    return { areaPath, linePath: smoothLine, points };
  };

  const { areaPath, linePath, points } = createAreaPath();

  const handlePointHover = (value, date, event) => {
    // Log each tooltip activation
    // logEvent('AccountMiniChart', 'tooltip_hover', {
    //   accountId,
    //   label: date,
    //   value: value
    // });

    const rect = event.currentTarget.getBoundingClientRect();
    setTooltipPosition({
      x: rect.left + rect.width / 2,
      y: rect.top - 10
    });
    setTooltipData({ value, date });
  };

  const handlePointLeave = () => {
    setTooltipData(null);
  };

  const chartColor = trendUp ? currentTheme.colors.primary : '#ef4444';
  const fillOpacity = darkMode ? '0.2' : '0.15';
  const strokeOpacity = darkMode ? '0.8' : '1';

  return (
    <div className="h-20 w-full relative overflow-hidden rounded-lg">
      {/* Background gradient */}
      <div className={`absolute inset-0`} style={{
        background: trendUp 
          ? (darkMode
            ? `linear-gradient(to right, ${currentTheme.colors.primary}20, ${currentTheme.colors.primary}05)`
            : `linear-gradient(to right, ${currentTheme.colors.primary}15, ${currentTheme.colors.primary}05)`)
          : (darkMode
            ? 'linear-gradient(to right, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.05))'
            : 'linear-gradient(to right, rgba(239, 68, 68, 0.15), rgba(239, 68, 68, 0.05))')
      }}></div>
             
      {/* Area Chart SVG */}
      <div className="absolute inset-0 p-1" style={{ paddingBottom: '20px' }}>
        <svg
          width="100%"
          height="100%"
          viewBox="0 0 100 100"
          preserveAspectRatio="none"
          className="overflow-visible"
        >
          {/* Area fill */}
          <path
            d={areaPath}
            fill={chartColor}
            fillOpacity={fillOpacity}
            className="transition-all duration-300"
          />
          
          {/* Line stroke */}
          <path
            d={linePath}
            fill="none"
            stroke={chartColor}
            strokeWidth="1.5"
            strokeOpacity={strokeOpacity}
            className="transition-all duration-300"
          />
          
          {/* Data points for hover interaction */}
          {points.map((point, index) => (
            <circle
              key={index}
              cx={point.x}
              cy={point.y}
              r="4"
              fill={chartColor}
              className="opacity-0 hover:opacity-100 transition-opacity duration-200 cursor-pointer"
              onMouseEnter={(e) => handlePointHover(values[index], chartData[index].date, e)}
              onMouseLeave={handlePointLeave}
            />
          ))}
        </svg>
      </div>

      {/* Date labels at bottom - Fixed positioning */}
      <div className="absolute bottom-0 left-0 right-0 px-2 pb-1">
        <div className="flex justify-between items-center h-4">
          {chartData.map((item, index) => (
            <div 
              key={index}
              className={`text-xs font-medium ${
                darkMode ? 'text-slate-400' : 'text-slate-500'
              } text-center`}
              style={{ 
                fontSize: '10px',
                flex: '1',
                minWidth: '0'
              }}
            >
              {item.date}
            </div>
          ))}
        </div>
      </div>
             
      {/* Trend indicator */}
      <div className="absolute top-2 right-2">
        <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium`}
          style={{
            backgroundColor: trendUp 
              ? (darkMode ? `${currentTheme.colors.primary}20` : `${currentTheme.colors.primary}20`)
              : (darkMode ? 'rgba(239, 68, 68, 0.2)' : 'rgba(239, 68, 68, 0.2)'),
            color: trendUp ? currentTheme.colors.primary : '#ef4444'
          }}>
          <FontAwesomeIcon 
            icon={trendUp ? faArrowUp : faArrowDown}
            className="text-xs"
          />
          {/* <span>{Math.abs(((lastValue - firstValue) / firstValue * 100) || 0).toFixed(0)}%</span> */}
        </div>
      </div>

      {/* Custom Tooltip */}
      {tooltipData && (
        <div 
          className={`fixed p-2 border shadow-sm rounded-md z-50 pointer-events-none ${
            darkMode 
              ? 'bg-slate-800 border-slate-600 text-white' 
              : 'bg-white border-gray-200'
          }`}
          style={{
            left: tooltipPosition.x,
            top: tooltipPosition.y,
            transform: 'translateX(-50%) translateY(-100%)'
          }}
        >
          <p className="text-xs font-medium">{tooltipData.date}</p>
          <p className={`text-xs font-semibold ${trendUp ? 'text-blue-600' : 'text-red-600'}`}>
            {`$${tooltipData.value.toLocaleString(undefined, { 
              minimumFractionDigits: 2, 
              maximumFractionDigits: 2 
            })}`}
          </p>
        </div>
      )}
    </div>
  );
};

export default AccountMiniChart;
