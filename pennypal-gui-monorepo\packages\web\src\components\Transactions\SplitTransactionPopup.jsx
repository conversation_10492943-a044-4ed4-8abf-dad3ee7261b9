import React, { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { logEvent } from '../../utils/EventLogger';
import { getCurrentUserId } from '../../utils/AuthUtil';
import {
  setDescription,
  setSelectedContacts,
  setSplitMethod,
  setSplitAmounts,
  setTotalSplitAmount,
  setEditableAmount,
  setInputValue,
  setActiveTab,
  setIsEditingAmount,
  resetState,
  fetchContactsRequest,
  fetchSplitTransactionsRequest,
  submitSplitRequest,
} from '../../../../logic/redux/splitTransactionSlice';
import {
  Dialog,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  IconButton,
  TextField,
  Grid,
  Autocomplete,
  Chip,
  Button,
  Divider,
  CircularProgress,
  FormControlLabel,
  Switch,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Alert,
} from '@mui/material';
import {
  Close as CloseIcon,
  CallSplit as CallSplitIcon,
  Summarize as SummarizeIcon,
  PersonAdd as PersonAddIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  MoneyOff as MoneyOffIcon,
  AttachMoney as AttachMoneyIcon,
} from '@mui/icons-material';

const SplitTransactionPopup = ({ open, onClose, transaction, onSubmitSplit ,isEmbedded}) => {
  const dispatch = useDispatch();
  const {
    description,
    selectedContacts,
    splitMethod,
    splitAmounts,
    totalSplitAmount,
    editableAmount,
    loading,
    inputValue,
    activeTab,
    isEditingAmount,
    contacts,
    splitTransactions,
    error,
    successMessage, // Added to access success feedback
  } = useSelector((state) => state.splitTransaction);

const userId = getCurrentUserId();
  const [splitMode, setSplitMode] = useState('normal'); // 'owedToMe', 'owedToOther', 'normal'
  const [editingPercentageId, setEditingPercentageId] = useState(null);
  const [tempPercentage, setTempPercentage] = useState('');
  const [hoveredContact, setHoveredContact] = useState(null);

useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => {
        onClose();
        dispatch(resetState());
      }, 3000); // Close after 3 seconds
      return () => clearTimeout(timer);
    }
  }, [successMessage, onClose, dispatch]);
  
  const calculateSplitAmounts = useCallback(() => {
    if (!transaction || selectedContacts.length === 0) return;

    const amount = Number(editableAmount) || 0;
    const numContacts = selectedContacts.length;
    const newSplitAmounts = {};

    if (splitMode === 'owedToMe') {
      const perPerson = (amount / numContacts).toFixed(2);
      selectedContacts.forEach(contact => {
        newSplitAmounts[contact.id] = perPerson;
      });
      newSplitAmounts[userId] = 0;
    } else if (splitMode === 'owedToOther') {
      const perPerson = (amount / numContacts).toFixed(2);
      selectedContacts.forEach(contact => {
        newSplitAmounts[contact.id] = perPerson;
      });
      newSplitAmounts[userId] = amount;
    } else {
      if (splitMethod === 'equal') {
        const equalAmount = (amount / (numContacts + 1)).toFixed(2);
        selectedContacts.forEach(contact => {
          newSplitAmounts[contact.id] = equalAmount;
        });
        newSplitAmounts[userId] = equalAmount;
      } else if (splitMethod === 'custom') {
        selectedContacts.forEach(contact => {
          newSplitAmounts[contact.id] = splitAmounts[contact.id] || '0';
        });
        newSplitAmounts[userId] = splitAmounts[userId] || '0';
      }
    }

    if (JSON.stringify(newSplitAmounts) !== JSON.stringify(splitAmounts)) {
      dispatch(setSplitAmounts(newSplitAmounts));
    }

    const totalToRequest = Object.entries(newSplitAmounts)
      .filter(([id]) => id !== userId)
      .reduce((sum, [, amount]) => sum + parseFloat(amount || 0), 0);

    if (totalToRequest !== totalSplitAmount) {
      dispatch(setTotalSplitAmount(totalToRequest));
    }
  }, [transaction, selectedContacts, editableAmount, splitMode, splitMethod, splitAmounts, userId, dispatch, totalSplitAmount]);

  useEffect(() => {
    if (open) {
      logEvent('SplitTransactionPopup', 'ComponentOpened', { 
        transactionId: transaction?.transaction_id 
      });
      dispatch(resetState());
      if (transaction) {
        dispatch(setDescription(`Split for ${transaction.name}`));
        dispatch(setEditableAmount(transaction.amount || 0));
      }
      dispatch(fetchContactsRequest(userId));
      dispatch(fetchSplitTransactionsRequest(userId));
    } else {
      logEvent('SplitTransactionPopup', 'ComponentClosed');
    }
  }, [open, dispatch, transaction, userId]);

  useEffect(() => {
    if (transaction && selectedContacts.length > 0) {
      calculateSplitAmounts();
    }
  }, [transaction, selectedContacts, splitMethod, editableAmount, splitMode, calculateSplitAmounts]);

  useEffect(() => {
    if (splitMode !== 'normal') {
      dispatch(setSplitMethod('equal'));
    }
  }, [splitMode, dispatch]);

  const calculateSummary = () => {
    let totalOwedToYou = 0;
    let totalYouOwe = 0;
    const amountsPerPerson = [];
    const contactTransactions = {};

    Object.entries(splitAmounts).forEach(([id, amount]) => {
      const parsedAmount = parseFloat(amount || 0);

      if (id !== userId) {
        if (splitMode === 'owedToMe') {
          totalOwedToYou += parsedAmount;
        } else if (splitMode === 'owedToOther') {
          totalYouOwe += parsedAmount;
        }

        const contact = selectedContacts.find((c) => c.id === id);
        if (contact) {
          amountsPerPerson.push({
            name: contact.name,
            amount: parsedAmount,
          });
        }
      }
    });

    splitTransactions.forEach((transaction) => {
      const amount = parseFloat(transaction.amount || 0);
      const contactName = transaction.userContactName || "Unknown Contact";

      if (!contactTransactions[contactName]) {
        contactTransactions[contactName] = {
          totalAmount: 0,
          date: transaction.date,
          transactions: []
        };
      }

      contactTransactions[contactName].totalAmount += amount;
      contactTransactions[contactName].transactions.push(transaction);

      if (amount > 0) {
        totalOwedToYou += amount;
      } else if (amount < 0) {
        totalYouOwe += Math.abs(amount);
      }
    });

    return {
      totalOwedToYou,
      totalYouOwe,
      amountsPerPerson,
      contactTransactions
    };
  };

  const { totalOwedToYou, totalYouOwe, amountsPerPerson, contactTransactions } = calculateSummary();

  const handlePercentageClick = (contactId, currentPercentage) => {
    setEditingPercentageId(contactId);
    setTempPercentage(currentPercentage);
  };

  const handlePercentageChange = (e) => {
    setTempPercentage(e.target.value);
  };

  const handlePercentageBlur = (contactId) => {
    if (tempPercentage) {
      const newPercentage = parseFloat(tempPercentage);
      if (!isNaN(newPercentage) && newPercentage >= 0 && newPercentage <= 100) {
        const amount = (newPercentage / 100) * parseFloat(editableAmount);
        handleCustomAmountChange(contactId, amount.toFixed(2));
      }
    }
    setEditingPercentageId(null);
  };

  const handlePercentageKeyPress = (e, contactId) => {
    if (e.key === 'Enter') {
      handlePercentageBlur(contactId);
    }
  };

  const handleSplitChange = (event, newValue) => {
    logEvent('SplitTransactionPopup', 'TabChanged', {
      previousTab: activeTab,
      newTab: newValue
    });
    dispatch(setActiveTab(newValue));
  };

  const handleAmountClick = () => {
    dispatch(setIsEditingAmount(true));
  };

  const handleAmountChange = (event) => {
    const value = parseFloat(event.target.value);
    logEvent('SplitTransactionPopup', 'AmountChanged', {
      previousAmount: editableAmount,
      newAmount: value
    });
    if (!isNaN(value)) {
      dispatch(setEditableAmount(value));
    } else {
      dispatch(setEditableAmount(0));
    }
  };

  const handleAmountBlur = () => {
    dispatch(setIsEditingAmount(false));
  };

  const handleAmountKeyPress = (event) => {
    if (event.key === 'Enter') {
      handleAmountBlur();
    }
  };

  const handleSplitMethodChange = (method) => {
    logEvent('SplitTransactionPopup', 'SplitMethodChanged', {
      method,
      previousMethod: splitMethod
    });
    dispatch(setSplitMethod(method));
  };

  const handleToggleOwedToMe = () => {
    setSplitMode(prev => prev === 'owedToMe' ? 'normal' : 'owedToMe');
    dispatch(setSplitMethod('equal'));
  };

  const handleToggleOwedToOther = () => {
    setSplitMode(prev => prev === 'owedToOther' ? 'normal' : 'owedToOther');
    dispatch(setSplitMethod('equal'));
  };

  const handleCustomAmountChange = (contactId, value) => {
    logEvent('SplitTransactionPopup', 'CustomAmountChanged', {
      contactId,
      amount: value
    });
    const newSplitAmounts = { ...splitAmounts };
    newSplitAmounts[contactId] = value;
    dispatch(setSplitAmounts(newSplitAmounts));

    const totalToRequest = Object.entries(newSplitAmounts)
      .filter(([id]) => id !== userId)
      .reduce((sum, [, amount]) => sum + parseFloat(amount || 0), 0);

    dispatch(setTotalSplitAmount(totalToRequest));
  };

  const handleSubmit = () => {
    const splitRequests = selectedContacts.map((contact) => ({
      userId,
      transactionId: transaction?.transaction_id,
      userContactId: contact.id,
      amount: splitMode === 'owedToMe' ?
        parseFloat(splitAmounts[contact.id] || 0) :
        splitMode === 'owedToOther' ?
          -parseFloat(splitAmounts[contact.id] || 0) :
          parseFloat(splitAmounts[contact.id] || 0),
      date: new Date().toISOString(),
      notesDesc: description,
      splitMethod: splitMode !== 'normal' ? splitMode : splitMethod,
      notificationType: 'SMS', // Explicitly set notification type
    }));

    logEvent('SplitTransactionPopup', 'SplitSubmitted', {
      transactionId: transaction?.transaction_id,
      splitRequestsCount: splitRequests.length,
      totalAmount: totalSplitAmount,
      splitMethod: splitMode !== 'normal' ? splitMode : splitMethod
    });

    dispatch(submitSplitRequest({ splitRequests }));
    onSubmitSplit();
    // Don't close immediately to show success/error message
  };

  if (!transaction) return null;

  const yourShare = splitAmounts[userId] ? parseFloat(splitAmounts[userId]) : 0;

  const content = (
    <>
      {/* <Box sx={{
        backgroundColor: '#8BC34A',
        padding: '16px',
        display: 'flex',
        justifyContent: 'center',
        position: 'relative'
      }}>
        <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
          Split Transaction
        </Typography>
        <IconButton onClick={onClose} sx={{ color: '#fff', position: 'absolute', right: '16px' }}>
          <CloseIcon />
        </IconButton>
      </Box> */}

      <Box sx={{ borderBottom: 1, borderColor: 'divider', display: 'flex', justifyContent: 'space-between', width: '80%', margin: '0 auto' }}>
        <Tabs
          value={activeTab}
          onChange={handleSplitChange}
          variant="fullWidth"
          sx={{
            width: '100%',
            '& .MuiTabs-indicator': {
              backgroundColor: '#8BC34A',
            },
            '& .MuiTab-root': {
              minWidth: '120px',
            }
          }}
        >
          <Tab
            label={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CallSplitIcon sx={{ mr: 1, fontSize: '18px' }} />
                <span>Split Request</span>
              </Box>
            }
            sx={{
              fontFamily: 'Architects Daughter, cursive',
              '&.Mui-selected': {
                color: '#8BC34A',
                fontWeight: 'bold',
              },
            }}
          />
          <Tab
            label={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <SummarizeIcon sx={{ mr: 1, fontSize: '18px' }} />
                <span>Summary</span>
              </Box>
            }
            sx={{
              fontFamily: 'Architects Daughter, cursive',
              '&.Mui-selected': {
                color: '#8BC34A',
                fontWeight: 'bold',
              },
            }}
          />
        </Tabs>
      </Box>

      <Box sx={{ padding: '16px' }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        {successMessage && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {successMessage}
          </Alert>
        )}
        
        {activeTab === 0 && (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, width: '100%', alignItems: 'center' }}>
            <Box sx={{
              textAlign: 'center',
              marginBottom: '16px',
              padding: '16px',
              borderBottom: '1px solid #8BC34A',
              borderTop: '1px solid #8BC34A',
              backgroundColor: '#f6fbf3',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              width: '90%',
            }}>
              <Typography
                variant="body1"
                sx={{
                  textAlign: 'center',
                  width: '100%',
                  cursor: 'pointer',
                  backgroundColor: isEditingAmount ? '#fff' : 'transparent',
                  border: isEditingAmount ? '1px solid #8BC34A' : 'none',
                  padding: isEditingAmount ? '5px' : '0',
                  fontWeight: 'bold'
                }}
                onClick={handleAmountClick}
              >
                {isEditingAmount ? (
                  <TextField
                    value={editableAmount}
                    onChange={handleAmountChange}
                    onBlur={handleAmountBlur}
                    onKeyPress={handleAmountKeyPress}
                    variant="outlined"
                    size="small"
                    sx={{ width: '30%' }}
                  />
                ) : (
                  `Amount: $${parseFloat(editableAmount).toFixed(2)}`
                )}
              </Typography>
              <Typography variant="body2" sx={{ textAlign: 'center', width: '100%', fontWeight: 'bold' }}>
                Date: {transaction.date}
              </Typography>
            </Box>

            <Grid container spacing={3} sx={{ width: '100%' }}>
              <Grid item xs={12}>
                <TextField
                  label="Description"
                  value={description}
                  onChange={(e) => dispatch(setDescription(e.target.value))}
                  fullWidth
                  margin="normal"
                  placeholder="Add a note about this split"
                />
              </Grid>

              <Grid item xs={12}>
                {loading ? (
                  <CircularProgress size={24} />
                ) : (
                  <>
                    <Autocomplete
                      freeSolo
                      multiple
                      options={contacts || []}
                      getOptionLabel={(option) => option.name || ''}
                      isOptionEqualToValue={(option, value) => option.id === value.id}
                      value={selectedContacts}
                      onChange={(event, newValue) => {
                        dispatch(setSelectedContacts(newValue));
                        logEvent('SplitTransactionPopup', 'ContactsSelected', {
                          contactIds: newValue.map(contact => contact.id),
                        });
                      }}
                      inputValue={inputValue}
                      onInputChange={(event, newInputValue) => {
                        dispatch(setInputValue(newInputValue));
                      }}
                      openOnFocus={true}
                      autoHighlight={true}
                      filterOptions={(options, state) => {
                        return options.filter((option) =>
                          option.name.toLowerCase().includes(state.inputValue.toLowerCase())
                        );
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Select Contacts to Split With"
                          placeholder="Type to search contacts"
                          variant="outlined"
                          fullWidth
                        />
                      )}
                      renderOption={(props, option) => (
                        <li {...props} key={option.id}>
                          {option.name}
                        </li>
                      )}
                      disableCloseOnSelect
                      renderTags={(value, getTagProps) =>
                        value.map((contact, index) => (
                          <Chip
                            label={contact.name}
                            {...getTagProps({ index })}
                            key={contact.id}
                          />
                        ))
                      }
                    />
                    {selectedContacts.length > 0 && (
                      <Box mt={2} display="flex" flexWrap="wrap" gap={1}>
                        {selectedContacts.map(contact => (
                          <React.Fragment key={contact.id}>
                            {contact.emailId && (
                              <Chip
                                label={` ${contact.emailId}`}
                                variant="outlined"
                                size="small"
                                sx={{ margin: '4px' }}
                              />
                            )}
                            {contact.phoneNumber && (
                              <Chip
                                label={` ${contact.phoneNumber}`}
                                variant="outlined"
                                size="small"
                                sx={{ margin: '4px' }}
                              />
                            )}
                          </React.Fragment>
                        ))}
                      </Box>
                    )}
                  </>
                )}
              </Grid>

              {selectedContacts.length > 0 && (
                <Grid item xs={12}>
                  <Box display="flex" gap={2} mb={2} justifyContent="center">
                    <Button
                      variant={splitMethod === 'equal' ? 'contained' : 'outlined'}
                      onClick={() => handleSplitMethodChange('equal')}
                      sx={{
                        backgroundColor: splitMethod === 'equal' ? '#8BC34A' : 'inherit',
                        color: splitMethod === 'equal' ? 'white' : 'inherit',
                        '&:hover': {
                          backgroundColor: splitMethod === 'equal' ? '#7CB342' : 'inherit'
                        }
                      }}
                    >
                      Split Equally
                    </Button>
                    <Button
                      variant={splitMethod === 'custom' ? 'contained' : 'outlined'}
                      onClick={() => handleSplitMethodChange('custom')}
                      sx={{
                        backgroundColor: splitMethod === 'custom' ? '#8BC34A' : 'inherit',
                        color: splitMethod === 'custom' ? 'white' : 'inherit',
                        '&:hover': {
                          backgroundColor: splitMethod === 'custom' ? '#7CB342' : 'inherit'
                        }
                      }}
                    >
                      Custom Split
                    </Button>
                  </Box>

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mt: 2 }}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={splitMode === 'owedToMe'}
                          onChange={handleToggleOwedToMe}
                          disabled={splitMode === 'owedToOther'}
                          sx={{
                            '& .MuiSwitch-switchBase.Mui-checked': {
                              color: '#8BC34A',
                            },
                            '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                              backgroundColor: '#8BC34A',
                            }
                          }}
                        />
                      }
                      label="You are owed the full amount"
                      sx={{ margin: 0 }}
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={splitMode === 'owedToOther'}
                          onChange={handleToggleOwedToOther}
                          disabled={splitMode === 'owedToMe'}
                          sx={{
                            '& .MuiSwitch-switchBase.Mui-checked': {
                              color: '#EF5350',
                            },
                            '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                              backgroundColor: '#EF5350',
                            }
                          }}
                        />
                      }
                      label="You owe the full amount"
                      sx={{ margin: 0 }}
                    />
                  </Box>
                </Grid>
              )}

              {(selectedContacts.length > 0 && (splitMethod === 'equal' || splitMethod === 'custom' || splitMode !== 'normal')) && (
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Split Details
                  </Typography>

                  <Box mb={2} p={2} bgcolor="#f5f5f5" borderRadius={1}>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <Box display="flex" justifyContent="space-between" alignItems="center">
                          <Typography variant="subtitle1">
                            <strong>Your share:</strong>
                          </Typography>
                          <Box display="flex" alignItems="center" gap={1}>
                            {editingPercentageId === userId ? (
                              <TextField
                                value={tempPercentage}
                                onChange={handlePercentageChange}
                                onBlur={() => handlePercentageBlur(userId)}
                                onKeyPress={(e) => handlePercentageKeyPress(e, userId)}
                                variant="outlined"
                                size="small"
                                sx={{ width: '70px' }}
                                inputProps={{
                                  style: { textAlign: 'right' },
                                  suffix: '%'
                                }}
                              />
                            ) : (
                              <Typography
                                variant="subtitle1"
                                onClick={() => handlePercentageClick(userId, (yourShare / parseFloat(editableAmount)) * 100)}
                                sx={{ cursor: 'pointer', minWidth: '60px' }}
                              >
                                {((yourShare / parseFloat(editableAmount)) * 100 || 0).toFixed(2)}%
                              </Typography>
                            )}
                            <Typography variant="subtitle1">
                              ${yourShare.toFixed(2)}
                            </Typography>
                          </Box>
                        </Box>
                        <Divider sx={{ my: 1 }} />
                      </Grid>

                      {selectedContacts.map((contact) => {
                        const contactAmount = parseFloat(splitAmounts[contact.id] || 0);
                        const percentage = ((contactAmount / parseFloat(editableAmount)) * 100 || 0).toFixed(2);

                        return (
                          <Grid item xs={12} key={contact.id}>
                            <Box display="flex" justifyContent="space-between" alignItems="center">
                              <Typography>{contact.name}</Typography>
                              <Box display="flex" alignItems="center" gap={1}>
                                {editingPercentageId === contact.id ? (
                                  <TextField
                                    value={tempPercentage}
                                    onChange={handlePercentageChange}
                                    onBlur={() => handlePercentageBlur(contact.id)}
                                    onKeyPress={(e) => handlePercentageKeyPress(e, contact.id)}
                                    variant="outlined"
                                    size="small"
                                    sx={{ width: '70px' }}
                                    inputProps={{
                                      style: { textAlign: 'right' },
                                      suffix: '%'
                                    }}
                                  />
                                ) : (
                                  <Typography
                                    variant="body1"
                                    onClick={() => handlePercentageClick(contact.id, percentage)}
                                    sx={{ cursor: 'pointer', minWidth: '60px', }}
                                  >
                                    {percentage}%
                                  </Typography>
                                )}
                                {splitMethod === 'custom' && splitMode === 'normal' ? (
                                  <TextField
                                    type="number"
                                    value={splitAmounts[contact.id] || ''}
                                    onChange={(e) => handleCustomAmountChange(contact.id, e.target.value)}
                                    variant="outlined"
                                    size="small"
                                    InputProps={{
                                      inputProps: { min: 0 },
                                    }}
                                    sx={{ width: '120px' }}
                                  />
                                ) : (
                                  <Typography>
                                    ${contactAmount.toFixed(2)}
                                  </Typography>
                                )}
                              </Box>
                            </Box>
                          </Grid>
                        );
                      })}

                      <Grid item xs={12}>
                        <Divider sx={{ my: 1 }} />
                        <Box display="flex" justifyContent="space-between" alignItems="center">
                          <Typography variant="subtitle1">
                            <strong>Total to request:</strong>
                          </Typography>
                          <Typography
                            variant="subtitle1"
                            sx={{
                              color: splitMode === 'owedToOther' ? '#EF5350' : '#8BC34A',
                              fontWeight: 'bold'
                            }}
                          >
                            ${totalSplitAmount.toFixed(2)}
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>
                </Grid>
              )}
            </Grid>
          </Box>
        )}

        {activeTab === 1 && (
          <Box sx={{ padding: '16px', display: 'flex', flexDirection: 'column', alignItems: 'center', width: '90%' }}>
            <Box sx={{
              display: 'flex',
              width: '100%',
              justifyContent: 'center',
              gap: 2,
              mb: 4,
              flexDirection: 'row'
            }}>
              <Box sx={{
                width: '46%',
                borderRadius: '16px',
                padding: '20px',
                position: 'relative',
                overflow: 'hidden',
                backgroundColor: 'white',
                boxShadow: '0 8px 24px rgba(76, 175, 80, 0.15)',
                transition: 'transform 0.3s, box-shadow 0.3s',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 12px 30px rgba(76, 175, 80, 0.25)'
                }
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, pb: 2, borderBottom: '1px dashed rgba(76, 175, 80, 0.3)' }}>
                  <CheckCircleIcon sx={{ color: '#4CAF50', mr: 1, fontSize: '24px', backgroundColor: 'rgba(76, 175, 80, 0.1)', padding: '4px', borderRadius: '50%' }} />
                  <Typography variant="subtitle2" sx={{ color: '#4CAF50', fontWeight: 'bold', letterSpacing: '0.5px', fontSize: '0.55rem' }}>
                    TOTAL OWED TO YOU
                  </Typography>
                </Box>
                <Typography variant="h5" sx={{
                  fontWeight: '900',
                  color: '#4CAF50',
                  textAlign: 'center'
                }}>
                  ${totalOwedToYou.toFixed(2)}
                </Typography>
              </Box>

              <Box sx={{
                width: '46%',
                borderRadius: '16px',
                padding: '20px',
                position: 'relative',
                overflow: 'hidden',
                backgroundColor: 'white',
                boxShadow: '0 8px 24px rgba(239, 83, 80, 0.15)',
                transition: 'transform 0.3s, box-shadow 0.3s',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 12px 30px rgba(239, 83, 80, 0.25)'
                }
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, pb: 2, borderBottom: '1px dashed rgba(239, 83, 80, 0.3)' }}>
                  <ErrorIcon sx={{ color: '#EF5350', mr: 1, fontSize: '24px', backgroundColor: 'rgba(239, 83, 80, 0.1)', padding: '4px', borderRadius: '50%' }} />
                  <Typography variant="subtitle2" sx={{ color: '#EF5350', fontWeight: 'bold', letterSpacing: '0.5px', fontSize: '0.55rem' }}>
                    TOTAL YOU OWE
                  </Typography>
                </Box>
                <Typography variant="h5" sx={{
                  fontWeight: '900',
                  color: '#EF5350',
                  textAlign: 'center'
                }}>
                  ${Math.abs(totalYouOwe).toFixed(2)}
                </Typography>
              </Box>
            </Box>

            <Typography variant="h6" gutterBottom sx={{ mt: 2, textAlign: 'center', width: '100%' }}>
              Split Transactions
            </Typography>
            <Box sx={{
              border: '1px solid #e0e0e0',
              borderRadius: 2,
              boxShadow: 1,
              width: '100%',
              maxWidth: '90%',
              margin: '0 auto',
            }}>
              <List sx={{ p: 0 }}>
                {Object.entries(contactTransactions).map(([contactName, data], index) => (
                  <React.Fragment key={contactName}>
                    <Box
                      position="relative"
                      onMouseEnter={() => setHoveredContact(contactName)}
                      onMouseLeave={() => setHoveredContact(null)}
                    >
                      <ListItem sx={{
                        py: 2,
                        background: data.totalAmount < 0
                          ? 'linear-gradient(90deg, rgba(255,235,238,1) 0%, rgba(255,245,245,1) 100%)'
                          : 'linear-gradient(90deg, rgba(232,245,233,1) 0%, rgba(240,250,240,1) 100%)',
                        '&:hover': {
                          background: data.totalAmount < 0
                            ? 'linear-gradient(90deg, rgba(255,205,210,0.4) 0%, rgba(255,235,238,1) 100%)'
                            : 'linear-gradient(90deg, rgba(200,230,201,0.4) 0%, rgba(232,245,233,1) 100%)'
                        },
                        borderLeft: data.totalAmount < 0
                          ? '4px solid #EF5350'
                          : '4px solid #4CAF50'
                      }}>
                        <ListItemAvatar>
                          <Avatar sx={{
                            bgcolor: data.totalAmount < 0 ? '#EF5350' : '#4CAF50',
                            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                          }}>
                            {data.totalAmount < 0 ? <MoneyOffIcon /> : <AttachMoneyIcon />}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                              {contactName}
                            </Typography>
                          }
                          secondary={
                            <Typography variant="body2">
                              {data.transactions.length > 1
                                ? `${data.transactions.length} transactions`
                                : data.date}
                            </Typography>
                          }
                        />
                        <Typography
                          variant="h6"
                          sx={{
                            color: data.totalAmount < 0 ? '#EF5350' : '#4CAF50',
                            fontWeight: 'bold'
                          }}
                        >
                          {data.totalAmount < 0 ? '- ' : '+ '}
                          ${Math.abs(parseFloat(data.totalAmount || 0)).toFixed(2)}
                        </Typography>
                        {data.transactions.length > 0 && hoveredContact === contactName && (
                          <Box
                            sx={{
                              position: 'absolute',
                              width: '90%',
                              left: '5%',
                              top: '100%',
                              backgroundColor: 'white',
                              borderRadius: '8px',
                              boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
                              padding: '16px',
                              zIndex: 10,
                              border: data.totalAmount < 0
                                ? '1px solid #EF5350'
                                : '1px solid #4CAF50',
                            }}
                          >
                            <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 'bold', color: data.totalAmount < 0 ? '#EF5350' : '#4CAF50' }}>
                              Transaction Details
                            </Typography>
                            {data.transactions.map((transaction, idx) => (
                              <Box key={idx} sx={{ mb: 2, pb: 2, borderBottom: idx < data.transactions.length - 1 ? '1px dashed #e0e0e0' : 'none' }}>
                                <Box display="flex" justifyContent="space-between">
                                  <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                                    {transaction.notesDesc || 'No description'}
                                  </Typography>
                                  <Typography variant="body2" sx={{
                                    fontWeight: 'medium',
                                    color: parseFloat(transaction.amount) < 0 ? '#EF5350' : '#4CAF50'
                                  }}>
                                    ${Math.abs(parseFloat(transaction.amount || 0)).toFixed(2)}
                                  </Typography>
                                </Box>
                                <Typography variant="caption" color="text.secondary">
                                  {transaction.date || 'No date'}
                                </Typography>
                              </Box>
                            ))}
                          </Box>
                        )}
                      </ListItem>
                    </Box>
                    {index < Object.entries(contactTransactions).length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </Box>
          </Box>
        )}
      </Box>

      <Box sx={{ display: 'flex', justifyContent: 'center', padding: '16px' }}>
        <Button
          onClick={handleSubmit}
          sx={{ backgroundColor: '#8BC34A' }}
          color="primary"
          variant="contained"
          disabled={selectedContacts.length === 0 || (!splitMethod && splitMode === 'normal')}
          startIcon={<PersonAddIcon />}
        >
          Send Split Requests
        </Button>
      </Box>
    </>
  );

  return isEmbedded ? (
    content
  ) : (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
    >
      {content}
    </Dialog>
  );
};

export default SplitTransactionPopup;