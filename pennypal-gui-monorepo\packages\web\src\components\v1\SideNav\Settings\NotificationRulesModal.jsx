import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { getCurrentUserId } from '../../../../utils/AuthUtil';
import {
  UilTrashAlt,
  UilSetting,
  UilBell,
  UilArrowDown,
  UilExclamationTriangle,
  UilExclamationOctagon,
  UilCheckCircle
} from '@iconscout/react-unicons';
import {
  fetchNotifications,
  fetchNotificationRules,
  markNotificationAsRead,
  clearAllNotifications,
} from '../../../../../../logic/redux/notificationSlice';

const Notifications = ({ slideIn, darkMode, onClose }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const {
    items,
    rules,
    editedRules,
    loading,
    error,
  } = useSelector((state) => state.notifications || {});
  const notificationRef = useRef(null);
  const observer = useRef(null);

  useEffect(() => {
    const userId = getCurrentUserId();
    dispatch(fetchNotifications({ userId: userId }));
  }, [dispatch]);

  useEffect(() => {
    const userId = getCurrentUserId();
    dispatch(fetchNotificationRules({ userId: userId }));
  }, [dispatch]);

  useEffect(() => {
    observer.current = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        const index = parseInt(entry.target.getAttribute('data-index'));
        const notif = items[index];
        if (notif && notif.state === 'unread' && entry.isIntersecting) {
          dispatch(markNotificationAsRead({ notificationId: notif.notificationId }));
        }
      });
    }, { root: notificationRef.current, threshold: 1.0 });

    const currentObserver = observer.current;
    if (notificationRef.current) {
      const notificationItems = notificationRef.current.querySelectorAll('.notification-item');
      notificationItems.forEach((item, index) => {
        item.setAttribute('data-index', index);
        currentObserver.observe(item);
      });
    }

    return () => currentObserver?.disconnect();
  }, [items, dispatch]);

  // const hasUnreadNotifications = items?.some((notif) => notif.state === 'unread');

  const handleClearNotifications = () => {
    const userId = getCurrentUserId();
    dispatch(clearAllNotifications({ userId: userId }));
  };
  const openSettingsPage = () => {
    navigate('/dashboard/settings?tab=notifications');
    onClose();
  };

  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (notificationRef.current && !notificationRef.current.contains(event.target)) {
        onClose();
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  if (slideIn) return null;
  const handleDeleteNotification = (notificationId) => {
    dispatch(deleteNotification(notificationId));
  };

  const getSeverityIcon = (severity) => {
    const severityLower = severity?.toLowerCase();
    const iconSize = "w-8 h-8";
    const iconClass = darkMode ? 'text-gray-300' : 'text-gray-600';

    switch (severityLower) {
      case 'low':
        return <UilArrowDown className={`${iconSize} text-green-500`} />;
      case 'medium':
        return <UilExclamationTriangle className={`${iconSize} text-yellow-500`} />;
      case 'high':
        return <UilExclamationOctagon className={`${iconSize} text-orange-500`} />;
      case 'critical':
        return <UilBell className={`${iconSize} text-red-500`} />;
      default:
        return <UilCheckCircle className={`${iconSize} ${iconClass}`} />;
    }
  };
  return (
        <div
          ref={notificationRef}
      className={`relative w-100 max-h-80 border  rounded-lg shadow-lg overflow-y-auto ${
        darkMode ? 'text-white border-gray-600 bg-gray-800' : 'bg-white  text-gray-900 border-gray-300'
      }`}
      style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
    >
      <style>{`.notification-item::-webkit-scrollbar { display: none; }`}</style>
      {/* Fixed Header */}
    <div className={`sticky top-0 z-10 bg-inherit flex items-center p-2 justify-between border-b ${darkMode ? 'border-gray-700' : 'border-gray-300'} pb-2`}>
      <h3 className="text-lg ">Notifications</h3>
      <div className="flex items-center space-x-2">
        <UilTrashAlt
          size="20"
          className={`cursor-pointer ${darkMode ? 'text-gray-300 hover:text-red-400' : 'text-gray-600 hover:text-red-600'}`}
          onClick={handleClearNotifications}
        />
        <UilSetting
          size="20"
          className={`cursor-pointer ${darkMode ? 'text-gray-300 hover:text-gray-200' : 'text-gray-600 hover:text-gray-800'}`}
          onClick={openSettingsPage}
        />
      </div>
    </div>
      <div className="space-y-2 p-2">
            {items.length > 0 ? (
              items.map((notif, index) => {
                let severityClass = '';
                switch (notif.severity?.toLowerCase()) {
                  case 'low':
                severityClass =  darkMode ? 'bg-green-900' : 'bg-green-100';
                    break;
                  case 'medium':
                severityClass = darkMode ? 'bg-yellow-900' : 'bg-yellow-100';
                    break;
                  case 'high':
                severityClass = darkMode ? 'bg-red-900' : 'bg-red-100';
                    break;
                  case 'critical':
                severityClass = darkMode ? 'bg-purple-900' : 'bg-purple-100';
                    break;
                  default:
                    severityClass = '';
                }
                const unreadClass = notif.state === 'unread' ? '' : '';
                const textClass = darkMode ? 'text-white' : 'text-gray-900';
                const borderClass = darkMode ? 'border-b border-gray-600' : 'border-b border-gray-300';
                return (
                  <div
                    key={notif.notificationId}
                    data-index={index}
                className={`group flex items-center justify-between p-2 text-sm rounded-lg ${
                  darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'
                } transition-colors duration-200`}
              >
                <div className="flex items-center space-x-3">
                  {getSeverityIcon(notif.severity)}
                  <span className={`${unreadClass} ${textClass}`}>
                    {notif.description}
                  </span>
                </div>
                <UilTrashAlt
                  size={18}
                  onClick={() => handleDeleteNotification(notif.notificationId)}
                  className={`opacity-0 group-hover:opacity-100 cursor-pointer ${
                    darkMode ? 'text-gray-300 hover:text-red-400' : 'text-gray-600 hover:text-red-600'
                  } transition-opacity duration-200`}
                />
                  </div>
                );
              })
            ) : (
<div className={`p-2 text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
  No notifications
</div>
            )}
          </div>
    </div>
  );
};

export default Notifications;

// import React, { useEffect, useRef, useState } from 'react';
// import { useDispatch, useSelector } from 'react-redux';
// import { useNavigate } from 'react-router-dom';
// import {
//   UilTrashAlt,
//   UilSetting,
// } from '@iconscout/react-unicons';
// import {
//   fetchNotifications,
//   fetchNotificationRules,
//   markNotificationAsRead,
//   clearAllNotifications,
// } from '../../../../../../logic/redux/notificationSlice';

// const Notifications = ({ slideIn, darkMode, onClose }) => {
//   const dispatch = useDispatch();
//   const navigate = useNavigate();
//   const {
//     items = [],
//     rules,
//     editedRules,
//     loading,
//     error,
//   } = useSelector((state) => state.notifications || {});
//   const notificationRef = useRef(null);
//   const observer = useRef(null);

//   useEffect(() => {
//     dispatch(fetchNotifications());
//     dispatch(fetchNotificationRules());
//   }, [dispatch]);

//   useEffect(() => {
//     observer.current = new IntersectionObserver((entries) => {
//       entries.forEach(entry => {
//         const index = parseInt(entry.target.getAttribute('data-index'));
//         const notif = items[index];
//         if (notif && notif.state === 'unread' && entry.isIntersecting) {
//           dispatch(markNotificationAsRead({ notificationId: notif.notificationId }));
//         }
//       });
//     }, { root: notificationRef.current, threshold: 1.0 });

//     const currentObserver = observer.current;
//     if (notificationRef.current) {
//       const notificationItems = notificationRef.current.querySelectorAll('.notification-item');
//       notificationItems.forEach((item, index) => {
//         item.setAttribute('data-index', index);
//         currentObserver.observe(item);
//       });
//     }

//     return () => currentObserver?.disconnect();
//   }, [items, dispatch]);

//   useEffect(() => {
//     const handleClickOutside = (event) => {
//       if (notificationRef.current && !notificationRef.current.contains(event.target)) {
//         onClose();
//       }
//     };
//     document.addEventListener('mousedown', handleClickOutside);
//     return () => document.removeEventListener('mousedown', handleClickOutside);
//   }, [onClose]);

//   const handleClearNotifications = () => dispatch(clearAllNotifications());

//   const openSettingsPage = () => {
//     navigate('/dashboard/settings?tab=notifications');
//     onClose();
//   };

//   if (slideIn) return null;

//   return (
//     <div
//       ref={notificationRef}
//       className={`relative w-80 max-h-96 border rounded-lg shadow-lg overflow-y-auto ${
//         darkMode ? 'text-white border-gray-600 bg-gray-800' : 'bg-white text-gray-900 border-gray-300'
//       }`}
//       style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
//     >
//       <div className={`flex items-center p-2 justify-between border-b ${darkMode ? 'border-gray-700' : 'border-gray-300'} pb-2 mb-2`}>
//         <h3 className="text-lg font-semibold">Notifications</h3>
//         <div className="flex items-center space-x-2">
//           <UilTrashAlt
//             size="15"
//             className={`cursor-pointer ${darkMode ? 'text-gray-300 hover:text-red-400' : 'text-gray-600 hover:text-red-600'}`}
//             onClick={handleClearNotifications}
//           />
//           <UilSetting
//             size="15"
//             className={`cursor-pointer ${darkMode ? 'text-gray-300 hover:text-gray-200' : 'text-gray-600 hover:text-gray-800'}`}
//             onClick={openSettingsPage}
//           />
//         </div>
//       </div>

//       <div className="p-2 overflow-auto">
//         {items.length > 0 ? (
//           <table className="min-w-full text-left text-sm border-collapse">
//             <thead className={`${darkMode ? 'text-gray-300' : 'text-gray-700'} uppercase text-xs border-b ${darkMode ? 'border-gray-600' : 'border-gray-300'}`}>
//               <tr>
//                 <th className="py-1 px-2">Description</th>
//                 <th className="py-1 px-2">Severity</th>
//                 <th className="py-1 px-2">Status</th>
//               </tr>
//             </thead>
//             <tbody>
//               {items.map((notif, index) => {
//                 const unreadClass = notif.state === 'unread' ? 'font-bold' : '';
//                 const severityColor = {
//                   low: darkMode ? 'text-green-400' : 'text-green-600',
//                   medium: darkMode ? 'text-yellow-400' : 'text-yellow-600',
//                   high: darkMode ? 'text-red-400' : 'text-red-600',
//                   critical: darkMode ? 'text-purple-400' : 'text-purple-600',
//                 }[notif.severity?.toLowerCase()] || '';

//                 return (
//                   <tr
//                     key={notif.notificationId}
//                     data-index={index}
//                     className={`notification-item border-b ${darkMode ? 'border-gray-700 text-white' : 'border-gray-200 text-gray-900'} ${unreadClass}`}
//                   >
//                     <td className="py-2 px-2">{notif.description}</td>
//                     <td className={`py-2 px-2 capitalize ${severityColor}`}>{notif.severity}</td>
//                     <td className="py-2 px-2 capitalize">{notif.state}</td>
//                   </tr>
//                 );
//               })}
//             </tbody>
//           </table>
//         ) : (
//           <div className={`p-2 text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
//             No notifications
//           </div>
//         )}
//       </div>
//     </div>
//   );
// };

// export default Notifications;

// import React, { useEffect, useRef, useState } from 'react';
// import { useDispatch, useSelector } from 'react-redux';
// import { useNavigate } from 'react-router-dom';
// import {
//   UilTrashAlt,
//   UilSetting,
//   UilBell
// } from '@iconscout/react-unicons';
// import {
//   fetchNotifications,
//   fetchNotificationRules,
//   markNotificationAsRead,
//   clearAllNotifications,
// } from '../../../../../../logic/redux/notificationSlice';

// const Notifications = ({ slideIn, darkMode, onClose }) => {
//   const dispatch = useDispatch();
//   const navigate = useNavigate();
//   const {
//     items,
//     rules,
//     editedRules,
//     loading,
//     error,
//   } = useSelector((state) => state.notifications || {});
//   const notificationRef = useRef(null);
//   const observer = useRef(null);

//   useEffect(() => {
//     dispatch(fetchNotifications());
//   }, [dispatch]);

//   useEffect(() => {
//     dispatch(fetchNotificationRules());
//   }, [dispatch]);

//   useEffect(() => {
//     observer.current = new IntersectionObserver((entries) => {
//       entries.forEach(entry => {
//         const index = parseInt(entry.target.getAttribute('data-index'));
//         const notif = items[index];
//         if (notif && notif.state === 'unread' && entry.isIntersecting) {
//           dispatch(markNotificationAsRead({ notificationId: notif.notificationId }));
//         }
//       });
//     }, { root: notificationRef.current, threshold: 1.0 });

//     const currentObserver = observer.current;
//     if (notificationRef.current) {
//       const notificationItems = notificationRef.current.querySelectorAll('.notification-item');
//       notificationItems.forEach((item, index) => {
//         item.setAttribute('data-index', index);
//         currentObserver.observe(item);
//       });
//     }

//     return () => currentObserver?.disconnect();
//   }, [items, dispatch]);

//   const hasUnreadNotifications = items?.some((notif) => notif.state === 'unread');

//   const handleClearNotifications = () => dispatch(clearAllNotifications());
//   const openSettingsPage = () => {
//     navigate('/dashboard/settings?tab=notifications');
//     onClose();
//   };

//   // Handle click outside to close
//   useEffect(() => {
//     const handleClickOutside = (event) => {
//       if (notificationRef.current && !notificationRef.current.contains(event.target)) {
//         onClose();
//       }
//     };
//     document.addEventListener('mousedown', handleClickOutside);
//     return () => document.removeEventListener('mousedown', handleClickOutside);
//   }, [onClose]);

//   if (slideIn) return null;

//   return (
//         <div
//           ref={notificationRef}
//       className={`relative w-100 max-h-80 border  rounded-lg shadow-lg overflow-y-auto ${
//         darkMode ? 'text-white border-gray-600 bg-gray-800' : 'bg-white  text-gray-900 border-gray-300'
//       }`}
//       style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
//     >
//       <style>{`.notification-item::-webkit-scrollbar { display: none; }`}</style>
//       {/* Fixed Header */}
//     <div className={`sticky top-0 z-10 bg-inherit flex items-center p-2 justify-between border-b ${darkMode ? 'border-gray-700' : 'border-gray-300'} pb-2`}>
//       <h3 className="text-lg font-semibold">Notifications</h3>
//       <div className="flex items-center space-x-2">
//         <UilTrashAlt
//           size="20"
//           className={`cursor-pointer ${darkMode ? 'text-gray-300 hover:text-red-400' : 'text-gray-600 hover:text-red-600'}`}
//           onClick={handleClearNotifications}
//         />
//         <UilSetting
//           size="20"
//           className={`cursor-pointer ${darkMode ? 'text-gray-300 hover:text-gray-200' : 'text-gray-600 hover:text-gray-800'}`}
//           onClick={openSettingsPage}
//         />
//       </div>
//     </div>
//       <div className="space-y-2 p-2">
//             {items.length > 0 ? (
//               items.map((notif, index) => {
//                 let severityClass = '';
//                 switch (notif.severity?.toLowerCase()) {
//                   case 'low':
//                 // severityClass =  darkMode ? 'bg-green-900' : 'bg-green-100';
//                     break;
//                   case 'medium':
//                 // severityClass = darkMode ? 'bg-yellow-900' : 'bg-yellow-100';
//                     break;
//                   case 'high':
//                 // severityClass = darkMode ? 'bg-red-900' : 'bg-red-100';
//                     break;
//                   case 'critical':
//                 // severityClass = darkMode ? 'bg-purple-900' : 'bg-purple-100';
//                     break;
//                   default:
//                     severityClass = '';
//                 }
//                 const unreadClass = notif.state === 'unread' ? 'font-bold' : '';
//                 const textClass = darkMode ? 'text-white' : 'text-gray-900';
//                 const borderClass = darkMode ? 'border-b border-gray-600' : 'border-b border-gray-300';
//                 return (
//                   <div
//                     key={notif.notificationId}
//                     data-index={index}
//                 className={`notification-item p-2 text- text-sm border-b${
//                   darkMode ? 'text-white border-gray-600' : 'text-gray-900 border-gray-300'
//                 } ${borderClass} ${unreadClass} ${severityClass} ${textClass} rounded ${
//                   index < items.length - 1 
//                 }`}
//                   >
//                     {notif.description}
//                   </div>
//                 );
//               })
//             ) : (
// <div className={`p-2 text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
//   No notifications
// </div>
//             )}
//           </div>
//     </div>
//   );
// };

// export default Notifications;