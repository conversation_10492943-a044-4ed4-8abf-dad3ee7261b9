import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  fetchGoalsRequest,
  fetchGoalRequest,
  createGoalRequest,
  updateGoalRequest,
  contributeToGoalRequest,
  clearMessages,
  setSelectedGoal
} from '../../../../logic/redux/goalSlice';
import { fetchAccountDetails } from '../../../../logic/redux/accountsDashboardSlice';
import RecurringGoal from './RecurringGoal';
import CreateGoal from './CreateGoal';

const Goals = () => {
  const dispatch = useDispatch();
  
  // Tab state
  const [activeTab, setActiveTab] = useState('active');
  
  // Fetch accounts from accountsDashboardSlice
  const { accounts, isLoading: accountsLoading } = useSelector(state => state.accounts || {});
  const [showRecurringModal, setShowRecurringModal] = useState(false);
  const [recurringGoal, setRecurringGoal] = useState(null);
  const [showCreateGoalModal, setShowCreateGoalModal] = useState(false);
  const [goalToEdit, setGoalToEdit] = useState(null);

  const handleOpenRecurringModal = (goal) => {
    setRecurringGoal(goal);
    setShowRecurringModal(true);
  };

  const handleCloseRecurringModal = () => {
    setShowRecurringModal(false);
    setRecurringGoal(null);
  };

  // Safely extract goal data from Redux state
  const goal = useSelector(state => {
    if (state.goal && state.goal.goals) {
      return state.goal;
    }
    for (const key of Object.keys(state)) {
      if (state[key] && Array.isArray(state[key].goals)) {
        return state[key];
      }
    }
    return { goals: [], loading: false };
  });
  
  const goals = goal?.goals || [];
  const selectedGoal = goal?.selectedGoal || null;
  const loading = goal?.loading || false;
  const error = goal?.error || null;
  const successMessage = goal?.successMessage || '';
  const isAddingGoal = goal?.isAddingGoal || false;
  const isAddingContribution = goal?.isAddingContribution || false;
  
  // Split goals based on completion status
  const activeGoals = goals.filter(goal => {
    const progressPercent = Math.min((goal.currentAmount / goal.goalAmount) * 100, 100);
    return progressPercent < 100;
  });
  
  const completedGoals = goals.filter(goal => {
    const progressPercent = Math.min((goal.currentAmount / goal.goalAmount) * 100, 100);
    return progressPercent >= 100;
  });
  
  // Contribution modal state
  const [showContributeModal, setShowContributeModal] = useState(false);
  const [contributionGoal, setContributionGoal] = useState(null);
  const [contributionAccountId, setContributionAccountId] = useState('');
  const [contributionAmount, setContributionAmount] = useState('');
  
  // Fetch goals and accounts data on component mount
  useEffect(() => {
    dispatch(fetchGoalsRequest());
    dispatch(fetchAccountDetails());
  }, [dispatch]);
  
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => {
        dispatch(clearMessages());
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [successMessage, dispatch]);
  
  const handleSelectGoal = (goalId) => {
    // First set the selected goal ID in Redux store
    const selectedGoal = goals.find(g => g.id === goalId);
    if (selectedGoal) {
      dispatch(setSelectedGoal(selectedGoal));
    } else {
      // If not found in local cache, fetch from server
      dispatch(fetchGoalRequest(goalId));
    }
    
    // Switch to the appropriate tab if the goal is in another tab
    const progressPercent = selectedGoal 
      ? Math.min((selectedGoal.currentAmount / selectedGoal.goalAmount) * 100, 100)
      : 0;
    
    if (progressPercent >= 100) {
      setActiveTab('completed');
    } else {
      setActiveTab('active');
    }
  };

  const handleOpenContributeModal = (e, goal) => {
    e.stopPropagation(); // Prevent goal selection when clicking contribute button
    setContributionGoal(goal);
    if (accounts && accounts.length > 0) {
      setContributionAccountId((accounts[0].id || accounts[0].account_id).toString());
    } else {
      setContributionAccountId('');
    }
    
    setContributionAmount('');
    setShowContributeModal(true);
  };

  // Close contribution modal
  const handleCloseContributeModal = () => {
    setShowContributeModal(false);
    setContributionGoal(null);
    setContributionAccountId('');
    setContributionAmount('');
  };

  const handleContributeToGoal = () => {
    // Validate input
    if (!contributionGoal || !contributionAccountId || !contributionAmount) {
      alert("Please select an account and enter an amount");
      return;
    }
    
    const amount = parseFloat(contributionAmount);
    if (isNaN(amount) || amount <= 0) {
      alert("Please enter a valid amount");
      return;
    }
    
    // Create contribution payload
    const contributionData = {
      goalId: contributionGoal.id,
      accountId: parseInt(contributionAccountId),
      amount: amount
    };
    
    console.log("Contributing:", contributionData);
    
    // Dispatch contribution action
    dispatch(contributeToGoalRequest(contributionData));
    handleCloseContributeModal();
  };

  const handleClearSelection = () => {
    dispatch(setSelectedGoal(null));
    setGoalToEdit(null);
  };

  const handleEditGoal = (goal) => {
    setGoalToEdit(goal);
    setShowCreateGoalModal(true);
  };

  const isOverdue = (goal) => {
    const today = new Date();
    const targetDate = new Date(goal.targetDate);
    const progressPercent = Math.min((goal.currentAmount / goal.goalAmount) * 100, 100);
    return targetDate < today && progressPercent < 100;
  };
 
  // Helper to format account balances
  const formatBalance = (balance) => {
    if (balance === undefined || balance === null) return '$0';
    return balance.toLocaleString('en-US', { style: 'currency', currency: 'USD' });
  };
  
  // Render a goal card
  const renderGoalCard = (goal) => {
    const progressPercent = Math.min((goal.currentAmount / goal.goalAmount) * 100, 100);
    const isCompleted = progressPercent >= 100;
    
    return (
      <div
        key={goal.id}
        className={`border rounded-lg overflow-hidden transition-all cursor-pointer ${
          selectedGoal && selectedGoal.id === goal.id 
            ? 'border-blue-400 bg-blue-50 shadow' 
            : 'border-gray-200'
        }`}
        onClick={() => handleSelectGoal(goal.id)}
      >
        <div className="px-5 py-4">
        
          {/* Add the overdue warning here */}
          {isOverdue(goal) && (
            <div className="mt-3 p-2 bg-yellow-50 border border-yellow-300 rounded-md">
              <div className="flex items-start">
                <svg className="w-5 h-5 text-yellow-600 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <div>
                  <p className="text-sm font-medium text-yellow-800">
                    Target date has passed but goal is not complete.
                  </p>
                  <p className="text-xs text-yellow-700 mt-1">
                    Please update your target date or consider adding more contributions to reach your goal.
                  </p>
                </div>
              </div>
              {selectedGoal && selectedGoal.id === goal.id && (
                <button 
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEditGoal(goal);
                  }}
                  className="mt-2 text-xs bg-yellow-100 hover:bg-yellow-200 text-yellow-800 px-3 py-1 rounded-md inline-flex items-center"
                >
                  <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                  Update Goal
                </button>
              )}
            </div>
          )}

          <div className="flex justify-between mt-5">
            <h4 className="font-medium text-lg text-gray-800">{goal.goalName}</h4>
            <span className={`text-sm px-2.5 py-1 rounded-full ${
              isCompleted 
                ? 'bg-green-100 text-green-800' 
                : 'bg-gray-100 text-gray-800'
            }`}>
              {isCompleted ? 'Completed' : `${Math.round(progressPercent)}%`}
            </span>
          </div>
          
          <div className="text-sm text-gray-600 mt-1 flex flex-wrap gap-x-4 gap-y-1">
            {goal.goalType && <span className="inline-block">{goal.goalType}</span>}
            <span className="inline-block">
              Start: {new Date(goal.startDate).toLocaleDateString()}
            </span>
            <span className="inline-block">
              Target: {new Date(goal.targetDate).toLocaleDateString()}
            </span>
          </div>
          
          {goal.description && (
            <div className="mt-2 text-sm text-gray-600">
              {goal.description}
            </div>
          )}
          
          {/* Enhanced Progress Bar */}
          <div className="mt-4">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>Progress: {goal.currentAmount ? goal.currentAmount.toLocaleString('en-US', { style: 'currency', currency: 'USD' }) : '$0'}</span>
              <span>Goal: {goal.goalAmount.toLocaleString('en-US', { style: 'currency', currency: 'USD' })}</span>
            </div>
            <div className="h-3 w-full bg-gray-200 rounded-full overflow-hidden">
              <div 
                className={`h-full ${isCompleted ? 'bg-green-500' : 'bg-blue-500'}`}
                style={{ width: `${progressPercent}%` }}
              ></div>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="mt-3 flex justify-end space-x-2">
            {!isCompleted && (
              <>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleOpenRecurringModal(goal);
                  }}
                  className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded text-sm flex items-center"
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Set Recurring
                </button>
                <button
                  onClick={(e) => handleOpenContributeModal(e, goal)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm flex items-center"
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Contribute
                </button>
              </>
            )}
            {selectedGoal && selectedGoal.id === goal.id && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleEditGoal(goal);
                }}
                className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm flex items-center"
              >
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
                Edit
              </button>
            )}
          </div>
          
          {/* Detailed Accounts expanded view */}
          {selectedGoal && selectedGoal.id === goal.id && goal.accounts && goal.accounts.length > 0 && (
            <div className="mt-4 pt-3 border-t border-gray-200">
              <h5 className="font-medium text-sm text-gray-700 mb-2">Funding Accounts</h5>
              <div className="space-y-2">
                {goal.accounts.map((acc) => (
                  <div key={acc.accountId} className="bg-white p-3 rounded-md border border-gray-100">
                    <div className="flex justify-between items-center">
                      <div className="text-sm font-medium text-gray-700">
                        {acc.accountName}
                      </div>
                      <div className="text-xs bg-blue-50 text-blue-700 px-2 py-1 rounded-full">
                        Allocation: {acc.allocationPercentage}%
                      </div>
                    </div>
                    
                    {/* Account contribution details */}
                    <div className="mt-2 grid grid-cols-2 gap-2 text-xs text-gray-600">
                      <div>
                        <span className="block text-gray-500">Allocated Amount:</span>
                        <span className="font-medium">{formatBalance(acc.allocatedAmount)}</span>
                      </div>
                      <div>
                        <span className="block text-gray-500">Current Contribution:</span>
                        <span className="font-medium">{formatBalance(acc.currentContribution)}</span>
                      </div>
                    </div>
                    
                    {/* Progress bar for this specific account */}
                    <div className="mt-2">
                      <div className="flex justify-between text-xs mb-1">
                        <span>Progress: {acc.progressPercentage?.toFixed(1) || 0}%</span>
                        <span className={acc.progressPercentage >= 100 ? "text-green-600" : "text-blue-600"}>
                          {acc.progressPercentage >= 100 ? "Completed" : "In Progress"}
                        </span>
                      </div>
                      <div className="h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                        <div 
                          className={acc.progressPercentage >= 100 ? "h-full bg-green-500" : "h-full bg-blue-500"}
                          style={{ width: `${Math.min(acc.progressPercentage || 0, 100)}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {selectedGoal && selectedGoal.id === goal.id && goal.recurringContributions && goal.recurringContributions.length > 0 && (
            <div className="mt-4 pt-3 border-t border-gray-200">
              <h5 className="font-medium text-sm text-gray-700 mb-2">Recurring Contributions</h5>
              <div className="space-y-2">
                {goal.recurringContributions.map((contribution, index) => (
                  <div key={index} className="bg-white p-3 rounded-md border border-gray-100">
                    <div className="flex justify-between items-center">
                      <div className="text-sm font-medium text-gray-700">
                        {formatBalance(contribution.amount)} {contribution.frequency.toLowerCase()}
                      </div>
                      <div className="text-xs bg-purple-50 text-purple-700 px-2 py-1 rounded-full">
                        From: {contribution.accountName}
                      </div>
                    </div>
                    <div className="mt-2 grid grid-cols-2 gap-2 text-xs text-gray-600">
                      <div>
                        <span className="block text-gray-500">Start Date:</span>
                        <span className="font-medium">{new Date(contribution.startDate).toLocaleDateString()}</span>
                      </div>
                      <div>
                        <span className="block text-gray-500">End Date:</span>
                        <span className="font-medium">{contribution.endDate ? new Date(contribution.endDate).toLocaleDateString() : 'Ongoing'}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };
  
  // Display loading state when fetching initial data
  if ((loading && goals.length === 0) || (accountsLoading && accounts.length === 0)) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">Loading data...</div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="p-6 max-w-4xl mx-auto bg-white rounded-lg shadow-sm">
     
      <div className="flex justify-between items-center mb-6 border-b pb-3">
        <h2 className="text-2xl font-bold text-gray-800">Financial Goals</h2>
        <button
          onClick={() => {
            setGoalToEdit(null);
            setShowCreateGoalModal(true);
          }}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add Goal
        </button>
      </div>

      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4 flex items-center">
          <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
          {successMessage}
        </div>
      )}
      
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4 flex items-center">
          <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
          {error}
        </div>
      )}
      
      {/* No accounts message */}
      {accounts && accounts.length === 0 && (
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded mb-4 flex items-center">
          <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          No accounts found. Please connect accounts before creating goals.
        </div>
      )}

      {/* Create Goal Modal */}
      {showCreateGoalModal && (
        <CreateGoal 
          selectedGoal={goalToEdit}
          onClose={() => {
            setShowCreateGoalModal(false);
            setGoalToEdit(null);
          }}
          accounts={accounts}
        />
      )}

      {/* Recurring Goal Modal */}
      {showRecurringModal && recurringGoal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <RecurringGoal 
            goal={recurringGoal} 
            accounts={accounts} 
            onClose={handleCloseRecurringModal} 
          />
        </div>
      )}  

      {/* Contribution Modal */}
      {showContributeModal && contributionGoal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">
              Contribute to: {contributionGoal.goalName}
            </h3>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                From Account
              </label>
              <select
                value={contributionAccountId}
                onChange={(e) => setContributionAccountId(e.target.value)}
                className="w-full border border-gray-300 px-3 py-2 rounded-md"
              >
                <option value="">Select Account</option>
                {accounts && accounts.map(account => (
                  <option key={account.id || account.account_id} value={account.id || account.account_id}>
                    {account.name || account.account_name} ({formatBalance(account.balance || account.current_balance)})
                  </option>
                ))}
              </select>
            </div>
            
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Contribution Amount ($)
              </label>
              <input
                type="number"
                value={contributionAmount}
                onChange={(e) => setContributionAmount(e.target.value)}
                className="w-full border border-gray-300 px-3 py-2 rounded-md"
                placeholder="Enter amount"
              />
            </div>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={handleCloseContributeModal}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleContributeToGoal}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Contribute
              </button>
            </div>
          </div>
        </div>
      )}
      {/* Goal List - Active & Completed Goals */}
      <div className="mt-6">
        <div className="flex mb-4">
          <button
            onClick={() => setActiveTab('active')}
            className={`px-4 py-2 text-sm font-medium rounded-md ${activeTab === 'active' ? 'bg-blue-600 text-black' : 'bg-gray-200 text-gray-800'}`}
          >
            Active Goals
          </button>
          <button
            onClick={() => setActiveTab('completed')}
            className={`ml-4 px-4 py-2 text-sm font-medium rounded-md ${activeTab === 'completed' ? 'bg-blue-600 text-black' : 'bg-gray-200 text-gray-800'}`}
          >
            Completed Goals
          </button>
        </div>

        <div className="space-y-4">
          {activeTab === 'active' &&
            activeGoals.map(goal => renderGoalCard(goal))}

          {activeTab === 'completed' &&
            completedGoals.map(goal => renderGoalCard(goal))}
        </div>
      </div>
    </div>
  );
};

export default Goals;
