// import React, { useState, useEffect } from 'react';
// import { useDispatch, useSelector } from 'react-redux';
// import { setupRecurringContributionRequest } from '../../../../logic/redux/recurringSlice';
// import { fetchAccountDetails } from '../../../../logic/redux/accountsDashboardSlice';

// const RecurringGoal = ({ goal, onClose }) => {
//   const dispatch = useDispatch();
  
//   // Get accounts from Redux store
//   const { accounts, loading: accountsLoading } = useSelector(state => state.accounts);
  
//   // State for multiple recurring contributions
//   const [recurringContributions, setRecurringContributions] = useState([
//     {
//       id: 1,
//       accountId: '',
//       amount: '',
//       frequency: 'MONTHLY',
//       dayOfMonth: '1',
//       dayOfWeek: 'MONDAY',
//       startDate: new Date().toISOString().split('T')[0],
//       endDate: '',
//       isEndDateEnabled: false
//     }
//   ]);
  
//   // Remaining goal amount
//   const [remainingGoalAmount, setRemainingGoalAmount] = useState(0);
  
//   // Format currency
//   const formatBalance = (balance) => {
//     if (balance === undefined || balance === null) return '$0';
//     return balance.toLocaleString('en-US', { style: 'currency', currency: 'USD' });
//   };
  
//   // Fetch accounts when component mounts
//   useEffect(() => {
//     dispatch(fetchAccountDetails());
//   }, [dispatch]);
  
//   // Calculate remaining goal amount on initial load and set default account
//   useEffect(() => {
//     // Calculate remaining goal amount
//     const remaining = Math.max(goal.goalAmount - goal.currentAmount, 0);
//     setRemainingGoalAmount(remaining);
    
//     // Set default account if accounts are loaded from Redux
//     if (accounts && accounts.length > 0) {
//       const defaultAccountId = accounts[0].id.toString();
      
//       setRecurringContributions(prevContribs => {
//         const updatedContribs = [...prevContribs];
//         if (updatedContribs[0]) {
//           updatedContribs[0] = {
//             ...updatedContribs[0],
//             accountId: defaultAccountId
//           };
//         }
//         return updatedContribs;
//       });
//     }
//   }, [goal, accounts]);
  
//   // Get account by ID
//   const getAccountById = (accountId) => {
//     if (!accounts || !accountId) return null;
//     return accounts.find(acc => acc.id.toString() === accountId);
//   };
  
//   // Add a new recurring contribution
//   const addContribution = () => {
//     const newId = Math.max(...recurringContributions.map(c => c.id)) + 1;
    
//     // Find an account that hasn't been used yet
//     const usedAccountIds = recurringContributions.map(c => c.accountId);
//     const availableAccount = accounts?.find(acc => 
//       !usedAccountIds.includes(acc.id.toString())
//     );
    
//     setRecurringContributions([
//       ...recurringContributions, 
//       {
//         id: newId,
//         accountId: availableAccount ? availableAccount.id.toString() : '',
//         amount: '',
//         frequency: 'MONTHLY',
//         dayOfMonth: '1',
//         dayOfWeek: 'MONDAY',
//         startDate: new Date().toISOString().split('T')[0],
//         endDate: '',
//         isEndDateEnabled: false
//       }
//     ]);
//   };
  
//   // Remove a recurring contribution
//   const removeContribution = (id) => {
//     if (recurringContributions.length === 1) {
//       // Don't remove the last one, just reset it
//       setRecurringContributions([{
//         id: 1,
//         accountId: '',
//         amount: '',
//         frequency: 'MONTHLY',
//         dayOfMonth: '1',
//         dayOfWeek: 'MONDAY',
//         startDate: new Date().toISOString().split('T')[0],
//         endDate: '',
//         isEndDateEnabled: false
//       }]);
//     } else {
//       setRecurringContributions(recurringContributions.filter(c => c.id !== id));
//     }
//   };
  
//   // Update a specific field for a contribution
//   const updateContribution = (id, field, value) => {
//     setRecurringContributions(prevContribs => {
//       return prevContribs.map(contrib => {
//         if (contrib.id === id) {
//           return { ...contrib, [field]: value };
//         }
//         return contrib;
//       });
//     });
//   };
  
//   // Handle form submission
//   const handleSubmit = () => {
//     // Validate input for all contributions
//     const hasInvalidContribution = recurringContributions.some(contrib => {
//       const account = getAccountById(contrib.accountId);
      
//       if (!contrib.accountId || !contrib.amount || parseFloat(contrib.amount) <= 0) {
//         return true; // Invalid
//       }
      
//       if (parseFloat(contrib.amount) > (account?.balance || 0)) {
//         return true; // Invalid - exceeds balance
//       }
      
//       return false; // Valid
//     });
    
//     if (hasInvalidContribution) {
//       alert("Please check all contributions. Each must have a valid account and amount.");
//       return;
//     }
    
//     // Process each contribution
//     recurringContributions.forEach(contrib => {
//       // Create recurring contribution data
//       const recurringData = {
//         goalId: goal.id,
//         accountId: parseInt(contrib.accountId),
//         amount: parseFloat(contrib.amount),
//         frequency: contrib.frequency,
//         startDate: contrib.startDate,
//         endDate: contrib.isEndDateEnabled ? contrib.endDate : null
//       };
      
//       // Add day of month/week based on frequency
//       if (contrib.frequency === 'MONTHLY') {
//         recurringData.dayOfMonth = parseInt(contrib.dayOfMonth);
//       } else if (contrib.frequency === 'WEEKLY') {
//         recurringData.dayOfWeek = contrib.dayOfWeek;
//       }
      
//       console.log("Setting up recurring contribution:", recurringData);
      
//       // Dispatch action to set up recurring contribution
//       dispatch(setupRecurringContributionRequest(recurringData));
//     });
    
//     onClose();
//   };
  
//   // Calculate the estimated completion date based on all contributions
//   const calculateEstimatedCompletion = () => {
//     // Get valid contributions
//     const validContributions = recurringContributions.filter(contrib => 
//       contrib.accountId && contrib.amount && parseFloat(contrib.amount) > 0
//     );
    
//     if (validContributions.length === 0 || remainingGoalAmount <= 0) {
//       return "Unknown";
//     }
    
//     // Calculate contribution amounts by frequency
//     const dailyAmount = validContributions
//       .filter(c => c.frequency === 'DAILY')
//       .reduce((sum, c) => sum + parseFloat(c.amount), 0);
      
//     const weeklyAmount = validContributions
//       .filter(c => c.frequency === 'WEEKLY')
//       .reduce((sum, c) => sum + parseFloat(c.amount), 0);
      
//     const monthlyAmount = validContributions
//       .filter(c => c.frequency === 'MONTHLY')
//       .reduce((sum, c) => sum + parseFloat(c.amount), 0);
      
//     const quarterlyAmount = validContributions
//       .filter(c => c.frequency === 'QUARTERLY')
//       .reduce((sum, c) => sum + parseFloat(c.amount), 0);
    
//     // Convert all to daily amounts (approximate)
//     const estimatedDailyTotal = 
//       dailyAmount + 
//       (weeklyAmount / 7) + 
//       (monthlyAmount / 30) + 
//       (quarterlyAmount / 90);
    
//     if (estimatedDailyTotal <= 0) {
//       return "Unknown";
//     }
    
//     // Estimate days to completion
//     const daysToCompletion = Math.ceil(remainingGoalAmount / estimatedDailyTotal);
    
//     // Get earliest start date
//     const startDates = validContributions.map(c => new Date(c.startDate));
//     const earliestStart = new Date(Math.min(...startDates));
    
//     // Calculate completion date
//     const completionDate = new Date(earliestStart);
//     completionDate.setDate(earliestStart.getDate() + daysToCompletion);
    
//     // Check if any end dates come before completion
//     const hasEndDateBeforeCompletion = validContributions.some(contrib => {
//       if (!contrib.isEndDateEnabled || !contrib.endDate) return false;
      
//       const endDate = new Date(contrib.endDate);
//       return endDate < completionDate;
//     });
    
//     if (hasEndDateBeforeCompletion) {
//       return "Goal may not be completed before some contributions end";
//     }
    
//     return completionDate.toLocaleDateString('en-US', { 
//       year: 'numeric', 
//       month: 'long', 
//       day: 'numeric' 
//     });
//   };
  
//   // Generate options for day of month
//   const generateDayOptions = () => {
//     const options = [];
//     for (let i = 1; i <= 31; i++) {
//       options.push(
//         <option key={i} value={i}>
//           {i}
//         </option>
//       );
//     }
//     return options;
//   };
  
//   // Check if we have any accounts available
//   const hasAccounts = accounts && accounts.length > 0;
  
//   // Calculate the total impact on goal
//   const calculateTotalGoalImpact = () => {
//     // Get total monthly contribution (very rough approximation)
//     const monthlyEquivalent = recurringContributions.reduce((total, contrib) => {
//       if (!contrib.accountId || !contrib.amount || parseFloat(contrib.amount) <= 0) {
//         return total;
//       }
      
//       const amount = parseFloat(contrib.amount);
      
//       switch (contrib.frequency) {
//         case 'DAILY':
//           return total + (amount * 30); // Approx 30 days/month
//         case 'WEEKLY':
//           return total + (amount * 4.3); // Approx 4.3 weeks/month
//         case 'MONTHLY':
//           return total + amount;
//         case 'QUARTERLY':
//           return total + (amount / 3); // 1/3 of quarterly per month
//         default:
//           return total;
//       }
//     }, 0);
    
//     return {
//       monthlyContribution: monthlyEquivalent,
//       estimatedCompletionDate: calculateEstimatedCompletion()
//     };
//   };
  
//   const goalImpact = calculateTotalGoalImpact();
  
//   // Generate ID for HTML elements to avoid duplication
//   const generateElementId = (contributionId, field) => {
//     return `contrib-${contributionId}-${field}`;
//   };
  
//   // Loading state while accounts are being fetched
//   if (accountsLoading) {
//     return (
//       <div className="bg-white rounded-lg p-6 w-full max-w-md">
//         <div className="flex justify-center items-center h-40">
//           <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
//         </div>
//       </div>
//     );
//   }
  
// // Change this return statement to add scrolling to the modal
// // Replace the main container div with this version
// return (
//   <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-screen overflow-hidden flex flex-col">
//     <h3 className="text-lg font-semibold mb-4">
//       Set Up Multiple Recurring Contributions
//     </h3>
//     <div className="overflow-y-auto flex-1 pr-2">
//       <div className="grid grid-cols-1 gap-4">
//         <div className="text-sm mb-4 pb-3 border-b border-gray-200">
//           <div><span className="font-medium">Goal:</span> {goal.goalName}</div>
//           <div><span className="font-medium">Target:</span> {formatBalance(goal.goalAmount)}</div>
//           <div className="flex justify-between">
//             <div><span className="font-medium">Current:</span> {formatBalance(goal.currentAmount)}</div>
//             <div><span className="font-medium">Remaining:</span> {formatBalance(remainingGoalAmount)}</div>
//           </div>
//         </div>
        
//         {!hasAccounts && (
//           <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md text-yellow-700 mb-4">
//             <div className="flex items-center">
//               <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
//                 <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
//               </svg>
//               <span>No accounts available. Please ensure you have accounts set up.</span>
//             </div>
//           </div>
//         )}
        
//         {/* Recurring Contributions Section */}
//         {recurringContributions.map((contribution, index) => {
//           const selectedAccount = getAccountById(contribution.accountId);
//           const isAmountValid = contribution.amount && 
//             parseFloat(contribution.amount) > 0 && 
//             parseFloat(contribution.amount) <= (selectedAccount?.balance || 0);
            
//           return (
//             <div 
//               key={contribution.id} 
//               className="border border-gray-200 rounded-lg p-4 mb-4"
//             >
//               <div className="flex justify-between items-center mb-3">
//                 <h4 className="font-medium">Contribution #{index + 1}</h4>
//                 {recurringContributions.length > 1 && (
//                   <button
//                     onClick={() => removeContribution(contribution.id)}
//                     className="text-red-500 hover:text-red-700"
//                     type="button"
//                   >
//                     <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
//                       <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
//                     </svg>
//                   </button>
//                 )}
//               </div>
              
//               <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
//                 <div className="mb-3">
//                   <label 
//                     className="block text-sm font-medium text-gray-700 mb-1"
//                     htmlFor={generateElementId(contribution.id, 'account')}
//                   >
//                     From Account
//                   </label>
//                   <select
//                     id={generateElementId(contribution.id, 'account')}
//                     value={contribution.accountId}
//                     onChange={(e) => updateContribution(contribution.id, 'accountId', e.target.value)}
//                     className="w-full border border-gray-300 px-3 py-2 rounded-md"
//                     disabled={!hasAccounts}
//                   >
//                     <option value="">Select Account</option>
//                     {hasAccounts && accounts.map(acc => (
//                       <option key={acc.id} value={acc.id.toString()}>
//                         {acc.name} ({formatBalance(acc.balance || 0)})
//                       </option>
//                     ))}
//                   </select>
//                 </div>
                
//                 <div className="mb-3">
//                   <label 
//                     className="block text-sm font-medium text-gray-700 mb-1"
//                     htmlFor={generateElementId(contribution.id, 'amount')}
//                   >
//                     Amount ($)
//                   </label>
//                   <input
//                     id={generateElementId(contribution.id, 'amount')}
//                     type="number"
//                     value={contribution.amount}
//                     onChange={(e) => updateContribution(contribution.id, 'amount', e.target.value)}
//                     className={`w-full border px-3 py-2 rounded-md ${
//                       contribution.amount && parseFloat(contribution.amount) > (selectedAccount?.balance || 0) 
//                         ? 'border-red-300' 
//                         : 'border-gray-300'
//                     }`}
//                     placeholder="Enter amount"
//                     disabled={!hasAccounts || !contribution.accountId}
//                   />
//                   {selectedAccount && (
//                     <div className="text-xs mt-1">
//                       <span className={`${
//                         contribution.amount && parseFloat(contribution.amount) > selectedAccount.balance
//                           ? 'text-red-500'
//                           : 'text-gray-500'
//                       }`}>
//                         Max available: {formatBalance(selectedAccount.balance || 0)}
//                       </span>
//                     </div>
//                   )}
//                 </div>
                
//                 <div className="mb-3">
//                   <label 
//                     className="block text-sm font-medium text-gray-700 mb-1"
//                     htmlFor={generateElementId(contribution.id, 'frequency')}
//                   >
//                     Frequency
//                   </label>
//                   <select
//                     id={generateElementId(contribution.id, 'frequency')}
//                     value={contribution.frequency}
//                     onChange={(e) => updateContribution(contribution.id, 'frequency', e.target.value)}
//                     className="w-full border border-gray-300 px-3 py-2 rounded-md"
//                     disabled={!hasAccounts || !contribution.accountId}
//                   >
//                     <option value="DAILY">Daily</option>
//                     <option value="WEEKLY">Weekly</option>
//                     <option value="MONTHLY">Monthly</option>
//                     <option value="QUARTERLY">Quarterly</option>
//                   </select>
//                 </div>
                
//                 {contribution.frequency === 'WEEKLY' && (
//                   <div className="mb-3">
//                     <label 
//                       className="block text-sm font-medium text-gray-700 mb-1"
//                       htmlFor={generateElementId(contribution.id, 'dayOfWeek')}
//                     >
//                       Day of Week
//                     </label>
//                     <select
//                       id={generateElementId(contribution.id, 'dayOfWeek')}
//                       value={contribution.dayOfWeek}
//                       onChange={(e) => updateContribution(contribution.id, 'dayOfWeek', e.target.value)}
//                       className="w-full border border-gray-300 px-3 py-2 rounded-md"
//                       disabled={!hasAccounts || !contribution.accountId}
//                     >
//                       <option value="MONDAY">Monday</option>
//                       <option value="TUESDAY">Tuesday</option>
//                       <option value="WEDNESDAY">Wednesday</option>
//                       <option value="THURSDAY">Thursday</option>
//                       <option value="FRIDAY">Friday</option>
//                       <option value="SATURDAY">Saturday</option>
//                       <option value="SUNDAY">Sunday</option>
//                     </select>
//                   </div>
//                 )}
                
//                 {contribution.frequency === 'MONTHLY' && (
//                   <div className="mb-3">
//                     <label 
//                       className="block text-sm font-medium text-gray-700 mb-1"
//                       htmlFor={generateElementId(contribution.id, 'dayOfMonth')}
//                     >
//                       Day of Month
//                     </label>
//                     <select
//                       id={generateElementId(contribution.id, 'dayOfMonth')}
//                       value={contribution.dayOfMonth}
//                       onChange={(e) => updateContribution(contribution.id, 'dayOfMonth', e.target.value)}
//                       className="w-full border border-gray-300 px-3 py-2 rounded-md"
//                       disabled={!hasAccounts || !contribution.accountId}
//                     >
//                       {generateDayOptions()}
//                     </select>
//                   </div>
//                 )}
                
//                 <div className="mb-3">
//                   <label 
//                     className="block text-sm font-medium text-gray-700 mb-1"
//                     htmlFor={generateElementId(contribution.id, 'startDate')}
//                   >
//                     Start Date
//                   </label>
//                   <input
//                     id={generateElementId(contribution.id, 'startDate')}
//                     type="date"
//                     value={contribution.startDate}
//                     onChange={(e) => updateContribution(contribution.id, 'startDate', e.target.value)}
//                     className="w-full border border-gray-300 px-3 py-2 rounded-md"
//                     disabled={!hasAccounts || !contribution.accountId}
//                   />
//                 </div>
                
//                 <div className="mb-3 col-span-2">
//                   <div className="flex items-center mb-2">
//                     <input
//                       type="checkbox"
//                       id={generateElementId(contribution.id, 'enableEndDate')}
//                       checked={contribution.isEndDateEnabled}
//                       onChange={() => updateContribution(contribution.id, 'isEndDateEnabled', !contribution.isEndDateEnabled)}
//                       className="h-4 w-4 text-blue-600 border-gray-300 rounded"
//                       disabled={!hasAccounts || !contribution.accountId}
//                     />
//                     <label 
//                       htmlFor={generateElementId(contribution.id, 'enableEndDate')} 
//                       className="ml-2 block text-sm text-gray-700"
//                     >
//                       Set End Date
//                     </label>
//                   </div>
                  
//                   {contribution.isEndDateEnabled && (
//                     <input
//                       type="date"
//                       value={contribution.endDate}
//                       onChange={(e) => updateContribution(contribution.id, 'endDate', e.target.value)}
//                       className="w-full border border-gray-300 px-3 py-2 rounded-md"
//                       disabled={!hasAccounts || !contribution.accountId}
//                       min={contribution.startDate}
//                     />
//                   )}
//                 </div>
                
//                 {/* Account impact summary */}
//                 {selectedAccount && isAmountValid && (
//                   <div className="col-span-2 text-xs bg-gray-50 p-2 rounded-md">
//                     <div className="font-medium mb-1">Account Impact:</div>
//                     <div>
//                       {selectedAccount.name}: {formatBalance(selectedAccount.balance)} → {formatBalance(selectedAccount.balance - parseFloat(contribution.amount))}
//                     </div>
//                   </div>
//                 )}
//               </div>
//             </div>
//           );
//         })}
        
//         {/* Add More Button */}
//         {hasAccounts && recurringContributions.length < (accounts?.length || 0) && (
//           <button
//             type="button"
//             onClick={addContribution}
//             className="w-full py-2 mb-4 border border-dashed border-blue-300 text-blue-600 rounded-md hover:bg-blue-50 flex items-center justify-center"
//           >
//             <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
//               <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
//             </svg>
//             Add Another Account Contribution
//           </button>
//         )}
        
//         {/* Summary section - shows total impact of contributions */}
//         {hasAccounts && recurringContributions.some(c => 
//           c.accountId && c.amount && parseFloat(c.amount) > 0
//         ) && (
//           <div className="p-4 bg-blue-50 border border-blue-200 rounded-md text-blue-700 mb-4">
//             <h4 className="font-medium mb-2">Total Contribution Summary</h4>
//             <div>
//               <div><span className="font-medium">Estimated Monthly Contribution:</span> {formatBalance(goalImpact.monthlyContribution)}</div>
//               <div><span className="font-medium">Current Goal Progress:</span> {formatBalance(goal.currentAmount)} of {formatBalance(goal.goalAmount)} ({Math.round((goal.currentAmount / goal.goalAmount) * 100)}%)</div>
//               <div><span className="font-medium">Estimated Completion Date:</span> {goalImpact.estimatedCompletionDate}</div>
//             </div>
//           </div>
//         )}
        
//         {/* Add scroll area for account selection if many accounts */}
//         {hasAccounts && accounts.length > 5 && (
//           <div className="mb-4">
//             <h4 className="font-medium mb-2">Available Accounts</h4>
//             <div className="max-h-40 overflow-y-auto border border-gray-200 rounded-md p-2">
//               {accounts.map(account => (
//                 <div key={account.id} className="mb-1 p-1 hover:bg-gray-50 rounded">
//                   <div className="flex justify-between">
//                     <span>{account.name}</span>
//                     <span>{formatBalance(account.balance)}</span>
//                   </div>
//                 </div>
//               ))}
//             </div>
//           </div>
//         )}
//       </div>
//     </div>
    
//     <div className="flex justify-end space-x-3 mt-4 pt-4 border-t border-gray-200">
//       <button
//         onClick={onClose}
//         className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
//       >
//         Cancel
//       </button>
//       <button
//         onClick={handleSubmit}
//         className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300 disabled:cursor-not-allowed"
//         disabled={
//           !hasAccounts || 
//           !recurringContributions.some(c => 
//             c.accountId && 
//             c.amount && 
//             parseFloat(c.amount) > 0 &&
//             parseFloat(c.amount) <= (getAccountById(c.accountId)?.balance || 0)
//           )
//         }
//       >
//         Set Up All Recurring Contributions
//       </button>
//     </div>
//   </div>
// );
// };

// export default RecurringGoal;

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  setupRecurringContributionRequest, 
  fetchGoalRecurringContributionsRequest,
  updateRecurringContributionRequest,
  deleteRecurringContributionRequest,
  triggerContributionRequest
} from '../../../../logic/redux/recurringSlice';
import { fetchAccountDetails } from '../../../../logic/redux/accountsDashboardSlice';

const RecurringGoal = ({ goal, onClose }) => {
  const dispatch = useDispatch();
  
  // Get accounts from Redux store
  const { accounts, loading: accountsLoading } = useSelector(state => state.accounts);
  
  // Get existing recurring contributions for this goal
  const { goalRecurringContributions, loading: recurringLoading, error } = useSelector(state => state.recurring);
  const existingContributions = goalRecurringContributions[goal.id] || [];
  
  // State for multiple recurring contributions
  const [recurringContributions, setRecurringContributions] = useState([
    {
      id: 1,
      accountId: '',
      amount: '',
      frequency: 'MONTHLY',
      dayOfMonth: '1',
      dayOfWeek: 'MONDAY',
      startDate: new Date().toISOString().split('T')[0],
      endDate: '',
      isEndDateEnabled: false,
      isExisting: false // Flag to identify new vs existing contributions
    }
  ]);
  
  // Remaining goal amount
  const [remainingGoalAmount, setRemainingGoalAmount] = useState(0);
  
  // Edit mode for existing contributions
  const [editMode, setEditMode] = useState(false);
  
  // Format currency
  const formatBalance = (balance) => {
    if (balance === undefined || balance === null) return '$0';
    return balance.toLocaleString('en-US', { style: 'currency', currency: 'USD' });
  };
  
  // Fetch accounts and existing recurring contributions when component mounts
  useEffect(() => {
    dispatch(fetchAccountDetails());
    dispatch(fetchGoalRecurringContributionsRequest(goal.id));
  }, [dispatch, goal.id]);
  
  // Calculate remaining goal amount on initial load and set default account
  useEffect(() => {
    // Calculate remaining goal amount
    const remaining = Math.max(goal.goalAmount - goal.currentAmount, 0);
    setRemainingGoalAmount(remaining);
    
    // Set default account if accounts are loaded from Redux
    if (accounts && accounts.length > 0 && recurringContributions.length === 1 && !recurringContributions[0].accountId) {
      const defaultAccountId = accounts[0].id.toString();
      
      setRecurringContributions(prevContribs => {
        const updatedContribs = [...prevContribs];
        if (updatedContribs[0]) {
          updatedContribs[0] = {
            ...updatedContribs[0],
            accountId: defaultAccountId
          };
        }
        return updatedContribs;
      });
    }
  }, [goal, accounts]);
  
  // Load existing contributions when they're fetched from the API
  useEffect(() => {
    if (existingContributions.length > 0 && !editMode) {
      // Map existing contributions to the format our component expects
      const formattedExistingContributions = existingContributions.map(contrib => ({
        dbId: contrib.id, // Store the database ID separately
        id: contrib.id,
        accountId: contrib.accountId.toString(),
        amount: contrib.amount.toString(),
        frequency: contrib.frequency,
        dayOfMonth: contrib.dayOfMonth?.toString() || '1',
        dayOfWeek: contrib.dayOfWeek || 'MONDAY',
        startDate: contrib.startDate,
        endDate: contrib.endDate || '',
        isEndDateEnabled: !!contrib.endDate,
        isExisting: true
      }));
      
      // If there are existing contributions, use them instead of our default
      if (formattedExistingContributions.length > 0) {
        setRecurringContributions(formattedExistingContributions);
        setEditMode(true);
      }
    }
  }, [existingContributions, editMode]);
  
  // Get account by ID
  const getAccountById = (accountId) => {
    if (!accounts || !accountId) return null;
    return accounts.find(acc => acc.id.toString() === accountId);
  };
  
  // Add a new recurring contribution
  const addContribution = () => {
    const newId = Math.max(...recurringContributions.map(c => c.id), 0) + 1;
    
    // Find an account that hasn't been used yet
    const usedAccountIds = recurringContributions.map(c => c.accountId);
    const availableAccount = accounts?.find(acc => 
      !usedAccountIds.includes(acc.id.toString())
    );
    
    setRecurringContributions([
      ...recurringContributions, 
      {
        id: newId,
        accountId: availableAccount ? availableAccount.id.toString() : '',
        amount: '',
        frequency: 'MONTHLY',
        dayOfMonth: '1',
        dayOfWeek: 'MONDAY',
        startDate: new Date().toISOString().split('T')[0],
        endDate: '',
        isEndDateEnabled: false,
        isExisting: false
      }
    ]);
  };
  
  // Remove a recurring contribution
  const removeContribution = (id) => {
    const contributionToRemove = recurringContributions.find(c => c.id === id);
    
    if (contributionToRemove?.isExisting) {
      // If it's an existing contribution, dispatch delete action
      if (window.confirm('Are you sure you want to delete this recurring contribution?')) {
        dispatch(deleteRecurringContributionRequest({
          contributionId: contributionToRemove.dbId,
          goalId: goal.id
        }));
        
        // Remove from local state too
        setRecurringContributions(recurringContributions.filter(c => c.id !== id));
      }
    } else {
      // For new contributions, just remove from state
      if (recurringContributions.length === 1) {
        // Don't remove the last one, just reset it
        setRecurringContributions([{
          id: 1,
          accountId: '',
          amount: '',
          frequency: 'MONTHLY',
          dayOfMonth: '1',
          dayOfWeek: 'MONDAY',
          startDate: new Date().toISOString().split('T')[0],
          endDate: '',
          isEndDateEnabled: false,
          isExisting: false
        }]);
      } else {
        setRecurringContributions(recurringContributions.filter(c => c.id !== id));
      }
    }
  };
  
  // Update a specific field for a contribution
  const updateContribution = (id, field, value) => {
    setRecurringContributions(prevContribs => {
      return prevContribs.map(contrib => {
        if (contrib.id === id) {
          return { ...contrib, [field]: value };
        }
        return contrib;
      });
    });
  };
  
  // Manually trigger a contribution
  const triggerContribution = (contrib) => {
    if (contrib.isExisting && contrib.dbId) {
      if (window.confirm('Are you sure you want to trigger this contribution now?')) {
        dispatch(triggerContributionRequest(contrib.dbId));
      }
    }
  };
  
  // Handle form submission
  const handleSubmit = () => {
    // Validate input for all contributions
    const hasInvalidContribution = recurringContributions.some(contrib => {
      const account = getAccountById(contrib.accountId);
      
      if (!contrib.accountId || !contrib.amount || parseFloat(contrib.amount) <= 0) {
        return true; // Invalid
      }
      
      if (parseFloat(contrib.amount) > (account?.balance || 0)) {
        return true; // Invalid - exceeds balance
      }
      
      return false; // Valid
    });
    
    if (hasInvalidContribution) {
      alert("Please check all contributions. Each must have a valid account and amount.");
      return;
    }
    
    // Process each contribution
    recurringContributions.forEach(contrib => {
      // Create recurring contribution data
      const recurringData = {
        goalId: goal.id,
        accountId: parseInt(contrib.accountId),
        amount: parseFloat(contrib.amount),
        frequency: contrib.frequency,
        startDate: contrib.startDate,
        endDate: contrib.isEndDateEnabled ? contrib.endDate : null
      };
      
      // Add day of month/week based on frequency
      if (contrib.frequency === 'MONTHLY') {
        recurringData.dayOfMonth = parseInt(contrib.dayOfMonth);
      } else if (contrib.frequency === 'WEEKLY') {
        recurringData.dayOfWeek = contrib.dayOfWeek;
      }
      
      if (contrib.isExisting) {
        // Update existing contribution
        dispatch(updateRecurringContributionRequest({
          contributionId: contrib.dbId,
          updateData: recurringData
        }));
      } else {
        // Create new contribution
        dispatch(setupRecurringContributionRequest(recurringData));
      }
    });
    
    onClose();
  };
  
  // Calculate the estimated completion date based on all contributions
  const calculateEstimatedCompletion = () => {
    // Get valid contributions
    const validContributions = recurringContributions.filter(contrib => 
      contrib.accountId && contrib.amount && parseFloat(contrib.amount) > 0
    );
    
    if (validContributions.length === 0 || remainingGoalAmount <= 0) {
      return "Unknown";
    }
    
    // Calculate contribution amounts by frequency
    const dailyAmount = validContributions
      .filter(c => c.frequency === 'DAILY')
      .reduce((sum, c) => sum + parseFloat(c.amount), 0);
      
    const weeklyAmount = validContributions
      .filter(c => c.frequency === 'WEEKLY')
      .reduce((sum, c) => sum + parseFloat(c.amount), 0);
      
    const monthlyAmount = validContributions
      .filter(c => c.frequency === 'MONTHLY')
      .reduce((sum, c) => sum + parseFloat(c.amount), 0);
      
    const quarterlyAmount = validContributions
      .filter(c => c.frequency === 'QUARTERLY')
      .reduce((sum, c) => sum + parseFloat(c.amount), 0);
    
    // Convert all to daily amounts (approximate)
    const estimatedDailyTotal = 
      dailyAmount + 
      (weeklyAmount / 7) + 
      (monthlyAmount / 30) + 
      (quarterlyAmount / 90);
    
    if (estimatedDailyTotal <= 0) {
      return "Unknown";
    }
    
    // Estimate days to completion
    const daysToCompletion = Math.ceil(remainingGoalAmount / estimatedDailyTotal);
    
    // Get earliest start date
    const startDates = validContributions.map(c => new Date(c.startDate));
    const earliestStart = new Date(Math.min(...startDates));
    
    // Calculate completion date
    const completionDate = new Date(earliestStart);
    completionDate.setDate(earliestStart.getDate() + daysToCompletion);
    
    // Check if any end dates come before completion
    const hasEndDateBeforeCompletion = validContributions.some(contrib => {
      if (!contrib.isEndDateEnabled || !contrib.endDate) return false;
      
      const endDate = new Date(contrib.endDate);
      return endDate < completionDate;
    });
    
    if (hasEndDateBeforeCompletion) {
      return "Goal may not be completed before some contributions end";
    }
    
    return completionDate.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };
  
  // Generate options for day of month
  const generateDayOptions = () => {
    const options = [];
    for (let i = 1; i <= 31; i++) {
      options.push(
        <option key={i} value={i}>
          {i}
        </option>
      );
    }
    return options;
  };
  
  // Check if we have any accounts available
  const hasAccounts = accounts && accounts.length > 0;
  
  // Calculate the total impact on goal
  const calculateTotalGoalImpact = () => {
    // Get total monthly contribution (very rough approximation)
    const monthlyEquivalent = recurringContributions.reduce((total, contrib) => {
      if (!contrib.accountId || !contrib.amount || parseFloat(contrib.amount) <= 0) {
        return total;
      }
      
      const amount = parseFloat(contrib.amount);
      
      switch (contrib.frequency) {
        case 'DAILY':
          return total + (amount * 30); // Approx 30 days/month
        case 'WEEKLY':
          return total + (amount * 4.3); // Approx 4.3 weeks/month
        case 'MONTHLY':
          return total + amount;
        case 'QUARTERLY':
          return total + (amount / 3); // 1/3 of quarterly per month
        default:
          return total;
      }
    }, 0);
    
    return {
      monthlyContribution: monthlyEquivalent,
      estimatedCompletionDate: calculateEstimatedCompletion()
    };
  };
  
  const goalImpact = calculateTotalGoalImpact();
  
  // Generate ID for HTML elements to avoid duplication
  const generateElementId = (contributionId, field) => {
    return `contrib-${contributionId}-${field}`;
  };
  
  // Loading state while accounts or recurring contributions are being fetched
  if (accountsLoading || recurringLoading) {
    return (
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }
  
  // Show error message if there was a problem fetching data
  if (error) {
    return (
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="p-4 bg-red-50 border border-red-200 rounded-md text-red-700">
          <h4 className="font-medium mb-2">Error</h4>
          <p>{error}</p>
          <button 
            onClick={onClose}
            className="mt-3 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Close
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-screen overflow-hidden flex flex-col">
      <h3 className="text-lg font-semibold mb-4">
        {editMode ? 'Manage Recurring Contributions' : 'Set Up Multiple Recurring Contributions'}
      </h3>
      <div className="overflow-y-auto flex-1 pr-2">
        <div className="grid grid-cols-1 gap-4">
          <div className="text-sm mb-4 pb-3 border-b border-gray-200">
            <div><span className="font-medium">Goal:</span> {goal.goalName}</div>
            <div><span className="font-medium">Target:</span> {formatBalance(goal.goalAmount)}</div>
            <div className="flex justify-between">
              <div><span className="font-medium">Current:</span> {formatBalance(goal.currentAmount)}</div>
              <div><span className="font-medium">Remaining:</span> {formatBalance(remainingGoalAmount)}</div>
            </div>
          </div>
          
          {!hasAccounts && (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md text-yellow-700 mb-4">
              <div className="flex items-center">
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <span>No accounts available. Please ensure you have accounts set up.</span>
              </div>
            </div>
          )}
          
          {/* Recurring Contributions Section */}
          {recurringContributions.map((contribution, index) => {
            const selectedAccount = getAccountById(contribution.accountId);
            const isAmountValid = contribution.amount && 
              parseFloat(contribution.amount) > 0 && 
              parseFloat(contribution.amount) <= (selectedAccount?.balance || 0);
              
            return (
              <div 
                key={contribution.id} 
                className={`border rounded-lg p-4 mb-4 ${contribution.isExisting ? 'border-blue-200 bg-blue-50' : 'border-gray-200'}`}
              >
                <div className="flex justify-between items-center mb-3">
                  <h4 className="font-medium">
                    {contribution.isExisting ? 'Existing Contribution' : `Contribution #${index + 1}`}
                  </h4>
                  <div className="flex space-x-2">
                    {contribution.isExisting && (
                      <button
                        onClick={() => triggerContribution(contribution)}
                        className="text-blue-500 hover:text-blue-700"
                        type="button"
                        title="Trigger contribution now"
                      >
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586L7.707 9.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z" clipRule="evenodd" />
                        </svg>
                      </button>
                    )}
                    <button
                      onClick={() => removeContribution(contribution.id)}
                      className="text-red-500 hover:text-red-700"
                      type="button"
                      title={contribution.isExisting ? "Delete recurring contribution" : "Remove"}
                    >
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="mb-3">
                    <label 
                      className="block text-sm font-medium text-gray-700 mb-1"
                      htmlFor={generateElementId(contribution.id, 'account')}
                    >
                      From Account
                    </label>
                    <select
                      id={generateElementId(contribution.id, 'account')}
                      value={contribution.accountId}
                      onChange={(e) => updateContribution(contribution.id, 'accountId', e.target.value)}
                      className="w-full border border-gray-300 px-3 py-2 rounded-md"
                      disabled={!hasAccounts}
                    >
                      <option value="">Select Account</option>
                      {hasAccounts && accounts.map(acc => (
                        <option key={acc.id} value={acc.id.toString()}>
                          {acc.name} ({formatBalance(acc.balance || 0)})
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  <div className="mb-3">
                    <label 
                      className="block text-sm font-medium text-gray-700 mb-1"
                      htmlFor={generateElementId(contribution.id, 'amount')}
                    >
                      Amount ($)
                    </label>
                    <input
                      id={generateElementId(contribution.id, 'amount')}
                      type="number"
                      value={contribution.amount}
                      onChange={(e) => updateContribution(contribution.id, 'amount', e.target.value)}
                      className={`w-full border px-3 py-2 rounded-md ${
                        contribution.amount && parseFloat(contribution.amount) > (selectedAccount?.balance || 0) 
                          ? 'border-red-300' 
                          : 'border-gray-300'
                      }`}
                      placeholder="Enter amount"
                      disabled={!hasAccounts || !contribution.accountId}
                    />
                    {selectedAccount && (
                      <div className="text-xs mt-1">
                        <span className={`${
                          contribution.amount && parseFloat(contribution.amount) > selectedAccount.balance
                            ? 'text-red-500'
                            : 'text-gray-500'
                        }`}>
                          Max available: {formatBalance(selectedAccount.balance || 0)}
                        </span>
                      </div>
                    )}
                  </div>
                  
                  <div className="mb-3">
                    <label 
                      className="block text-sm font-medium text-gray-700 mb-1"
                      htmlFor={generateElementId(contribution.id, 'frequency')}
                    >
                      Frequency
                    </label>
                    <select
                      id={generateElementId(contribution.id, 'frequency')}
                      value={contribution.frequency}
                      onChange={(e) => updateContribution(contribution.id, 'frequency', e.target.value)}
                      className="w-full border border-gray-300 px-3 py-2 rounded-md"
                      disabled={!hasAccounts || !contribution.accountId}
                    >
                      <option value="DAILY">Daily</option>
                      <option value="WEEKLY">Weekly</option>
                      <option value="MONTHLY">Monthly</option>
                      <option value="QUARTERLY">Quarterly</option>
                    </select>
                  </div>
                  
                  {contribution.frequency === 'WEEKLY' && (
                    <div className="mb-3">
                      <label 
                        className="block text-sm font-medium text-gray-700 mb-1"
                        htmlFor={generateElementId(contribution.id, 'dayOfWeek')}
                      >
                        Day of Week
                      </label>
                      <select
                        id={generateElementId(contribution.id, 'dayOfWeek')}
                        value={contribution.dayOfWeek}
                        onChange={(e) => updateContribution(contribution.id, 'dayOfWeek', e.target.value)}
                        className="w-full border border-gray-300 px-3 py-2 rounded-md"
                        disabled={!hasAccounts || !contribution.accountId}
                      >
                        <option value="MONDAY">Monday</option>
                        <option value="TUESDAY">Tuesday</option>
                        <option value="WEDNESDAY">Wednesday</option>
                        <option value="THURSDAY">Thursday</option>
                        <option value="FRIDAY">Friday</option>
                        <option value="SATURDAY">Saturday</option>
                        <option value="SUNDAY">Sunday</option>
                      </select>
                    </div>
                  )}
                  
                  {contribution.frequency === 'MONTHLY' && (
                    <div className="mb-3">
                      <label 
                        className="block text-sm font-medium text-gray-700 mb-1"
                        htmlFor={generateElementId(contribution.id, 'dayOfMonth')}
                      >
                        Day of Month
                      </label>
                      <select
                        id={generateElementId(contribution.id, 'dayOfMonth')}
                        value={contribution.dayOfMonth}
                        onChange={(e) => updateContribution(contribution.id, 'dayOfMonth', e.target.value)}
                        className="w-full border border-gray-300 px-3 py-2 rounded-md"
                        disabled={!hasAccounts || !contribution.accountId}
                      >
                        {generateDayOptions()}
                      </select>
                    </div>
                  )}
                  
                  <div className="mb-3">
                    <label 
                      className="block text-sm font-medium text-gray-700 mb-1"
                      htmlFor={generateElementId(contribution.id, 'startDate')}
                    >
                      Start Date
                    </label>
                    <input
                      id={generateElementId(contribution.id, 'startDate')}
                      type="date"
                      value={contribution.startDate}
                      onChange={(e) => updateContribution(contribution.id, 'startDate', e.target.value)}
                      className="w-full border border-gray-300 px-3 py-2 rounded-md"
                      disabled={!hasAccounts || !contribution.accountId}
                    />
                  </div>
                  
                  <div className="mb-3 col-span-2">
                    <div className="flex items-center mb-2">
                      <input
                        type="checkbox"
                        id={generateElementId(contribution.id, 'enableEndDate')}
                        checked={contribution.isEndDateEnabled}
                        onChange={() => updateContribution(contribution.id, 'isEndDateEnabled', !contribution.isEndDateEnabled)}
                        className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                        disabled={!hasAccounts || !contribution.accountId}
                      />
                      <label 
                        htmlFor={generateElementId(contribution.id, 'enableEndDate')} 
                        className="ml-2 block text-sm text-gray-700"
                      >
                        Set End Date
                      </label>
                    </div>
                    
                    {contribution.isEndDateEnabled && (
                      <input
                        type="date"
                        value={contribution.endDate}
                        onChange={(e) => updateContribution(contribution.id, 'endDate', e.target.value)}
                        className="w-full border border-gray-300 px-3 py-2 rounded-md"
                        disabled={!hasAccounts || !contribution.accountId}
                        min={contribution.startDate}
                      />
                    )}
                  </div>
                  
                  {/* Account impact summary */}
                  {selectedAccount && isAmountValid && (
                    <div className="col-span-2 text-xs bg-gray-50 p-2 rounded-md">
                      <div className="font-medium mb-1">Account Impact:</div>
                      <div>
                        {selectedAccount.name}: {formatBalance(selectedAccount.balance)} → {formatBalance(selectedAccount.balance - parseFloat(contribution.amount))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
          
          {/* Add More Button */}
          {hasAccounts && recurringContributions.length < (accounts?.length || 0) && (
            <button
              type="button"
              onClick={addContribution}
              className="w-full py-2 mb-4 border border-dashed border-blue-300 text-blue-600 rounded-md hover:bg-blue-50 flex items-center justify-center"
            >
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
              Add Another Contribution
            </button>
          )}
          
          {/* Goal Impact Summary */}
          {hasAccounts && recurringContributions.some(c => c.accountId && c.amount && parseFloat(c.amount) > 0) && (
            <div className="border border-green-200 bg-green-50 rounded-lg p-4 mb-4">
              <h4 className="font-medium text-green-800 mb-2">Goal Impact Summary</h4>
              <div className="text-sm text-green-700">
                <div className="mb-1">
                  <span className="font-medium">Monthly equivalent contribution:</span> {formatBalance(goalImpact.monthlyContribution)}
                </div>
                <div>
                  <span className="font-medium">Estimated completion date:</span> {goalImpact.estimatedCompletionDate}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Action Buttons */}
      <div className="pt-4 flex justify-end space-x-3 border-t border-gray-200 mt-4">
        <button
          type="button"
          onClick={onClose}
          className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          type="button"
          onClick={handleSubmit}
          disabled={!hasAccounts || recurringContributions.every(c => !c.accountId || !c.amount)}
          className={`px-4 py-2 rounded-md ${
            !hasAccounts || recurringContributions.every(c => !c.accountId || !c.amount)
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {editMode ? 'Save Changes' : 'Set Up Contributions'}
        </button>
      </div>
    </div>
  );
};

export default RecurringGoal;