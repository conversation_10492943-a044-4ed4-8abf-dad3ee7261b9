import { combineEpics, ofType } from 'redux-observable';
import { from, of } from 'rxjs';
import { catchError, mergeMap, map, tap } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';

import {
  fetchNotifications,
  fetchNotificationsSuccess,
  fetchNotificationsFailure,
  fetchNotificationRules,
  fetchNotificationRulesSuccess,
  fetchNotificationRulesFailure,
  markNotificationAsRead,
  markNotificationAsReadSuccess,
  markNotificationAsReadFailure,
  clearAllNotifications,
  clearAllNotificationsSuccess,
  clearAllNotificationsFailure,
  saveNotificationRule,
  saveNotificationRuleSuccess,
  saveNotificationRuleFailure,
  deleteNotification,
  deleteNotificationSuccess,
  deleteNotificationFailure
} from '../redux/notificationSlice';

// Epic for fetching notifications
export const fetchNotificationsEpic = (action$) =>
  action$.pipe(
    ofType(fetchNotifications.type),
    mergeMap((action) =>
      from(axiosInstance.get(`pennypal/api/v1/notifications/fetch/${action.payload.userId}`)).pipe(
        map((response) => fetchNotificationsSuccess(response.data)),
        catchError((error) => of(fetchNotificationsFailure(error.message)))
      )
    )
  );

// Epic for fetching notification rules
export const fetchNotificationRulesEpic = (action$) =>
  action$.pipe(
    ofType(fetchNotificationRules.type),
    mergeMap((action) =>
      from(axiosInstance.get(`pennypal/api/v1/user-notification-rules/user/${action.payload.userId}`)).pipe(
        map((response) => fetchNotificationRulesSuccess(response.data)),
        catchError((error) =>
          of(fetchNotificationRulesFailure(error.message))
        )
      )
    )
  );

// Epic for marking a notification as read
export const markNotificationAsReadEpic = (action$) =>
  action$.pipe(
    ofType(markNotificationAsRead.type),
    mergeMap((action) =>
      from(
        axiosInstance.put(`pennypal/api/v1/notifications/mark-read/${action.payload.notificationId}`)
      ).pipe(
        map(() => markNotificationAsReadSuccess({ notificationId: action.payload.notificationId })),
        catchError((error) =>
          of(markNotificationAsReadFailure(error.message))
        )
      )
    )
  );

// Epic for clearing all notifications
export const clearAllNotificationsEpic = (action$) =>
  action$.pipe(
    ofType(clearAllNotifications.type),
    mergeMap((action) =>
      from(
        axiosInstance.delete(`pennypal/api/v1/notifications/delete/${action.payload.userId}`)
      ).pipe(
        map(() => clearAllNotificationsSuccess()),
        catchError((error) =>
          of(clearAllNotificationsFailure(error.message))
        )
      )
    )
  );

// Epic for saving a notification rule
export const saveNotificationRuleEpic = (action$) =>
  action$.pipe(
    ofType(saveNotificationRule.type),
    mergeMap((action) => {
      // Changes to handle multiple rule saves at once
      const rules = Array.isArray(action.payload) ? action.payload : [action.payload];
      return from(
        Promise.all(
          rules.map((rule) =>
            axiosInstance.put('pennypal/api/v1/user-notification-rules/edit', rule).then((response) => ({
              ruleId: rule.ruleId,
              data: response.data
            }))
          )
        )
      ).pipe(
        map((responses) =>
          saveNotificationRuleSuccess(
            responses.map((response) => response.data)
          )
        ),
        catchError((error) => {
          return of(saveNotificationRuleFailure(error.message));
        })
      );
    })
  );

// Epic for deleting a notification
export const deleteNotificationEpic = (action$) =>
  action$.pipe(
    ofType(deleteNotification.type),
    mergeMap((action) =>
      from(
        axiosInstance.delete(`pennypal/api/v1/notifications/delete_one/${action.payload.notificationId}`)
      ).pipe(
        map(() => deleteNotificationSuccess({ notificationId: action.payload.notificationId })),
        catchError((error) =>
          of(deleteNotificationFailure(error.message))
        )
      )
    )
  );

export const rootNotificationsEpic = combineEpics(
  fetchNotificationsEpic,
  fetchNotificationRulesEpic,
  markNotificationAsReadEpic,
  clearAllNotificationsEpic,
  saveNotificationRuleEpic,
  deleteNotificationEpic
);