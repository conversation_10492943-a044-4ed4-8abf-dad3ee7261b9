// Updated PortfolioChart Component - Uses Redux Data with Smooth Curves and PaymentLoader
import React from 'react';
import { useSelector } from 'react-redux';
import { selectCurrentType, selectCurrentPeriod, formatYAxisValue } from '../../../../../../logic/redux/accountChartSlice'; 

const PortfolioChart = ({ darkMode, accounts, selectedTimePeriod, accountType = 'cash', chartView = 'bar', selectedAccount = 'all', currentTheme }) => {
  // Get chart data from Redux store
  const chartData = useSelector(state => state.accountChart?.chartData || []);
  const hasNoDataForCurrentType = useSelector(state => state.accountChart?.hasNoDataForCurrentType || false);
  const hasNoAccounts = useSelector(state => state.accountChart?.hasNoAccounts || false);
  const selectedChartType = accountType; 
  const loading = useSelector(state => state.accountChart?.loading || false);
  const currentType = useSelector(selectCurrentType);
  const currentPeriod = useSelector(selectCurrentPeriod);

  // Only render the chart when the data matches the current dropdown selection and not loading
  if (loading || currentType !== accountType || currentPeriod !== selectedTimePeriod) {
    return (
      <div className="flex items-center justify-center h-64">
        <span className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></span>
        <span className="ml-4 text-lg">Loading chart...</span>
      </div>
    );
  }

  // Process the Redux data for chart display
  const processChartData = () => {
    if (!chartData || chartData.length === 0) {
      return [];
    }

    // Map the Redux data to chart format
    return chartData.map(item => ({
      month: item.name || 'N/A',
      value: Math.abs(item.balance || 0), // Use absolute value for display
      cash: selectedAccount === 'all' ? Math.abs(item.balance || 0) * 0.4 : Math.abs(item.balance || 0),
      investments: selectedAccount === 'all' ? Math.abs(item.balance || 0) * 0.35 : 0,
      credit: selectedAccount === 'all' ? Math.abs(item.balance || 0) * 0.15 : 0,
      loans: selectedAccount === 'all' ? Math.abs(item.balance || 0) * 0.1 : 0,
      originalBalance: item.balance, // Keep original value for tooltips
      isMockData: item.isMockData || false
    }));
  };
  const formatValue = (val) => {
    return val.toLocaleString(undefined, { maximumFractionDigits: 0 });
  };
  const processedData = processChartData();
  const maxValue = processedData.length > 0 ? Math.max(...processedData.map(d => d.value)) : 0;
  const totalValue = processedData.length > 0 ? processedData[processedData.length - 1]?.value || 0 : 0;

  // Helper function to create smooth curve path
  const createSmoothPath = (points) => {
    if (points.length < 2) return '';
    
    let path = `M ${points[0].x} ${points[0].y}`;
    
    for (let i = 1; i < points.length; i++) {
      const prev = points[i - 1];
      const current = points[i];
      const next = points[i + 1];
      
      // Calculate control points for smooth curve
      const tension = 0.3; // Adjust this value to control curve smoothness (0-1)
      
      let cp1x, cp1y, cp2x, cp2y;
      
      if (i === 1) {
        // First curve
        cp1x = prev.x + (current.x - prev.x) * tension;
        cp1y = prev.y;
      } else {
        const prevPrev = points[i - 2];
        cp1x = prev.x + (current.x - prevPrev.x) * tension;
        cp1y = prev.y + (current.y - prevPrev.y) * tension;
      }
      
      if (i === points.length - 1) {
        // Last curve
        cp2x = current.x - (current.x - prev.x) * tension;
        cp2y = current.y;
      } else {
        cp2x = current.x - (next.x - prev.x) * tension;
        cp2y = current.y - (next.y - prev.y) * tension;
      }
      
      path += ` C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${current.x} ${current.y}`;
    }
    
    return path;
  };



  // No data state
  if (processedData.length === 0 && !hasNoAccounts) {
    return (
      <div className={`${
        darkMode 
          ? 'bg-gradient-to-br from-slate-800/50 to-slate-700/30' 
          : 'bg-gradient-to-br from-white to-green-50/10'
      } rounded-2xl p-6 backdrop-blur-sm border ${
        darkMode ? 'border-slate-700/50' : 'border-green-200/50'
      } shadow-xl hover:shadow-2xl transition-all duration-300`}>
        
        <div className="flex flex-col justify-center items-center h-64">
          <div className={`text-6xl mb-4 ${darkMode ? 'text-slate-600' : 'text-slate-400'}`}>📊</div>
          <h3 className={`text-xl font-semibold mb-2 ${darkMode ? 'text-white' : 'text-slate-800'}`}>
            No Data Available
          </h3>
          <p className={`text-sm ${darkMode ? 'text-slate-400' : 'text-slate-600'} text-center`}>
            {hasNoDataForCurrentType 
              ? `No ${selectedChartType} accounts found for the selected time period.`
              : 'No data available for the selected criteria.'
            }
          </p>
        </div>
      </div>
    );
  }

  // Get account type label for display
  const getAccountTypeLabel = () => {
    const typeLabels = {
      'cash': 'Cash Accounts',
      'creditCard': 'Credit Cards',
      'loan': 'Loans', 
      'investment': 'Investments',
      'liability': 'Total Liabilities',
      'networth': 'Net Worth'
    };
    return typeLabels[selectedChartType] || 'Portfolio';
  };

  // Render different chart types based on chartView prop
  const renderChart = () => {
    if (chartView === 'line' || chartView === 'area') {
      return renderLineAreaChart();
    }
    return renderBarChart();
  };

  const renderBarChart = () => (
    <div className="absolute inset-0 flex items-end justify-between px-4 py-4 ml-20">
      {processedData.map((data, index) => {
        const barHeight = maxValue > 0 ? (data.value / maxValue) * 100 : 0;
        
        return (
          <div key={index} className="flex flex-col items-center space-y-2 group">
            {/* Stacked Bar */}
            <div className="relative flex flex-col-reverse" style={{ height: '220px' }}>
              <div 
                className="w-12 rounded-t-lg overflow-hidden shadow-lg group-hover:shadow-xl transition-all duration-300"
                style={{ height: `${barHeight}%` }}
              >
                {selectedAccount === 'all' && selectedChartType === 'cash' ? (
                  <>
                    {/* Cash */}
                    <div 
                      className="transition-all duration-300"
                      style={{ 
                        backgroundColor: currentTheme.colors.primary,
                        height: `${data.cash / data.value * 100}%` 
                      }}
                    ></div>
                    {/* Investments */}
                    <div 
                      className="transition-all duration-300"
                      style={{ 
                        backgroundColor: currentTheme.colors.accent,
                        height: `${data.investments / data.value * 100}%` 
                      }}
                    ></div>
                    {/* Credit */}
                    <div 
                      className="transition-all duration-300"
                      style={{ 
                        backgroundColor: currentTheme.colors.secondary,
                        height: `${data.credit / data.value * 100}%` 
                      }}
                    ></div>
                    {/* Loans */}
                    <div 
                      className="transition-all duration-300"
                      style={{ 
                        backgroundColor: currentTheme.colors.success,
                        height: `${data.loans / data.value * 100}%` 
                      }}
                    ></div>
                  </>
                ) : (
                  /* Single account type bar */
                  <div 
                    className="transition-all duration-300"
                    style={{ 
                      backgroundColor: currentTheme.colors.primary,
                      height: '100%' 
                    }}
                  ></div>
                )}
              </div>
              
              {/* Hover Tooltip */}
              <div className={`absolute -top-12 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-all duration-200 ${
                darkMode ? 'bg-slate-800 text-white' : 'bg-white text-slate-800'
              } px-3 py-2 rounded-lg shadow-xl border ${
                darkMode ? 'border-slate-600' : 'border-slate-200'
              } text-xs font-medium whitespace-nowrap z-20 pointer-events-none`}>
                ${data.originalBalance?.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                {data.isMockData && (
                  <div className="text-xs opacity-75 mt-1">Demo Data</div>
                )}
              </div>
            </div>
            
            {/* Month Label */}
            <span className={`text-sm font-medium ${darkMode ? 'text-slate-400' : 'text-slate-600'}`}>
              {data.month}
            </span>
          </div>
        );
      })}
    </div>
  );

  const renderLineAreaChart = () => {
    const points = processedData.map((data, index) => {
      const x = (index / (processedData.length - 1)) * 100;
      const y = 100 - ((data.value / maxValue) * 80); // 80% of height for padding
      return { x, y, data };
    });

    // Create smooth curve path
    const smoothPath = createSmoothPath(points);
    
    const areaPath = chartView === 'area' 
      ? `${smoothPath} L 100 100 L 0 100 Z`
      : null;

    return (
      <div className="absolute inset-0 px-4 py-4 ml-8">
        <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
          <defs>
            <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor={currentTheme.colors.primary} stopOpacity="0.3" />
              <stop offset="100%" stopColor={currentTheme.colors.primary} stopOpacity="0.05" />
            </linearGradient>
          </defs>
          
          {/* Area fill */}
          {chartView === 'area' && (
            <path
              d={areaPath}
              fill="url(#areaGradient)"
              className="transition-all duration-300"
            />
          )}
          
          {/* Smooth Line with reduced thickness */}
          <path
            d={smoothPath}
            fill="none"
            stroke={currentTheme.colors.primary}
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="transition-all duration-300"
          />
          
          {/* Data points with hover areas */}
          {points.map((point, index) => (
            <g key={index}>
              {/* Invisible hover area */}
              <circle
                cx={point.x}
                cy={point.y}
                r="8"
                fill="transparent"
                className="cursor-pointer"
              />
              {/* Visible data point - smaller and subtle */}
              <circle
                cx={point.x}
                cy={point.y}
                r="0.8"
                fill={currentTheme.colors.primary}
                className="transition-all duration-300 hover:r-1.5 cursor-pointer"
                style={{ filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.1))' }}
              />
              {/* Hover tooltip */}
              <g className="opacity-0 hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                <rect
                  x={point.x - 15}
                  y={point.y - 25}
                  width="30"
                  height="20"
                  rx="4"
                  fill={darkMode ? '#1e293b' : '#ffffff'}
                  stroke={darkMode ? '#475569' : '#e2e8f0'}
                  strokeWidth="1"
                />
                <text
                  x={point.x}
                  y={point.y - 12}
                  textAnchor="middle"
                  className={`text-xs font-medium ${darkMode ? 'fill-white' : 'fill-slate-800'}`}
                  style={{ fontSize: '3px' }}
                >
                  ${Math.round(point.data.originalBalance / 1000)}K
                  {point.data.isMockData && '*'}
                </text>
              </g>
            </g>
          ))}
        </svg>
        
        {/* Month labels */}
        <div className="absolute bottom-0 left-8 right-0 flex justify-between px-4">
          {processedData.map((data, index) => (
            <span key={index} className={`text-sm font-medium ${darkMode ? 'text-slate-400' : 'text-slate-600'}`}>
              {data.month}
            </span>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className={`${
      darkMode 
        ? 'bg-gradient-to-br from-slate-800/50 to-slate-700/30' 
        : 'bg-gradient-to-br from-white to-green-50/10'
    } rounded-2xl p-6 backdrop-blur-sm border ${
      darkMode ? 'border-slate-700/50' : 'border-green-200/50'
    } shadow-xl hover:shadow-2xl transition-all duration-300`}>
      
      {/* Header */}
      <div className="flex justify-between items-start mb-6">
        <div>
          <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-slate-800'} mb-2`}>
             {(() => {
    const typeLabels = {
      'cash': 'Cash Accounts',
      'creditCard': 'Credit Cards', 
      'loan': 'Loans',
      'investment': 'Investments',
      'liability': 'Total Liabilities',
      'networth': 'Net Worth'
    };
    return typeLabels[accountType] || 'Portfolio';
  })()}
          </h2>
          <p className={`text-sm ${darkMode ? 'text-slate-400' : 'text-slate-600'}`}>
            {selectedAccount === 'all' 
              ? `Total ${accountType} balance over time` 
              : accounts?.find(acc => (acc.id || acc.accountId) === selectedAccount)?.financialInstName || 'Selected account'
            }
            {hasNoAccounts && (
              <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                Demo Data
              </span>
            )}
          </p>
        </div>
        
        {/* Legend - Only show for all accounts and cash type */}
        {selectedAccount === 'all' && selectedChartType === 'cash' && (
          <div className="flex flex-wrap gap-4 text-sm">
            {[
              { label: 'Cash', color: currentTheme.colors.primary },
              { label: 'Investments', color: currentTheme.colors.accent },
              { label: 'Credit', color: currentTheme.colors.secondary },
              { label: 'Loans', color: currentTheme.colors.success }
            ].map((item, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full`} style={{ backgroundColor: item.color }}></div>
                <span className={`${darkMode ? 'text-slate-300' : 'text-slate-700'} font-medium`}>
                  {item.label}
                </span>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Chart Area */}
      <div className="relative h-64">
        {/* Grid Lines */}
        <div className="absolute inset-0 flex flex-col justify-between ml-8">
          {[0, 1, 2, 3, 4].map(i => (
            <div key={i} className={`border-t ${darkMode ? 'border-slate-700/30' : 'border-slate-200/50'}`}></div>
          ))}
        </div>

        {/* Chart */}
        {renderChart()}

        {/* Y-axis Labels */}
        <div className="absolute left-0 top-0 h-full flex flex-col justify-between py-4 w-8">
          {[4, 3, 2, 1, 0].map(i => {
            const value = (maxValue / 4) * i;
            return (
              <span key={i} className={`text-xs ${darkMode ? 'text-slate-500' : 'text-slate-400'} text-right pr-2`}>
                {formatYAxisValue(value)}
              </span>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default PortfolioChart;