import { ofType } from 'redux-observable';
import { of, from } from 'rxjs';
import { switchMap, catchError, tap, map } from 'rxjs/operators';
import { axiosInstance, setAuthToken, setRefreshToken, plainAxios } from '../api/axiosConfig';
import {
  registrationRequest,
  registrationSuccess,
  registrationFailure,
  signInRequest,
  signInSuccess,
  signInFailure,
  googleSignInRequest,
  googleSignInSuccess,
  googleSignInFailure,
  otpVerifyRequest,
  setUser,
  otpVerifySuccess,
  otpVerifyFailure,
  setPasswordRequest,
  setPasswordSuccess,
  setPasswordFailure,
  updateAuthFromStorage
} from '../redux/authSlice';
import AsyncStorage from '@react-native-async-storage/async-storage';

const JWT_TOKEN_KEY = 'pennypal_jwt_token';
const REFRESH_TOKEN_KEY = 'pennypal_refresh_token';
const USER_DATA_STORAGE_KEY = 'pennypal_user_data';

// Helper function to store auth data
const storeAuthData = async (response) => {
  try {
    const { jwtToken, refreshToken, ...userData } = response.data;
    
    if (jwtToken) {
      await AsyncStorage.setItem(JWT_TOKEN_KEY, jwtToken);
      await setAuthToken(jwtToken);
      console.log('JWT token saved to AsyncStorage');
    }
    
    if (refreshToken) {
      await AsyncStorage.setItem(REFRESH_TOKEN_KEY, refreshToken);
      setRefreshToken(refreshToken);
      console.log('Refresh token saved to AsyncStorage');
    }
    
    // Store complete user data including isPrimary
    const completeUserData = {
      id: userData.id,
      firstName: userData.firstName,
      lastName: userData.lastName,
      emailId: userData.emailId,
      phoneNumber: userData.phoneNumber,
      mobileNumber: userData.mobileNumber,
      isPrimary: userData.isPrimary,
      // Add any other fields returned by your backend
      ...userData
    };
    
    await AsyncStorage.setItem(USER_DATA_STORAGE_KEY, JSON.stringify(completeUserData));
    console.log('User data saved to AsyncStorage:', completeUserData);
    
    return completeUserData;
  } catch (error) {
    console.error('Error storing auth data:', error);
    throw error;
  }
};

export const signInEpic = (action$) =>
  action$.pipe(
    ofType(signInRequest.type),
    switchMap((action) =>
      from(axiosInstance.post('/pennypal/api/v1/auth/signin', action.payload)).pipe(
        switchMap(async (response) => {
          console.log("SignIn Success Response:", response.data);
          
          try {
            const userData = await storeAuthData(response);
            
            // Return success action with properly formatted data
            return signInSuccess({
              user: userData,
              jwtToken: response.data.jwtToken,
              refreshToken: response.data.refreshToken
            });
          } catch (error) {
            console.error('Error processing sign-in response:', error);
            throw error;
          }
        }),
        catchError((error) => {
          console.error('Sign-in error:', error);
          const errorMessage = error.response?.data?.message || 
                              error.response?.data || 
                              'Authentication failed';
          return of(signInFailure(errorMessage));
        })
      )
    )
  );

export const googleSignInEpic = (action$) =>
  action$.pipe(
    ofType(googleSignInRequest.type),
    switchMap((action) =>
      from(axiosInstance.post('/pennypal/api/v1/auth/signin/google', action.payload)).pipe(
        switchMap(async (response) => {
          console.log("Google SignIn Success Response:", response.data);
          
          try {
            const userData = await storeAuthData(response);
            
            return googleSignInSuccess({
              user: userData,
              jwtToken: response.data.jwtToken,
              refreshToken: response.data.refreshToken
            });
          } catch (error) {
            console.error('Error processing Google sign-in response:', error);
            throw error;
          }
        }),
        catchError((error) => {
          console.error('Google sign-in error:', error);
          const errorMessage = error.response?.data?.message || 
                              error.response?.data || 
                              'Google authentication failed';
          return of(googleSignInFailure(errorMessage));
        })
      )
    )
  );

export const otpVerifyEpic = (action$) =>
  action$.pipe(
    ofType(otpVerifyRequest.type),
    switchMap((action) =>
      from(axiosInstance.post('/pennypal/api/v1/auth/signin/otp/verify', action.payload)).pipe(
        switchMap(async (response) => {
          console.log("OTP Verify Success Response:", response.data);
          
          try {
            const userData = await storeAuthData(response);
            
            return otpVerifySuccess({
              user: userData,
              jwtToken: response.data.jwtToken,
              refreshToken: response.data.refreshToken
            });
          } catch (error) {
            console.error('Error processing OTP verification response:', error);
            throw error;
          }
        }),
        catchError((error) => {
          console.error('OTP verification error:', error);
          const errorMessage = error.response?.data?.message || 
                              error.response?.data || 
                              'OTP verification failed';
          return of(otpVerifyFailure(errorMessage));
        })
      )
    )
  );

// Epic for setting password and registering user
export const setPasswordEpic = (action$) =>
  action$.pipe(
    ofType(setPasswordRequest.type),
    switchMap((action) =>
      from(plainAxios.post('/pennypal/api/v1/auth/verify', action.payload)).pipe(
        switchMap(async (response) => {
          console.log("Set Password Success Response:", response.data);
          
          try {
            // For registration, we might not get tokens immediately
            // Just store the user data
            const userData = {
              id: response.data.id,
              firstName: response.data.firstName,
              lastName: response.data.lastName,
              emailId: response.data.emailId,
              phoneNumber: response.data.phoneNumber,
              mobileNumber: response.data.mobileNumber,
              isPrimary: response.data.isPrimary,
              ...response.data
            };
            
            await AsyncStorage.setItem(USER_DATA_STORAGE_KEY, JSON.stringify(userData));
            console.log('Registration user data saved:', userData);
            
            return setPasswordSuccess({
              user: userData,
              message: 'Password set successfully'
            });
          } catch (error) {
            console.error('Error processing set password response:', error);
            throw error;
          }
        }),
        catchError((error) => {
          console.error('Set password error:', error);
          const errorMessage = error.response?.data?.message || 
                              error.response?.data || 
                              'Password set failed';
          return of(setPasswordFailure(errorMessage));
        })
      )
    )
  );

export const signUpEpic = (action$) =>
  action$.pipe(
    ofType(registrationRequest.type),
    switchMap((action) =>
      from(axiosInstance.post('/pennypal/api/v1/auth/register', action.payload)).pipe(
        switchMap(async (response) => {
          console.log("Registration Success Response:", response.data);
          
          try {
            // Registration might return tokens or just user data
            if (response.data.jwtToken) {
              const userData = await storeAuthData(response);
              return registrationSuccess({
                user: userData,
                jwtToken: response.data.jwtToken,
                refreshToken: response.data.refreshToken,
                message: 'Registration successful'
              });
            } else {
              // Just user data without tokens (email verification flow)
              const userData = {
                id: response.data.id,
                firstName: response.data.firstName,
                lastName: response.data.lastName,
                emailId: response.data.emailId,
                phoneNumber: response.data.phoneNumber,
                mobileNumber: response.data.mobileNumber,
                isPrimary: response.data.isPrimary,
                ...response.data
              };
              
              return registrationSuccess({
                user: userData,
                message: response.data.message || 'Registration initiated'
              });
            }
          } catch (error) {
            console.error('Error processing registration response:', error);
            throw error;
          }
        }),
        catchError((error) => {
          console.error('Registration error:', error);
          const errorMessage = error.response?.data?.message || 
                              error.response?.data || 
                              'Registration failed';
          return of(registrationFailure(errorMessage));
        })
      )
    )
  );

// Epic to initialize user from stored token and data
export const initializeUserFromTokenEpic = (action$, state$) => {
  const isAuthenticated = state$.value.auth?.isUserAuthenticated;
  const hasCompleteUserData = state$.value.auth?.user?.id && 
                             state$.value.auth?.user?.isPrimary !== undefined;
  
  if (isAuthenticated && !hasCompleteUserData) {
    // Try to get user data from AsyncStorage first
    return from(AsyncStorage.getItem(USER_DATA_STORAGE_KEY)).pipe(
      switchMap((storedUserData) => {
        if (storedUserData) {
          try {
            const userData = JSON.parse(storedUserData);
            console.log("Retrieved user data from AsyncStorage:", userData);
            return of(setUser(userData));
          } catch (error) {
            console.error("Error parsing stored user data:", error);
          }
        }
        
        // If no stored data, fetch from backend
        return from(axiosInstance.get('/pennypal/api/v1/auth/me')).pipe(
          map((response) => {
            console.log("Retrieved user data from backend:", response.data);
            // Store the fetched data
            AsyncStorage.setItem(USER_DATA_STORAGE_KEY, JSON.stringify(response.data))
              .catch(error => console.error('Error storing fetched user data:', error));
            return setUser(response.data);
          }),
          catchError((error) => {
            console.error("Failed to initialize user from token:", error);
            return of({ type: 'AUTH_INIT_FAILED' });
          })
        );
      }),
      catchError((error) => {
        console.error("Error reading from AsyncStorage:", error);
        return of({ type: 'AUTH_INIT_FAILED' });
      })
    );
  }
  
  return of({ type: 'AUTH_INIT_NOT_NEEDED' });
};

// Enhanced checkAuthStatusEpic to handle all stored data
export const checkAuthStatusEpic = () => {
  return async (dispatch) => {
    try {
      const [token, refreshToken, userDataString] = await Promise.all([
        AsyncStorage.getItem(JWT_TOKEN_KEY),
        AsyncStorage.getItem(REFRESH_TOKEN_KEY),
        AsyncStorage.getItem(USER_DATA_STORAGE_KEY)
      ]);
      
      let userData = null;
      
      if (userDataString) {
        try {
          userData = JSON.parse(userDataString);
          console.log('Loaded user data from AsyncStorage:', userData);
        } catch (e) {
          console.error('Error parsing user data from AsyncStorage:', e);
        }
      }
      
      if (token) {
        // Set tokens in axios headers
        await setAuthToken(token);
        if (refreshToken) {
          setRefreshToken(refreshToken);
        }
        
        dispatch(updateAuthFromStorage({ 
          token, 
          refreshToken, 
          userData 
        }));
        
        console.log('Authentication restored from AsyncStorage');
      } else {
        console.log('No authentication data found in AsyncStorage');
      }
    } catch (error) {
      console.error('Error checking authentication status:', error);
    }
  };
};