// cacheSyncEpic.js - Epics to synchronize cache data with component states

import { ofType, combineEpics } from 'redux-observable';
import { of } from 'rxjs';
import { mergeMap } from 'rxjs/operators';

// Import component actions
import { fetchAccountDetails } from '../redux/accountsDashboardSlice';
import { fetchMonthlyExpensesSuccess, fetchYearlyExpensesSuccess } from '../redux/spendingDashboardSlice';

/**
 * Epic to update account dashboard when user accounts cache is populated
 */
export const syncAccountDashboardFromCacheEpic = (action$) =>
  action$.pipe(
    ofType('cache/fetchUserAccountsSuccess'),
    mergeMap((action) => {
      console.log('🔄 User accounts cache updated, syncing account dashboard');
      return of(fetchAccountDetails.fulfilled(action.payload));
    })
  );

/**
 * Epic to update spending dashboard when budget data cache is populated
 */
export const syncSpendingDashboardFromCacheEpic = (action$) =>
  action$.pipe(
    ofType('cache/fetchBudgetDataSuccess'),
    mergeMap((action) => {
      console.log('🔄 Budget data cache updated, syncing spending dashboard');
      return of(fetchMonthlyExpensesSuccess(action.payload));
    })
  );

/**
 * Epic to update budget summary when cache is populated
 */
export const syncBudgetSummaryFromCacheEpic = (action$) =>
  action$.pipe(
    ofType('cache/fetchBudgetSummarySuccess'),
    mergeMap((action) => {
      console.log('🔄 Budget summary cache updated, syncing budget dashboard');
      // Dispatch to budget dashboard if it exists
      return of({ type: 'budgetDashboard/setBudgetSummary', payload: action.payload });
    })
  );

/**
 * Epic to update transaction components when cache is populated
 */
export const syncTransactionsFromCacheEpic = (action$) =>
  action$.pipe(
    ofType('cache/fetchTransactionsSuccess'),
    mergeMap((action) => {
      console.log('🔄 Transactions cache updated, syncing transaction components');
      // Update transaction dashboard/components
      return of({ type: 'transactions/setTransactions', payload: action.payload });
    })
  );

/**
 * Epic to update recurring transaction components when cache is populated
 */
export const syncRecurringTransactionsFromCacheEpic = (action$) =>
  action$.pipe(
    ofType('cache/fetchRecurringTransactionsSuccess'),
    mergeMap((action) => {
      console.log('🔄 Recurring transactions cache updated, syncing components');
      return of({ type: 'recurringTransactions/setTransactions', payload: action.payload });
    })
  );

/**
 * Epic to update payment components when subscription cache is populated
 */
export const syncPaymentSubscriptionFromCacheEpic = (action$) =>
  action$.pipe(
    ofType('cache/fetchPaymentSubscriptionSuccess'),
    mergeMap((action) => {
      console.log('🔄 Payment subscription cache updated, syncing payment components');
      return of({ type: 'stripeDashboard/setSubscriptionInfo', payload: action.payload });
    })
  );

/**
 * Epic to update payment components when payment methods cache is populated
 */
export const syncPaymentMethodsFromCacheEpic = (action$) =>
  action$.pipe(
    ofType('cache/fetchPaymentMethodsSuccess'),
    mergeMap((action) => {
      console.log('🔄 Payment methods cache updated, syncing payment components');
      return of({ type: 'stripeDashboard/setPaymentMethods', payload: action.payload });
    })
  );

/**
 * Epic to update receipt components when cache is populated
 */
export const syncReceiptsFromCacheEpic = (action$) =>
  action$.pipe(
    ofType(
      'cache/fetchReceiptTransactionIdsSuccess',
      'cache/fetchReceiptItemsSuccess',
      'cache/fetchReceiptSummarySuccess',
      'cache/fetchUserReceiptsSuccess'
    ),
    mergeMap((action) => {
      console.log('🔄 Receipt cache updated, syncing receipt components');
      const actionType = action.type.replace('cache/fetch', '').replace('Success', '');
      return of({ type: `receipts/set${actionType}`, payload: action.payload });
    })
  );

/**
 * Epic to update chatbot components when cache is populated
 */
export const syncChatbotFromCacheEpic = (action$) =>
  action$.pipe(
    ofType('cache/fetchChatbotHistorySuccess'),
    mergeMap((action) => {
      console.log('🔄 Chatbot history cache updated, syncing chatbot components');
      return of({ type: 'chatbot/setChatHistory', payload: action.payload });
    })
  );

/**
 * Epic to handle loading states - set components to loading when cache starts loading
 */
export const syncLoadingStatesEpic = (action$) =>
  action$.pipe(
    ofType(
      'cache/fetchUserAccountsStart',
      'cache/fetchBudgetDataStart',
      'cache/fetchBudgetSummaryStart',
      'cache/fetchTransactionsStart',
      'cache/fetchRecurringTransactionsStart',
      'cache/fetchFutureRecurringTransactionsStart',
      'cache/fetchPaymentSubscriptionStart',
      'cache/fetchPaymentMethodsStart',
      'cache/fetchPaymentProductsStart',
      'cache/fetchPaymentInvoicesStart',
      'cache/fetchUpcomingInvoiceStart',
      'cache/fetchDistinctSubcategoriesStart',
      'cache/fetchAccountIdsStart',
      'cache/fetchAccountBalancesInvestmentStart',
      'cache/fetchAccountBalancesDepositoryStart',
      'cache/fetchAccountBalancesLoanStart',
      'cache/fetchAccountBalancesCreditStart'
    ),
    mergeMap((action) => {
      const cacheType = action.type.replace('cache/fetch', '').replace('Start', '');
      console.log(`🔄 ${cacheType} cache loading started, clearing component loading states`);

      // Return a generic loading state clear action that components can listen to
      return of({ type: 'cache/loadingStateUpdate', payload: { cacheType, loading: false } });
    })
  );

/**
 * Epic to handle error states - set components to error when cache fails
 */
export const syncErrorStatesEpic = (action$) =>
  action$.pipe(
    ofType(
      'cache/fetchUserAccountsFailure',
      'cache/fetchBudgetDataFailure',
      'cache/fetchBudgetSummaryFailure',
      'cache/fetchTransactionsFailure',
      'cache/fetchRecurringTransactionsFailure',
      'cache/fetchPaymentSubscriptionFailure',
      'cache/fetchPaymentMethodsFailure'
    ),
    mergeMap((action) => {
      const cacheType = action.type.replace('cache/fetch', '').replace('Failure', '');
      console.log(`❌ ${cacheType} cache loading failed, updating component error states`);
      
      // Map cache types to component error actions
      const errorActions = {
        'UserAccounts': { type: 'accountsDashboard/setError', payload: action.payload },
        'BudgetData': { type: 'spendingDashboard/setError', payload: action.payload },
        'BudgetSummary': { type: 'budgetDashboard/setError', payload: action.payload },
        'Transactions': { type: 'transactions/setError', payload: action.payload },
        'RecurringTransactions': { type: 'recurringTransactions/setError', payload: action.payload },
        'PaymentSubscription': { type: 'stripeDashboard/setError', payload: action.payload },
        'PaymentMethods': { type: 'stripeDashboard/setError', payload: action.payload }
      };
      
      return of(errorActions[cacheType] || { type: 'cache/unknownErrorState' });
    })
  );

// Combined cache sync epic
export const cacheSyncEpic = combineEpics(
  syncAccountDashboardFromCacheEpic,
  syncSpendingDashboardFromCacheEpic,
  syncBudgetSummaryFromCacheEpic,
  syncTransactionsFromCacheEpic,
  syncRecurringTransactionsFromCacheEpic,
  syncPaymentSubscriptionFromCacheEpic,
  syncPaymentMethodsFromCacheEpic,
  syncReceiptsFromCacheEpic,
  syncChatbotFromCacheEpic,
  syncLoadingStatesEpic,
  syncErrorStatesEpic
);

export default cacheSyncEpic;
