import React, { useState, useEffect } from 'react';
import { DndContext, useSensor, useSensors, PointerSensor } from '@dnd-kit/core';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { SortableContext, rectSortingStrategy } from '@dnd-kit/sortable';
import { useNavigate } from 'react-router-dom'; // Add this import
import RecurringTransactionsCard from './RecurringTransactionsCard';
import RecentTransactions from './RecentTransactions';
import DashboardNetWorthAreaChart from './DashboardNetWorthAreaChart';
import BudgetDashboard from './BudgetDashboard';
import InvestmentCard from './InvestmentCard';
import SpendingDashboard from './SpendingDashboard';
// Import empty state components
import { 
  EmptyNetWorthChart, 
  EmptyRecentTransactions, 
  EmptyRecurringTransactions, 
  EmptyBudgetDashboard, 
  EmptySpendingDashboard, 
  EmptyInvestmentCard 
} from './EmptyStates';

// Color constants
const COLORS = {
  primary: '#7fe029',
  primaryLight: '#eaf7e0',
  primaryDark: '#5db01e',
  white: '#ffffff',
  lightGray: '#f8f9fa',
  textPrimary: '#333333',
  textSecondary: '#666666',
  success: '#2ecc71',
  danger: '#e74c3c',
  shadow: 'rgba(127, 224, 41, 0.15)'
};

// Draggable Card Component with auto-sizing
const DraggableCard = ({ id, children }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });
  
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 10 : 1,
    // Allow content to determine height
  };
  
  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      <div className="rounded-xl shadow-lg bg-white p-4 border-l-4 cursor-move mb-4" style={{ 
        borderLeftColor: COLORS.primary,
        boxShadow: `0 4px 12px ${COLORS.shadow}`,
        // Remove fixed height constraints to allow content to determine size
      }}>
        {children}
      </div>
    </div>
  );
};

// Main Dashboard Component
const Dashboard = () => {
  const navigate = useNavigate(); // Add navigation hook
  
  // State for chart type
  const [activeChartType, setActiveChartType] = useState('networth');
  
  // State to track if the user is a first-time visitor
  const [isFirstTimeUser, setIsFirstTimeUser] = useState(true);
  
  // Updated default card positions with the new cards
  const defaultItems = [
    { id: 'networth', position: 'top-left' },
    { id: 'transactions', position: 'top-right' },
    { id: 'recurring', position: 'middle-left' },
    { id: 'budget', position: 'middle-right' },
    { id: 'spending', position: 'bottom-left' },
    { id: 'investment', position: 'bottom-right' },
  ];
  
  // Load saved card positions from localStorage or use defaults
  const [items, setItems] = useState(() => {
    const savedLayout = localStorage.getItem('dashboardLayout');
    const savedItems = savedLayout ? JSON.parse(savedLayout) : defaultItems;
    
    // Check if this is the first time the user visits
    if (!savedLayout) {
      setIsFirstTimeUser(true);
    } else {
      setIsFirstTimeUser(false);
    }
    
    // Add new cards if they don't exist in saved layout
    if (!savedItems.some(item => item.id === 'spending')) {
      savedItems.push({ id: 'spending', position: 'bottom-left' });
    }
    if (!savedItems.some(item => item.id === 'investment')) {
      savedItems.push({ id: 'investment', position: 'bottom-right' });
    }
    
    return savedItems;
  });
  
  // Save layout to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('dashboardLayout', JSON.stringify(items));
  }, [items]);
  
  // Configure sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );
  
  // Handle drag end event
  const handleDragEnd = (event) => {
    const { active, over } = event;
    
    if (active.id !== over.id) {
      setItems((items) => {
        // Find indices of the dragged and target items
        const activeIndex = items.findIndex(item => item.id === active.id);
        const overIndex = items.findIndex(item => item.id === over.id);
        
        // Create a new array with swapped positions
        const newItems = [...items];
        const activeItem = { ...newItems[activeIndex] };
        const overItem = { ...newItems[overIndex] };
        
        // Swap positions
        const tempPosition = activeItem.position;
        activeItem.position = overItem.position;
        overItem.position = tempPosition;
        
        newItems[activeIndex] = overItem;
        newItems[overIndex] = activeItem;
        
        return newItems;
      });
    }
  };
  
  // Reset layout to default positions
  const resetLayout = () => {
    setItems(defaultItems);
  };
  
  // Navigation handlers
  const handleNavigateToAccounts = () => {
    navigate('/accounts');
  };
  
  const handleNavigateToTransactions = () => {
    navigate('/transactions');
  };
  
  const handleNavigateToRecurring = () => {
    navigate('/recurring');
  };
  
  const handleNavigateToBudget = () => {
    navigate('/budget');
  };
  
  const handleNavigateToSpending = () => {
    navigate('/spending');
  };
  
  const handleNavigateToInvestments = () => {
    navigate('/investments');
  };
  
  // Updated renderComponent function with empty states for first-time users
  const renderComponent = (componentId) => {
    if (isFirstTimeUser) {
      // Render empty states for first-time users with navigation handlers
      switch (componentId) {
        case 'networth':
          return <EmptyNetWorthChart onAddAccounts={handleNavigateToAccounts} />;
        case 'transactions':
          return <EmptyRecentTransactions onAddTransaction={handleNavigateToTransactions} />;
        case 'budget':
          return <EmptyBudgetDashboard onCreateBudget={handleNavigateToBudget} />;
        case 'recurring':
          return <EmptyRecurringTransactions onAddRecurring={handleNavigateToRecurring} />;
        case 'spending':
          return <EmptySpendingDashboard onStartTracking={handleNavigateToSpending} />;
        case 'investment':
          return <EmptyInvestmentCard onLinkInvestments={handleNavigateToInvestments} />;
        default:
          return null;
      }
    } else {
      // Render normal components for returning users
      switch (componentId) {
        case 'networth':
          return (
            <div className="flex flex-col" style={{ fontSize: '0.9rem' }}>
              <DashboardNetWorthAreaChart activeChartType={activeChartType} />
            </div>
          );
        case 'transactions':
          return (
            <div className="flex flex-col" style={{ fontSize: '0.9rem' }}>
              <div className="bg-white rounded-lg">
                <RecentTransactions /> 
              </div>
            </div>
          );
        case 'budget':
          return (
            <div className="flex flex-col" style={{ fontSize: '0.9rem' }}>
              <BudgetDashboard />
            </div>
          );
        case 'recurring':
          return (
            <div style={{ fontSize: '0.9rem' }}>
              <RecurringTransactionsCard />
            </div>
          );
        case 'spending':
          return (
            <div style={{ fontSize: '0.9rem' }}>
              <SpendingDashboard />
            </div>
          );
        case 'investment':
          return (
            <div style={{ fontSize: '0.9rem' }}>
              <InvestmentCard />
            </div>
          );
        default:
          return null;
      }
    }
  };
  
  // Sort items by position for rendering
  const getComponentForPosition = (position) => {
    const item = items.find(item => item.position === position);
    return item ? item.id : null;
  };
  
  // Function to handle onboarding completion
  const handleOnboardingComplete = () => {
    setIsFirstTimeUser(false);
    localStorage.setItem('onboardingComplete', 'true');
  };
  
  return (
    <div style={{ backgroundColor: COLORS.primaryLight, minHeight: "100vh" }} className="w-full p-6">
      <div className="text-3xl font-bold mb-6 text-center" style={{ color: COLORS.primaryDark }}>
        Financial Dashboard
      </div>
      
      {isFirstTimeUser && (
        <div className="mb-6 bg-white p-4 rounded-lg shadow-md text-center">
          <h3 className="text-xl font-semibold mb-2">Welcome to Your Financial Dashboard!</h3>
          <p className="mb-4">Get started by adding your accounts and transactions to see your financial data.</p>
          <button 
            className="px-4 py-2 rounded-md"
            style={{ backgroundColor: COLORS.primary, color: COLORS.white }}
            onClick={handleOnboardingComplete}
          >
            Got it!
          </button>
        </div>
      )}
      
      {/* Grid Layout with DnD Context */}
      <DndContext sensors={sensors} onDragEnd={handleDragEnd}>
        <SortableContext items={items.map(item => item.id)} strategy={rectSortingStrategy}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Top Row */}
            <div>
              <DraggableCard id={getComponentForPosition('top-left')}>
                {renderComponent(getComponentForPosition('top-left'))}
              </DraggableCard>
            </div>
            
            <div>
              <DraggableCard id={getComponentForPosition('top-right')}>
                {renderComponent(getComponentForPosition('top-right'))}
              </DraggableCard>
            </div>
            
            {/* Middle Row */}
            <div>
              <DraggableCard id={getComponentForPosition('middle-left')}>
                {renderComponent(getComponentForPosition('middle-left'))}
              </DraggableCard>
            </div>
            
            <div>
              <DraggableCard id={getComponentForPosition('middle-right')}>
                {renderComponent(getComponentForPosition('middle-right'))}
              </DraggableCard>
            </div>
            
            {/* Bottom Row - New Cards */}
            <div>
              <DraggableCard id={getComponentForPosition('bottom-left')}>
                {renderComponent(getComponentForPosition('bottom-left'))}
              </DraggableCard>
            </div>
            
            <div>
              <DraggableCard id={getComponentForPosition('bottom-right')}>
                {renderComponent(getComponentForPosition('bottom-right'))}
              </DraggableCard>
            </div>
          </div>
        </SortableContext>
      </DndContext>
      
      {isFirstTimeUser && (
        <div className="mt-6 text-center">
          <button 
            className="px-4 py-2 rounded-md"
            style={{ backgroundColor: COLORS.primary, color: COLORS.white }}
            onClick={resetLayout}
          >
            Reset Dashboard Layout
          </button>
        </div>
      )}
    </div>
  );
};

export default Dashboard;