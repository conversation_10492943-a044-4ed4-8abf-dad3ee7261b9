import { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faLink,
  faSync,
  faPlus,
  faRedo,
  faTimes,
  faClock,
  faUniversity,
  faExclamationTriangle, 
  faArrowUp, 
  faArrowDown, 
  faEquals,  
  faSpinner,
  faChartLine,
  faCreditCard,
  faCoins,
  faLandmark,
  faSackDollar,
  faRobot,
  faExchangeAlt
} from "@fortawesome/free-solid-svg-icons";
// Design Palette Dropdown Component (inline to avoid import issues)
const DesignPaletteDropdown = ({ darkMode = false, onThemeChange, className = "" }) => {
  // Predefined color themes
  const colorThemes = {
    default: {
      name: "Default Green",
      primary: "#8BC34A",
      secondary: "#7CB342",
      accent: "#9CCC65",
      success: "#4CAF50",
      background: "#f8fffe",
      darkBackground: "#0f172a",
      cardBg: "#ffffff",
      darkCardBg: "#1e293b",
      text: "#334155",
      darkText: "#f1f5f9",
      border: "#e2e8f0",
      darkBorder: "#475569"
    },
    ocean: {
      name: "Ocean Blue",
      primary: "#0EA5E9",
      secondary: "#0284C7",
      accent: "#38BDF8",
      success: "#06B6D4",
      background: "#f0f9ff",
      darkBackground: "#0c1425",
      cardBg: "#ffffff",
      darkCardBg: "#1e293b",
      text: "#334155",
      darkText: "#f1f5f9",
      border: "#bae6fd",
      darkBorder: "#1e40af"
    },
    sunset: {
      name: "Sunset Orange",
      primary: "#F97316",
      secondary: "#EA580C",
      accent: "#FB923C",
      success: "#F59E0B",
      background: "#fffbf0",
      darkBackground: "#1a0f0a",
      cardBg: "#ffffff",
      darkCardBg: "#2d1b14",
      text: "#334155",
      darkText: "#f1f5f9",
      border: "#fed7aa",
      darkBorder: "#c2410c"
    },
    purple: {
      name: "Royal Purple",
      primary: "#8B5CF6",
      secondary: "#7C3AED",
      accent: "#A78BFA",
      success: "#6366F1",
      background: "#faf8ff",
      darkBackground: "#1a1425",
      cardBg: "#ffffff",
      darkCardBg: "#2d1b3d",
      text: "#334155",
      darkText: "#f1f5f9",
      border: "#d8b4fe",
      darkBorder: "#7c3aed"
    },
    rose: {
      name: "Rose Pink",
      primary: "#EC4899",
      secondary: "#DB2777",
      accent: "#F472B6",
      success: "#F43F5E",
      background: "#fef7f7",
      darkBackground: "#1f0a14",
      cardBg: "#ffffff",
      darkCardBg: "#3d1a2b",
      text: "#334155",
      darkText: "#f1f5f9",
      border: "#fce7f3",
      darkBorder: "#be185d"
    },
    emerald: {
      name: "Emerald Green",
      primary: "#10B981",
      secondary: "#059669",
      accent: "#34D399",
      success: "#6EE7B7",
      background: "#f0fdf4",
      darkBackground: "#0a1f14",
      cardBg: "#ffffff",
      darkCardBg: "#1a3d2b",
      text: "#334155",
      darkText: "#f1f5f9",
      border: "#dcfce7",
      darkBorder: "#047857"
    },
    slate: {
      name: "Modern Slate",
      primary: "#64748B",
      secondary: "#475569",
      accent: "#94A3B8",
      success: "#0F172A",
      background: "#f8fafc",
      darkBackground: "#020617",
      cardBg: "#ffffff",
      darkCardBg: "#0f172a",
      text: "#1e293b",
      darkText: "#f1f5f9",
      border: "#e2e8f0",
      darkBorder: "#475569"
    }
  };

  // Font options
  const fontOptions = [
    { name: "Inter", value: "'Inter', sans-serif", category: "Modern" },
    { name: "Roboto", value: "'Roboto', sans-serif", category: "Clean" },
    { name: "Poppins", value: "'Poppins', sans-serif", category: "Friendly" },
    { name: "Source Sans Pro", value: "'Source Sans Pro', sans-serif", category: "Professional" },
    { name: "Nunito", value: "'Nunito', sans-serif", category: "Rounded" },
    { name: "Lato", value: "'Lato', sans-serif", category: "Classic" },
    { name: "Open Sans", value: "'Open Sans', sans-serif", category: "Readable" },
    { name: "Montserrat", value: "'Montserrat', sans-serif", category: "Bold" }
  ];

  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('themes');
  const [selectedTheme, setSelectedTheme] = useState('default');
  const [customColors, setCustomColors] = useState(colorThemes.default);
  const [selectedFont, setSelectedFont] = useState(fontOptions[0]);

  // Apply theme changes
  const applyTheme = (theme, font = selectedFont, colors = customColors) => {
    if (onThemeChange) {
      onThemeChange({
        theme,
        font,
        colors,
        darkMode
      });
    }
  };

  const handleThemeSelect = (themeKey) => {
    setSelectedTheme(themeKey);
    const theme = colorThemes[themeKey];
    setCustomColors(theme);
    applyTheme(themeKey, selectedFont, theme);
  };

  const handleColorChange = (colorKey, value) => {
    const newColors = { ...customColors, [colorKey]: value };
    setCustomColors(newColors);
    setSelectedTheme('custom');
    applyTheme('custom', selectedFont, newColors);
  };

  const handleFontChange = (font) => {
    setSelectedFont(font);
    applyTheme(selectedTheme, font, customColors);
  };

  const resetToDefault = () => {
    setSelectedTheme('default');
    setCustomColors(colorThemes.default);
    setSelectedFont(fontOptions[0]);
    applyTheme('default', fontOptions[0], colorThemes.default);
  };

  const ColorPicker = ({ label, colorKey, value }) => (
    <div className="flex items-center justify-between py-2">
      <span className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
        {label}
      </span>
      <div className="flex items-center space-x-2">
        <div 
          className="w-8 h-8 rounded-lg border-2 border-gray-300 cursor-pointer shadow-sm"
          style={{ backgroundColor: value }}
          onClick={() => document.getElementById(`color-${colorKey}`).click()}
        />
        <input
          type="color"
          value={value}
          onChange={(e) => handleColorChange(colorKey, e.target.value)}
          className="w-0 h-0 opacity-0 absolute"
          id={`color-${colorKey}`}
        />
        <button 
          onClick={() => document.getElementById(`color-${colorKey}`).click()}
          className={`px-3 py-1 text-xs rounded-md transition-colors ${
            darkMode 
              ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          Edit
        </button>
      </div>
    </div>
  );

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`flex items-center space-x-2 px-4 py-2 rounded-xl font-medium transition-all duration-200 ${
          darkMode
            ? 'bg-slate-800/50 text-white border border-slate-600/50 hover:bg-slate-700/50'
            : 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50'
        } shadow-lg hover:shadow-xl`}
      >
        <FontAwesomeIcon icon={faRobot} style={{ color: customColors.primary }} />
        <span>Theme</span>
        <FontAwesomeIcon icon={faArrowDown} className={`text-sm transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <>
          <div className="fixed inset-0 z-40" onClick={() => setIsOpen(false)} />
          <div className={`absolute top-full right-0 mt-2 w-96 rounded-2xl shadow-2xl border z-50 ${
            darkMode ? 'bg-slate-800/95 border-slate-600/50 backdrop-blur-xl' : 'bg-white/95 border-gray-200 backdrop-blur-xl'
          }`}>
            <div className={`p-4 border-b ${darkMode ? 'border-slate-700' : 'border-gray-200'}`}>
              <div className="flex items-center justify-between">
                <h3 className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                  Design Themes
                </h3>
                <div className="flex items-center space-x-2">
                  <button onClick={resetToDefault} className={`p-2 rounded-lg transition-colors ${darkMode ? 'hover:bg-slate-700' : 'hover:bg-gray-100'}`}>
                    <FontAwesomeIcon icon={faRedo} className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`} />
                  </button>
                  <button onClick={() => setIsOpen(false)} className={`p-2 rounded-lg transition-colors ${darkMode ? 'hover:bg-slate-700' : 'hover:bg-gray-100'}`}>
                    <FontAwesomeIcon icon={faTimes} className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`} />
                  </button>
                </div>
              </div>
              
              <div className="flex space-x-1 mt-4">
                {[
                  { id: 'themes', label: 'Themes' },
                  { id: 'colors', label: 'Colors' },
                  { id: 'fonts', label: 'Fonts' }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`px-3 py-2 rounded-lg text-sm font-medium transition-all ${
                      activeTab === tab.id
                        ? `text-white shadow-lg`
                        : (darkMode ? 'text-gray-400 hover:text-gray-300 hover:bg-slate-700' : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100')
                    }`}
                    style={activeTab === tab.id ? { backgroundColor: customColors.primary } : {}}
                  >
                    {tab.label}
                  </button>
                ))}
              </div>
            </div>

            <div className="p-4 max-h-96 overflow-y-auto">
              {activeTab === 'themes' && (
                <div className="space-y-3">
                  {Object.entries(colorThemes).map(([key, theme]) => (
                    <div
                      key={key}
                      onClick={() => handleThemeSelect(key)}
                      className={`p-3 rounded-xl cursor-pointer transition-all duration-200 border-2 ${
                        selectedTheme === key
                          ? 'border-current shadow-lg'
                          : (darkMode ? 'border-slate-600/50 hover:border-slate-500' : 'border-gray-200 hover:border-gray-300')
                      }`}
                      style={selectedTheme === key ? { borderColor: theme.primary } : {}}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className={`font-semibold ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                            {theme.name}
                          </h4>
                          <div className="flex space-x-1 mt-2">
                            {[theme.primary, theme.secondary, theme.accent, theme.success].map((color, index) => (
                              <div key={index} className="w-6 h-6 rounded-full border border-white shadow-sm" style={{ backgroundColor: color }} />
                            ))}
                          </div>
                        </div>
                        {selectedTheme === key && <FontAwesomeIcon icon={faArrowUp} style={{ color: theme.primary }} />}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {activeTab === 'colors' && (
                <div className="space-y-4">
                  <div>
                    <h4 className={`font-semibold mb-3 ${darkMode ? 'text-white' : 'text-gray-800'}`}>Brand Colors</h4>
                    <div className="space-y-2">
                      <ColorPicker label="Primary" colorKey="primary" value={customColors.primary} />
                      <ColorPicker label="Secondary" colorKey="secondary" value={customColors.secondary} />
                      <ColorPicker label="Accent" colorKey="accent" value={customColors.accent} />
                      <ColorPicker label="Success" colorKey="success" value={customColors.success} />
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'fonts' && (
                <div className="space-y-2">
                  {fontOptions.map((font) => (
                    <div
                      key={font.value}
                      onClick={() => handleFontChange(font)}
                      className={`p-3 rounded-lg cursor-pointer transition-all border ${
                        selectedFont.value === font.value
                          ? 'border-current shadow-lg'
                          : (darkMode ? 'border-slate-600/50 hover:border-slate-500' : 'border-gray-200 hover:border-gray-300')
                      }`}
                      style={selectedFont.value === font.value ? { borderColor: customColors.primary } : {}}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className={`font-semibold ${darkMode ? 'text-white' : 'text-gray-800'}`} style={{ fontFamily: font.value }}>
                            {font.name}
                          </div>
                          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{font.category}</div>
                        </div>
                        {selectedFont.value === font.value && <FontAwesomeIcon icon={faArrowUp} style={{ color: customColors.primary }} />}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default DesignPaletteDropdown;