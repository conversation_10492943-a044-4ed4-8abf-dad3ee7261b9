import React from "react";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import ListAltIcon from "@mui/icons-material/ListAlt";

import CloseIcon from "@mui/icons-material/Close";

import ReceiptIcon from "@mui/icons-material/Receipt";

import CurrentReceiptTabView from "./CurrentReceiptView";
import ReceiptViewTransactionHistoryTabView from "./ReceiptViewTransactionHistoryTabView";
import UploadReceiptModal from "./UploadReceiptModal";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Button,
  IconButton,
  InputAdornment,
  FormControl,
  InputLabel,
  LinearProgress,
  CircularProgress,
  TextField,
  Table,
  TableHead,
  Tooltip,
  Select,
  Grid2,
  Paper,
  ListSubheader,
  TableBody,
  TableRow,
  TableCell,
  Tabs,
  Tab,
  MenuItem,
  Radio,
  Box,
  Typography,
  FormControlLabel,
  Checkbox,
} from "@mui/material";

const ParsedReceiptDisplayModal = ({
  closeReceiptModal,
  selectedTransaction,
  selectedFile,
  setSelectedFile,
}) => {
  const {
    errorMessage,
    jsonResponse,
    currentTab,
    receiptUploadModal,
    showError,
    blinkError,
    isReceiptModalOpen,
    selectedReceipt,
    // selectedFile,
    fileMetadata,
    // selectedTransaction,
    uploadPopupWidth,
    editingField,
    editedValue,
    editedItemIndex,
    selectedDate,
    uploadProgress,
    isUploading,
    isProcessing,
    isPopupVisible,

    //  receiptNewTransaction,
    receiptTransactionIds,
  } = useSelector((state) => state.receipts);

  return (
    <Dialog
      open={receiptUploadModal}
      onClose={closeReceiptModal}
      sx={{
        "& .MuiDialog-paper": {
          width:
            jsonResponse && Object.keys(jsonResponse).length > 0
              ? { xs: "95vw", sm: "85vw", md: "70vw", lg: "60vw", xl: "50vw" }
              : { xs: "90vw", sm: "500px", md: "600px" },
          maxWidth:
            jsonResponse && Object.keys(jsonResponse).length > 0
              ? "700px"
              : "650px",
          height:
            jsonResponse && Object.keys(jsonResponse).length > 0
              ? "85vh"
              : "auto",
          maxHeight: "90vh",
          fontFamily: "Roboto, sans-serif",
          padding: "10px",
          boxSizing: "border-box",
          position: "relative",
          overflow: "visible",
          backgroundColor: "white",
          display: "flex",
          flexDirection: "column",
          transition: "width 0.3s ease",
          clipPath: `polygon(
                          0% 0%, 100% 0%, 100% 95%, 95% 98%, 90% 95%, 85% 98%, 80% 95%, 75% 98%, 70% 95%, 65% 98%,
                          60% 95%, 55% 98%, 50% 95%, 45% 98%, 40% 95%, 35% 98%, 30% 95%, 25% 98%, 20% 95%, 15% 98%,
                          10% 95%, 5% 98%, 0% 95%)`,
        },
        "& .MuiBackdrop-root": {
          backgroundColor: "rgba(0, 0, 0, 0.5)",
        },
      }}
    >
      {console.log("DIALOG STATE - jsonResponse:", jsonResponse)}
      {console.log("DIALOG STATE - selectedTransaction:", selectedTransaction)}
      {console.log("Dialog jsonResponse:", jsonResponse)} {/* Debug log */}
      {jsonResponse && Object.keys(jsonResponse).length > 0 ? (
        <>
          <Box
            sx={{
              backgroundColor: "#8BC34A",
              padding: "16px",
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              borderRadius: "4px 4px 0 0",
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <ReceiptIcon sx={{ marginRight: "8px", color: "#333" }} />
              <Typography
                variant="h6"
                sx={{ color: "#333", fontWeight: "bold" }}
              >
                Receipt
              </Typography>
            </Box>
            <IconButton onClick={closeReceiptModal} sx={{ color: "#333" }}>
              <CloseIcon />
            </IconButton>
          </Box>

          <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
            <Tabs
              value={currentTab}
              onChange={(e, newValue) => dispatch(setCurrentTab(newValue))}
              aria-label="receipt tabs"
              sx={{
                "& .MuiTabs-indicator": {
                  backgroundColor: "#8BC34A",
                },
              }}
            >
              <Tab
                label={
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <ReceiptIcon sx={{ mr: 1, fontSize: "18px" }} />
                    <span>Current Receipt</span>
                  </Box>
                }
                value="receipt"
                sx={{
                  fontFamily: "Roboto, sans-serif",
                  "&.Mui-selected": {
                    color: "#8BC34A",
                    fontWeight: "bold",
                  },
                }}
              />
              <Tab
                label={
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <ListAltIcon sx={{ mr: 1, fontSize: "18px" }} />
                    <span>Transaction History</span>
                  </Box>
                }
                value="matchingTransaction"
                sx={{
                  fontFamily: "Roboto, sans-serif",
                  "&.Mui-selected": {
                    color: "#8BC34A",
                    fontWeight: "bold",
                  },
                }}
              />
            </Tabs>
          </Box>

          {currentTab === "receipt" && <CurrentReceiptTabView />}
          {currentTab === "matchingTransaction" && (
            <ReceiptViewTransactionHistoryTabView />
          )}
        </>
      ) : (
        <UploadReceiptModal
          selectedFile={selectedFile}
          setSelectedFile={setSelectedFile}
        />
      )}
    </Dialog>
  );
};

export default ParsedReceiptDisplayModal;
