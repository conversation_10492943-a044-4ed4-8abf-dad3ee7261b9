// Setup for Redux and Epic testing
import { vi } from 'vitest'

// Mock console methods if needed for cleaner test output
global.console = {
  ...console,
  // Uncomment to suppress console logs during tests
  // log: vi.fn(),
  // debug: vi.fn(),
  // info: vi.fn(),
  // warn: vi.fn(),
  // error: vi.fn(),
}

// Global test utilities for Redux/Epic testing
global.testUtils = {
  // Add any common utilities for Redux/Epic testing
  mockStore: null, // Can be set up with a store factory
  mockApi: null,   // Can be set up with API mocks
}

// Setup any global mocks needed for your logic tests
vi.mock('some-external-api', () => ({
  // Mock implementation
}))

console.log('✅ Logic tests setup complete')
