// spendingDashboardSlice.js
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  monthlyExpenses: [],
  yearlyExpenses: [],
  isLoading: false,
  error: null,
};

const spendingDashboardSlice = createSlice({
  name: 'spendingDashboard',
  initialState,
  reducers: {
    fetchExpensesStart(state) {
      state.isLoading = true;
      state.error = null;
    },
    fetchMonthlyExpensesSuccess(state, action) {
      state.monthlyExpenses = action.payload;
      state.isLoading = false;
    },
    fetchYearlyExpensesSuccess(state, action) {
      state.yearlyExpenses = action.payload;
      state.isLoading = false;
    },
    fetchExpensesFailure(state, action) {
      state.isLoading = false;
      state.error = action.payload;
    },
    setError(state, action) {
      state.error = action.payload;
      state.isLoading = false;
    },
  },
});

export const {
  fetchExpensesStart,
  fetchMonthlyExpensesSuccess,
  fetchYearlyExpensesSuccess,
  fetchExpensesFailure,
  setError,
} = spendingDashboardSlice.actions;

export default spendingDashboardSlice.reducer;