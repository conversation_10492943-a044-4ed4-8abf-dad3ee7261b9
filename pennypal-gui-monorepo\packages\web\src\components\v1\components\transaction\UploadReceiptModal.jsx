import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { useState } from "react";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Button,
  IconButton,
  InputAdornment,
  FormControl,
  InputLabel,
  LinearProgress,
  CircularProgress,
  TextField,
  Table,
  TableHead,
  Tooltip,
  Select,
  Grid2,
  Paper,
  ListSubheader,
  TableBody,
  TableRow,
  TableCell,
  Tabs,
  Tab,
  MenuItem,
  Radio,
  Box,
  Typography,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";

import {
  setCurrentTab,
  setReceiptUploadModal,
  // setSelectedFile,
  setErrorMessage,
  setShowError,
  // setFileMetadata,
  setBlinkError,
  setIsReceiptModalOpen,
  setSelectedReceipt,
  // setSelectedTransaction,
  setEditingField,
  setEditedValue,
  setEditedItemIndex,
  setSelectedDate,
  setUploadProgress,
  setIsUploading,
  setIsProcessing,
  // setIsPopupVisible,
  // setReceiptNewTransaction,
  uploadReceiptRequest,
  saveReceiptRequest,
  addNewTransaction,
  fetchReceiptTransactionIdsRequest,
  fetchReceiptDetailsRequest,
  // addTransactionRequest,
  updateReceiptField,
  setJsonResponse,
  setIsMatchingTransactionAdd,
} from "@pp-logic/redux/receiptSlice";

import {
  fetchTransactionsStart,
  setSelectedTransaction,
  setOpenModal,
  toggleCardVisibility,
  setSearchName,
  setStartDate,
  setEndDate,
  setSearchDate,
  setSelectedDateRange,
  toggleDateFilter,
  setSortOrder,
  toggleSelectTransaction,
  toggleSelectAll,
  toggleAddTransactionModal,
  updateNewTransaction,
  resetNewTransaction,
  applyFilters,
  addTransactionRequest,
  fetchTransactionSummaryStart,
  setCustomSearchText,
  selectAllTransactions,
  deselectAllTransactions,
  setPage,
} from "@pp-logic/redux/transactionSlice";

const UploadReceiptModal = ({ selectedFile, setSelectedFile }) => {
  /*
            handleDrop, -- done - depentencies(done)
            handleFileRemove, -- done - depentencies(done)
            handleFileChange, -- done - depentencies(done)
            closeReceiptModal -- done
            handleUpload -- done

            isUploading, -- from receipt state
            uploadProgress, -- from receipt state
            isProcessing, -- from receipt state
            selectedFile, -- local state
            errorMessage -- errorMessage
            showError -- from receipt state

        */

  const dispatch = useDispatch();

  // Replace with actual data source

  // Receipts state
  const {
    errorMessage,
    jsonResponse,
    currentTab,
    receiptUploadModal,
    showError,
    blinkError,
    isReceiptModalOpen,
    selectedReceipt,
    // selectedFile,
    fileMetadata,
    // selectedTransaction,
    uploadPopupWidth,
    editingField,
    editedValue,
    editedItemIndex,
    selectedDate,
    uploadProgress,
    isUploading,
    isProcessing,
    isPopupVisible,
    //  receiptNewTransaction,
    receiptTransactionIds,
  } = useSelector((state) => state.receipts);

  const selectedTransaction = useSelector(
    (state) => state.transactions.selectedTransaction
  );

  const handleFileRemove = (index) => {
    setSelectedFile((prevFiles) => prevFiles.filter((_, i) => i !== index));
  };

  const handleDrop = (event) => {
    event.preventDefault();
    const files = Array.from(event.dataTransfer.files);
    setSelectedFile(files);
    dispatch(setErrorMessage("")); // Clear error message
    dispatch(setShowError(false));
  };

  const handleFileChange = (event) => {
    const files = Array.from(event.target.files);
    setSelectedFile(files);
    dispatch(setErrorMessage("")); // Clear error message
    dispatch(setShowError(false));
  };

  // Handle receipt upload
  const handleUpload = () => {
    if (selectedFile.length === 0) return;

    console.log("handleUpload selectedFile:", selectedFile); // Debug log
    dispatch(setIsUploading(true));
    dispatch(setUploadProgress(0));

    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      if (progress >= 100) {
        clearInterval(interval);
        dispatch(setUploadProgress(100));
        dispatch(setIsUploading(false));
        dispatch(setIsProcessing(true));
      } else {
        dispatch(setUploadProgress(progress));
      }
    }, 500);

    const formData = new FormData();
    selectedFile.forEach((file) => formData.append("file", file));
    console.log("handleUpload dispatching uploadReceiptRequest"); // Debug log
    dispatch(uploadReceiptRequest(formData));
  };

  // Close receipt modal
  const closeReceiptModal = () => {
    console.log("closeReceiptModal called"); // Debug log
    dispatch(setReceiptUploadModal(false));
    dispatch(setSelectedTransaction(null));
    dispatch(setJsonResponse({})); // Reset jsonResponse
    dispatch(setIsUploading(false));
    dispatch(setIsProcessing(false));
    dispatch(setUploadProgress(0));
    setSelectedFile([]); // Clear local state
    dispatch(setErrorMessage("")); // Clear any error messages
    dispatch(setShowError(false));
  };

  return (
    <>
      <DialogTitle sx={{ textAlign: "center" }}>Upload Receipt</DialogTitle>
      <DialogContent>
        <div style={{ display: "flex", gap: "10px" }}>
          <div
            style={{
              flex: 1,
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
            }}
          >
            <div
              onDrop={handleDrop}
              onDragOver={(e) => e.preventDefault()}
              onClick={() => document.getElementById("fileInput").click()}
              style={{
                padding: "20px",
                textAlign: "center",
                marginBottom: "10px",
                cursor: "pointer",
                borderRadius: "8px",
                maxWidth: "400px",
                backgroundColor: "#6aa84f",
                border: "2px dashed white",
                color: "white",
                fontWeight: "bold",
              }}
            >
              <p>Click to upload or Drag and Drop</p>
            </div>
            {isUploading && (
              <div
                style={{ width: "100%", maxWidth: "300px", marginTop: "10px" }}
              >
                <LinearProgress
                  variant="determinate"
                  value={uploadProgress}
                  sx={{
                    height: "10px",
                    borderRadius: "5px",
                    backgroundColor: "#e0e0e0",
                    "& .MuiLinearProgress-bar": {
                      backgroundColor: "#4caf50",
                    },
                  }}
                />
                <p
                  style={{
                    textAlign: "center",
                    marginTop: "5px",
                    color: "#4caf50",
                  }}
                >
                  Uploading... {uploadProgress}%
                </p>
              </div>
            )}
            {uploadProgress === 100 && !isUploading && !isProcessing && (
              <p style={{ color: "green", textAlign: "center" }}>
                Receipt uploaded successfully!
              </p>
            )}
            {isProcessing && (
              <div
                style={{
                  textAlign: "center",
                  marginTop: "10px",
                  color: "green",
                }}
              >
                <p>Processing receipt...</p>
                <CircularProgress size={24} style={{ marginTop: "10px" }} />
              </div>
            )}
            <div style={{ width: "100%", maxWidth: "300px" }}>
              {selectedFile && selectedFile.length > 0
                ? selectedFile.map((file, index) => (
                    <div
                      key={index}
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        background: "white",
                        borderRadius: "20px",
                        padding: "10px",
                        marginBottom: "5px",
                        border: "1px solid #ccc",
                        boxShadow: "2px 2px 5px rgba(0,0,0,0.1)",
                      }}
                    >
                      {file.name}
                      <IconButton
                        size="small"
                        onClick={() => handleFileRemove(index)}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                      {errorMessage && (
                        <p
                          style={{
                            color: "red",
                            fontWeight: "bold",
                            textAlign: "center",
                            visibility: showError ? "visible" : "hidden",
                          }}
                        >
                          {errorMessage}
                        </p>
                      )}
                    </div>
                  ))
                : null}
            </div>
            <input
              type="file"
              style={{ display: "none" }}
              id="fileInput"
              multiple
              onChange={handleFileChange}
            />
          </div>
          <div
            style={{
              flex: 1,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              borderLeft: "2px solid #aaa",
            }}
          >
            <img
              src="/images/qr-code.png"
              alt="QR Code"
              style={{ width: "120px", height: "120px" }}
            />
          </div>
        </div>
      </DialogContent>
      <DialogActions>
        <Button onClick={closeReceiptModal} color="secondary">
          Cancel
        </Button>
        <Button
          onClick={handleUpload}
          color="primary"
          variant="contained"
          disabled={!selectedFile || selectedFile.length === 0}
        >
          Upload
        </Button>
      </DialogActions>
    </>
  );
};
export default UploadReceiptModal;
