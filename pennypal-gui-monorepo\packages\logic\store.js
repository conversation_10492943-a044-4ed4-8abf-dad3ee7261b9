import { configureStore } from '@reduxjs/toolkit';
import accountsDashboardReducer from './redux/accountsDashboardSlice';
import accountChartReducer from './redux/accountChartSlice';
import accountsReducer from './redux/accountsSlice';
import reportsReducer from './redux/reportsSlice';
import budgetReducer from './redux/budgetSlice';
import budgetDashboardReducer from './redux/budgetDashboardSlice';
import authReducer from './redux/authSlice';
import plaidReducer from './features/plaidSlice';
import transactionReducer from './redux/transactionSlice';
import receiptReducer from './redux/receiptSlice';
import budgetRuleReducer from './redux/budgetRulesSlice';
import notificationReducer from './redux/notificationSlice';
import documentReducer from './redux/documentSlice';
import reconcileReducer from './redux/reconcileSlice'
import spendingDashboardReducer from './redux/spendingDashboardSlice'; // Import the spendingDashboardReducer
import splitTransactionReducer from './redux/splitTransactionSlice';
import recurringReducer from './redux/recurringSlice'
import chatbotReducer from './redux/chatbotSlice';
import { combineEpics, createEpicMiddleware } from 'redux-observable';
import investmentReducer from './redux/investmentSlice';
import goalReducer from './redux/goalSlice';
import {fetchDeltaDataEpic, fetchAccountTypeDeltaDataEpic} from './epics/deltaEpic';
import deltaReducer from './redux/deltaSlice';
import { signInEpic, signUpEpic, googleSignInEpic, otpVerifyEpic, setPasswordEpic } from './epics/authEpics';
import bankIconEpic from './epics/bankIconEpic';
import bankIconReducer from './redux/bankIconsSlice';
import cacheEpic from './epics/cacheEpic';
import cacheInvalidationEpic from './epics/cacheInvalidationEpic';
import cacheReducer from './redux/cacheSlice';
import receiptItemsReducer from './redux/receiptItemsSlice';
import { fetchReceiptItemsEpic,fetchItemSummaryEpic } from './epics/receiptItemsEpic';
import accountManagementReducer from './redux/accountManagementSlice';
import {
  changePasswordEpic,
  deleteAccountEpic,
  checkDeletionStatusEpic,
} from './epics/accountManagementEpic';  
import {uploadReceiptEpic,saveReceiptEpic,fetchReceiptDetailsEpic,fetchReceiptTransactionIdsEpic} from './epics/receiptEpics';
import rootTransactionEpic from './epics/transactionEpics';
import rootBudgetEpic from './epics/budgetEpics';
import {accountsDashboardEpic} from './epics/accountsDashboardEpic';
import {fetchChartDataEpic} from './epics/fetchChartDataEpic';
import recurringTransactionsReducer from './redux/recurringTransactionsSlice';
import { fetchRecurringTransactionsEpic } from './epics/fetchRecurringTransactionsEpic';
import {rootBudgetRulesEpic} from './epics/budgetRulesEpic';
import {rootNotificationsEpic} from './epics/notificationEpic';
import {fetchDocumentsEpic,uploadDocumentEpic,deleteDocumentEpic,generateQrDataEpic, confirmUploadEpic,fetchDocumentDetailsEpic,} from './epics/documentEpics';
import {fetchReconcileEpic} from './epics/reconcileEpic';
import{fetchContactsEpic,fetchSplitTransactionsEpic,submitSplitEpic} from './epics/splitTransactionEpics';
import { spendingDashboardEpics } from './epics/spendingDashboardEpic';
import { getBudgetDashboardEpic } from './epics/budgetDashboardEpics';
import {
  fetchInvestmentsEpic,
  fetchPerformanceEpic,
  fetchInvestmentAccountsEpic,
  fetchPortfolioDiversityEpic,
  fetchPortfolioOptimizationEpic,
  
} from './epics/investmentEpics';
import investmentsReducer from './redux/investmentsSlice';
import{ syncInvestmentDataEpic,
  fetchUserInvestmentsEpic,
  fetchDailyInvestmentStocksEpic,
  fetchPortfolioSummaryEpic,
 fetchInvestmentByTickerEpic,
  fetchAllStocksHistoryEpic,
  fetchStockHistoryEpic,
} from './epics/investmentsEpics'
import recurringEpics from './epics/recurringEpics';
import membershipEpics from './epics/membershipEpics';
import  goalEpics  from './epics/goalEpics';
// import { inviteFamilyMemberEpic, validateInviteLinkEpic, completeSignupEpic } from './epics/membershipEpics.js';
import membershipReducer from './redux/memberSlice';
import ratingReducer from './redux/ratingSlice';
import {
   submitRatingEpic,
  getUserRatingEpic,
  getRatingStatsEpic,
  updateRatingEpic,
  deleteRatingEpic,
} from './epics/RatingEpic';
// import accountDeltaReducer from './redux/accountDeltaSlice';
import themeReducer from './redux/themeSlice';
import profileReducer from './redux/profileSlice';
import { fetchUserEpic, updateUserEpic} from './epics/profileEpic';
import twoFactorAuthReducer from './redux/twoFactorAuthSlice';
import {
  setupTwoFactorAuthEpic,
  enableTwoFactorAuthEpic,
  disableTwoFactorAuthEpic,
  verifyTwoFactorCodeEpic,
  getTwoFactorStatusEpic,
} from './epics/TwoFactorAuthEpic';
import chatbotEpic from './epics/chatbotEpic';
import paymentEpic from './epics/paymentEpic';
import authEpic from './epics/authEpic';

export const rootEpic = combineEpics(
  signUpEpic,
  signInEpic,
  googleSignInEpic,
  otpVerifyEpic,
  setPasswordEpic,
  rootTransactionEpic,
  rootBudgetEpic, 
   accountsDashboardEpic,
  fetchChartDataEpic,
  bankIconEpic,
  cacheEpic,
  cacheInvalidationEpic,
  fetchRecurringTransactionsEpic,
  rootNotificationsEpic,
  rootBudgetRulesEpic,
  spendingDashboardEpics,
  getBudgetDashboardEpic,
  fetchInvestmentsEpic,
  fetchPerformanceEpic,
  fetchInvestmentAccountsEpic,
  fetchPortfolioDiversityEpic,
  fetchPortfolioOptimizationEpic,
  membershipEpics  ,
  goalEpics,
  recurringEpics,
  fetchDocumentsEpic,
  uploadDocumentEpic,
  confirmUploadEpic,
  deleteDocumentEpic,
  generateQrDataEpic,
  fetchDocumentDetailsEpic,
  fetchReconcileEpic,
  fetchReconcileEpic,
  fetchContactsEpic,
  fetchSplitTransactionsEpic,
  submitSplitEpic,
    fetchDeltaDataEpic,
    fetchAccountTypeDeltaDataEpic,
  uploadReceiptEpic,
  saveReceiptEpic,
  fetchReceiptDetailsEpic,
  fetchReceiptTransactionIdsEpic,
  fetchReceiptItemsEpic,
   fetchItemSummaryEpic,
   syncInvestmentDataEpic,
  fetchUserInvestmentsEpic,
  fetchDailyInvestmentStocksEpic,
  fetchPortfolioSummaryEpic,
   fetchInvestmentByTickerEpic,
    fetchAllStocksHistoryEpic,
    fetchStockHistoryEpic,
   fetchUserEpic, updateUserEpic,
    submitRatingEpic,
  getUserRatingEpic,
  getRatingStatsEpic,
  updateRatingEpic,
  deleteRatingEpic,
   changePasswordEpic,
  deleteAccountEpic,
  checkDeletionStatusEpic,
  setupTwoFactorAuthEpic,
  enableTwoFactorAuthEpic,
  disableTwoFactorAuthEpic,
  verifyTwoFactorCodeEpic,
  getTwoFactorStatusEpic,
 fetchReceiptItemsEpic,
   fetchItemSummaryEpic,
  chatbotEpic,
  paymentEpic,
  authEpic,
  // // No spread operator needed now
  //receiptEpics,
  //transactionEpics,
  // Add more epics here
);

/*
import signInEpic from './epics/signInEpic';
export const rootEpic = combineEpics(
  signInEpic
);
*/
const epicMiddleware = createEpicMiddleware();

export const store = configureStore({
  reducer: {
    plaid: plaidReducer,
    //accounts: accountsReducer,
    accounts: accountsDashboardReducer,
    accountChart: accountChartReducer,
    delta:deltaReducer,
    accounts: accountsDashboardReducer,
    reports: reportsReducer,
    budget: budgetReducer,
    budgetDashboard: budgetDashboardReducer,
    auth: authReducer,
    transactions: transactionReducer,
    receipts: receiptReducer,
    recurringTransactions: recurringTransactionsReducer,
    budgetRule: budgetRuleReducer,
    notifications: notificationReducer,
    bankIcons: bankIconReducer,
    cache: cacheReducer,
    spendingDashboard: spendingDashboardReducer,
    theme: themeReducer,
    documents: documentReducer,
    reconcile:reconcileReducer,
    mock_investments: investmentReducer,
    membership: membershipReducer,
    goals: goalReducer,
    splitTransaction: splitTransactionReducer,
    recurring:recurringReducer,
    receiptItems: receiptItemsReducer,
  investment:investmentsReducer,
     profile: profileReducer,
       rating: ratingReducer,
    chatbot: chatbotReducer,
        accountManagement: accountManagementReducer,
         twoFactorAuth: twoFactorAuthReducer,
  },
  middleware: (getDefaultMiddleware) => 
    getDefaultMiddleware().concat(epicMiddleware)
});

epicMiddleware.run(rootEpic);
