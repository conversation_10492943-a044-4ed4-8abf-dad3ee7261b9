import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { TrendingUp, TrendingDown, Minus, Calendar, RefreshCw, DollarSign, ChevronDown, Filter } from 'lucide-react';
import { fetchAccountBalanceMonthlyDeltasGrouped } from '../../../../logic/redux/accountsDashboardSlice';

// Custom UI Components with Green Theme
const Card = ({ children, className = '', ...props }) => (
  <div className={`bg-white rounded-xl border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 ${className}`} {...props}>
    {children}
  </div>
);

const CardHeader = ({ children, className = '', ...props }) => (
  <div className={`p-6 pb-0 ${className}`} {...props}>
    {children}
  </div>
);

const CardTitle = ({ children, className = '', ...props }) => (
  <h3 className={`text-lg font-semibold leading-none tracking-tight text-gray-800 ${className}`} {...props}>
    {children}
  </h3>
);

const CardContent = ({ children, className = '', ...props }) => (
  <div className={`p-6 pt-4 ${className}`} {...props}>
    {children}
  </div>
);

const Button = ({ children, variant = 'default', size = 'default', disabled = false, onClick, className = '', ...props }) => {
  const baseClasses = 'inline-flex items-center justify-center rounded-lg text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md';
  
  const variants = {
    default: 'text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5',
    outline: 'border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 text-gray-700',
    destructive: 'bg-red-500 text-white hover:bg-red-600 shadow-lg hover:shadow-xl'
  };
  
  const sizes = {
    default: 'h-11 px-6 py-2',
    sm: 'h-9 rounded-lg px-4',
    lg: 'h-12 rounded-lg px-8'
  };
  
  const defaultStyle = variant === 'default' ? {
    background: 'linear-gradient(135deg, #05df72 0%, #04c767 100%)'
  } : {};
  
  return (
    <button
      className={`${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`}
      disabled={disabled}
      onClick={onClick}
      style={defaultStyle}
      {...props}
    >
      {children}
    </button>
  );
};

const Select = ({ value, onValueChange, children, placeholder }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState(value);
  
  useEffect(() => {
    setSelectedValue(value);
  }, [value]);
  
  const handleSelect = (newValue, label) => {
    setSelectedValue(newValue);
    onValueChange(newValue);
    setIsOpen(false);
  };
  
  const selectedLabel = React.Children.toArray(children).find(child => 
    child.props.value === selectedValue
  )?.props.children || placeholder;
  
  return (
    <div className="relative">
      <button
        type="button"
        className="flex h-11 w-full items-center justify-between rounded-lg border-2 border-gray-200 bg-white px-4 py-2 text-sm ring-offset-background placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200 hover:border-gray-300 shadow-sm hover:shadow-md"
        style={{
          '--tw-ring-color': '#05df72'
        }}
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="text-gray-700">{selectedLabel}</span>
        <ChevronDown className="h-4 w-4 opacity-50 text-gray-500" />
      </button>
      {isOpen && (
        <div className="absolute z-50 mt-2 max-h-60 w-full overflow-auto rounded-lg border border-gray-200 bg-white py-2 text-base shadow-xl ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
          {React.Children.map(children, (child) => (
            <div
              key={child.props.value}
              className="relative cursor-pointer select-none py-3 px-4 hover:text-white transition-all duration-150 text-gray-700"
              onMouseEnter={(e) => {
                e.target.style.background = 'linear-gradient(135deg, #05df72 0%, #04c767 100%)';
                e.target.style.color = 'white';
              }}
              onMouseLeave={(e) => {
                e.target.style.background = '';
                e.target.style.color = '#374151';
              }}
              onClick={() => handleSelect(child.props.value, child.props.children)}
            >
              {child.props.children}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const SelectItem = ({ value, children }) => <div value={value}>{children}</div>;

const Alert = ({ children, variant = 'default', className = '' }) => {
  const variants = {
    default: 'border-gray-200 bg-white text-gray-900',
    destructive: 'border-red-200 bg-red-50 text-red-900'
  };
  
  return (
    <div className={`relative w-full rounded-xl border p-4 shadow-sm ${variants[variant]} ${className}`}>
      {children}
    </div>
  );
};

const AlertDescription = ({ children }) => (
  <div className="text-sm">
    {children}
  </div>
);

const Badge = ({ children, variant = 'default' }) => {
  const variants = {
    default: 'text-white shadow-sm',
    destructive: 'bg-red-100 text-red-800 border border-red-200',
    secondary: 'text-gray-700 border border-gray-200',
    liability: 'text-white shadow-sm',
    networth: 'text-white shadow-sm'
  };
  
  const defaultStyle = variant === 'default' ? {
    background: 'linear-gradient(135deg, #8bc34a 0%, #689f38 100%)'
  } : {};
  
  const secondaryStyle = variant === 'secondary' ? {
    backgroundColor: '#c5e1a5'
  } : {};

  const liabilityStyle = variant === 'liability' ? {
    background: 'linear-gradient(135deg, #ff7043 0%, #f4511e 100%)'
  } : {};

  const networthStyle = variant === 'networth' ? {
    background: 'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)'
  } : {};
  
  return (
    <span 
      className={`inline-flex items-center rounded-full px-3 py-1 text-xs font-medium ${variants[variant]}`}
      style={variant === 'default' ? defaultStyle : variant === 'secondary' ? secondaryStyle : variant === 'liability' ? liabilityStyle : variant === 'networth' ? networthStyle : {}}
    >
      {children}
    </span>
  );
};

const AccountBalanceDelta = () => {
  const dispatch = useDispatch();
  const { 
    balanceDeltasGrouped, 
    balanceDeltasGroupedLoading, 
    balanceDeltasGroupedError 
  } = useSelector(state => state.accounts);

  const [selectedPeriod, setSelectedPeriod] = useState({
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1
  });

  const [selectedAccountType, setSelectedAccountType] = useState('all');

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 5 }, (_, i) => currentYear - i);
  const months = [
    { value: 1, label: 'January' },
    { value: 2, label: 'February' },
    { value: 3, label: 'March' },
    { value: 4, label: 'April' },
    { value: 5, label: 'May' },
    { value: 6, label: 'June' },
    { value: 7, label: 'July' },
    { value: 8, label: 'August' },
    { value: 9, label: 'September' },
    { value: 10, label: 'October' },
    { value: 11, label: 'November' },
    { value: 12, label: 'December' }
  ];

  const accountTypes = [
    { value: 'all', label: 'All Account Types' },
    { value: 'credit', label: 'Credit' },
    { value: 'depository', label: 'Depository' },
    { value: 'loan', label: 'Loan' },
    { value: 'investment', label: 'Investment' },
    { value: 'liability', label: 'Total Liability (Credit + Loan)' },
    { value: 'networth', label: 'Net Worth (Assets - Liabilities)' }
  ];

  useEffect(() => {
    dispatch(fetchAccountBalanceMonthlyDeltasGrouped({
      userId: null,
      year: selectedPeriod.year,
      month: selectedPeriod.month
    }));
  }, [dispatch, selectedPeriod]);

  const handlePeriodChange = (field, value) => {
    setSelectedPeriod(prev => ({
      ...prev,
      [field]: parseInt(value)
    }));
  };

  const formatCurrency = (amount) => {
    if (amount === null || amount === undefined || isNaN(amount)) return '$0.00';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPercentage = (percentage) => {
    if (percentage === null || percentage === undefined || isNaN(percentage)) return '0.00%';
    return `${percentage.toFixed(2)}%`;
  };

  const getTrendIcon = (amount) => {
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount)) return <Minus className="w-5 h-5 text-gray-500" />;
    if (numAmount > 0) return <TrendingUp className="w-5 h-5 text-green-600" />;
    if (numAmount < 0) return <TrendingDown className="w-5 h-5 text-red-500" />;
    return <Minus className="w-5 h-5 text-gray-500" />;
  };

  const getTrendColor = (amount) => {
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount)) return 'text-gray-600';
    if (numAmount > 0) return 'text-green-600';
    if (numAmount < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getBadgeVariant = (amount) => {
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount)) return 'secondary';
    if (numAmount > 0) return 'default';
    if (numAmount < 0) return 'destructive';
    return 'secondary';
  };

  const getBadgeText = (amount) => {
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount)) return '→ No Change';
    if (numAmount > 0) return '↗ Increase';
    if (numAmount < 0) return '↘ Decrease';
    return '→ No Change';
  };

  // Calculate total liability (credit + loan accounts combined)
  const calculateTotalLiability = () => {
    if (!balanceDeltasGrouped) return null;

    const liabilityAccounts = balanceDeltasGrouped.filter(account => 
      account.accountType === 'credit' || account.accountType === 'loan'
    );

    if (liabilityAccounts.length === 0) return null;

    const totalPreviousBalance = liabilityAccounts.reduce((sum, account) => 
      sum + (parseFloat(account.previousBalance) || 0), 0
    );

    const totalCurrentBalance = liabilityAccounts.reduce((sum, account) => 
      sum + (parseFloat(account.currentBalance) || 0), 0
    );

    const totalDeltaAmount = liabilityAccounts.reduce((sum, account) => 
      sum + (parseFloat(account.deltaAmount || account.absoluteChange || 0)), 0
    );

    const totalDeltaPercentage = totalPreviousBalance !== 0 
      ? (totalDeltaAmount / Math.abs(totalPreviousBalance)) * 100 
      : 0;

    return {
      accountName: 'Total Liability',
      accountType: 'liability',
      accountCategory: 'Combined',
      accountId: 'COMBINED',
      previousBalance: totalPreviousBalance,
      currentBalance: totalCurrentBalance,
      deltaAmount: totalDeltaAmount,
      deltaPercentage: totalDeltaPercentage,
      accountCount: liabilityAccounts.length,
      includedTypes: ['credit', 'loan']
    };
  };

  // Calculate net worth ((depository + investment) - (credit + loan))
  const calculateNetWorth = () => {
    if (!balanceDeltasGrouped) return null;

    const assetAccounts = balanceDeltasGrouped.filter(account => 
      account.accountType === 'depository' || account.accountType === 'investment'
    );

    const liabilityAccounts = balanceDeltasGrouped.filter(account => 
      account.accountType === 'credit' || account.accountType === 'loan'
    );

    // Calculate asset totals
    const totalAssetsPrevious = assetAccounts.reduce((sum, account) => 
      sum + (parseFloat(account.previousBalance) || 0), 0
    );

    const totalAssetsCurrent = assetAccounts.reduce((sum, account) => 
      sum + (parseFloat(account.currentBalance) || 0), 0
    );

    const totalAssetsDelta = assetAccounts.reduce((sum, account) => 
      sum + (parseFloat(account.deltaAmount || account.absoluteChange || 0)), 0
    );

    // Calculate liability totals
    const totalLiabilitiesPrevious = liabilityAccounts.reduce((sum, account) => 
      sum + (parseFloat(account.previousBalance) || 0), 0
    );

    const totalLiabilitiesCurrent = liabilityAccounts.reduce((sum, account) => 
      sum + (parseFloat(account.currentBalance) || 0), 0
    );

    const totalLiabilitiesDelta = liabilityAccounts.reduce((sum, account) => 
      sum + (parseFloat(account.deltaAmount || account.absoluteChange || 0)), 0
    );

    // Calculate net worth
    const netWorthPrevious = totalAssetsPrevious - totalLiabilitiesPrevious;
    const netWorthCurrent = totalAssetsCurrent - totalLiabilitiesCurrent;
    const netWorthDelta = totalAssetsDelta - totalLiabilitiesDelta;

    const networthDeltaPercentage = netWorthPrevious !== 0 
      ? (netWorthDelta / Math.abs(netWorthPrevious)) * 100 
      : 0;

    return {
      accountName: 'Net Worth',
      accountType: 'networth',
      accountCategory: 'Calculated',
      accountId: 'NETWORTH',
      previousBalance: netWorthPrevious,
      currentBalance: netWorthCurrent,
      deltaAmount: netWorthDelta,
      deltaPercentage: networthDeltaPercentage,
      accountCount: assetAccounts.length + liabilityAccounts.length,
      includedTypes: ['depository', 'investment', 'credit', 'loan'],
      assetAccounts: assetAccounts.length,
      liabilityAccounts: liabilityAccounts.length,
      totalAssets: totalAssetsCurrent,
      totalLiabilities: totalLiabilitiesCurrent
    };
  };

  // Filter accounts based on selected account type
  const getFilteredAccounts = () => {
    if (!balanceDeltasGrouped) return [];

    if (selectedAccountType === 'liability') {
      const totalLiability = calculateTotalLiability();
      return totalLiability ? [totalLiability] : [];
    }

    if (selectedAccountType === 'networth') {
      const netWorth = calculateNetWorth();
      return netWorth ? [netWorth] : [];
    }

    if (selectedAccountType === 'all') return balanceDeltasGrouped;

    return balanceDeltasGrouped.filter(account => 
      account.accountType === selectedAccountType
    );
  };

  const filteredAccounts = getFilteredAccounts();

  const renderAccountCards = () => (
    <div className="space-y-6">
      {filteredAccounts.length === 0 ? (
        <Card>
          <CardContent className="p-8">
            <p className="text-center text-gray-500 text-lg">
              {selectedAccountType === 'all' 
                ? "No balance data available for the selected period."
                : selectedAccountType === 'liability'
                ? "No credit or loan accounts found for the selected period."
                : selectedAccountType === 'networth'
                ? "No accounts found to calculate net worth for the selected period."
                : `No ${selectedAccountType} accounts found for the selected period.`
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        filteredAccounts.map((account, index) => {
          const deltaAmount = account.deltaAmount || account.absoluteChange || 0;
          const deltaPercentage = account.deltaPercentage || account.percentageChange || 0;
          const isLiabilityTotal = account.accountType === 'liability';
          const isNetWorth = account.accountType === 'networth';
          
          return (
            <Card key={index} className="hover:shadow-2xl transition-all duration-300">
              <CardHeader className="pb-4" style={{ 
                background: isNetWorth
                  ? 'linear-gradient(135deg, #bbdefb 0%, #90caf9 100%)'
                  : isLiabilityTotal 
                  ? 'linear-gradient(135deg, #ffcc80 0%, #ffb74d 100%)'
                  : 'linear-gradient(135deg, #c5e1a5 0%, #dcedc8 100%)' 
              }}>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-xl font-bold text-gray-800">
                      {account.accountName || `Account ${account.accountId}`}
                      {(isLiabilityTotal || isNetWorth) && (
                        <span className="ml-2 text-sm font-normal text-gray-600">
                          ({account.accountCount} accounts {isNetWorth ? 'included' : 'combined'})
                        </span>
                      )}
                    </CardTitle>
                    <p className="text-sm text-gray-600 font-medium mt-1">
                      {isNetWorth 
                        ? `Net Worth • Assets - Liabilities • ${account.assetAccounts} Assets, ${account.liabilityAccounts} Liabilities`
                        : isLiabilityTotal 
                        ? `Liability • Combined Credit & Loan • Total Balance`
                        : `${account.accountType} • ${account.accountCategory} • ID: ${account.accountId}`
                      }
                    </p>
                  </div>
                  <div className="flex flex-col items-end space-y-2">
                    <Badge variant={getBadgeVariant(deltaAmount)}>
                      {getBadgeText(deltaAmount)}
                    </Badge>
                    <Badge variant={isNetWorth ? 'networth' : isLiabilityTotal ? 'liability' : 'secondary'}>
                      {isNetWorth ? 'Net Worth' : isLiabilityTotal ? 'Total Liability' : account.accountType}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-6">
                {(isLiabilityTotal || isNetWorth) && (
                  <div className="mb-4 p-3 rounded-lg border" style={{
                    backgroundColor: isNetWorth ? '#e3f2fd' : '#fff3e0',
                    borderColor: isNetWorth ? '#2196f3' : '#ff9800'
                  }}>
                    <p className="text-sm font-medium" style={{
                      color: isNetWorth ? '#1565c0' : '#e65100'
                    }}>
                      {isNetWorth ? (
                        <>💎 Net Worth = Total Assets ({formatCurrency(account.totalAssets)}) - Total Liabilities ({formatCurrency(account.totalLiabilities)})</>
                      ) : (
                        <>💡 This represents the combined total of all Credit and Loan accounts. Individual account details are aggregated to show overall liability position.</>
                      )}
                    </p>
                  </div>
                )}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-3 p-4 rounded-lg" style={{ backgroundColor: '#f8f9fa' }}>
                    <p className="text-sm text-gray-600 font-semibold uppercase tracking-wide">
                      Previous {isNetWorth ? 'Net Worth' : 'Balance'}
                      {(isLiabilityTotal || isNetWorth) && <span className="text-xs ml-1">({isNetWorth ? 'Calculated' : 'Combined'})</span>}
                    </p>
                    <p className="text-2xl font-bold text-gray-800">
                      {formatCurrency(account.previousBalance)}
                    </p>
                  </div>
                  <div className="space-y-3 p-4 rounded-lg" style={{ backgroundColor: '#f8f9fa' }}>
                    <p className="text-sm text-gray-600 font-semibold uppercase tracking-wide">
                      Current {isNetWorth ? 'Net Worth' : 'Balance'}
                      {(isLiabilityTotal || isNetWorth) && <span className="text-xs ml-1">({isNetWorth ? 'Calculated' : 'Combined'})</span>}
                    </p>
                    <p className="text-2xl font-bold text-gray-800">
                      {formatCurrency(account.currentBalance)}
                    </p>
                  </div>
                  <div className="space-y-3 p-4 rounded-lg" style={{ backgroundColor: '#f8f9fa' }}>
                    <p className="text-sm text-gray-600 font-semibold uppercase tracking-wide">
                      Change
                      {(isLiabilityTotal || isNetWorth) && <span className="text-xs ml-1">({isNetWorth ? 'Calculated' : 'Combined'})</span>}
                    </p>
                    <div className="flex items-center space-x-3">
                      {getTrendIcon(deltaAmount)}
                      <div className="flex flex-col">
                        <span className={`text-xl font-bold ${getTrendColor(deltaAmount)}`}>
                          {formatCurrency(Math.abs(parseFloat(deltaAmount) || 0))}
                        </span>
                        <span className={`text-sm font-medium ${getTrendColor(deltaAmount)}`}>
                          ({formatPercentage(parseFloat(deltaPercentage) || 0)})
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })
      )}
    </div>
  );

  const getDisplayText = () => {
    if (selectedAccountType === 'liability') {
      const totalLiability = calculateTotalLiability();
      if (totalLiability) {
        return `Showing total liability (${totalLiability.accountCount} credit & loan accounts combined)`;
      }
      return 'No liability accounts found';
    }

    if (selectedAccountType === 'networth') {
      const netWorth = calculateNetWorth();
      if (netWorth) {
        return `Showing net worth calculation (${netWorth.assetAccounts} asset + ${netWorth.liabilityAccounts} liability accounts)`;
      }
      return 'No accounts found for net worth calculation';
    }
    
    const typeText = selectedAccountType === 'all' ? '' : ` (${selectedAccountType} type)`;
    const count = filteredAccounts.length;
    const totalCount = balanceDeltasGrouped?.length || 0;
    
    return `Showing ${count} account${count !== 1 ? 's' : ''}${typeText} for ${months.find(m => m.value === selectedPeriod.month)?.label} ${selectedPeriod.year}${totalCount > count ? ` • Filtered from ${totalCount} total accounts` : ''}`;
  };

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8 min-h-screen bg-white">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-3 rounded-full shadow-lg" style={{ background: 'linear-gradient(135deg, #8bc34a 0%, #689f38 100%)' }}>
            <DollarSign className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-800">Account Balance Delta</h1>
        </div>
      </div>

      {/* Period Selection */}
      <Card className="shadow-xl">
        <CardHeader className="pb-6" style={{ background: 'linear-gradient(135deg, #c5e1a5 0%, #dcedc8 100%)' }}>
          <CardTitle className="flex items-center space-x-3 text-xl">
            <Calendar className="w-6 h-6 text-gray-700" />
            <span>Select Period & Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="space-y-3">
              <label className="text-sm font-semibold text-gray-700 uppercase tracking-wide">Year</label>
              <div className="w-full">
                <Select 
                  value={selectedPeriod.year?.toString()} 
                  onValueChange={(value) => handlePeriodChange('year', value)}
                  placeholder="Year"
                >
                  {years.map(year => (
                    <SelectItem key={year} value={year.toString()}>{year}</SelectItem>
                  ))}
                </Select>
              </div>
            </div>
            <div className="space-y-3">
              <label className="text-sm font-semibold text-gray-700 uppercase tracking-wide">Month</label>
              <div className="w-full">
                <Select 
                  value={selectedPeriod.month?.toString()} 
                  onValueChange={(value) => handlePeriodChange('month', value)}
                  placeholder="Month"
                >
                  {months.map(month => (
                    <SelectItem key={month.value} value={month.value.toString()}>
                      {month.label}
                    </SelectItem>
                  ))}
                </Select>
              </div>
            </div>
            <div className="space-y-3">
              <label className="text-sm font-semibold text-gray-700 uppercase tracking-wide flex items-center space-x-2">
                <Filter className="w-4 w-4" />
                <span>Account Type</span>
              </label>
              <div className="w-full">
                <Select 
                  value={selectedAccountType} 
                  onValueChange={setSelectedAccountType}
                  placeholder="Account Type"
                >
                  {accountTypes.map(type => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </Select>
              </div>
            </div>
            <div className="space-y-3">
              <label className="text-sm font-semibold text-gray-700 uppercase tracking-wide">Actions</label>
              <Button 
                onClick={() => {
                  setSelectedAccountType('all');
                  setSelectedPeriod({
                    year: new Date().getFullYear(),
                    month: new Date().getMonth() + 1
                  });
                }}
                variant="outline"
                className="w-full"
              >
                Reset Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {balanceDeltasGroupedError && (
        <Alert variant="destructive" className="shadow-lg">
          <AlertDescription>
            <div className="flex items-center justify-between">
              <span className="font-medium">{balanceDeltasGroupedError}</span>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => dispatch({ type: 'accounts/clearGroupedError' })}
              >
                Dismiss
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Loading State */}
      {balanceDeltasGroupedLoading && (
        <Card className="shadow-xl">
          <CardContent className="p-8">
            <div className="flex items-center justify-center space-x-3">
              <RefreshCw className="w-6 h-6 animate-spin text-green-600" />
              <span className="text-lg font-medium text-gray-700">Loading balance changes...</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Data Display */}
      {!balanceDeltasGroupedLoading && (
        <div>
          {balanceDeltasGrouped && balanceDeltasGrouped.length > 0 && (
            <div className="mb-6 p-4 rounded-lg shadow-sm text-sm text-gray-700 font-medium" style={{ backgroundColor: '#c5e1a5' }}>
              📊 {getDisplayText()}
            </div>
          )}
          {renderAccountCards()}
        </div>
      )}
    </div>
  );
};

export default AccountBalanceDelta;