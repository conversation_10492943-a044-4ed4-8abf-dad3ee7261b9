import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  // Data states
  userInvestments: [],
  dailyInvestmentStocks: [],
  portfolioSummary: {},
  investmentByTicker: null,
  
  // Loading states
  loading: false,
  syncLoading: false,
  
  // Error and success states
  error: null,
  success: false,
  message: '',
  
  // Filter states
  filters: {
    date: null,
    ticker: null
  },
  
  // Stock history states
  stockHistory: {},
  stockHistoryCustom: {},
  allStocksHistory: null,
  
  // Loading states for history
  stockHistoryLoading: false,
  allStocksHistoryLoading: false,
};

// Helper function to get interval days based on months
const getIntervalDays = (months) => {
  const intervalMapping = {
    1: 3,   // 1 month -> 3 days interval
    3: 10,  // 3 months -> 10 days interval
    6: 20,  // 6 months -> 20 days interval
    12: 41  // 12 months -> 41 days interval
  };
  return intervalMapping[months] || 3; // Default to 3 if not found
};

export const investmentsSlice = createSlice({
  name: 'investment',
  initialState,
  reducers: {
    // Sync Investment Data
    syncInvestmentDataRequest: (state, action) => {
      console.log('Redux: syncInvestmentDataRequest');
      state.syncLoading = true;
      state.error = null;
      state.message = '';
      state.success = false;
    },
    syncInvestmentDataSuccess: (state, action) => {
      console.log('Redux: syncInvestmentDataSuccess', action.payload);
      state.syncLoading = false;
      state.success = true;
      state.message = action.payload?.message || 'Investment data synced successfully';
      state.error = null;
    },
    
    // Get Investments by User ID
    fetchUserInvestmentsRequest: (state, action) => {
      console.log('Redux: fetchUserInvestmentsRequest');
      state.loading = true;
      state.error = null;
      state.success = false;
    },
    fetchUserInvestmentsSuccess: (state, action) => {
      console.log('Redux: fetchUserInvestmentsSuccess', action.payload);
      state.loading = false;
      state.userInvestments = action.payload?.investments || [];
      state.success = true;
      state.error = null;
      console.log('Redux: Setting userInvestments to:', state.userInvestments);
    },
    
    // Get Daily Investment Stocks
    fetchDailyInvestmentStocksRequest: (state, action) => {
      console.log('Redux: fetchDailyInvestmentStocksRequest', action.payload);
      state.loading = true;
      state.error = null;
      state.success = false;
      if (action.payload) {
        state.filters = {
          ...state.filters,
          date: action.payload.date || state.filters.date,
          ticker: action.payload.ticker || state.filters.ticker
        };
      }
    },
    fetchDailyInvestmentStocksSuccess: (state, action) => {
      console.log('Redux: fetchDailyInvestmentStocksSuccess', action.payload);
      state.loading = false;
      state.dailyInvestmentStocks = action.payload?.dailyStocks || [];
      state.success = true;
      state.error = null;
      
      if (action.payload?.filters) {
        state.filters = { ...state.filters, ...action.payload.filters };
      }
      console.log('Redux: Setting dailyInvestmentStocks to:', state.dailyInvestmentStocks);
    },
    
    // Get Portfolio Summary
    fetchPortfolioSummaryRequest: (state, action) => {
      console.log('Redux: fetchPortfolioSummaryRequest');
      state.loading = true;
      state.error = null;
      state.success = false;
    },
    fetchPortfolioSummarySuccess: (state, action) => {
      console.log('Redux: fetchPortfolioSummarySuccess', action.payload);
      state.loading = false;
      state.portfolioSummary = action.payload?.portfolioSummary || {};
      state.success = true;
      state.error = null;
      console.log('Redux: Setting portfolioSummary to:', state.portfolioSummary);
    },
    
    // Get Investment by Ticker
    fetchInvestmentByTickerRequest: (state, action) => {
      console.log('Redux: fetchInvestmentByTickerRequest', action.payload);
      state.loading = true;
      state.error = null;
      state.success = false;
    },
    fetchInvestmentByTickerSuccess: (state, action) => {
      console.log('Redux: fetchInvestmentByTickerSuccess', action.payload);
      state.loading = false;
      state.investmentByTicker = action.payload?.investment || null;
      state.success = true;
      state.error = null;
      console.log('Redux: Setting investmentByTicker to:', state.investmentByTicker);
    },
    
    // Get All Stocks Aggregated Data
    fetchAllStocksHistoryRequest: (state, action) => {
      console.log('Redux: fetchAllStocksHistoryRequest', action.payload);
      state.allStocksHistoryLoading = true;
      state.error = null;
      state.success = false;
    },
    fetchAllStocksHistorySuccess: (state, action) => {
      console.log('Redux: fetchAllStocksHistorySuccess', action.payload);
      state.allStocksHistoryLoading = false;
      
      // Transform the data with x,y mapping
      const { data, months } = action.payload;
      const transformedData = {
        ...data,
        chartData: data.chartData?.map((item, index) => ({
          x: index,
          y: item.totalValue || 0,
          date: item.date,
          totalValue: item.totalValue,
          totalShares: item.totalShares
        })) || [],
        months,
        intervalDays: getIntervalDays(months)
      };
      
      state.allStocksHistory = transformedData;
      state.success = true;
      state.error = null;
      console.log('Redux: Setting allStocksHistory to:', state.allStocksHistory);
    },
    
    // Get Single Stock Aggregated Data
    fetchStockHistoryRequest: (state, action) => {
      console.log('Redux: fetchStockHistoryRequest', action.payload);
      state.stockHistoryLoading = true;
      state.error = null;
      state.success = false;
    },
    fetchStockHistorySuccess: (state, action) => {
      console.log('Redux: fetchStockHistorySuccess', action.payload);
      state.stockHistoryLoading = false;
      
      // Transform the data with x,y mapping
      const { data, ticker, months } = action.payload;
      const transformedData = {
        ...data,
        chartData: data.chartData?.map((item, index) => ({
          x: index,
          y: item.totalValue || 0,
          date: item.date,
          totalValue: item.totalValue,
          totalShares: item.totalShares,
          price: item.price
        })) || [],
        ticker,
        months,
        intervalDays: getIntervalDays(months)
      };
      
      // Store by ticker for easy access
      state.stockHistory = {
        ...state.stockHistory,
        [ticker]: transformedData
      };
      
      state.success = true;
      state.error = null;
      console.log('Redux: Setting stockHistory for', ticker, ':', transformedData);
    },
   
    // Failure action
    investmentActionFailure: (state, action) => {
      console.log('Redux: investmentActionFailure', action.payload);
      state.loading = false;
      state.syncLoading = false;
      state.stockHistoryLoading = false;
      state.allStocksHistoryLoading = false;
      state.error = action.payload;
      state.success = false;
      state.message = '';
    },
    
    // Reset states
    resetInvestmentStatus: (state) => {
      console.log('Redux: resetInvestmentStatus');
      state.loading = false;
      state.syncLoading = false;
      state.stockHistoryLoading = false;
      state.allStocksHistoryLoading = false;
      state.success = false;
      state.error = null;
      state.message = '';
    },
    
    // Clear specific data
    clearInvestmentByTicker: (state) => {
      console.log('Redux: clearInvestmentByTicker');
      state.investmentByTicker = null;
    },
    
    clearDailyInvestmentStocks: (state) => {
      console.log('Redux: clearDailyInvestmentStocks');
      state.dailyInvestmentStocks = [];
    },
    
    clearPortfolioSummary: (state) => {
      console.log('Redux: clearPortfolioSummary');
      state.portfolioSummary = {};
    },
    
    clearStockHistory: (state, action) => {
      console.log('Redux: clearStockHistory', action.payload);
      if (action.payload?.ticker) {
        // Clear specific ticker
        delete state.stockHistory[action.payload.ticker];
      } else {
        // Clear all stock history
        state.stockHistory = {};
      }
    },
    
    clearAllStocksHistory: (state) => {
      console.log('Redux: clearAllStocksHistory');
      state.allStocksHistory = null;
    },
    
    // Set filter states
    setFilters: (state, action) => {
      console.log('Redux: setFilters', action.payload);
      state.filters = {
        ...state.filters,
        ...action.payload
      };
    },
    
    // Clear all data
    clearAllInvestmentData: (state) => {
      console.log('Redux: clearAllInvestmentData');
      state.userInvestments = [];
      state.dailyInvestmentStocks = [];
      state.portfolioSummary = {};
      state.investmentByTicker = null;
      state.filters = { date: null, ticker: null };
      state.stockHistory = {};
      state.stockHistoryCustom = {};
      state.allStocksHistory = null;
      state.error = null;
      state.success = false;
      state.message = '';
    }
  }
});

export const {
  // Sync actions
  syncInvestmentDataRequest,
  syncInvestmentDataSuccess,
  
  // Fetch actions
  fetchUserInvestmentsRequest,
  fetchUserInvestmentsSuccess,
  fetchDailyInvestmentStocksRequest,
  fetchDailyInvestmentStocksSuccess,
  fetchPortfolioSummaryRequest,
  fetchPortfolioSummarySuccess,
  fetchInvestmentByTickerRequest,
  fetchInvestmentByTickerSuccess,
  
  // Stock history actions
  fetchAllStocksHistoryRequest,
  fetchAllStocksHistorySuccess,
  fetchStockHistoryRequest,
  fetchStockHistorySuccess,
  
  // Utility actions
  investmentActionFailure,
  resetInvestmentStatus,
  clearInvestmentByTicker,
  clearDailyInvestmentStocks,
  clearPortfolioSummary,
  clearStockHistory,
  clearAllStocksHistory,
  setFilters,
  clearAllInvestmentData,

} = investmentsSlice.actions;

export default investmentsSlice.reducer;