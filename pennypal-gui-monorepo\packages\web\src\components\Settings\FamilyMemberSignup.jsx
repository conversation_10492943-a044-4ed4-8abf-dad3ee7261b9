// import React, { useState, useEffect } from 'react';
// import { useDispatch, useSelector } from 'react-redux';
// import { useNavigate, useLocation } from 'react-router-dom';
// import {
//   validateInviteLinkRequest,
//   completeSignupRequest
// } from '../../../../logic/redux/familySlice';
// import {
//   Container,
//   Grid,
//   Typography,
//   Paper,
//   TextField,
//   Button,
//   CircularProgress,
//   Alert,
//   Stepper,
//   Step,
//   StepLabel,
//   Box,
//   Avatar,
//   Link
// } from '@mui/material';
// import HowToRegIcon from '@mui/icons-material/HowToReg';
// import CheckCircleIcon from '@mui/icons-material/CheckCircle';
// import ErrorIcon from '@mui/icons-material/Error';
// import ArrowBackIcon from '@mui/icons-material/ArrowBack';
// import { signInSuccess } from '../../../../logic/redux/authSlice';

// const FamilyMemberSignup = () => {
//   const dispatch = useDispatch();
//   const navigate = useNavigate();
//   const location = useLocation();
  
//   // Get token from URL query parameter
//   const queryParams = new URLSearchParams(location.search);
//   const inviteToken = queryParams.get('token');
  
//   const { loading, error, validationResponse, membershipValid, signupComplete } = useSelector(
//     (state) => state.membership
//   );
  
//   const [activeStep, setActiveStep] = useState(0);
//   const [formData, setFormData] = useState({
//     firstName: '',
//     lastName: '',
//     mobileNumber: '',
//     password: '',
//     confirmPassword: ''
//   });
//   const [passwordError, setPasswordError] = useState('');
  
//   // Validate the invitation link when component mounts
//   useEffect(() => {
//     if (inviteToken) {
//       dispatch(validateInviteLinkRequest(inviteToken));
//     }
//   }, [dispatch, inviteToken]);
  
//   // Pre-fill form with validation response data
//   useEffect(() => {
//     if (validationResponse) {
//       setFormData(prevState => ({
//         ...prevState,
//         firstName: validationResponse.firstName || '',
//         lastName: validationResponse.lastName || '',
//         email: validationResponse.email || ''
//       }));
      
//       // Move to step 1 when validation is successful
//       if (membershipValid) {
//         setActiveStep(1);
//       }
//     }
//   }, [validationResponse, membershipValid]);
  
//   // Redirect to dashboard when signup is complete
//   useEffect(() => {
//     if (signupComplete) {
//       // If the API returns user data, update the auth state
//       if (validationResponse && validationResponse.user) {
//         dispatch(signInSuccess({ user: validationResponse.user }));
//       }
      
//       // Show success message and redirect after a short delay
//       setActiveStep(2);
//       setTimeout(() => {
//         navigate('/dashboard');
//       }, 3000);
//     }
//   }, [signupComplete, navigate, dispatch, validationResponse]);
  
//   const handleInputChange = (e) => {
//     const { name, value } = e.target;
//     setFormData({
//       ...formData,
//       [name]: value
//     });
    
//     // Password validation
//     if (name === 'confirmPassword' || name === 'password') {
//       if (name === 'confirmPassword' && value !== formData.password) {
//         setPasswordError('Passwords do not match');
//       } else if (name === 'password' && formData.confirmPassword && value !== formData.confirmPassword) {
//         setPasswordError('Passwords do not match');
//       } else {
//         setPasswordError('');
//       }
//     }
//   };
  
//   const handleSubmit = (e) => {
//     e.preventDefault();
    
//     // Check if passwords match
//     if (formData.password !== formData.confirmPassword) {
//       setPasswordError('Passwords do not match');
//       return;
//     }
    
//     // Submit the signup details
//     dispatch(completeSignupRequest({
//       token: inviteToken,
//       signupDetails: {
//         firstName: formData.firstName,
//         lastName: formData.lastName,
//         mobileNumber: formData.mobileNumber,
//         password: formData.password
//       }
//     }));
//   };
  
//   const steps = ['Validate Invitation', 'Complete Your Profile', 'Welcome!'];
  
//   return (
//     <Container maxWidth="md" sx={{ mt: 8, mb: 8 }}>
//       <Paper elevation={3} sx={{ p: 4 }}>
//         <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4 }}>
//           <Avatar sx={{ m: 1, bgcolor: 'primary.main', width: 56, height: 56 }}>
//             <HowToRegIcon fontSize="large" />
//           </Avatar>
//         </Box>
        
//         <Typography component="h1" variant="h4" align="center" gutterBottom>
//           Family Member Signup
//         </Typography>
        
//         <Stepper activeStep={activeStep} alternativeLabel sx={{ mb: 5, mt: 3 }}>
//           {steps.map((label) => (
//             <Step key={label}>
//               <StepLabel>{label}</StepLabel>
//             </Step>
//           ))}
//         </Stepper>
        
//         {/* Step 0: Validating Invitation */}
//         {activeStep === 0 && (
//           <Box sx={{ textAlign: 'center', py: 3 }}>
//             {loading ? (
//               <>
//                 <CircularProgress sx={{ mb: 2 }} />
//                 <Typography variant="h6">Validating your invitation...</Typography>
//               </>
//             ) : error ? (
//               <>
//                 <ErrorIcon color="error" sx={{ fontSize: 60, mb: 2 }} />
//                 <Typography variant="h6" color="error" gutterBottom>
//                   Invalid or Expired Invitation
//                 </Typography>
//                 <Typography variant="body1" paragraph>
//                   The invitation link you're using is invalid or has expired.
//                 </Typography>
//                 <Button
//                   variant="contained"
//                   color="primary"
//                   startIcon={<ArrowBackIcon />}
//                   onClick={() => navigate('/login')}
//                 >
//                   Back to Login
//                 </Button>
//               </>
//             ) : !membershipValid && (
//               <>
//                 <CircularProgress sx={{ mb: 2 }} />
//                 <Typography variant="h6">Validating your invitation...</Typography>
//               </>
//             )}
//           </Box>
//         )}
        
//         {/* Step 1: Complete Profile Form */}
//         {activeStep === 1 && (
//           <Box component="form" onSubmit={handleSubmit} noValidate>
//             <Grid container spacing={2}>
//               <Grid item xs={12} sm={6}>
//                 <TextField
//                   required
//                   fullWidth
//                   id="firstName"
//                   label="First Name"
//                   name="firstName"
//                   value={formData.firstName}
//                   onChange={handleInputChange}
//                   disabled={loading}
//                 />
//               </Grid>
//               <Grid item xs={12} sm={6}>
//                 <TextField
//                   required
//                   fullWidth
//                   id="lastName"
//                   label="Last Name"
//                   name="lastName"
//                   value={formData.lastName}
//                   onChange={handleInputChange}
//                   disabled={loading}
//                 />
//               </Grid>
//               <Grid item xs={12}>
//                 <TextField
//                   required
//                   fullWidth
//                   id="email"
//                   label="Email Address"
//                   name="email"
//                   value={formData.email}
//                   disabled={true} // Email cannot be changed
//                   helperText="Email cannot be changed"
//                 />
//               </Grid>
//               <Grid item xs={12}>
//                 <TextField
//                   required
//                   fullWidth
//                   id="mobileNumber"
//                   label="Mobile Number"
//                   name="mobileNumber"
//                   value={formData.mobileNumber}
//                   onChange={handleInputChange}
//                   disabled={loading}
//                 />
//               </Grid>
//               <Grid item xs={12}>
//                 <TextField
//                   required
//                   fullWidth
//                   name="password"
//                   label="Password"
//                   type="password"
//                   id="password"
//                   value={formData.password}
//                   onChange={handleInputChange}
//                   disabled={loading}
//                 />
//               </Grid>
//               <Grid item xs={12}>
//                 <TextField
//                   required
//                   fullWidth
//                   name="confirmPassword"
//                   label="Confirm Password"
//                   type="password"
//                   id="confirmPassword"
//                   value={formData.confirmPassword}
//                   onChange={handleInputChange}
//                   error={!!passwordError}
//                   helperText={passwordError}
//                   disabled={loading}
//                 />
//               </Grid>
//             </Grid>
            
//             {error && (
//               <Alert severity="error" sx={{ mt: 2 }}>
//                 {error}
//               </Alert>
//             )}
            
//             <Button
//               type="submit"
//               fullWidth
//               variant="contained"
//               sx={{ mt: 3, mb: 2 }}
//               disabled={loading || !!passwordError}
//             >
//               {loading ? <CircularProgress size={24} /> : 'Complete Signup'}
//             </Button>
            
//             <Grid container justifyContent="center">
//               <Grid item>
//                 <Link component="button" variant="body2" onClick={() => navigate('/login')}>
//                   Already have an account? Sign in
//                 </Link>
//               </Grid>
//             </Grid>
//           </Box>
//         )}
        
//         {/* Step 2: Success Message */}
//         {activeStep === 2 && (
//           <Box sx={{ textAlign: 'center', py: 3 }}>
//             <CheckCircleIcon color="success" sx={{ fontSize: 60, mb: 2 }} />
//             <Typography variant="h5" gutterBottom>
//               Welcome to PennyPal!
//             </Typography>
//             <Typography variant="body1" paragraph>
//               Your account has been successfully created. You are now a family member.
//             </Typography>
//             <Typography variant="body2" color="textSecondary">
//               Redirecting to dashboard...
//             </Typography>
//             <CircularProgress size={20} sx={{ mt: 2 }} />
//           </Box>
//         )}
//       </Paper>
//     </Container>
//   );
// };

// export default FamilyMemberSignup;