// import React, { useState, useEffect } from 'react';
// import { useDispatch, useSelector } from 'react-redux';
// import { 
//   User, 
//   Settings, 
//   Shield, 
//   Star, 
//   Lock, 
//   Trash2, 
//   Camera, 
//   Edit3, 
//   Check, 
//   X, 
//   Eye, 
//   EyeOff,
//   AlertTriangle,
//   Loader2,
//   Mail,
//   Phone,
//   Calendar,
//   MapPin
// } from 'lucide-react';

// // Profile slice actions
// import {
//   fetchUserStart,
//   fetchUserSuccess, 
//   fetchUserFailure,
//   updateUserStart,
//   updateUserSuccess,
//   updateUserFailure,
//   clearUpdateError,
//   clearUpdateSuccess,
//   selectUser,
//   selectFetchLoading,
//   selectFetchError,
//   selectUpdateLoading,
//   selectUpdateError,
//   selectUpdateSuccess
// } from '../../../../logic/redux/profileSlice';

// // Two-factor auth actions
// import {
//   setupTwoFactorAuthRequest,
//   enableTwoFactorAuthRequest,
//   disableTwoFactorAuthRequest,
//   getTwoFactorStatusRequest,
//   resetTwoFactorAuthStatus
// } from '../../../../logic/redux/twoFactorAuthSlice';

// // Rating actions
// import {
//   getUserRatingRequest,
//   getRatingStatsRequest
// } from '../../../../logic/redux/ratingSlice';

// // Account management actions
// import {
//   changePassword,
//   deleteAccountPermanently,
//   clearChangePasswordState,
//   clearDeleteAccountState
// } from '../../../../logic/redux/accountManagementSlice';

// const Profile = () => {
//   const dispatch = useDispatch();
  
//   // Profile state
//   const user = useSelector(selectUser);
//   const fetchLoading = useSelector(selectFetchLoading);
//   const fetchError = useSelector(selectFetchError);
//   const updateLoading = useSelector(selectUpdateLoading);
//   const updateError = useSelector(selectUpdateError);
//   const updateSuccess = useSelector(selectUpdateSuccess);
  
//   // Two-factor auth state
//   const twoFactorState = useSelector(state => state.twoFactorAuth);
  
//   // Rating state
//   const ratingState = useSelector(state => state.rating);
  
//   // Account management state
//   const accountState = useSelector(state => state.accountManagement);
  
//   // Local state
//   const [activeTab, setActiveTab] = useState('profile');
//   const [isEditing, setIsEditing] = useState(false);
//   const [showPasswordChange, setShowPasswordChange] = useState(false);
//   const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
//   const [showPasswords, setShowPasswords] = useState({
//     current: false,
//     new: false,
//     confirm: false
//   });
  
//   // Form states
//   const [profileForm, setProfileForm] = useState({
//     name: '',
//     email: '',
//     phone: '',
//     bio: '',
//     address: '',
//   });
  
//   const [passwordForm, setPasswordForm] = useState({
//     currentPassword: '',
//     newPassword: '',
//     confirmPassword: ''
//   });
  
//   const [deleteForm, setDeleteForm] = useState({
//     password: '',
//     confirmText: ''
//   });

//   // Initialize data on component mount
//   useEffect(() => {
//     dispatch(fetchUserStart());
//     dispatch(getTwoFactorStatusRequest());
//     dispatch(getUserRatingRequest());
//     dispatch(getRatingStatsRequest());
//   }, [dispatch]);

//   // Update form when user data loads
//   useEffect(() => {
//     if (user) {
//       setProfileForm({
//         name: user.name || '',
//         email: user.email || '',
//         phone: user.phone || '',
//         bio: user.bio || '',
//         address: user.address || '',
//       });
//     }
//   }, [user]);

//   // Clear success message after 3 seconds
//   useEffect(() => {
//     if (updateSuccess) {
//       const timer = setTimeout(() => {
//         dispatch(clearUpdateSuccess());
//       }, 3000);
//       return () => clearTimeout(timer);
//     }
//   }, [updateSuccess, dispatch]);

//   // Clear error message after 5 seconds
//   useEffect(() => {
//     if (updateError) {
//       const timer = setTimeout(() => {
//         dispatch(clearUpdateError());
//       }, 5000);
//       return () => clearTimeout(timer);
//     }
//   }, [updateError, dispatch]);

//   const handleProfileSubmit = (e) => {
//     e.preventDefault();
//     dispatch(updateUserStart(profileForm));
//     setIsEditing(false);
//   };

//   const handlePasswordChange = (e) => {
//     e.preventDefault();
//     if (passwordForm.newPassword !== passwordForm.confirmPassword) {
//       return;
//     }
//     dispatch(changePassword({
//       currentPassword: passwordForm.currentPassword,
//       newPassword: passwordForm.newPassword
//     }));
//     setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' });
//     setShowPasswordChange(false);
//   };

//   const handleDeleteAccount = (e) => {
//     e.preventDefault();
//     if (deleteForm.confirmText === 'DELETE') {
//       dispatch(deleteAccountPermanently({
//         password: deleteForm.password
//       }));
//       setShowDeleteConfirm(false);
//     }
//   };

//   const handleTwoFactorToggle = () => {
//     if (twoFactorState.isEnabled) {
//       dispatch(disableTwoFactorAuthRequest());
//     } else {
//       dispatch(setupTwoFactorAuthRequest());
//     }
//   };

//   const handleCancelEdit = () => {
//     // Reset form to original user data
//     if (user) {
//       setProfileForm({
//         name: user.name || '',
//         email: user.email || '',
//         phone: user.phone || '',
//         bio: user.bio || '',
//         address: user.address || '',
//       });
//     }
//     setIsEditing(false);
//     dispatch(clearUpdateError());
//   };

//   const handleEditToggle = () => {
//     if (isEditing) {
//       handleCancelEdit();
//     } else {
//       setIsEditing(true);
//     }
//   };

//   const tabs = [
//     { id: 'profile', label: 'Profile', icon: User },
//     { id: 'security', label: 'Security', icon: Shield },
//   ];

//   if (fetchLoading) {
//     return (
//       <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center">
//         <div className="flex items-center space-x-3">
//           <Loader2 className="w-8 h-8 animate-spin text-indigo-600" />
//           <span className="text-lg font-medium text-gray-700">Loading your profile...</span>
//         </div>
//       </div>
//     );
//   }

//   return (
//     <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
//       <div className="max-w-6xl mx-auto px-4 py-8">
//         {/* Header */}
//         <div className="bg-white rounded-2xl shadow-xl overflow-hidden mb-8">
//           <div className="h-32 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500"></div>
//           <div className="px-8 pb-8">
//             <div className="flex items-center space-x-6 -mt-16">
//               <div className="relative">
//                 <div className="w-24 h-24 bg-white rounded-full p-1 shadow-lg">
//                   <div className="w-full h-full bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full flex items-center justify-center">
//                     <User className="w-10 h-10 text-white" />
//                   </div>
//                 </div>
//                 <button className="absolute -bottom-1 -right-1 w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center shadow-lg hover:bg-indigo-600 transition-colors">
//                   <Camera className="w-4 h-4 text-white" />
//                 </button>
//               </div>
//               <div className="flex-1 pt-16">
//                 <div className="flex items-center justify-between">
//                   <div>
//                     <h1 className="text-3xl font-bold text-gray-900">
//                       {user?.name || 'Loading...'}
//                     </h1>
//                     <p className="text-gray-600 mt-1">{user?.email || 'Loading...'}</p>
//                     <div className="flex items-center space-x-4 mt-2">
//                       {user?.address && (
//                         <div className="flex items-center space-x-1 text-sm text-gray-500">
//                           <MapPin className="w-4 h-4" />
//                           <span>{user.address}</span>
//                         </div>
//                       )}
//                       {ratingState.ratingStats && (
//                         <div className="flex items-center space-x-1 text-sm text-gray-500">
//                           <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
//                           <span>{ratingState.ratingStats.averageRating}/5</span>
//                         </div>
//                       )}
//                     </div>
//                   </div>
//                   {updateSuccess && (
//                     <div className="bg-green-50 text-green-600 px-4 py-2 rounded-lg flex items-center space-x-2">
//                       <Check className="w-4 h-4" />
//                       <span>Profile updated successfully!</span>
//                     </div>
//                   )}
//                 </div>
//               </div>
//             </div>
//           </div>
//         </div>

//         {/* Navigation Tabs */}
//         <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
//           <div className="border-b border-gray-200">
//             <nav className="flex space-x-8 px-8">
//               {tabs.map((tab) => {
//                 const Icon = tab.icon;
//                 return (
//                   <button
//                     key={tab.id}
//                     onClick={() => setActiveTab(tab.id)}
//                     className={`flex items-center space-x-2 py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
//                       activeTab === tab.id
//                         ? 'border-indigo-500 text-indigo-600'
//                         : 'border-transparent text-gray-500 hover:text-gray-700'
//                     }`}
//                   >
//                     <Icon className="w-4 h-4" />
//                     <span>{tab.label}</span>
//                   </button>
//                 );
//               })}
//             </nav>
//           </div>

//           <div className="p-8">
//             {/* Profile Tab */}
//             {activeTab === 'profile' && (
//               <div className="space-y-6">
//                 <div className="flex items-center justify-between">
//                   <h2 className="text-2xl font-bold text-gray-900">Personal Information</h2>
//                   <button
//                     onClick={handleEditToggle}
//                     className="flex items-center space-x-2 px-4 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors"
//                   >
//                     <Edit3 className="w-4 h-4" />
//                     <span>{isEditing ? 'Cancel' : 'Edit'}</span>
//                   </button>
//                 </div>

//                 <form onSubmit={handleProfileSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-6">
//                   <div>
//                     <label className="block text-sm font-medium text-gray-700 mb-2">Name</label>
//                     <input
//                       type="text"
//                       value={profileForm.name}
//                       onChange={(e) => setProfileForm({...profileForm, name: e.target.value})}
//                       disabled={!isEditing}
//                       className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent disabled:bg-gray-50"
//                       placeholder="Enter your name"
//                     />
//                   </div>

//                   <div>
//                     <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
//                     <input
//                       type="email"
//                       value={profileForm.email}
//                       onChange={(e) => setProfileForm({...profileForm, email: e.target.value})}
//                       disabled={!isEditing}
//                       className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent disabled:bg-gray-50"
//                       placeholder="Enter your email"
//                     />
//                   </div>

//                   <div>
//                     <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
//                     <input
//                       type="tel"
//                       value={profileForm.phone}
//                       onChange={(e) => setProfileForm({...profileForm, phone: e.target.value})}
//                       disabled={!isEditing}
//                       className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent disabled:bg-gray-50"
//                       placeholder="Enter your phone number"
//                     />
//                   </div>

//                   <div>
//                     <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
//                     <input
//                       type="text"
//                       value={profileForm.address}
//                       onChange={(e) => setProfileForm({...profileForm, address: e.target.value})}
//                       disabled={!isEditing}
//                       className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent disabled:bg-gray-50"
//                       placeholder="Enter your address"
//                     />
//                   </div>

//                   <div className="md:col-span-2">
//                     <label className="block text-sm font-medium text-gray-700 mb-2">Bio</label>
//                     <textarea
//                       value={profileForm.bio}
//                       onChange={(e) => setProfileForm({...profileForm, bio: e.target.value})}
//                       disabled={!isEditing}
//                       rows={3}
//                       className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent disabled:bg-gray-50"
//                       placeholder="Tell us about yourself..."
//                     />
//                   </div>

//                   {isEditing && (
//                     <div className="md:col-span-2 flex items-center space-x-4">
//                       <button
//                         type="submit"
//                         disabled={updateLoading}
//                         className="flex items-center space-x-2 px-6 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
//                       >
//                         {updateLoading ? (
//                           <Loader2 className="w-4 h-4 animate-spin" />
//                         ) : (
//                           <Check className="w-4 h-4" />
//                         )}
//                         <span>Save Changes</span>
//                       </button>
//                       <button
//                         type="button"
//                         onClick={handleCancelEdit}
//                         disabled={updateLoading}
//                         className="flex items-center space-x-2 px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
//                       >
//                         <X className="w-4 h-4" />
//                         <span>Cancel</span>
//                       </button>
//                     </div>
//                   )}
//                 </form>

//                 {/* Ratings Summary */}
//                 <div className="mt-8">
//                   <h3 className="text-xl font-bold text-gray-900 mb-4">Ratings Summary</h3>
//                   {ratingState.isLoadingStats ? (
//                     <div className="flex items-center justify-center py-12">
//                       <Loader2 className="w-8 h-8 animate-spin text-indigo-600" />
//                     </div>
//                   ) : ratingState.ratingStats ? (
//                     <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
//                       <div className="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl p-6 text-white">
//                         <div className="flex items-center space-x-3">
//                           <Star className="w-8 h-8 fill-white" />
//                           <div>
//                             <p className="text-2xl font-bold">{ratingState.ratingStats.averageRating}</p>
//                             <p className="text-yellow-100">Average Rating</p>
//                           </div>
//                         </div>
//                       </div>
//                       <div className="bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl p-6 text-white">
//                         <div className="flex items-center space-x-3">
//                           <User className="w-8 h-8" />
//                           <div>
//                             <p className="text-2xl font-bold">{ratingState.ratingStats.totalRatings}</p>
//                             <p className="text-blue-100">Total Ratings</p>
//                           </div>
//                         </div>
//                       </div>
//                       <div className="bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl p-6 text-white">
//                         <div className="flex items-center space-x-3">
//                           <Check className="w-8 h-8" />
//                           <div>
//                             <p className="text-2xl font-bold">{ratingState.ratingStats.fiveStarCount}</p>
//                             <p className="text-green-100">5-Star Ratings</p>
//                           </div>
//                         </div>
//                       </div>
//                     </div>
//                   ) : (
//                     <div className="text-center py-12">
//                       <Star className="w-16 h-16 text-gray-300 mx-auto mb-4" />
//                       <p className="text-gray-500">No ratings data available</p>
//                     </div>
//                   )}
//                 </div>
//               </div>
//             )}

//             {/* Security Tab */}
//             {activeTab === 'security' && (
//               <div className="space-y-8">
//                 <h2 className="text-2xl font-bold text-gray-900">Security Settings</h2>

//                 {/* Password Change */}
//                 <div className="bg-gray-50 rounded-xl p-6">
//                   <div className="flex items-center justify-between mb-4">
//                     <div>
//                       <h3 className="text-lg font-semibold text-gray-900">Password</h3>
//                       <p className="text-sm text-gray-600">Update your password to keep your account secure</p>
//                     </div>
//                     <button
//                       onClick={() => setShowPasswordChange(!showPasswordChange)}
//                       className="px-4 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors"
//                     >
//                       Change Password
//                     </button>
//                   </div>

//                   {showPasswordChange && (
//                     <form onSubmit={handlePasswordChange} className="space-y-4 mt-4">
//                       <div>
//                         <label className="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
//                         <div className="relative">
//                           <input
//                             type={showPasswords.current ? 'text' : 'password'}
//                             value={passwordForm.currentPassword}
//                             onChange={(e) => setPasswordForm({...passwordForm, currentPassword: e.target.value})}
//                             className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
//                             required
//                           />
//                           <button
//                             type="button"
//                             onClick={() => setShowPasswords({...showPasswords, current: !showPasswords.current})}
//                             className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
//                           >
//                             {showPasswords.current ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
//                           </button>
//                         </div>
//                       </div>

//                       <div>
//                         <label className="block text-sm font-medium text-gray-700 mb-2">New Password</label>
//                         <div className="relative">
//                           <input
//                             type={showPasswords.new ? 'text' : 'password'}
//                             value={passwordForm.newPassword}
//                             onChange={(e) => setPasswordForm({...passwordForm, newPassword: e.target.value})}
//                             className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
//                             required
//                           />
//                           <button
//                             type="button"
//                             onClick={() => setShowPasswords({...showPasswords, new: !showPasswords.new})}
//                             className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
//                           >
//                             {showPasswords.new ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
//                           </button>
//                         </div>
//                       </div>

//                       <div>
//                         <label className="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
//                         <div className="relative">
//                           <input
//                             type={showPasswords.confirm ? 'text' : 'password'}
//                             value={passwordForm.confirmPassword}
//                             onChange={(e) => setPasswordForm({...passwordForm, confirmPassword: e.target.value})}
//                             className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
//                             required
//                           />
//                           <button
//                             type="button"
//                             onClick={() => setShowPasswords({...showPasswords, confirm: !showPasswords.confirm})}
//                             className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
//                           >
//                             {showPasswords.confirm ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
//                           </button>
//                         </div>
//                       </div>

//                       <div className="flex items-center space-x-4">
//                         <button
//                           type="submit"
//                           disabled={accountState.changePasswordLoading}
//                           className="flex items-center space-x-2 px-6 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors disabled:opacity-50"
//                         >
//                           {accountState.changePasswordLoading ? (
//                             <Loader2 className="w-4 h-4 animate-spin" />
//                           ) : (
//                             <Lock className="w-4 h-4" />
//                           )}
//                           <span>Update Password</span>
//                         </button>
//                         <button
//                           type="button"
//                           onClick={() => setShowPasswordChange(false)}
//                           className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
//                         >
//                           Cancel
//                         </button>
//                       </div>
//                     </form>
//                   )}
//                 </div>

//                 {/* Two-Factor Authentication */}
//                 <div className="bg-gray-50 rounded-xl p-6">
//                   <div className="flex items-center justify-between">
//                     <div>
//                       <h3 className="text-lg font-semibold text-gray-900">Two-Factor Authentication</h3>
//                       <p className="text-sm text-gray-600">Add an extra layer of security to your account</p>
//                     </div>
//                     <div className="flex items-center space-x-3">
//                       <span className={`text-sm font-medium ${twoFactorState.isEnabled ? 'text-green-600' : 'text-gray-500'}`}>
//                         {twoFactorState.isEnabled ? 'Enabled' : 'Disabled'}
//                       </span>
//                       <button
//                         onClick={handleTwoFactorToggle}
//                         disabled={twoFactorState.loading}
//                         className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
//                           twoFactorState.isEnabled ? 'bg-indigo-600' : 'bg-gray-200'
//                         }`}
//                       >
//                         <span
//                           className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
//                             twoFactorState.isEnabled ? 'translate-x-6' : 'translate-x-1'
//                           }`}
//                         />
//                       </button>
//                     </div>
//                   </div>
//                 </div>

//                 {/* Delete Account */}
//                 <div className="bg-red-50 rounded-xl p-6 border border-red-200">
//                   <div className="flex items-start space-x-3">
//                     <AlertTriangle className="w-6 h-6 text-red-500 mt-0.5" />
//                     <div className="flex-1">
//                       <h3 className="text-lg font-semibold text-red-900">Delete Account</h3>
//                       <p className="text-sm text-red-700 mt-1">
//                         Once you delete your account, there is no going back. Please be certain.
//                       </p>
//                       <button
//                         onClick={() => setShowDeleteConfirm(true)}
//                         className="mt-4 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
//                       >
//                         Delete Account
//                       </button>
//                     </div>
//                   </div>
//                 </div>

//                 {/* Delete Confirmation Modal */}
//                 {showDeleteConfirm && (
//                   <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
//                     <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
//                       <div className="flex items-center space-x-3 mb-4">
//                         <AlertTriangle className="w-6 h-6 text-red-500" />
//                         <h3 className="text-lg font-semibold text-gray-900">Confirm Account Deletion</h3>
//                       </div>
//                       <p className="text-gray-600 mb-4">
//                         This action cannot be undone. Type "DELETE" to confirm.
//                       </p>
//                       <form onSubmit={handleDeleteAccount} className="space-y-4">
//                         <input
//                           type="password"
//                           placeholder="Enter your password"
//                           value={deleteForm.password}
//                           onChange={(e) => setDeleteForm({...deleteForm, password: e.target.value})}
//                           className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
//                           required
//                         />
//                         <input
//                           type="text"
//                           placeholder="Type DELETE to confirm"
//                           value={deleteForm.confirmText}
//                           onChange={(e) => setDeleteForm({...deleteForm, confirmText: e.target.value})}
//                           className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
//                           required
//                         />
//                         <div className="flex items-center space-x-4">
//                           <button
//                             type="submit"
//                             disabled={deleteForm.confirmText !== 'DELETE' || accountState.deleteAccountLoading}
//                             className="flex items-center space-x-2 px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50"
//                           >
//                             {accountState.deleteAccountLoading ? (
//                               <Loader2 className="w-4 h-4 animate-spin" />
//                             ) : (
//                               <Trash2 className="w-4 h-4" />
//                             )}
//                             <span>Delete Account</span>
//                           </button>
//                           <button
//                             type="button"
//                             onClick={() => setShowDeleteConfirm(false)}
//                             className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
//                           >
//                             Cancel
//                           </button>
//                         </div>
//                       </form>
//                     </div>
//                   </div>
//                 )}
//               </div>
//             )}
//           </div>
//         </div>

//         {/* Error Messages */}
//         {(fetchError || updateError || accountState.changePasswordError || accountState.deleteAccountError) && (
//           <div className="fixed top-4 right-4 bg-red-50 border border-red-200 rounded-lg p-4 max-w-md z-50">
//             <div className="flex items-start space-x-3">
//               <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5" />
//               <div>
//                 <h4 className="text-sm font-medium text-red-800">Error</h4>
//                 <p className="text-sm text-red-700 mt-1">
//                   {fetchError || updateError || accountState.changePasswordError || accountState.deleteAccountError}
//                 </p>
//               </div>
//             </div>
//           </div>
//         )}
//         {/* Success Messages */}
//         {(accountState.changePasswordSuccess || accountState.deleteAccountSuccess) && (
//           <div className="fixed top-4 right-4 bg-green-50 border border-green-200 rounded-lg p-4 max-w-md z-50">
//             <div className="flex items-start space-x-3">
//               <Check className="w-5 h-5 text-green-500 mt-0.5" />
//               <div>
//                 <h4 className="text-sm font-medium text-green-800">Success</h4>
//                 <p className="text-sm text-green-700 mt-1">
//                   {accountState.changePasswordSuccess && "Password changed successfully"}
//                   {accountState.deleteAccountSuccess && "Account deleted successfully"}
//                 </p>
//               </div>
//             </div>
//           </div>
//         )}

//         {/* Two-Factor Auth Setup Modal */}
//         {twoFactorState.showSetup && (
//           <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
//             <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
//               <div className="text-center mb-6">
//                 <h3 className="text-lg font-semibold text-gray-900 mb-2">Set up Two-Factor Authentication</h3>
//                 <p className="text-sm text-gray-600">
//                   Scan the QR code below with your authenticator app
//                 </p>
//               </div>

//               {twoFactorState.qrCodeUrl && (
//                 <div className="flex justify-center mb-6">
//                   <img 
//                     src={twoFactorState.qrCodeUrl} 
//                     alt="QR Code for 2FA setup"
//                     className="w-48 h-48 border border-gray-300 rounded-lg"
//                   />
//                 </div>
//               )}

//               <div className="mb-4">
//                 <label className="block text-sm font-medium text-gray-700 mb-2">
//                   Enter verification code
//                 </label>
//                 <input
//                   type="text"
//                   placeholder="000000"
//                   maxLength={6}
//                   className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-center font-mono text-lg"
//                 />
//               </div>

//               <div className="flex items-center space-x-4">
//                 <button
//                   onClick={() => dispatch(enableTwoFactorAuthRequest())}
//                   disabled={twoFactorState.loading}
//                   className="flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors disabled:opacity-50"
//                 >
//                   {twoFactorState.loading ? (
//                     <Loader2 className="w-4 h-4 animate-spin" />
//                   ) : (
//                     <Shield className="w-4 h-4" />
//                   )}
//                   <span>Enable 2FA</span>
//                 </button>
//                 <button
//                   onClick={() => dispatch(resetTwoFactorAuthStatus())}
//                   className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
//                 >
//                   Cancel
//                 </button>
//               </div>
//             </div>
//           </div>
//         )}
//       </div>
//     </div>
//   );
// };

// export default Profile;
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  User, 
  Settings, 
  Shield, 
  Star, 
  Camera, 
  Check, 
  AlertTriangle,
  Loader2,
  MapPin
} from 'lucide-react';

// Import tab components
import UserProfile from './UserProfile';
import Security from './Security';

// Profile slice actions
import {
  fetchUserStart,
  fetchUserSuccess, 
  fetchUserFailure,
  selectUser,
  selectFetchLoading,
  selectFetchError,
  selectUpdateSuccess,
  clearUpdateSuccess
} from '../../../../logic/redux/profileSlice';

// Two-factor auth actions
import {
  getTwoFactorStatusRequest,
} from '../../../../logic/redux/twoFactorAuthSlice';

// Rating actions
import {
  getUserRatingRequest,
  getRatingStatsRequest
} from '../../../../logic/redux/ratingSlice';

const Profile = () => {
  const dispatch = useDispatch();
  
  // Profile state
  const user = useSelector(selectUser);
  const fetchLoading = useSelector(selectFetchLoading);
  const fetchError = useSelector(selectFetchError);
  const updateSuccess = useSelector(selectUpdateSuccess);
  
  // Rating state
  const ratingState = useSelector(state => state.rating);
  
  // Local state
  const [activeTab, setActiveTab] = useState('profile');

  // Initialize data on component mount
  useEffect(() => {
    dispatch(fetchUserStart());
    dispatch(getTwoFactorStatusRequest());
    dispatch(getUserRatingRequest());
    dispatch(getRatingStatsRequest());
  }, [dispatch]);

  // Clear success message after 3 seconds
  useEffect(() => {
    if (updateSuccess) {
      const timer = setTimeout(() => {
        dispatch(clearUpdateSuccess());
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [updateSuccess, dispatch]);

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'security', label: 'Security', icon: Shield },
  ];

  if (fetchLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center">
        <div className="flex items-center space-x-3">
          <Loader2 className="w-8 h-8 animate-spin text-indigo-600" />
          <span className="text-lg font-medium text-gray-700">Loading your profile...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden mb-8">
          <div className="h-32 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500"></div>
          <div className="px-8 pb-8">
            <div className="flex items-center space-x-6 -mt-16">
              <div className="relative">
                <div className="w-24 h-24 bg-white rounded-full p-1 shadow-lg">
                  <div className="w-full h-full bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full flex items-center justify-center">
                    <User className="w-10 h-10 text-white" />
                  </div>
                </div>
                <button className="absolute -bottom-1 -right-1 w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center shadow-lg hover:bg-indigo-600 transition-colors">
                  <Camera className="w-4 h-4 text-white" />
                </button>
              </div>
              <div className="flex-1 pt-16">
                <div className="flex items-center justify-between">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900">
                      {user?.name || 'Loading...'}
                    </h1>
                    <p className="text-gray-600 mt-1">{user?.email || 'Loading...'}</p>
                    <div className="flex items-center space-x-4 mt-2">
                      {user?.address && (
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <MapPin className="w-4 h-4" />
                          <span>{user.address}</span>
                        </div>
                      )}
                      {ratingState.ratingStats && (
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                          <span>{ratingState.ratingStats.averageRating}/5</span>
                        </div>
                      )}
                    </div>
                  </div>
                  {updateSuccess && (
                    <div className="bg-green-50 text-green-600 px-4 py-2 rounded-lg flex items-center space-x-2">
                      <Check className="w-4 h-4" />
                      <span>Profile updated successfully!</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-8">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab.id
                        ? 'border-indigo-500 text-indigo-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          <div className="p-8">
            {/* Profile Tab */}
            {activeTab === 'profile' && <UserProfile />}
            
            {/* Security Tab */}
            {activeTab === 'security' && <Security />}
          </div>
        </div>

        {/* Global Error Messages */}
        {fetchError && (
          <div className="fixed top-4 right-4 bg-red-50 border border-red-200 rounded-lg p-4 max-w-md z-50">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-red-800">Error</h4>
                <p className="text-sm text-red-700 mt-1">{fetchError}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Profile;