import React from 'react';

export const EmptyNetWorthChart = ({onAddAccounts}) => {
  return (
    <div className="h-full flex flex-col items-center justify-center p-6 text-center">
      <div className="text-lg font-semibold mb-2">Net Worth Overview</div>
      <div className="text-gray-500 mb-4">Track your financial progress over time</div>
      <div className="w-full h-40 bg-gray-100 rounded-lg flex items-center justify-center mb-4">
        <svg className="w-16 h-16 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
        </svg>
      </div>
      <button className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
       onClick={onAddAccounts}>
        Add Accounts
      </button>
    </div>
  );
};

export const EmptyRecentTransactions = ({ onAddTransaction }) => {
  return (
    <div className="h-full flex flex-col p-4 border rounded-lg shadow-sm bg-white">
      <div className="flex justify-between items-center mb-4">
        <div className="text-lg font-semibold">Recent Transactions</div>
        {/* Optional: Replace or hide this if needed */}
        <div className="text-sm text-gray-400">No data yet</div>
      </div>

      <div className="flex flex-col items-center justify-center flex-grow text-center">
        <div className="w-full h-40 bg-gray-100 rounded-lg flex items-center justify-center mb-4">
          <svg
            className="w-16 h-16 text-gray-300"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
            ></path>
          </svg>
        </div>

        <p className="text-gray-500 mb-4">
          Your latest financial activity will appear here once you add a transaction.
        </p>

        <button
          onClick={onAddTransaction}
          className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
        >
          Add Transaction
        </button>
      </div>
    </div>
  );
};


// EmptyRecurringTransactions component
export const EmptyRecurringTransactions = ({onAddRecurring }) => {
  return (
    <div className="h-full flex flex-col items-center justify-center p-6 text-center">
      <div className="text-lg font-semibold mb-2">Recurring Transactions</div>
      <div className="text-gray-500 mb-4">Set up and track your regular income and expenses</div>
      <div className="w-full h-40 bg-gray-100 rounded-lg flex items-center justify-center mb-4">
        <svg className="w-16 h-16 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
      </div>
      <button   onClick={onAddRecurring} className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
        Add Recurring Item
      </button>
    </div>
  );
};

export const EmptyBudgetDashboard = ({onCreateBudget }) => {
  return (
    <div className="h-full flex flex-col">
      <div className="flex justify-between items-center mb-4">
        <div className="text-lg font-semibold">February Budget</div>
      </div>

      <div className="flex flex-col flex-grow bg-white rounded-lg shadow">
        {/* Placeholder Budget Section */}
        <div className="px-4 py-3 border-b">
          <div className="text-sm text-gray-500 mb-2">Budget Overview</div>
          <div className="text-xl font-bold text-gray-400">$0.00</div>

          <div className="flex justify-between mt-3 mb-2">
            <div className="text-sm text-gray-500">Budget Usage</div>
            <div className="text-sm font-medium text-gray-400">0%</div>
          </div>

          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div className="h-2.5 rounded-full bg-gray-300" style={{ width: '0%' }}></div>
          </div>

          <div className="flex justify-between mt-2 text-xs text-gray-500">
            <span>$0.00 used</span>
            <span>$0.00 total</span>
          </div>
        </div>

        {/* Placeholder Income Section */}
        <div className="px-4 py-3 border-b">
          <div className="flex justify-between mb-2">
            <div className="text-sm text-gray-500">Income Progress</div>
            <div className="text-sm font-medium text-gray-400">0%</div>
          </div>

          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div className="h-2.5 rounded-full bg-gray-300" style={{ width: '0%' }}></div>
          </div>

          <div className="flex justify-between mt-2 text-xs text-gray-500">
            <span>$0.00 received</span>
            <span>Target: $0.00</span>
          </div>
        </div>

        {/* Action Section */}
        <div className="flex flex-col items-center justify-center py-6 text-center">
          <svg
            className="w-16 h-16 text-gray-300 mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"
            ></path>
          </svg>
          <div className="text-gray-500 mb-4">Create budgets to manage your spending</div>
          <button  onClick={onCreateBudget} className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
            Create Budget
          </button>
        </div>
      </div>
    </div>
  );
};


// EmptySpendingDashboard component
export const EmptySpendingDashboard = ({onStartTracking }) => {
  return (
    <div className="h-full flex flex-col items-center justify-center p-6 text-center">
      <div className="text-lg font-semibold mb-2">Spending Insights</div>
      <div className="text-gray-500 mb-4">Analyze your spending patterns by category</div>
      <div className="w-full h-40 bg-gray-100 rounded-lg flex items-center justify-center mb-4">
        <svg className="w-16 h-16 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
        </svg>
      </div>
      <button  onClick={onStartTracking} className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
        Start Tracking
      </button>
    </div>
  );
};

// EmptyInvestmentCard component
export const EmptyInvestmentCard = ({onLinkInvestments}) => {
  return (
    <div className="h-full flex flex-col items-center justify-center p-6 text-center">
      <div className="text-lg font-semibold mb-2">Investment Performance</div>
      <div className="text-gray-500 mb-4">Track your investment growth and returns</div>
      <div className="w-full h-40 bg-gray-100 rounded-lg flex items-center justify-center mb-4">
        <svg className="w-16 h-16 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      </div>
      <button  onClick={onLinkInvestments} className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
        Link Investments
      </button>
    </div>
  );
};