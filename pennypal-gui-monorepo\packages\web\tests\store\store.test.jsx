import React from 'react'
import { describe, it, expect, vi } from 'vitest'
import { Provider, useSelector, useDispatch } from 'react-redux'
import { render, screen, fireEvent } from '@testing-library/react'
import { configureStore } from '@reduxjs/toolkit'

// Import your actual store and slices
// Example: import { store } from '../../src/store'
// Example: import { userSlice } from '../../src/store/slices/userSlice'

// Mock store setup for demonstration
const createMockStore = (initialState = {}) => {
  const mockReducer = (state = { user: null, count: 0, ...initialState }, action) => {
    switch (action.type) {
      case 'SET_USER':
        return { ...state, user: action.payload }
      case 'INCREMENT':
        return { ...state, count: state.count + 1 }
      case 'DECREMENT':
        return { ...state, count: state.count - 1 }
      case 'RESET':
        return { ...state, count: 0 }
      default:
        return state
    }
  }

  return configureStore({
    reducer: mockReducer,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
        immutableCheck: false,
      }),
  })
}

// Mock React component that uses Redux
const TestComponent = () => {
  const user = useSelector(state => state.user)
  const count = useSelector(state => state.count)
  const dispatch = useDispatch()

  return (
    <div>
      <div data-testid="user">{user ? user.name : 'No user'}</div>
      <div data-testid="count">Count: {count}</div>
      <button onClick={() => dispatch({ type: 'INCREMENT' })}>
        Increment
      </button>
      <button onClick={() => dispatch({ type: 'DECREMENT' })}>
        Decrement
      </button>
      <button onClick={() => dispatch({ type: 'RESET' })}>
        Reset
      </button>
      <button 
        onClick={() => dispatch({ 
          type: 'SET_USER', 
          payload: { id: 1, name: 'John Doe' } 
        })}
      >
        Set User
      </button>
    </div>
  )
}

// Helper to render component with Redux store
const renderWithStore = (component, { initialState = {}, store = null } = {}) => {
  const testStore = store || createMockStore(initialState)
  
  return {
    ...render(
      <Provider store={testStore}>
        {component}
      </Provider>
    ),
    store: testStore,
  }
}

describe('Redux Store Integration', () => {
  describe('Store Configuration', () => {
    it('should create store with initial state', () => {
      const store = createMockStore()
      const state = store.getState()
      
      expect(state).toEqual({
        user: null,
        count: 0
      })
    })

    it('should create store with custom initial state', () => {
      const initialState = {
        user: { id: 1, name: 'Test User' },
        count: 5
      }
      
      const store = createMockStore(initialState)
      const state = store.getState()
      
      expect(state).toEqual({
        user: { id: 1, name: 'Test User' },
        count: 5
      })
    })
  })

  describe('Redux Actions', () => {
    it('should handle INCREMENT action', () => {
      const store = createMockStore()
      
      store.dispatch({ type: 'INCREMENT' })
      expect(store.getState().count).toBe(1)
      
      store.dispatch({ type: 'INCREMENT' })
      expect(store.getState().count).toBe(2)
    })

    it('should handle DECREMENT action', () => {
      const store = createMockStore({ count: 5 })
      
      store.dispatch({ type: 'DECREMENT' })
      expect(store.getState().count).toBe(4)
    })

    it('should handle RESET action', () => {
      const store = createMockStore({ count: 10 })
      
      store.dispatch({ type: 'RESET' })
      expect(store.getState().count).toBe(0)
    })

    it('should handle SET_USER action', () => {
      const store = createMockStore()
      const user = { id: 1, name: 'John Doe' }
      
      store.dispatch({ type: 'SET_USER', payload: user })
      expect(store.getState().user).toEqual(user)
    })
  })

  describe('React-Redux Integration', () => {
    it('should render component with initial state', () => {
      renderWithStore(<TestComponent />)
      
      expect(screen.getByTestId('user')).toHaveTextContent('No user')
      expect(screen.getByTestId('count')).toHaveTextContent('Count: 0')
    })

    it('should render component with custom initial state', () => {
      const initialState = {
        user: { id: 1, name: 'Jane Doe' },
        count: 5
      }
      
      renderWithStore(<TestComponent />, { initialState })
      
      expect(screen.getByTestId('user')).toHaveTextContent('Jane Doe')
      expect(screen.getByTestId('count')).toHaveTextContent('Count: 5')
    })

    it('should dispatch actions on button clicks', () => {
      const { store } = renderWithStore(<TestComponent />)
      
      // Test increment
      fireEvent.click(screen.getByText('Increment'))
      expect(screen.getByTestId('count')).toHaveTextContent('Count: 1')
      expect(store.getState().count).toBe(1)
      
      // Test decrement
      fireEvent.click(screen.getByText('Decrement'))
      expect(screen.getByTestId('count')).toHaveTextContent('Count: 0')
      expect(store.getState().count).toBe(0)
    })

    it('should handle user actions', () => {
      const { store } = renderWithStore(<TestComponent />)
      
      fireEvent.click(screen.getByText('Set User'))
      
      expect(screen.getByTestId('user')).toHaveTextContent('John Doe')
      expect(store.getState().user).toEqual({ id: 1, name: 'John Doe' })
    })

    it('should reset count while preserving other state', () => {
      const initialState = {
        user: { id: 1, name: 'Jane Doe' },
        count: 10
      }
      
      const { store } = renderWithStore(<TestComponent />, { initialState })
      
      fireEvent.click(screen.getByText('Reset'))
      
      expect(store.getState().count).toBe(0)
      expect(store.getState().user).toEqual({ id: 1, name: 'Jane Doe' })
    })
  })

  describe('Store Subscriptions', () => {
    it('should notify subscribers of state changes', () => {
      const store = createMockStore()
      const subscriber = vi.fn()
      
      const unsubscribe = store.subscribe(subscriber)
      
      store.dispatch({ type: 'INCREMENT' })
      expect(subscriber).toHaveBeenCalledTimes(1)
      
      store.dispatch({ type: 'INCREMENT' })
      expect(subscriber).toHaveBeenCalledTimes(2)
      
      unsubscribe()
      store.dispatch({ type: 'INCREMENT' })
      expect(subscriber).toHaveBeenCalledTimes(2) // Should not be called after unsubscribe
    })
  })

  describe('Error Handling', () => {
    it('should handle unknown actions gracefully', () => {
      const store = createMockStore()
      const initialState = store.getState()
      
      store.dispatch({ type: 'UNKNOWN_ACTION' })
      
      expect(store.getState()).toEqual(initialState)
    })

    it('should handle malformed actions', () => {
      const store = createMockStore()
      const initialState = store.getState()
      
      // Dispatch without type
      store.dispatch({})
      expect(store.getState()).toEqual(initialState)
      
      // Dispatch with null
      store.dispatch(null)
      expect(store.getState()).toEqual(initialState)
    })
  })
})

// Test utilities for other test files
export const createTestStore = createMockStore
export const renderWithRedux = renderWithStore

// Custom matcher for Redux testing
expect.extend({
  toHaveDispatchedAction(store, expectedAction) {
    const actions = store.getActions?.() || []
    const hasAction = actions.some(action => 
      action.type === expectedAction.type &&
      JSON.stringify(action.payload) === JSON.stringify(expectedAction.payload)
    )
    
    return {
      pass: hasAction,
      message: () => 
        hasAction 
          ? `Expected store not to have dispatched action ${expectedAction.type}`
          : `Expected store to have dispatched action ${expectedAction.type}`
    }
  }
})