import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  fetchInvestmentsRequest, 
  fetchPerformanceRequest,
  resetInvestmentStatus
} from '../../../../logic/redux/investmentSlice';
import {
  Box,
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  Grid,
  Card,
  CardContent
} from '@mui/material';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import InvestmentChart from './InvestmentChart';
import PaymentLoader from '../load/PaymentLoader'; // Adjust the path as needed
const Investment = ({ darkMode }) => {
  const dispatch = useDispatch();
  const userId = useSelector(state => state.auth.user?.id || '1');
  const investmentState = useSelector(state => state.mock_investments);
  const { loading, error } = investmentState;

  useEffect(() => {
    dispatch(fetchInvestmentsRequest(userId));
    dispatch(fetchPerformanceRequest(userId));
    return () => dispatch(resetInvestmentStatus());
  }, [dispatch, userId]);

  const { processedInvestments, investmentAccounts, performanceMetrics, portfolioTotal } = React.useMemo(() => {
    const investmentData = 
      investmentState.investmentData || 
      investmentState.data || 
      investmentState.investments;

    let processed = {};
    let accounts = [];
    let total = 0;

    if (investmentData?.success && investmentData?.rawData) {
      const { rawData } = investmentData;
      const holdings = rawData.holdings || [];
      const securities = rawData.securities || [];
      accounts = rawData.accounts || [];

      const securitiesMap = securities.reduce((acc, sec) => {
        acc[sec.security_id] = sec;
        return acc;
      }, {});

      processed = holdings.reduce((acc, holding) => {
        const accountId = holding.account_id;
        if (!acc[accountId]) acc[accountId] = [];

        const security = securitiesMap[holding.security_id];
        if (security) {
          const price = holding.institution_price || 0;
          const totalValue = holding.quantity * price;
          
          // Add to portfolio total
          total += totalValue;

          acc[accountId].push({
            ...holding,
            ticker_symbol: security.ticker_symbol || 'N/A',
            name: security.name || 'Unknown Security',
            close_price: price,
            total_value: totalValue,
            type: security.type || 'Unknown',
            fixed_income: security.fixed_income
          });
        }
        return acc;
      }, {});
    }

    return {
      processedInvestments: processed,
      investmentAccounts: accounts,
      performanceMetrics: investmentState.performanceMetrics || null,
      portfolioTotal: total
    };
  }, [investmentState]);

  const getAccountName = (accountId) => {
    const account = investmentAccounts.find(acc => acc.account_id === accountId);
    return account ? account.name : `Account ${accountId}`;
  };

  // Calculate total across all accounts
  const calculateTotalPortfolioValue = () => {
    let total = 0;
    Object.keys(processedInvestments).forEach(accountId => {
      total += processedInvestments[accountId].reduce((sum, inv) => sum + (Number(inv.total_value) || 0), 0);
    });
    return total;
  };

  if (loading) {
    return (
      <div className={`min-h-screen w-full p-5 flex flex-col items-center justify-center ${darkMode ? 'bg-gray-900 text-gray-100' : 'bg-white text-gray-900'}`}>
        <PaymentLoader darkMode={darkMode} />
        <p className={`mt-4 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          Loading investment data...
        </p>
      </div>
    );
  }

  if (error) {
    return (
      <Box sx={{ mt: 2 }}>
        <Alert 
          severity="error" 
          sx={{ 
            backgroundColor: darkMode ? '#451a03' : undefined, 
            color: darkMode ? '#fed7aa' : undefined 
          }}
        >
          {error}
        </Alert>
      </Box>
    );
  }

  const hasInvestments = Object.keys(processedInvestments).length > 0;
  const totalPortfolioValue = calculateTotalPortfolioValue();

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value);
  };

  const formatPercentage = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value / 100);
  };

  // Ensure the performance metrics match the actual total if we have investments
  const displayedPerformanceMetrics = performanceMetrics && hasInvestments ? {
    ...performanceMetrics,
    totalValue: totalPortfolioValue,
    // Recalculate other metrics based on the correct total
    totalGain: totalPortfolioValue - performanceMetrics.totalCost,
    totalGainPercent: ((totalPortfolioValue - performanceMetrics.totalCost) / performanceMetrics.totalCost) * 100,
    dailyChange: totalPortfolioValue * 0.01, // 1% daily change (matching backend calculation)
    dailyChangePercent: 1.0 // 1% as defined in backend
  } : performanceMetrics;

  return (
    <div className={`p-5 w-full min-h-screen ${darkMode ? 'bg-gray-900 text-gray-100' : 'bg-white text-gray-900'} font-roboto text-lg`}>
      <h1 className={`flex text-2xl items-center mb-10 ${darkMode ? 'text-white' : 'text-black'}`}>
        Investment Portfolio
      </h1>

      <InvestmentChart userId={userId} darkMode={darkMode} />

      {/* Performance Metrics Grid */}
      {displayedPerformanceMetrics && (
        <Box sx={{ mb: 4 }}>
          <Typography 
            variant="h6" 
            sx={{ 
              mb: 2, 
              color: darkMode ? 'white' : 'text.primary' 
            }}
          >
            Portfolio Performance
          </Typography>
          <Grid container spacing={2}>
            {/* Total Value */}
            <Grid item xs={12} sm={6} md={4}>
              <Card 
                sx={{ 
                  backgroundColor: darkMode ? '#1f2937' : 'white',
                  color: darkMode ? 'white' : 'text.primary'
                }}
              >
                <CardContent>
                  <Typography 
                    color={darkMode ? 'gray.400' : 'textSecondary'} 
                    gutterBottom
                  >
                    Portfolio Value
                  </Typography>
                  <Typography 
                    variant="h5" 
                    component="div"
                    sx={{ color: darkMode ? 'white' : 'text.primary' }}
                  >
                    {formatCurrency(displayedPerformanceMetrics.totalValue)}
                  </Typography>
                  <Typography 
                    variant="body2" 
                    color={darkMode ? 'gray.400' : 'textSecondary'}
                  >
                    Total Cost: {formatCurrency(displayedPerformanceMetrics.totalCost)}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Daily Change */}
            <Grid item xs={12} sm={6} md={4}>
              <Card 
                sx={{ 
                  backgroundColor: darkMode ? '#1f2937' : 'white',
                  color: darkMode ? 'white' : 'text.primary'
                }}
              >
                <CardContent>
                  <Typography 
                    color={darkMode ? 'gray.400' : 'textSecondary'} 
                    gutterBottom
                  >
                    Daily Change
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {displayedPerformanceMetrics.dailyChange >= 0 ? (
                      <TrendingUpIcon color="success" />
                    ) : (
                      <TrendingDownIcon color="error" />
                    )}
                    <Typography 
                      variant="h5" 
                      component="div" 
                      color={displayedPerformanceMetrics.dailyChange >= 0 ? 'success.main' : 'error.main'}
                      sx={{ ml: 1 }}
                    >
                      {formatCurrency(displayedPerformanceMetrics.dailyChange)}
                    </Typography>
                  </Box>
                  <Typography 
                    variant="body2" 
                    color={displayedPerformanceMetrics.dailyChangePercent >= 0 ? 'success.main' : 'error.main'}
                  >
                    {formatPercentage(displayedPerformanceMetrics.dailyChangePercent)}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Total Gain/Loss */}
            <Grid item xs={12} sm={6} md={4}>
              <Card 
                sx={{ 
                  backgroundColor: darkMode ? '#1f2937' : 'white',
                  color: darkMode ? 'white' : 'text.primary'
                }}
              >
                <CardContent>
                  <Typography 
                    color={darkMode ? 'gray.400' : 'textSecondary'} 
                    gutterBottom
                  >
                    Total Gain/Loss
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {displayedPerformanceMetrics.totalGain >= 0 ? (
                      <TrendingUpIcon color="success" />
                    ) : (
                      <TrendingDownIcon color="error" />
                    )}
                    <Typography 
                      variant="h5" 
                      component="div"
                      color={displayedPerformanceMetrics.totalGain >= 0 ? 'success.main' : 'error.main'}
                      sx={{ ml: 1 }}
                    >
                      {formatCurrency(displayedPerformanceMetrics.totalGain)}
                    </Typography>
                  </Box>
                  <Typography 
                    variant="body2"
                    color={displayedPerformanceMetrics.totalGainPercent >= 0 ? 'success.main' : 'error.main'}
                  >
                    {formatPercentage(displayedPerformanceMetrics.totalGainPercent)}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      )}

      {!hasInvestments ? (
        <Alert 
          severity="info" 
          sx={{ 
            backgroundColor: darkMode ? '#1e3a8a' : undefined, 
            color: darkMode ? '#bfdbfe' : undefined 
          }}
        >
          No investment data available.
        </Alert>
      ) : (
        <>
          {Object.keys(processedInvestments).map(accountId => (
            <Box key={accountId} sx={{ mb: 4 }}>
              <Typography 
                variant="h6" 
                sx={{ 
                  mb: 2, 
                  color: darkMode ? 'white' : 'text.primary' 
                }}
              >
                {getAccountName(accountId)}
              </Typography>

              <TableContainer 
                component={Paper} 
                sx={{ 
                  backgroundColor: darkMode ? '#1f2937' : 'white' 
                }}
              >
                <Table>
                  <TableHead
                    sx={{
                      backgroundColor: darkMode ? '#1f2937' : '#c5e1a5', // Fixed background color for table header
                    }}
                  >
                    <TableRow>
                      <TableCell sx={{ color: darkMode ? 'white' : 'text.primary', fontSize: 16, fontWeight: 'bold' }}>
                        Security Name
                      </TableCell>
                      <TableCell sx={{ color: darkMode ? 'white' : 'text.primary', fontSize: 16, fontWeight: 'bold' }}>
                        Ticker
                      </TableCell>
                      <TableCell sx={{ color: darkMode ? 'white' : 'text.primary', fontSize: 16,  fontWeight: 'bold' }}>
                        Type
                      </TableCell>
                      <TableCell 
                        align="right" 
                        sx={{ color: darkMode ? 'white' : 'text.primary', fontSize: 16 , fontWeight: 'bold' }}
                      >
                        Quantity
                      </TableCell>
                      <TableCell 
                        align="right" 
                        sx={{ color: darkMode ? 'white' : 'text.primary', fontSize: 16, fontWeight: 'bold' }}
                      >
                        Price ($)
                      </TableCell>
                      <TableCell 
                        align="right" 
                        sx={{ color: darkMode ? 'white' : 'text.primary', fontSize: 16, fontWeight: 'bold' }}
                      >
                        Total Value ($)
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {processedInvestments[accountId].map((investment, idx) => (
                      <TableRow key={investment.security_id || idx}>
                        <TableCell sx={{ color: darkMode ? 'white' : 'text.primary' , fontSize: 14 }}>
                          {investment.name}
                        </TableCell>
                        <TableCell sx={{ color: darkMode ? 'white' : 'text.primary' , fontSize: 14}}>
                          {investment.ticker_symbol}
                        </TableCell>
                        <TableCell sx={{ color: darkMode ? 'white' : 'text.primary', fontSize: 14 }}>
                          {investment.type}
                        </TableCell>
                        <TableCell 
                          align="right" 
                          sx={{ color: darkMode ? 'white' : 'text.primary' , fontSize: 14}}
                        >
                          {Number(investment.quantity).toFixed(2)}
                        </TableCell>
                        <TableCell 
                          align="right" 
                          sx={{ color: darkMode ? 'white' : 'text.primary', fontSize: 14 }}
                        >
                          {Number(investment.close_price).toFixed(2)}
                        </TableCell>
                        <TableCell 
                          align="right" 
                          sx={{ color: darkMode ? 'white' : 'text.primary', fontSize: 14 }}
                        >
                          {Number(investment.total_value).toFixed(2)}
                        </TableCell>
                      </TableRow>
                    ))}
                    <TableRow>
                      <TableCell 
                        colSpan={5} 
                        align="right" 
                        sx={{ color: darkMode ? 'white' : 'text.primary' }}
                      >
                        <strong>Account Total:</strong>
                      </TableCell>
                      <TableCell 
                        align="right" 
                        sx={{ color: darkMode ? 'white' : 'text.primary' }}
                      >
                        <strong>
                          {processedInvestments[accountId]
                            .reduce((sum, inv) => sum + (Number(inv.total_value) || 0), 0)
                            .toFixed(2)}
                        </strong>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          ))}
          
          {/* Portfolio total across all accounts */}
          {Object.keys(processedInvestments).length > 1 && (
            <Box sx={{ mb: 4 }}>
              <TableContainer 
                component={Paper} 
                sx={{ 
                  backgroundColor: darkMode ? '#1f2937' : 'white' 
                }}
              >
                <Table>
                  <TableBody>
                    <TableRow>
                      <TableCell 
                        colSpan={5} 
                        align="right"
                      >
                        <Typography 
                          variant="h6"
                          sx={{ color: darkMode ? 'white' : 'text.primary' }}
                        >
                          Portfolio Total:
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography 
                          variant="h6"
                          sx={{ color: darkMode ? 'white' : 'text.primary' }}
                        >
                          {formatCurrency(totalPortfolioValue)}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}
        </>
      )}
    {/* </Container> */}
    </div>
  );
};

export default Investment;