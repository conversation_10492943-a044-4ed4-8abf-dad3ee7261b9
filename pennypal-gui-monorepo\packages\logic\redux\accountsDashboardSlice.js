import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { axiosInstance } from '../api/axiosConfig';
import { createAction } from '@reduxjs/toolkit';
import { getCurrentUserId } from '../../web/src/utils/AuthUtil'; // Import the utility function

// Async thunks
export const fetchLinkToken = createAsyncThunk(
  'accounts/fetchLinkToken',
  async (_, { rejectWithValue }) => {
    try {
      const currentUserId = getCurrentUserId();
      
      if (!currentUserId) {
        console.error('fetchLinkToken: No currentUserId available!');
        return rejectWithValue('No user ID available to generate link token');
      }

      const response = await axiosInstance.post(
        '/pennypal/api/create_link_token',
        { userId: currentUserId },
        { withCredentials: true }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to generate link token');
    }
  }
);

export const fetchAccountDetails = createAsyncThunk(
  'accounts/fetchAccountDetails',
  async (_, { rejectWithValue, getState, dispatch }) => {
    try {
      const currentUserId = getCurrentUserId();

      if (!currentUserId) {
        console.error('fetchAccountDetails: No currentUserId available!');
        return rejectWithValue('User ID is required to fetch account details');
      }

      console.log('Fetching account details for user ID:', currentUserId);

      // Check cache first
      const cache = getState().cache;
      if (cache?.userAccountsLoaded && cache?.userAccounts?.length >= 0 &&
          cache?.userAccountsParams?.userId == currentUserId) {
        console.log('✅ Using cached user accounts data in fetchAccountDetails');
        return cache.userAccounts;
      }

      // If not cached, dispatch cache action
      console.log('🔄 User accounts not cached, dispatching cache action');
      dispatch({ type: 'cache/fetchUserAccountsStart', payload: { userId: currentUserId } });

      // Return empty array for now, cache epic will handle the fetch
      return [];
      
    } catch (error) {
      console.error('Error fetching account details:', error);
      return rejectWithValue(error.response?.data || 'Failed to fetch accounts');
    }
  }
);

export const exchangePublicToken = createAsyncThunk(
  'accounts/exchangePublicToken',
  async (publicToken, { rejectWithValue, dispatch }) => {
    try {
      const currentUserId = getCurrentUserId();
      
      if (!currentUserId) {
        console.error('exchangePublicToken: No currentUserId available!');
        return rejectWithValue('User ID is required to exchange public token');
      }

      console.log('Exchanging public token for user ID:', currentUserId);
      const response = await axiosInstance.post('/pennypal/api/exchange_public_token', {
        publicToken,
        userId: currentUserId
      });
      
      // Dispatch action to fetch updated account details
      dispatch(fetchAccountDetails());
      
      return response.data;
    } catch (error) {
      console.error('Error exchanging public token:', error);
      return rejectWithValue(error.response?.data || 'Failed to exchange public token');
    }
  }
);

export const refreshAllAccounts = createAsyncThunk(
  'accounts/refreshAllAccounts',
  async (_, { rejectWithValue, dispatch }) => {
    try {
      const currentUserId = getCurrentUserId();

      if (!currentUserId) {
        console.error('refreshAllAccounts: No currentUserId available!');
        return rejectWithValue('User ID is required to refresh accounts');
      }

      console.log('Refreshing all accounts for user ID:', currentUserId);
      const response = await axiosInstance.post(`/pennypal/api/users/${currentUserId}/syncallaccounts`);

      if (response.data.success) {
        // Fetch updated account details after refresh initiation
        dispatch(fetchAccountDetails());
      }
      return response.data;
    } catch (error) {
      console.error('Error refreshing all accounts:', error);
      return rejectWithValue(error.response?.data || 'Failed to refresh accounts');
    }
  }
);

export const syncAccount = createAsyncThunk(
  'accounts/syncAccount',
  async (accountId, { rejectWithValue, dispatch }) => {
    if (!accountId) {
      console.error('syncAccount: No accountId provided!');
      return rejectWithValue('Account ID is required to sync account');
    }

    try {
      const currentUserId = getCurrentUserId();
      
      if (!currentUserId) {
        console.error('syncAccount: No currentUserId available!');
        return rejectWithValue('User ID is required to sync account');
      }

      console.log('Syncing account ID:', accountId, 'for user:', currentUserId);
      const response = await axiosInstance.post(`/pennypal/api/accounts/${accountId}/synctransactions`, {
        userId: currentUserId
      });

      // Fetch updated account details after sync - always use current user
      if (response.data.success) {
        dispatch(fetchAccountDetails());
      }
      
      return response.data;
    } catch (error) {
      console.error('Error syncing account:', error);
      return rejectWithValue(error.response?.data || 'Failed to sync account');
    }
  }
);
export const fetchAccountBalanceMonthlyDeltas = createAsyncThunk(
  'accounts/fetchAccountBalanceMonthlyDeltas',
  async ({ userId, year, month }, { rejectWithValue }) => {
    try {
      if (!userId) {
        const currentUserId = getCurrentUserId();
        if (!currentUserId) {
          console.error('fetchAccountBalanceMonthlyDeltas: No userId provided and no currentUserId available!');
          return rejectWithValue('User ID is required to fetch balance deltas');
        }
        userId = currentUserId;
      }

      console.log('Fetching balance deltas for user ID:', userId, 'year:', year, 'month:', month);
      const response = await axiosInstance.get(`/pennypal/api/v1/account/balances/delta/monthly/${userId}/${year}/${month}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching balance deltas:', error);
      return rejectWithValue(error.response?.data || 'Failed to fetch balance deltas');
    }
  }
);

export const fetchAccountBalanceMonthlyDeltasGrouped = createAsyncThunk(
  'accounts/fetchAccountBalanceMonthlyDeltasGrouped',
  async ({ userId, year, month }, { rejectWithValue }) => {
    try {
      if (!userId) {
        const currentUserId = getCurrentUserId();
        if (!currentUserId) {
          console.error('fetchAccountBalanceMonthlyDeltasGrouped: No userId provided and no currentUserId available!');
          return rejectWithValue('User ID is required to fetch grouped balance deltas');
        }
        userId = currentUserId;
      }

      console.log('Fetching grouped balance deltas for user ID:', userId, 'year:', year, 'month:', month);
      const response = await axiosInstance.get(`/pennypal/api/v1/account/balances/delta/monthly/accountid/${userId}/${year}/${month}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching grouped balance deltas:', error);
      return rejectWithValue(error.response?.data || 'Failed to fetch grouped balance deltas');
    }
  }
);

export const connectMx = createAsyncThunk(
  'accounts/connectMx',
  // async (_, { rejectWithValue, getState }) => {
  async (params, { rejectWithValue, getState }) => {
    return params;
  }
);

/*
export const connectMx = createAsyncThunk(
  'accounts/connectMx',
  async (_, { rejectWithValue, getState }) => {
    try {
      // Get the current user ID from state
      const userId = getState().auth?.user?.id;
      if (!userId) {
        console.error('connectMx thunk: No userId available in state!');
        return rejectWithValue('User ID is required to connect MX');
      }

      console.log('Connecting to MX for user ID:', userId);
      const response = await axiosInstance.post('/pennypal/api/connect_mx', { userId });
      window.open(response.data.connect_url, "mx_connect", "width=600,height=600");
      return response.data;
    } catch (error) {
      console.error('Error connecting to MX:', error);
      return rejectWithValue(error.response?.data || 'Failed to initialize MX connection');
    }
  }
);*/

export const connectStripe = createAsyncThunk(
  'accounts/connectStripe',
  async (_, { rejectWithValue, getState }) => {
    try {
      // Get the current user ID from state
      const userId = getState().auth?.user?.id;
      if (!userId) {
        console.error('connectStripe thunk: No userId available in state!');
        return rejectWithValue('User ID is required to connect Stripe');
      }

      console.log('Connecting to Stripe for user ID:', userId);
      const response = await axiosInstance.post('/pennypal/api/connect_stripe', { userId });
      window.open(response.data.connect_url, "stripe_connect", "width=600,height=600");
      return response.data;
    } catch (error) {
      console.error('Error connecting to Stripe:', error);
      return rejectWithValue(error.response?.data || 'Failed to initialize Stripe connection');
    }
  }
);

export const connectFinicity = createAsyncThunk(
  'accounts/connectFinicity',
  // async (_, { rejectWithValue, getState }) => {
  async (params, { rejectWithValue, getState }) => {
    // try {
    //   // Get the current user ID from state
    //   const userId = getState().auth?.user?.id;
    //   if (!userId) {
    //     console.error('connectFinicity thunk: No userId available in state!');
    //     return rejectWithValue('User ID is required to connect Finicity');
    //   }

    //   console.log('Connecting to Finicity for user ID:', userId);
    //   const response = await axiosInstance.post('/pennypal/api/connect_finicity', { userId });
    //   window.open(response.data.connect_url, "finicity_connect", "width=600,height=600");
    //   return response.data;
    // } catch (error) {
    //   console.error('Error connecting to Finicity:', error);
    //   return rejectWithValue(error.response?.data || 'Failed to initialize Finicity connection');
    // }
    return params;
  }
);

export const fetchSankeyChartData = createAction('accounts/fetchSankeyChartData');
export const fetchSankeyChartDataSuccess = createAction('accounts/fetchSankeyChartDataSuccess');
export const fetchSankeyChartDataFailure = createAction('accounts/fetchSankeyChartDataFailure');

// Slice
const accountsDashboardSlice = createSlice({
  name: 'accounts',
  initialState: {
    linkToken: null,
    accounts: [],
    balanceHistory: [], // Add this for storing balance history data
    isLinked: false,
    isLoading: false,
     hasLoadedOnce: false,
    error: null,
    refreshStatus: null,
    syncStatus: {},
    syncingAccounts: [], // Track which accounts are being synced
    sankeyChartData: {
      nodes: [],
      links: [],
      nodeLevels: {},
       balanceDeltas: [],
    balanceDeltasGrouped: [],
    balanceDeltasLoading: false,
    balanceDeltasError: null,
    balanceDeltasGroupedLoading: false,
    balanceDeltasGroupedError: null
    },
    sankeyChartLoading: false,
    sankeyChartError: null
  },
  reducers: {
    resetError: (state) => {
      state.error = null;
    },
    setIsLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
      state.isLoading = false;
    },

  },
  extraReducers: (builder) => {
    builder
    // fetchAccountBalanceMonthlyDeltas
     .addCase(fetchAccountBalanceMonthlyDeltas.pending, (state) => {
        state.balanceDeltasLoading = true;
        state.balanceDeltasError = null;
      })
      .addCase(fetchAccountBalanceMonthlyDeltas.fulfilled, (state, action) => {
        state.balanceDeltas = action.payload;
        state.balanceDeltasLoading = false;
        state.balanceDeltasError = null;
      })
      .addCase(fetchAccountBalanceMonthlyDeltas.rejected, (state, action) => {
        state.balanceDeltasError = action.payload || 'Failed to fetch balance deltas';
        state.balanceDeltasLoading = false;
      })
        .addCase(fetchAccountBalanceMonthlyDeltasGrouped.pending, (state) => {
        state.balanceDeltasGroupedLoading = true;
        state.balanceDeltasGroupedError = null;
      })
      .addCase(fetchAccountBalanceMonthlyDeltasGrouped.fulfilled, (state, action) => {
        state.balanceDeltasGrouped = action.payload;
        state.balanceDeltasGroupedLoading = false;
        state.balanceDeltasGroupedError = null;
      })
      .addCase(fetchAccountBalanceMonthlyDeltasGrouped.rejected, (state, action) => {
        state.balanceDeltasGroupedError = action.payload || 'Failed to fetch grouped balance deltas';
        state.balanceDeltasGroupedLoading = false;
      })
      // fetchLinkToken
      .addCase(fetchLinkToken.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchLinkToken.fulfilled, (state, action) => {
        state.linkToken = action.payload.link_token;
        state.isLoading = false;
        state.error = null;
      })
      .addCase(fetchLinkToken.rejected, (state, action) => {
        state.error = action.payload || 'Failed to fetch link token';
        state.isLoading = false;
      })
      
      // fetchAccountDetails
      .addCase(fetchAccountDetails.pending, (state) => {
       state.isLoading = !state.hasLoadedOnce;
      })
      .addCase(fetchAccountDetails.fulfilled, (state, action) => {
        const data = action.payload;
        console.log('Processing account details in reducer:', data);

        // Check if the response has the new structure with accounts and balanceHistory
        if (data.accounts) {
          if (Array.isArray(data.accounts) && data.accounts.length > 0) {
            console.log(`Found ${data.accounts.length} accounts in response`);
            state.accounts = data.accounts;
            state.isLinked = true;
          } else {
            console.log('No accounts in response or invalid format');
            state.isLinked = false;
          }
          
          // Store balance history data - ensure it's an array
          if (data.balanceHistory && Array.isArray(data.balanceHistory)) {
            // Ensure all accountIds are strings for consistent comparison
            state.balanceHistory = data.balanceHistory.map(item => ({
              ...item,
              accountId: String(item.accountId || item.account_id || '')
            }));
            console.log("Set balance history in state:", state.balanceHistory.length, "records");
          } else {
            console.warn("Balance history data is not in expected format", data.balanceHistory);
            state.balanceHistory = [];
          }
        } else {
          // Fallback for old response structure
          if (Array.isArray(data) && data.length > 0) {
            console.log(`Found ${data.length} accounts in response (old format)`);
            state.accounts = data;
            state.isLinked = true;
            state.balanceHistory = []; // Reset balance history if not provided
          } else {
            console.log('No accounts in response or invalid format (old format)');
            state.isLinked = false;
          }
        }
        state.isLoading = false;
         state.hasLoadedOnce = true; 
        state.error = null;
      })
      .addCase(fetchAccountDetails.rejected, (state, action) => {
        state.error = action.payload || 'Failed to fetch account details';
        state.isLoading = false;
         state.hasLoadedOnce = true;
        console.error('Error fetching account details in reducer:', action.payload);
      })
      
      // exchangePublicToken
      .addCase(exchangePublicToken.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(exchangePublicToken.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null;
      })
      .addCase(exchangePublicToken.rejected, (state, action) => {
        state.error = action.payload || 'Failed to exchange public token';
        state.isLoading = false;
      })
      
      // refreshAllAccounts
      .addCase(refreshAllAccounts.pending, (state) => {
        state.isLoading = true;
        state.refreshStatus = 'loading';
      })
      .addCase(refreshAllAccounts.fulfilled, (state, action) => {
     
        state.refreshStatus = action.payload.success ? 'success' : 'failed';
        state.error = null;
      })
      .addCase(refreshAllAccounts.rejected, (state, action) => {
        state.error = action.payload || 'Failed to refresh accounts';
        state.refreshStatus = 'failed';
    
      })
      
      // syncAccount
      .addCase(syncAccount.pending, (state, action) => {
        const accountId = action.meta.arg;
        state.syncStatus[accountId] = 'loading';
        state.syncingAccounts = [...state.syncingAccounts, accountId];
      })
      .addCase(syncAccount.fulfilled, (state, action) => {
        const accountId = action.meta.arg;
        state.syncStatus[accountId] = 'success';
        state.syncingAccounts = state.syncingAccounts.filter(id => id !== accountId);
        state.error = null;
      })
      .addCase(syncAccount.rejected, (state, action) => {
        const accountId = action.meta.arg;
        state.syncStatus[accountId] = 'failed';
        state.syncingAccounts = state.syncingAccounts.filter(id => id !== accountId);
        state.error = action.payload || 'Failed to sync account';
      })
      
      // Connect services
      .addCase(connectMx.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(connectMx.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null;
      })
      .addCase(connectMx.rejected, (state, action) => {
        state.error = action.payload || 'Failed to connect to MX';
        state.isLoading = false;
      })
      
      .addCase(connectStripe.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(connectStripe.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null;
      })
      .addCase(connectStripe.rejected, (state, action) => {
        state.error = action.payload || 'Failed to connect to Stripe';
        state.isLoading = false;
      })
      
      .addCase(connectFinicity.pending, (state, action) => {
        state.isLoading = true;
      })
      .addCase(connectFinicity.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null;
      })
      .addCase(connectFinicity.rejected, (state, action) => {
        state.error = action.payload || 'Failed to connect to Finicity';
        state.isLoading = false;
      })
      // Sankey chart data
      .addCase(fetchSankeyChartData, (state) => {
        state.sankeyChartLoading = true;
        state.sankeyChartError = null;
      })
      .addCase(fetchSankeyChartDataSuccess, (state, action) => {
        state.sankeyChartData = {
          nodes: action.payload.nodes || [],
          links: action.payload.links || [],
          nodeLevels: action.payload.nodeLevels || {}
        };
        state.sankeyChartLoading = false;
        state.sankeyChartError = null;
      })
      .addCase(fetchSankeyChartDataFailure, (state, action) => {
        state.sankeyChartError = action.payload || 'Failed to fetch sankey chart data';
        state.sankeyChartLoading = false;
      });
      
  },
});

export const {  resetError, setIsLoading, setError, resetBalanceDeltasError} = accountsDashboardSlice.actions;

export default accountsDashboardSlice.reducer;