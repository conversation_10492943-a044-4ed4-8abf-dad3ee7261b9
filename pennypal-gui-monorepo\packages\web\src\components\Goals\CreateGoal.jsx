import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  createGoalRequest,
  updateGoalRequest,
  clearMessages
} from '../../../../logic/redux/goalSlice'; // Correct import path

const CreateGoal = ({ selectedGoal, onClose, accounts }) => {
  const dispatch = useDispatch();
  
  // Get state from Redux
  const { isAddingGoal, error, successMessage } = useSelector(state => state.goals);
  
  // Basic goal details
  const [goalName, setGoalName] = useState('');
  const [goalAmount, setGoalAmount] = useState('');
  const [startDate, setStartDate] = useState('');
  const [targetDate, setTargetDate] = useState('');
  const [goalType, setGoalType] = useState('HOUSING');
  const [description, setDescription] = useState('');
  const [selectedContributionAccount, setSelectedContributionAccount] = useState('');
  const [initialContribution, setInitialContribution] = useState('');

  // Set default dates
  useEffect(() => {
    const today = new Date();
    const nextYear = new Date();
    nextYear.setFullYear(today.getFullYear() + 1);
    
    setStartDate(today.toISOString().split('T')[0]);
    setTargetDate(nextYear.toISOString().split('T')[0]);
  }, []);

  // Set form values when a goal is selected for editing
  useEffect(() => {
    if (selectedGoal) {
      setGoalName(selectedGoal.goalName || '');
      setGoalAmount(selectedGoal.goalAmount ? selectedGoal.goalAmount.toString() : '');
      setStartDate(selectedGoal.startDate ? new Date(selectedGoal.startDate).toISOString().split('T')[0] : '');
      setTargetDate(selectedGoal.targetDate ? new Date(selectedGoal.targetDate).toISOString().split('T')[0] : '');
      setGoalType(selectedGoal.goalType || 'HOUSING');
      setDescription(selectedGoal.description || '');
    }
  }, [selectedGoal]);

  // Clear messages when component unmounts
  useEffect(() => {
    return () => {
      dispatch(clearMessages());
    };
  }, [dispatch]);

  // Handle success message and close modal
  useEffect(() => {
    if (successMessage && !isAddingGoal) {
      setTimeout(() => {
        dispatch(clearMessages());
        onClose();
      }, 1000);
    }
  }, [successMessage, isAddingGoal, dispatch, onClose]);

  const getAvailableBalance = (accountId) => {
    const account = accounts.find(acc => (acc.id || acc.account_id) === parseInt(accountId));
    return account ? (account.balance || account.current_balance || 0) : 0;
  };

  // Helper to format account balances
  const formatBalance = (balance) => {
    if (balance === undefined || balance === null) return '$0.00';
    return balance.toLocaleString('en-US', { style: 'currency', currency: 'USD' });
  };

  const validateForm = () => {
    if (!goalName.trim()) {
      alert("Please enter a goal name");
      return false;
    }

    if (!goalAmount || parseFloat(goalAmount) <= 0) {
      alert("Please enter a valid goal amount");
      return false;
    }

    if (!startDate) {
      alert("Please select a start date");
      return false;
    }

    if (!targetDate) {
      alert("Please select a target date");
      return false;
    }

    if (new Date(startDate) >= new Date(targetDate)) {
      alert("Target date must be after start date");
      return false;
    }

    // Validate initial contribution if provided
    if (selectedContributionAccount && initialContribution) {
      const amount = parseFloat(initialContribution);
      if (isNaN(amount) || amount <= 0) {
        alert("Please enter a valid initial contribution amount");
        return false;
      }
      
      const availableBalance = getAvailableBalance(selectedContributionAccount);
      if (amount > availableBalance) {
        alert(`Insufficient balance for initial contribution. Available: ${formatBalance(availableBalance)}`);
        return false;
      }
    }

    return true;
  };

  const handleCreateGoal = () => {
    if (!validateForm()) return;

    const newGoal = {
      goalName: goalName.trim(),
      goalAmount: parseFloat(goalAmount),
      startDate,
      targetDate,
      goalType,
      description: description.trim(),
    };

    // Add initial contribution if provided
    if (selectedContributionAccount && initialContribution && parseFloat(initialContribution) > 0) {
      newGoal.initialContribution = {
        accountId: parseInt(selectedContributionAccount),
        amount: parseFloat(initialContribution)
      };
    }
  
    dispatch(createGoalRequest(newGoal));
  };

  const handleUpdateGoal = () => {
    if (!selectedGoal) {
      console.error("No goal selected for update");
      return;
    }
    
    if (!validateForm()) return;

    const updatedGoal = {
      goalName: goalName.trim(),
      goalAmount: parseFloat(goalAmount),
      startDate,
      targetDate,
      goalType,
      description: description.trim(),
    };

    // Add initial contribution if provided (for updates this might create a new contribution)
    if (selectedContributionAccount && initialContribution && parseFloat(initialContribution) > 0) {
      updatedGoal.additionalContribution = {
        accountId: parseInt(selectedContributionAccount),
        amount: parseFloat(initialContribution)
      };
    }

    // Keep existing properties
    if (selectedGoal.currentAmount) {
      updatedGoal.currentAmount = selectedGoal.currentAmount;
    }
    
    dispatch(updateGoalRequest({
      goalId: selectedGoal.id,
      goalData: updatedGoal
    }));
  };

  const resetForm = () => {
    setGoalName('');
    setGoalAmount('');
    
    const today = new Date();
    const nextYear = new Date();
    nextYear.setFullYear(today.getFullYear() + 1);
    
    setStartDate(today.toISOString().split('T')[0]);
    setTargetDate(nextYear.toISOString().split('T')[0]);
    
    setGoalType('HOUSING');
    setDescription('');
    setSelectedContributionAccount('');
    setInitialContribution('');
  };

  const handleClose = () => {
    dispatch(clearMessages());
    resetForm();
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-screen overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-semibold text-gray-800">
            {selectedGoal ? 'Update Goal' : 'Create New Goal'}
          </h3>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Error and Success Messages */}
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}
        
        {successMessage && (
          <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
            {successMessage}
          </div>
        )}

        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Goal Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                placeholder="e.g., Housing Investment"
                value={goalName}
                onChange={(e) => setGoalName(e.target.value)}
                className="w-full border border-gray-300 px-3 py-2 rounded-md focus:ring-blue-500 focus:border-blue-500"
                disabled={isAddingGoal}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Target Amount ($) <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                min="0"
                step="0.01"
                placeholder="100000"
                value={goalAmount}
                onChange={(e) => setGoalAmount(e.target.value)}
                className="w-full border border-gray-300 px-3 py-2 rounded-md focus:ring-blue-500 focus:border-blue-500"
                disabled={isAddingGoal}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Start Date <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="w-full border border-gray-300 px-3 py-2 rounded-md focus:ring-blue-500 focus:border-blue-500"
                disabled={isAddingGoal}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Target Date <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                value={targetDate}
                onChange={(e) => setTargetDate(e.target.value)}
                className="w-full border border-gray-300 px-3 py-2 rounded-md focus:ring-blue-500 focus:border-blue-500"
                disabled={isAddingGoal}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Goal Type</label>
              <select
                value={goalType}
                onChange={(e) => setGoalType(e.target.value)}
                className="w-full border border-gray-300 px-3 py-2 rounded-md focus:ring-blue-500 focus:border-blue-500"
                disabled={isAddingGoal}
              >
                <option value="HOUSING">Housing</option>
                <option value="RETIREMENT">Retirement</option>
                <option value="EDUCATION">Education</option>
                <option value="EMERGENCY">Emergency Fund</option>
                <option value="VACATION">Vacation</option>
                <option value="VEHICLE">Vehicle</option>
                <option value="OTHER">Other</option>
              </select>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              placeholder="Describe your goal..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows="3"
              className="w-full border border-gray-300 px-3 py-2 rounded-md focus:ring-blue-500 focus:border-blue-500"
              disabled={isAddingGoal}
            />
          </div>

          {/* Initial Goal Contribution */}
          <div className="mt-6">
            <div className="flex justify-between items-center mb-3">
              <h4 className="font-medium text-gray-700">
                {selectedGoal ? 'Additional Contribution' : 'Initial Goal Contribution'}
              </h4>
            </div>
            
            <div className="space-y-3">
              {accounts && accounts.length > 0 ? (
                <div className="flex flex-col md:flex-row gap-3 p-3 bg-gray-50 rounded-md border border-gray-200">
                  <div className="flex-1">
                    <label className="block text-xs font-medium text-gray-500 mb-1">Select Account</label>
                    <select
                      value={selectedContributionAccount || ''}
                      onChange={(e) => setSelectedContributionAccount(e.target.value)}
                      className="w-full border border-gray-300 px-3 py-2 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      disabled={isAddingGoal}
                    >
                      <option value="">Select an account</option>
                      {accounts.map(account => {
                        const accountId = account.id || account.account_id;
                        const availableBalance = getAvailableBalance(accountId);
                        const isDisabled = availableBalance <= 0;
                        
                        return (
                          <option 
                            key={accountId} 
                            value={accountId}
                            disabled={isDisabled}
                            className={isDisabled ? 'text-gray-400' : ''}
                          >
                            {account.name || account.account_name} ({formatBalance(availableBalance)})
                            {isDisabled ? ' - No Available Balance' : ''}
                          </option>
                        );
                      })}
                    </select>
                  </div>
                  <div className="w-full md:w-1/3">
                    <label className="block text-xs font-medium text-gray-500 mb-1">Amount ($)</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={initialContribution}
                      onChange={(e) => setInitialContribution(e.target.value)}
                      className="w-full border border-gray-300 px-3 py-2 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter amount"
                      disabled={isAddingGoal}
                    />
                  </div>
                </div>
              ) : (
                <p className="text-gray-500 text-sm">No accounts available for contribution</p>
              )}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-8 flex justify-end space-x-3">
          <button
            onClick={handleClose}
            className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            disabled={isAddingGoal}
          >
            Cancel
          </button>
          <button
            onClick={selectedGoal ? handleUpdateGoal : handleCreateGoal}
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isAddingGoal}
          >
            {isAddingGoal ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {selectedGoal ? 'Updating...' : 'Creating...'}
              </span>
            ) : (
              selectedGoal ? 'Update Goal' : 'Create Goal'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CreateGoal;