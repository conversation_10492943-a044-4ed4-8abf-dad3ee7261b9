import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  description: '',
  selectedContacts: [],
  splitMethod: null,
  splitAmounts: {},
  totalSplitAmount: 0,
  editableAmount: 0,
  loading: false,
  inputValue: '',
  activeTab: 0,
  isEditingAmount: false,
  error: null,
  successMessage: null, // Added for success feedback
  splitTransactions: [],
  contacts: [],
};

const splitTransactionSlice = createSlice({
  name: 'splitTransaction',
  initialState,
  reducers: {
    setDescription: (state, action) => {
      state.description = action.payload;
    },
    setSelectedContacts: (state, action) => {
      state.selectedContacts = action.payload;
    },
    setSplitMethod: (state, action) => {
      state.splitMethod = action.payload;
    },
    setSplitAmounts: (state, action) => {
      state.splitAmounts = action.payload;
    },
    setTotalSplitAmount: (state, action) => {
      state.totalSplitAmount = action.payload;
    },
    setEditableAmount: (state, action) => {
      state.editableAmount = action.payload;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setInputValue: (state, action) => {
      state.inputValue = action.payload;
    },
    setActiveTab: (state, action) => {
      state.activeTab = action.payload;
    },
    setIsEditingAmount: (state, action) => {
      state.isEditingAmount = action.payload;
    },
    resetState: (state) => {
      Object.assign(state, initialState);
    },
    fetchContactsRequest: (state) => {
      state.loading = true;
      state.error = null;
      state.successMessage = null;
    },
    fetchContactsSuccess: (state, action) => {
      state.contacts = action.payload;
      state.loading = false;
    },
    fetchContactsFailure: (state, action) => {
      state.error = action.payload;
      state.loading = false;
      state.successMessage = null;
    },
    fetchSplitTransactionsRequest: (state) => {
      state.loading = true;
      state.error = null;
      state.successMessage = null;
    },
    fetchSplitTransactionsSuccess: (state, action) => {
      state.splitTransactions = action.payload;
      state.loading = false;
    },
    fetchSplitTransactionsFailure: (state, action) => {
      state.error = action.payload;
      state.loading = false;
      state.successMessage = null;
    },
    submitSplitRequest: (state) => {
      state.loading = true;
      state.error = null;
      state.successMessage = null;
    },
    submitSplitSuccess: (state) => {
      state.loading = false;
      state.successMessage = 'Split requests and notifications sent successfully!';
    },
    submitSplitFailure: (state, action) => {
      state.error = action.payload;
      state.loading = false;
      state.successMessage = null;
    },
  },
});

export const {
  setDescription,
  setSelectedContacts,
  setSplitMethod,
  setSplitAmounts,
  setTotalSplitAmount,
  setEditableAmount,
  setLoading,
  setInputValue,
  setActiveTab,
  setIsEditingAmount,
  resetState,
  fetchContactsRequest,
  fetchContactsSuccess,
  fetchContactsFailure,
  fetchSplitTransactionsRequest,
  fetchSplitTransactionsSuccess,
  fetchSplitTransactionsFailure,
  submitSplitRequest,
  submitSplitSuccess,
  submitSplitFailure,
} = splitTransactionSlice.actions;

export default splitTransactionSlice.reducer;