// import React, { useState, useEffect } from 'react';
// import { useDispatch, useSelector } from 'react-redux';
// import { useLocation, useNavigate } from 'react-router-dom';
// import { 
//   // Invite and signup related actions
//   inviteFamilyMemberRequest, 
//   validateInviteLinkRequest,
//   completeSignupRequest,
//   revokeFamilyMemberRequest,
//   resetInviteState,
//   resetValidateState,
//   resetSignupState,
//   resetRevokeState,
//   selectInviteStatus,
//   selectInviteError,
//   selectInviteResponse,
//   selectValidateStatus,
//   selectValidateError,
//   selectValidateResponse,
//   selectSignupStatus,
//   selectSignupError,
//   selectSignupResponse,
//   selectRevokeStatus,
//   selectRevokeError,
//   // Family members list related actions
//   getFamilyMembersRequest,
//   selectFamilyMembersStatus,
//   selectFamilyMembersError,
//   selectFamilyMembers
// } from '../../../../logic/redux/memberSlice';
// import PaymentLoader from '../load/PaymentLoader'; // Adjust path as needed
// import { themeClasses } from '../../utils/tailwindUtils'; // Adjust path as needed

// // Maximum allowed family members per primary user
// const MAX_FAMILY_MEMBERS = 2;

// const FamilySection = ({ darkMode, isPrimary = false }) => {
//   const dispatch = useDispatch();
//   const location = useLocation();
//   const navigate = useNavigate();
  
//   // UI State
//   const [showInviteForm, setShowInviteForm] = useState(false);
//   const [showRevokeConfirm, setShowRevokeConfirm] = useState(false);
//   const [selectedMemberId, setSelectedMemberId] = useState(null);
  
//   // Form states
//   const [inviteForm, setInviteForm] = useState({
//     emailId: '',
//     relationshipType: 'SPOUSE', // Default relationship
//     permissionType: 'READ_ONLY', // Default permission
//     fullName: ''
//   });
  
//   const [signupForm, setSignupForm] = useState({
//     password: '',
//     confirmPassword: '',
//     username: '',
//     emailId: '',
//     agreedToTerms: false
//   });
  
//   // Get token from URL if present
//   const urlParams = new URLSearchParams(location.search);
//   const inviteToken = urlParams.get('token');
  
//   // Redux state selectors for invite/signup functionality
//   const inviteStatus = useSelector(selectInviteStatus);
//   const inviteError = useSelector(selectInviteError);
//   const inviteResponse = useSelector(selectInviteResponse);
  
//   const validateStatus = useSelector(selectValidateStatus);
//   const validateError = useSelector(selectValidateError);
//   const validateResponse = useSelector(selectValidateResponse);
  
//   const signupStatus = useSelector(selectSignupStatus);
//   const signupError = useSelector(selectSignupError);
//   const signupResponse = useSelector(selectSignupResponse);
  
//   const revokeStatus = useSelector(selectRevokeStatus);
//   const revokeError = useSelector(selectRevokeError);
  
//   // Redux state selectors for family members list
//   const familyMembersStatus = useSelector(selectFamilyMembersStatus);
//   const familyMembersError = useSelector(selectFamilyMembersError);
//   const familyMembers = useSelector(selectFamilyMembers);
  
//   // Check if we're in invite view or signup view
//   const isInviteView = !inviteToken;
  
//   // Check if the family members limit has been reached
//   const isFamilyMembersLimitReached = familyMembers.length >= MAX_FAMILY_MEMBERS;
  
//   // Validate token on component mount if present
//   useEffect(() => {
//     if (inviteToken) {
//       dispatch(validateInviteLinkRequest(inviteToken));
//     } else {
//       // Only fetch family members when we're in the normal view (not signup)
//       dispatch(getFamilyMembersRequest());
//     }
    
//     // Cleanup on unmount
//     return () => {
//       dispatch(resetInviteState());
//       dispatch(resetValidateState());
//       dispatch(resetSignupState());
//       dispatch(resetRevokeState());
//     };
//   }, [dispatch, inviteToken]);
  
//   // Pre-fill signup form with validated data
//   useEffect(() => {
//     if (validateStatus === 'succeeded' && validateResponse) {
//       // Pre-fill what we can from the validation response
//       if (validateResponse.emailId) {
//         setSignupForm(prev => ({
//           ...prev,
//           emailId: validateResponse.emailId
//         }));
//       }
//     }
//   }, [validateStatus, validateResponse]);
  
//   // Redirect after successful signup
//   useEffect(() => {
//     if (signupStatus === 'succeeded') {
//       alert('Signup completed successfully! You can now log in.');
//       navigate('/login');
//     }
//   }, [signupStatus, navigate]);
  
//   // Reset the form after successful invite
//   useEffect(() => {
//     if (inviteStatus === 'succeeded') {
//       setInviteForm({
//         emailId: '',
//         relationshipType: 'SPOUSE',
//         permissionType: 'READ_ONLY',
//         fullName: ''
//       });
      
//       // Refresh the family members list
//       dispatch(getFamilyMembersRequest());
      
//       // Hide the invite form after 3 seconds
//       setTimeout(() => {
//         setShowInviteForm(false);
//       }, 3000);
//     }
//   }, [inviteStatus, dispatch]);
  
//   // Refresh the list after successful revoke
//   useEffect(() => {
//     if (revokeStatus === 'succeeded') {
//       // Close the confirmation dialog
//       setShowRevokeConfirm(false);
//       setSelectedMemberId(null);
      
//       // Refresh the family members list
//       dispatch(getFamilyMembersRequest());
      
//       // Show a success notification
//       alert('Family member access revoked successfully');
//     }
//   }, [revokeStatus, dispatch]);
  
//   // Handle form input changes
//   const handleInviteInputChange = (e) => {
//     const { name, value } = e.target;
//     setInviteForm(prev => ({ ...prev, [name]: value }));
//   };
  
//   const handleSignupInputChange = (e) => {
//     const { name, value, type, checked } = e.target;
//     const inputValue = type === 'checkbox' ? checked : value;
//     setSignupForm(prev => ({ ...prev, [name]: inputValue }));
//   };
  
//   // Form submission handlers
//   const handleInviteSubmit = (e) => {
//     e.preventDefault();
    
//     // Prepare the payload with correct field names
//     const payload = {
//       emailId: inviteForm.emailId,
//       relationshipType: inviteForm.relationshipType,
//       permissionType: inviteForm.permissionType,
//       fullName: inviteForm.fullName
//     };
    
//     dispatch(inviteFamilyMemberRequest(payload));
//   };
  
//   const handleSignupSubmit = (e) => {
//     e.preventDefault();
//     if (signupForm.password !== signupForm.confirmPassword) {
//       alert("Passwords don't match!");
//       return;
//     }
    
//     dispatch(completeSignupRequest({
//       token: inviteToken,
//       signupDetails: {
//         password: signupForm.password,
//         name: signupForm.username,
//         phoneNumber: '',
//         emailId: signupForm.emailId
//       }
//     }));
//   };
  
//   // Handle revoke confirmation
//   const handleRevokeMember = (relationshipId) => {
//     setSelectedMemberId(relationshipId);
//     setShowRevokeConfirm(true);
//   };
  
//   const confirmRevoke = () => {
//     if (selectedMemberId) {
//       dispatch(revokeFamilyMemberRequest(selectedMemberId));
//     }
//   };
  
//   const cancelRevoke = () => {
//     setShowRevokeConfirm(false);
//     setSelectedMemberId(null);
//   };
  
//   // Helper function to display relationship type in human-readable format
//   const formatRelationshipType = (type) => {
//     switch (type) {
//       case 'SPOUSE': return 'Spouse';
//       case 'CHILD': return 'Child';
//       case 'PARENT': return 'Parent';
//       case 'SIBLING': return 'Sibling';
//       case 'OTHER': return 'Other';
//       default: return type;
//     }
//   };

//   // Helper function to display permission type in human-readable format
//   const formatPermissionType = (type) => {
//     return type === 'READ_ONLY' ? 'View Only' : 'Full Access';
//   };
  
//   // Show PaymentLoader for invite validation
//   if (inviteToken && validateStatus === 'loading') {
//     return (
//       <div
//         className={`min-h-screen w-full p-5 flex flex-col justify-center items-center ${themeClasses.pageContainer(
//           darkMode,
//         )}`}
//       >
//         <PaymentLoader darkMode={darkMode} />
//         {/* <p className={`mt-2 text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
//           Validating invite link...
//         </p> */}
//       </div>
//     );
//   }
  
//   // Show error if token is invalid
//   if (inviteToken && validateStatus === 'failed') {
//     return (
//    <div
//         className={`max-w-md mx-auto mt-20 p-6 rounded-lg ${themeClasses.modalContent(darkMode)} shadow-lg`}
//       >
//                 <h2 className="text-xl font-bold text-red-600 mb-4">Invalid Invite Link</h2>
//         <p className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
//           {validateError || "This invite link is invalid or has expired."}
//         </p>
//         <button 
//           onClick={() => navigate('/')}
//           className={`mt-4 px-4 py-2 rounded font-medium ${themeClasses.button(darkMode)}`}
//         >
//           Return to Home
//         </button>
//       </div>
//     );
//   }
  
//   // Render the signup form if token is valid
//   if (inviteToken && validateStatus === 'succeeded') {
//     return (
//   <div
//         className={`max-w-md mx-auto mt-20 p-10 rounded-lg ${themeClasses.modalContent(darkMode)} shadow-lg`}
//       >
//                 <h2 className="text-xl font-bold mb-4">Complete Your Account Setup</h2>
        
//         {signupStatus === 'failed' && (
//           <div className={`mb-4 p-3 rounded ${themeClasses.error(darkMode)}`}>
//             {signupError || "Failed to complete signup. Please try again."}
//           </div>
//         )}
        
//         <form onSubmit={handleSignupSubmit}>
//           <div className="mb-4">
//             <label className={`block text-sm font-medium mb-1 ${themeClasses.descriptionLabel(darkMode)}`}>
//               Email
//             </label>
//             <input
//               type="email"
//               name="emailId"
//               value={signupForm.emailId}
//               onChange={handleSignupInputChange}
//               readOnly
//               className={`w-full p-2 border rounded ${themeClasses.inputField(darkMode)} bg-gray-100`}
//             />
//             <p className={`text-xs mt-1 ${themeClasses.descriptionText(darkMode)}`}>
//               Email is pre-filled from your invitation and cannot be changed.
//             </p>
//           </div>
          
//           <div className="mb-4">
//             <label className={`block text-sm font-medium mb-1 ${themeClasses.descriptionLabel(darkMode)}`}>
//               Full Name
//             </label>
//             <input
//               type="text"
//               name="username"
//               value={signupForm.username}
//               onChange={handleSignupInputChange}
//               required
//               className={`w-full p-2 border rounded ${themeClasses.inputField(darkMode)}`}
//               placeholder="Enter your full name"
//             />
//           </div>
          
//           <div className="mb-4">
//             <label className={`block text-sm font-medium mb-1 ${themeClasses.descriptionLabel(darkMode)}`}>
//               Password
//             </label>
//             <input
//               type="password"
//               name="password"
//               value={signupForm.password}
//               onChange={handleSignupInputChange}
//               required
//               className={`w-full p-2 border rounded ${themeClasses.inputField(darkMode)}`}
//               placeholder="Create a password"
//               minLength="8"
//             />
//           </div>
          
//           <div className="mb-6">
//             <label className={`block text-sm font-medium mb-1 ${themeClasses.descriptionLabel(darkMode)}`}>
//               Confirm Password
//             </label>
//             <input
//               type="password"
//               name="confirmPassword"
//               value={signupForm.confirmPassword}
//               onChange={handleSignupInputChange}
//               required
//               className={`w-full p-2 border rounded ${themeClasses.inputField(darkMode)}`}
//               placeholder="Confirm your password"
//               minLength="8"
//             />
//           </div>
          
//           <div className="mb-6">
//             <label className="flex items-center">
//               <input
//                 type="checkbox"
//                 name="agreedToTerms"
//                 checked={signupForm.agreedToTerms}
//                 onChange={handleSignupInputChange}
//                 required
//                 className={`mr-2 ${themeClasses.checkboxInput(darkMode)}`}
//               />
//               <span className={`text-sm ${themeClasses.descriptionLabel(darkMode)}`}>
//                 I agree to the{' '}
//                 <a href="/terms" className={`${themeClasses.link(darkMode)}`}>
//                   Terms and Conditions
//                 </a>
//               </span>
//             </label>
//           </div>
          
//           <button
//             type="submit"
//             disabled={signupStatus === 'loading' || !signupForm.agreedToTerms}
//             className={`w-full py-2 px-4 rounded font-medium ${
//               signupStatus === 'loading' || !signupForm.agreedToTerms
//                 ? themeClasses.actionButtonDisabled(darkMode)
//                 : themeClasses.reconcileButton(darkMode)
//             }`}
//           >
//             {signupStatus === 'loading' ? (
//               <div className="flex items-center justify-center">
//                 <PaymentLoader darkMode={darkMode} />
//                 {/* <span className="ml-2">Creating Account...</span> */}
//               </div>
//             ) : (
//               'Create Account'
//             )}
//           </button>
//         </form>
//       </div>
//     );
//   }
  
//   // The main family section view (when not handling invites/signups)
//   return (
//     <div className={`p-2 ${themeClasses.pageContainer(darkMode)}`}>
//       <div className={`${themeClasses.pageContainer(darkMode)}`}>
//         <div className="flex justify-between items-center mb-6">
//           <h2 className="text-2xl">Family Members</h2>

//           {/* Show Add button only when invite form is NOT open, limit not reached, and not loading */}
//           {!showInviteForm && !isFamilyMembersLimitReached && familyMembersStatus !== 'loading' && !isPrimary && (
//             <button
//               onClick={() => setShowInviteForm(true)}
//               className={`px-4 py-2 rounded flex items-center ${themeClasses.reconcileButton(darkMode)}`}
//             >
//               <span className="mr-1">+</span> Add Family Member
//             </button>
//           )}

//           {/* Show Cancel button only when invite form is open */}
//           {!isPrimary && showInviteForm && (
//             <button
//               onClick={() => setShowInviteForm(false)}
//               className={`px-4 py-2 rounded ${themeClasses.actionButton(darkMode)}`}
//             >
//               Cancel
//             </button>
//           )}
//         </div>
        
//         {/* Display limit reached message if applicable */}
//         {isFamilyMembersLimitReached && !showInviteForm && (
//           <div className={`mb-4 p-3 rounded-lg ${themeClasses.warning(darkMode)}`}>
//             You have reached the maximum limit of {MAX_FAMILY_MEMBERS} family members.
//           </div>
//         )}
        
//         {/* Show message for non-primary users */}
//         {isPrimary && (
//            <div
//             className={`mb-6 p-4 border rounded-lg ${themeClasses.cardContainer(darkMode)} ${themeClasses.border(
//               darkMode,
//             )}`}
//           >
//             Only the primary account holder can manage family members.
//           </div>
//         )}
        
//         {/* Invite form - only show for primary users */}
//         {!isPrimary && showInviteForm && (
//           <div className={`mb-6 p-4 border rounded-lg ${darkMode ? 'border-gray-600 bg-gray-800' : 'border-gray-200 bg-white'}`}>
//             <h3 className="text-lg font-semibold mb-4">Invite a Family Member</h3>
            
//             {inviteStatus === 'failed' && (
//               <div className={`mb-4 p-3 rounded ${themeClasses.error(darkMode)}`}>
//                 {inviteError || 'Failed to send invitation. Please try again.'}
//               </div>
//             )}
            
//             {inviteStatus === 'succeeded' && (
//               <div className={`mb-4 p-3 rounded ${themeClasses.success(darkMode)}`}>
//                 Invitation sent successfully to {inviteResult?.emailId || inviteForm.emailId}!
//               </div>
//             )}
            
//             <form onSubmit={handleInviteSubmit}>
//               <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                 <div>
//                   <label className={`block text-sm font-medium mb-1 ${themeClasses.descriptionLabel(darkMode)}`}>
//                     Email Address
//                   </label>
//                   <input
//                     type="email"
//                     name="emailId"
//                     value={inviteForm.emailId}
//                     onChange={handleInviteInputChange}
//                     required
//                     disabled={inviteStatus === 'loading'}
//                     className={`w-full p-2 border rounded ${themeClasses.inputField(darkMode)}`}
//                     placeholder="<EMAIL>"
//                   />
//                 </div>
                
//                 <div>
//                   <label className={`block text-sm font-medium mb-1 ${themeClasses.descriptionLabel(darkMode)}`}>
//                     Full Name
//                   </label>
//                   <input
//                     type="text"
//                     name="fullName"
//                     value={inviteForm.fullName}
//                     onChange={handleInviteInputChange}
//                     required
//                     disabled={inviteStatus === 'loading'}
//                     className={`w-full p-2 border rounded ${themeClasses.inputField(darkMode)}`}
//                     placeholder="John Doe"
//                   />
//                 </div>
                
//                 <div>
//                   <label className={`block text-sm font-medium mb-1 ${themeClasses.descriptionLabel(darkMode)}`}>
//                     Relationship
//                   </label>
//                   <select
//                     name="relationshipType"
//                     value={inviteForm.relationshipType}
//                     onChange={handleInviteInputChange}
//                     disabled={inviteStatus === 'loading'}
//                     className={`w-full p-2 border rounded ${themeClasses.selectField(darkMode)}`}
//                   >
//                     <option value="SPOUSE">Spouse</option>
//                     <option value="CHILD">Child</option>
//                     <option value="PARENT">Parent</option>
//                     <option value="SIBLING">Sibling</option>
//                     <option value="OTHER">Other</option>
//                   </select>
//                 </div>
                
//                 <div>
//                   <label className={`block text-sm font-semibold mb-1 ${themeClasses.descriptionLabel(darkMode)}`}>
//                     Permissions
//                   </label>
//                   <select
//                     name="permissionType"
//                     value={inviteForm.permissionType}
//                     onChange={handleInviteInputChange}
//                     disabled={inviteStatus === 'loading'}
//                     className={`w-full p-2 border rounded ${themeClasses.selectField(darkMode)}`}
//                   >
//                     <option value="READ_ONLY">View Only</option>
//                     <option value="FULL_ACCESS">Full Access</option>
//                   </select>
//                 </div>
//               </div>
              
//               <div className="mt-4">
//                 <button
//                   type="submit"
//                   disabled={inviteStatus === 'loading'}
//                   className={`px-4 py-2 rounded font-medium ${
//                     inviteStatus === 'loading'
//                       ? themeClasses.actionButtonDisabled(darkMode)
//                       : themeClasses.reconcileButton(darkMode)
//                   }`}
//                 >
//                   {inviteStatus === 'loading' ? (
//                     <div className="flex items-center justify-center">
//                       <PaymentLoader darkMode={darkMode} />
//                       {/* <span className="ml-2">Sending Invitation...</span> */}
//                     </div>
//                   ) : (
//                     'Send Invitation'
//                   )}
//                 </button>
//               </div>
//             </form>
//           </div>
//         )}
        
//         {/* Revoke confirmation modal */}
//         {showRevokeConfirm && (
//           <div className={`fixed inset-0 ${themeClasses.modalOverlay(darkMode)} flex items-center justify-center z-50`}>
//             <div className={`p-6 rounded-lg max-w-md w-full ${themeClasses.modalContent(darkMode)} shadow-lg`}>
//               <h3 className="text-lg font-semibold mb-2">Revoke Family Member Access</h3>
//               <p className={`mb-6 ${themeClasses.descriptionText(darkMode)}`}>
//                 Are you sure you want to revoke access for this family member? They will no longer be able to access your account information.
//               </p>
              
//               {revokeStatus === 'failed' && (
//                 <div className={`mb-4 p-4 rounded ${themeClasses.error(darkMode)}`}>
//                   {revokeError || "Failed to revoke access. Please try again."}
//                 </div>
//               )}
              
//               <div className="flex justify-end space-x-4">
//                 <button
//                   onClick={cancelRevoke}
//                   className={`px-4 py-2 rounded ${themeClasses.actionButton(darkMode)}`}

//                   disabled={revokeStatus === 'loading'}
//                 >
//                   Cancel
//                 </button>
//                 <button
//                   onClick={confirmRevoke}
//                   className={`px-4 py-2 rounded font-medium ${
//                     revokeStatus === 'loading'
//                       ? themeClasses.actionButtonDisabled(darkMode)
//                       : themeClasses.deleteButton(darkMode)
//                   }`}
//                   disabled={revokeStatus === 'loading'}
//                 >
//                   {revokeStatus === 'loading' ? (
//                     <div className="flex items-center justify-center">
//                       <PaymentLoader darkMode={darkMode} />
//                       {/* <span className="ml-2">Revoking...</span> */}
//                     </div>
//                   ) : (
//                     'Revoke Access'
//                   )}
//                 </button>
//               </div>
//             </div>
//           </div>
//         )}
        
//         {/* Family members list */}
//         <div>
//           {familyMembersStatus === 'loading' && (
//             <div className={`min-h-screen w-full flex flex-col justify-center items-center ${darkMode ? 'bg-gray-900 text-gray-100' : 'bg-white text-gray-900'}`}>
//               <PaymentLoader darkMode={darkMode} />
//               {/* <p className={`mt-2 text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
//                 Loading family members...
//               </p> */}
//             </div>
//           )}
          
//             {familyMembersStatus === 'failed' && (
//               <div className={`p-4 rounded ${themeClasses.error(darkMode)}`}>
//                 {familyMembersError || 'Failed to load family members. Please try again.'}
//               </div>
//             )}
          
//           {familyMembersStatus === 'succeeded' && familyMembers.length === 0 && !showInviteForm && (
//               <div className={`text-center py-4 ${themeClasses.noChartsText(darkMode)}`}>
//               <p className="mb-4">
//                 {!isPrimary ? "You haven't added any family members yet." : "No family members have been added to this account."}
//               </p>
//               {!isPrimary && (
//                 <button
//                   onClick={() => setShowInviteForm(true)}
//                   className={`px-4 py-2 rounded-md ${themeClasses.reconcileButton(darkMode,
//                     )}`}

//                 >
//                   Add Your First Family Member
//                 </button>
//               )}
//             </div>
//           )}
          
//           {familyMembersStatus === 'succeeded' && familyMembers.length > 0 && (
//             <div className="overflow-x-auto shadow-lg">
//               <table className={`min-w-full rounded-t-xl rounded-b-xl overflow-hidden border-2 shadow-lg ${darkMode ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-200'} ${darkMode ? 'shadow-b-dark' : 'shadow-b-light'}`}>
//                 <thead className={` ${darkMode ? 'bg-gray-700' : 'bg-[#c5e1a5]'}`}>
//                     <tr className={`h-10 ${themeClasses.tableHeaderRow(darkMode)} ${themeClasses.border(darkMode)}`}>
//                       <th className={`px-6 py-3 text-left ${themeClasses.descriptionLabel(darkMode)}`}>Name</th>
//                       <th className={`px-6 py-3 text-left ${themeClasses.descriptionLabel(darkMode)}`}>Email</th>
//                       <th className={`px-6 py-3 text-left ${themeClasses.descriptionLabel(darkMode)}`}>Relationship</th>
//                       <th className={`px-6 py-3 text-left ${themeClasses.descriptionLabel(darkMode)}`}>Permission</th>
//                       <th className={`px-6 py-3 text-left ${themeClasses.descriptionLabel(darkMode)}`}>Status</th>
//                       <th className={`px-6 py-3 text-right ${themeClasses.descriptionLabel(darkMode)}`}>Actions</th>
//                     </tr>
//                 </thead>
//                   <tbody className={`${themeClasses.tableBody(darkMode)}`}>
//                   {familyMembers.map((member) => (
//                     <tr key={member.relationshipId || member.emailId}>
//                       <td className="px-6 py-4 whitespace-nowrap">
//                           <div className={`${themeClasses.tableCell(darkMode)}`}>
//                             {member.name || member.fullName}
//                           </div>
//                         </td>
//                         <td className={`px-6 py-3 whitespace-nowrap text-sm ${themeClasses.descriptionText(darkMode)}`}>
//                           {member.emailId}
//                         </td>
//                         <td className={`px-6 py-3 whitespace-nowrap text-sm ${themeClasses.descriptionText(darkMode)}`}>
//                           {formatRelationshipType(member.relationshipType)}
//                         </td>
//                         <td className={`px-6 py-3 whitespace-nowrap text-sm ${themeClasses.descriptionText(darkMode)}`}>
//                         {formatPermissionType(member.permissionType)}
//                       </td>
//                       <td className="px-6 py-4 whitespace-nowrap">
//                         <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
//                           member.status === 'ACTIVE' 
//                             ? 'bg-green-100 text-green-800' 
//                             : member.status === 'PENDING'
//                             ? 'bg-yellow-100 text-yellow-800'
//                             : 'bg-red-100 text-red-800'
//                         }`}>
//                           {member.status || 'PENDING'}
//                         </span>
//                       </td>
//                       {/* Only show Actions column for primary users */}
//                       {!isPrimary && (
//                         <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
//                           <button
//                             onClick={() => handleRevokeMember(member.relationshipId)}
//                             className="text-red-600 hover:text-red-900 hover:underline"
//                           >
//                             Revoke
//                           </button>
//                         </td>
//                       )}
//                     </tr>
//                   ))}
//                 </tbody>
//               </table>
//             </div>
//           )}
//         </div>
//       </div>
//     </div>
//   );
// };

// export default FamilySection;
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import { 
  // Invite and signup related actions
  inviteFamilyMemberRequest, 
  validateInviteLinkRequest,
  completeSignupRequest,
  revokeFamilyMemberRequest,
  resetInviteState,
  resetValidateState,
  resetSignupState,
  resetRevokeState,
  selectInviteStatus,
  selectInviteError,
  selectInviteResponse,
  selectValidateStatus,
  selectValidateError,
  selectValidateResponse,
  selectSignupStatus,
  selectSignupError,
  selectSignupResponse,
  selectRevokeStatus,
  selectRevokeError,
  // Family members list related actions
  getFamilyMembersRequest,
  selectFamilyMembersStatus,
  selectFamilyMembersError,
  selectFamilyMembers
} from '../../../../logic/redux/memberSlice';
import PaymentLoader from '../load/PaymentLoader'; // Adjust path as needed
import { themeClasses } from '../../utils/tailwindUtils'; // Adjust path as needed

// Maximum allowed family members per primary user
const MAX_FAMILY_MEMBERS = 2;

// Mock data for the blurred table
const mockFamilyMembers = [
  {
    id: 1,
    name: 'John Doe',
    emailId: '<EMAIL>',
    relationshipType: 'SPOUSE',
    permissionType: 'READ_ONLY',
    status: 'ACTIVE'
  },
  {
    id: 2,
    name: 'Jane Smith',
    emailId: '<EMAIL>',
    relationshipType: 'CHILD',
    permissionType: 'FULL_ACCESS',
    status: 'PENDING'
  }
];

const FamilySection = ({ darkMode, isPrimary = false }) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const navigate = useNavigate();
  
  // UI State
  const [showInviteForm, setShowInviteForm] = useState(false);
  const [showRevokeConfirm, setShowRevokeConfirm] = useState(false);
  const [selectedMemberId, setSelectedMemberId] = useState(null);
  
  // Form states
  const [inviteForm, setInviteForm] = useState({
    emailId: '',
    relationshipType: 'SPOUSE', // Default relationship
    permissionType: 'READ_ONLY', // Default permission
    fullName: ''
  });
  
  const [signupForm, setSignupForm] = useState({
    password: '',
    confirmPassword: '',
    username: '',
    emailId: '',
    agreedToTerms: false
  });
  
  // Get token from URL if present
  const urlParams = new URLSearchParams(location.search);
  const inviteToken = urlParams.get('token');
  
  // Redux state selectors for invite/signup functionality
  const inviteStatus = useSelector(selectInviteStatus);
  const inviteError = useSelector(selectInviteError);
  const inviteResponse = useSelector(selectInviteResponse);
  
  const validateStatus = useSelector(selectValidateStatus);
  const validateError = useSelector(selectValidateError);
  const validateResponse = useSelector(selectValidateResponse);
  
  const signupStatus = useSelector(selectSignupStatus);
  const signupError = useSelector(selectSignupError);
  const signupResponse = useSelector(selectSignupResponse);
  
  const revokeStatus = useSelector(selectRevokeStatus);
  const revokeError = useSelector(selectRevokeError);
  
  // Redux state selectors for family members list
  const familyMembersStatus = useSelector(selectFamilyMembersStatus);
  const familyMembersError = useSelector(selectFamilyMembersError);
  const familyMembers = useSelector(selectFamilyMembers);
  
  // Check if we're in invite view or signup view
  const isInviteView = !inviteToken;
  
  // Check if the family members limit has been reached
  const isFamilyMembersLimitReached = familyMembers.length >= MAX_FAMILY_MEMBERS;
  
  // Validate token on component mount if present
  useEffect(() => {
    if (inviteToken) {
      dispatch(validateInviteLinkRequest(inviteToken));
    } else {
      // Only fetch family members when we're in the normal view (not signup)
      dispatch(getFamilyMembersRequest());
    }
    
    // Cleanup on unmount
    return () => {
      dispatch(resetInviteState());
      dispatch(resetValidateState());
      dispatch(resetSignupState());
      dispatch(resetRevokeState());
    };
  }, [dispatch, inviteToken]);
  
  // Pre-fill signup form with validated data
  useEffect(() => {
    if (validateStatus === 'succeeded' && validateResponse) {
      // Pre-fill what we can from the validation response
      if (validateResponse.emailId) {
        setSignupForm(prev => ({
          ...prev,
          emailId: validateResponse.emailId
        }));
      }
    }
  }, [validateStatus, validateResponse]);
  
  // Redirect after successful signup
  useEffect(() => {
    if (signupStatus === 'succeeded') {
      alert('Signup completed successfully! You can now log in.');
      navigate('/login');
    }
  }, [signupStatus, navigate]);
  
  // Reset the form after successful invite
  useEffect(() => {
    if (inviteStatus === 'succeeded') {
      setInviteForm({
        emailId: '',
        relationshipType: 'SPOUSE',
        permissionType: 'READ_ONLY',
        fullName: ''
      });
      
      // Refresh the family members list
      dispatch(getFamilyMembersRequest());
      
      // Hide the invite form after 3 seconds
      setTimeout(() => {
        setShowInviteForm(false);
      }, 3000);
    }
  }, [inviteStatus, dispatch]);
  
  // Refresh the list after successful revoke
  useEffect(() => {
    if (revokeStatus === 'succeeded') {
      // Close the confirmation dialog
      setShowRevokeConfirm(false);
      setSelectedMemberId(null);
      
      // Refresh the family members list
      dispatch(getFamilyMembersRequest());
      
      // Show a success notification
      alert('Family member access revoked successfully');
    }
  }, [revokeStatus, dispatch]);
  
  // Handle form input changes
  const handleInviteInputChange = (e) => {
    const { name, value } = e.target;
    setInviteForm(prev => ({ ...prev, [name]: value }));
  };
  
  const handleSignupInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    const inputValue = type === 'checkbox' ? checked : value;
    setSignupForm(prev => ({ ...prev, [name]: inputValue }));
  };
  
  // Form submission handlers
  const handleInviteSubmit = (e) => {
    e.preventDefault();
    
    // Prepare the payload with correct field names
    const payload = {
      emailId: inviteForm.emailId,
      relationshipType: inviteForm.relationshipType,
      permissionType: inviteForm.permissionType,
      fullName: inviteForm.fullName
    };
    
    dispatch(inviteFamilyMemberRequest(payload));
  };
  
  const handleSignupSubmit = (e) => {
    e.preventDefault();
    if (signupForm.password !== signupForm.confirmPassword) {
      alert("Passwords don't match!");
      return;
    }
    
    dispatch(completeSignupRequest({
      token: inviteToken,
      signupDetails: {
        password: signupForm.password,
        name: signupForm.username,
        phoneNumber: '',
        emailId: signupForm.emailId
      }
    }));
  };
  
  // Handle revoke confirmation
  const handleRevokeMember = (relationshipId) => {
    setSelectedMemberId(relationshipId);
    setShowRevokeConfirm(true);
  };
  
  const confirmRevoke = () => {
    if (selectedMemberId) {
      dispatch(revokeFamilyMemberRequest(selectedMemberId));
    }
  };
  
  const cancelRevoke = () => {
    setShowRevokeConfirm(false);
    setSelectedMemberId(null);
  };
  
  // Helper function to display relationship type in human-readable format
  const formatRelationshipType = (type) => {
    switch (type) {
      case 'SPOUSE': return 'Spouse';
      case 'CHILD': return 'Child';
      case 'PARENT': return 'Parent';
      case 'SIBLING': return 'Sibling';
      case 'OTHER': return 'Other';
      default: return type;
    }
  };

  // Helper function to display permission type in human-readable format
  const formatPermissionType = (type) => {
    return type === 'READ_ONLY' ? 'View Only' : 'Full Access';
  };
  
  // Show PaymentLoader for invite validation
  if (inviteToken && validateStatus === 'loading') {
    return (
      <div
        className={`min-h-screen w-full p-5 flex flex-col justify-center items-center ${themeClasses.pageContainer(
          darkMode,
        )}`}
      >
        <PaymentLoader darkMode={darkMode} />
        {/* <p className={`mt-2 text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          Validating invite link...
        </p> */}
      </div>
    );
  }
  
  // Show error if token is invalid
  if (inviteToken && validateStatus === 'failed') {
    return (
   <div
        className={`max-w-md mx-auto mt-20 p-6 rounded-lg ${themeClasses.modalContent(darkMode)} shadow-lg`}
      >
                <h2 className="text-xl font-bold text-red-600 mb-4">Invalid Invite Link</h2>
        <p className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
          {validateError || "This invite link is invalid or has expired."}
        </p>
        <button 
          onClick={() => navigate('/')}
          className={`mt-4 px-4 py-2 rounded font-medium ${themeClasses.button(darkMode)}`}
        >
          Return to Home
        </button>
      </div>
    );
  }
  
  // Render the signup form if token is valid
  if (inviteToken && validateStatus === 'succeeded') {
    return (
  <div
        className={`max-w-md mx-auto mt-20 p-10 rounded-lg ${themeClasses.modalContent(darkMode)} shadow-lg`}
      >
                <h2 className="text-xl font-bold mb-4">Complete Your Account Setup</h2>
        
        {signupStatus === 'failed' && (
          <div className={`mb-4 p-3 rounded ${themeClasses.error(darkMode)}`}>
            {signupError || "Failed to complete signup. Please try again."}
          </div>
        )}
        
        <form onSubmit={handleSignupSubmit}>
          <div className="mb-4">
            <label className={`block text-sm font-medium mb-1 ${themeClasses.descriptionLabel(darkMode)}`}>
              Email
            </label>
            <input
              type="email"
              name="emailId"
              value={signupForm.emailId}
              onChange={handleSignupInputChange}
              readOnly
              className={`w-full p-2 border rounded ${themeClasses.inputField(darkMode)} bg-gray-100`}
            />
            <p className={`text-xs mt-1 ${themeClasses.descriptionText(darkMode)}`}>
              Email is pre-filled from your invitation and cannot be changed.
            </p>
          </div>
          
          <div className="mb-4">
            <label className={`block text-sm font-medium mb-1 ${themeClasses.descriptionLabel(darkMode)}`}>
              Full Name
            </label>
            <input
              type="text"
              name="username"
              value={signupForm.username}
              onChange={handleSignupInputChange}
              required
              className={`w-full p-2 border rounded ${themeClasses.inputField(darkMode)}`}
              placeholder="Enter your full name"
            />
          </div>
          
          <div className="mb-4">
            <label className={`block text-sm font-medium mb-1 ${themeClasses.descriptionLabel(darkMode)}`}>
              Password
            </label>
            <input
              type="password"
              name="password"
              value={signupForm.password}
              onChange={handleSignupInputChange}
              required
              className={`w-full p-2 border rounded ${themeClasses.inputField(darkMode)}`}
              placeholder="Create a password"
              minLength="8"
            />
          </div>
          
          <div className="mb-6">
            <label className={`block text-sm font-medium mb-1 ${themeClasses.descriptionLabel(darkMode)}`}>
              Confirm Password
            </label>
            <input
              type="password"
              name="confirmPassword"
              value={signupForm.confirmPassword}
              onChange={handleSignupInputChange}
              required
              className={`w-full p-2 border rounded ${themeClasses.inputField(darkMode)}`}
              placeholder="Confirm your password"
              minLength="8"
            />
          </div>
          
          <div className="mb-6">
            <label className="flex items-center">
              <input
                type="checkbox"
                name="agreedToTerms"
                checked={signupForm.agreedToTerms}
                onChange={handleSignupInputChange}
                required
                className={`mr-2 ${themeClasses.checkboxInput(darkMode)}`}
              />
              <span className={`text-sm ${themeClasses.descriptionLabel(darkMode)}`}>
                I agree to the{' '}
                <a href="/terms" className={`${themeClasses.link(darkMode)}`}>
                  Terms and Conditions
                </a>
              </span>
            </label>
          </div>
          
          <button
            type="submit"
            disabled={signupStatus === 'loading' || !signupForm.agreedToTerms}
            className={`w-full py-2 px-4 rounded font-medium ${
              signupStatus === 'loading' || !signupForm.agreedToTerms
                ? themeClasses.actionButtonDisabled(darkMode)
                : themeClasses.reconcileButton(darkMode)
            }`}
          >
            {signupStatus === 'loading' ? (
              <div className="flex items-center justify-center">
                <PaymentLoader darkMode={darkMode} />
                {/* <span className="ml-2">Creating Account...</span> */}
              </div>
            ) : (
              'Create Account'
            )}
          </button>
        </form>
      </div>
    );
  }
  
  // The main family section view (when not handling invites/signups)
  return (
    <div className={`p-2 ${themeClasses.pageContainer(darkMode)}`}>
      <div className={`${themeClasses.pageContainer(darkMode)}`}>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl">Family Members</h2>

          {/* Show Add button only when invite form is NOT open, limit not reached, and not loading */}
        {!showInviteForm && !isFamilyMembersLimitReached && familyMembersStatus !== 'loading' && !isPrimary && familyMembers.length > 0 && (
  <button
    onClick={() => setShowInviteForm(true)}
    className={`px-4 py-2 rounded flex items-center ${themeClasses.reconcileButton(darkMode)}`}
  >
    <span className="mr-1">+</span> Add Family Member
  </button>
)}

          {/* Show Cancel button only when invite form is open */}
          {!isPrimary && showInviteForm && (
            <button
              onClick={() => setShowInviteForm(false)}
              className={`px-4 py-2 rounded ${themeClasses.actionButton(darkMode)}`}
            >
              Cancel
            </button>
          )}
        </div>
        
        {/* Display limit reached message if applicable */}
        {isFamilyMembersLimitReached && !showInviteForm && (
          <div className={`mb-4 p-3 rounded-lg ${themeClasses.warning(darkMode)}`}>
            You have reached the maximum limit of {MAX_FAMILY_MEMBERS} family members.
          </div>
        )}
        
        {/* Show message for non-primary users */}
        {isPrimary && (
           <div
            className={`mb-6 p-4 border rounded-lg ${themeClasses.cardContainer(darkMode)} ${themeClasses.border(
              darkMode,
            )}`}
          >
            Only the primary account holder can manage family members.
          </div>
        )}
        
        {/* Invite form - only show for primary users */}
        {!isPrimary && showInviteForm && (
          <div className={`mb-6 p-4 border rounded-lg ${darkMode ? 'border-gray-600 bg-gray-800' : 'border-gray-200 bg-white'}`}>
            <h3 className="text-lg font-semibold mb-4">Invite a Family Member</h3>
            
            {inviteStatus === 'failed' && (
              <div className={`mb-4 p-3 rounded ${themeClasses.error(darkMode)}`}>
                {inviteError || 'Failed to send invitation. Please try again.'}
              </div>
            )}
            
            {inviteStatus === 'succeeded' && (
              <div className={`mb-4 p-3 rounded ${themeClasses.success(darkMode)}`}>
                Invitation sent successfully to {inviteResult?.emailId || inviteForm.emailId}!
              </div>
            )}
            
            <form onSubmit={handleInviteSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className={`block text-sm font-medium mb-1 ${themeClasses.descriptionLabel(darkMode)}`}>
                    Email Address
                  </label>
                  <input
                    type="email"
                    name="emailId"
                    value={inviteForm.emailId}
                    onChange={handleInviteInputChange}
                    required
                    disabled={inviteStatus === 'loading'}
                    className={`w-full p-2 border rounded ${themeClasses.inputField(darkMode)}`}
                    placeholder="<EMAIL>"
                  />
                </div>
                
                <div>
                  <label className={`block text-sm font-medium mb-1 ${themeClasses.descriptionLabel(darkMode)}`}>
                    Full Name
                  </label>
                  <input
                    type="text"
                    name="fullName"
                    value={inviteForm.fullName}
                    onChange={handleInviteInputChange}
                    required
                    disabled={inviteStatus === 'loading'}
                    className={`w-full p-2 border rounded ${themeClasses.inputField(darkMode)}`}
                    placeholder="John Doe"
                  />
                </div>
                
                <div>
                  <label className={`block text-sm font-medium mb-1 ${themeClasses.descriptionLabel(darkMode)}`}>
                    Relationship
                  </label>
                  <select
                    name="relationshipType"
                    value={inviteForm.relationshipType}
                    onChange={handleInviteInputChange}
                    disabled={inviteStatus === 'loading'}
                    className={`w-full p-2 border rounded ${themeClasses.selectField(darkMode)}`}
                  >
                    <option value="SPOUSE">Spouse</option>
                    <option value="CHILD">Child</option>
                    <option value="PARENT">Parent</option>
                    <option value="SIBLING">Sibling</option>
                    <option value="OTHER">Other</option>
                  </select>
                </div>
                
                <div>
                  <label className={`block text-sm font-semibold mb-1 ${themeClasses.descriptionLabel(darkMode)}`}>
                    Permissions
                  </label>
                  <select
                    name="permissionType"
                    value={inviteForm.permissionType}
                    onChange={handleInviteInputChange}
                    disabled={inviteStatus === 'loading'}
                    className={`w-full p-2 border rounded ${themeClasses.selectField(darkMode)}`}
                  >
                    <option value="READ_ONLY">View Only</option>
                    <option value="FULL_ACCESS">Full Access</option>
                  </select>
                </div>
              </div>
              
              <div className="mt-4">
                <button
                  type="submit"
                  disabled={inviteStatus === 'loading'}
                  className={`px-4 py-2 rounded font-medium ${
                    inviteStatus === 'loading'
                      ? themeClasses.actionButtonDisabled(darkMode)
                      : themeClasses.reconcileButton(darkMode)
                  }`}
                >
                  {inviteStatus === 'loading' ? (
                    <div className="flex items-center justify-center">
                      <PaymentLoader darkMode={darkMode} />
                      {/* <span className="ml-2">Sending Invitation...</span> */}
                    </div>
                  ) : (
                    'Send Invitation'
                  )}
                </button>
              </div>
            </form>
          </div>
        )}
        
        {/* Revoke confirmation modal */}
        {showRevokeConfirm && (
          <div className={`fixed inset-0 ${themeClasses.modalOverlay(darkMode)} flex items-center justify-center z-50`}>
            <div className={`p-6 rounded-lg max-w-md w-full ${themeClasses.modalContent(darkMode)} shadow-lg`}>
              <h3 className="text-lg font-semibold mb-2">Revoke Family Member Access</h3>
              <p className={`mb-6 ${themeClasses.descriptionText(darkMode)}`}>
                Are you sure you want to revoke access for this family member? They will no longer be able to access your account information.
              </p>
              
              {revokeStatus === 'failed' && (
                <div className={`mb-4 p-4 rounded ${themeClasses.error(darkMode)}`}>
                  {revokeError || "Failed to revoke access. Please try again."}
                </div>
              )}
              
              <div className="flex justify-end space-x-4">
                <button
                  onClick={cancelRevoke}
                  className={`px-4 py-2 rounded ${themeClasses.actionButton(darkMode)}`}

                  disabled={revokeStatus === 'loading'}
                >
                  Cancel
                </button>
                <button
                  onClick={confirmRevoke}
                  className={`px-4 py-2 rounded font-medium ${
                    revokeStatus === 'loading'
                      ? themeClasses.actionButtonDisabled(darkMode)
                      : themeClasses.deleteButton(darkMode)
                  }`}
                  disabled={revokeStatus === 'loading'}
                >
                  {revokeStatus === 'loading' ? (
                    <div className="flex items-center justify-center">
                      <PaymentLoader darkMode={darkMode} />
                      {/* <span className="ml-2">Revoking...</span> */}
                    </div>
                  ) : (
                    'Revoke Access'
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
        
        {/* Family members list */}
        <div>
          {familyMembersStatus === 'loading' && (
            <div className={`min-h-screen w-full flex flex-col justify-center items-center ${darkMode ? 'bg-gray-900 text-gray-100' : 'bg-white text-gray-900'}`}>
              <PaymentLoader darkMode={darkMode} />
              {/* <p className={`mt-2 text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Loading family members...
              </p> */}
            </div>
          )}
          
            {familyMembersStatus === 'failed' && (
              <div className={`p-4 rounded ${themeClasses.error(darkMode)}`}>
                {familyMembersError || 'Failed to load family members. Please try again.'}
              </div>
            )}
          
          {familyMembersStatus === 'succeeded' && familyMembers.length === 0 && !showInviteForm && (
            <div className="relative">
              {/* Blurred mock table */}
              <div className={`overflow-x-auto shadow-lg blur-sm pointer-events-none ${darkMode ? 'opacity-30' : 'opacity-40'}`}>
                <table className={`min-w-full rounded-t-xl rounded-b-xl overflow-hidden border-2 shadow-lg ${darkMode ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-200'} ${darkMode ? 'shadow-b-dark' : 'shadow-b-light'}`}>
                  <thead className={` ${darkMode ? 'bg-gray-700' : 'bg-[#c5e1a5]'}`}>
                    <tr className={`h-10 ${themeClasses.tableHeaderRow(darkMode)} ${themeClasses.border(darkMode)}`}>
                      <th className={`px-6 py-3 text-left ${themeClasses.descriptionLabel(darkMode)}`}>Name</th>
                      <th className={`px-6 py-3 text-left ${themeClasses.descriptionLabel(darkMode)}`}>Email</th>
                      <th className={`px-6 py-3 text-left ${themeClasses.descriptionLabel(darkMode)}`}>Relationship</th>
                      <th className={`px-6 py-3 text-left ${themeClasses.descriptionLabel(darkMode)}`}>Permission</th>
                      <th className={`px-6 py-3 text-left ${themeClasses.descriptionLabel(darkMode)}`}>Status</th>
                      <th className={`px-6 py-3 text-right ${themeClasses.descriptionLabel(darkMode)}`}>Actions</th>
                    </tr>
                  </thead>
                  <tbody className={`${themeClasses.tableBody(darkMode)}`}>
                    {mockFamilyMembers.map((member) => (
                      <tr key={member.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className={`${themeClasses.tableCell(darkMode)}`}>
                            {member.name}
                          </div>
                        </td>
                        <td className={`px-6 py-3 whitespace-nowrap text-sm ${themeClasses.descriptionText(darkMode)}`}>
                          {member.emailId}
                        </td>
                        <td className={`px-6 py-3 whitespace-nowrap text-sm ${themeClasses.descriptionText(darkMode)}`}>
                          {formatRelationshipType(member.relationshipType)}
                        </td>
                        <td className={`px-6 py-3 whitespace-nowrap text-sm ${themeClasses.descriptionText(darkMode)}`}>
                          {formatPermissionType(member.permissionType)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                            member.status === 'ACTIVE' 
                              ? 'bg-green-100 text-green-800' 
                              : member.status === 'PENDING'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {member.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button className="text-red-600 hover:text-red-900 hover:underline">
                            Revoke
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              {/* Centered content overlay */}
              <div className="absolute inset-0 flex flex-col items-center justify-center bg-transparent">
                <div className={`text-center py-8 px-6 rounded-lg ${darkMode ? 'bg-gray-800/90' : 'bg-white/90'} backdrop-blur-sm shadow-lg border ${darkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                  <div className={`${themeClasses.noChartsText(darkMode)} mb-4`}>
                    <h3 className="text-lg font-semibold mb-2">
                      {!isPrimary ? "No Family Members Yet" : "No Family Members Added"}
                    </h3>
                    <p className="text-sm">
                      {!isPrimary 
                        ? "Add family members to share your financial information securely." 
                        : "Only the primary account holder can manage family members."
                      }
                    </p>
                  </div>
                  {!isPrimary && (
                    <button
                      onClick={() => setShowInviteForm(true)}
                      className={`px-6 py-3 rounded-md font-medium ${themeClasses.reconcileButton(darkMode)} shadow-md hover:shadow-lg transition-shadow`}
                    >
                      Add Your First Family Member
                    </button>
                  )}
                </div>
              </div>
            </div>
          )} {familyMembersStatus === 'succeeded' && familyMembers.length > 0 && (
            <div className={`overflow-x-auto shadow-lg`}>
              <table className={`min-w-full rounded-t-xl rounded-b-xl overflow-hidden border-2 shadow-lg ${darkMode ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-200'} ${darkMode ? 'shadow-b-dark' : 'shadow-b-light'}`}>
                <thead className={` ${darkMode ? 'bg-gray-700' : 'bg-[#c5e1a5]'}`}>
                  <tr className={`h-10 ${themeClasses.tableHeaderRow(darkMode)} ${themeClasses.border(darkMode)}`}>
                    <th className={`px-6 py-3 text-left ${themeClasses.descriptionLabel(darkMode)}`}>Name</th>
                    <th className={`px-6 py-3 text-left ${themeClasses.descriptionLabel(darkMode)}`}>Email</th>
                    <th className={`px-6 py-3 text-left ${themeClasses.descriptionLabel(darkMode)}`}>Relationship</th>
                    <th className={`px-6 py-3 text-left ${themeClasses.descriptionLabel(darkMode)}`}>Permission</th>
                    <th className={`px-6 py-3 text-left ${themeClasses.descriptionLabel(darkMode)}`}>Status</th>
                    {!isPrimary && (
                      <th className={`px-6 py-3 text-right ${themeClasses.descriptionLabel(darkMode)}`}>Actions</th>
                    )}
                  </tr>
                </thead>
                <tbody className={`${themeClasses.tableBody(darkMode)}`}>
                  {familyMembers.map((member) => (
                    <tr key={member.id || member.relationshipId}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`${themeClasses.tableCell(darkMode)}`}>
                          {member.name || member.fullName}
                        </div>
                      </td>
                      <td className={`px-6 py-3 whitespace-nowrap text-sm ${themeClasses.descriptionText(darkMode)}`}>
                        {member.emailId}
                      </td>
                      <td className={`px-6 py-3 whitespace-nowrap text-sm ${themeClasses.descriptionText(darkMode)}`}>
                        {formatRelationshipType(member.relationshipType)}
                      </td>
                      <td className={`px-6 py-3 whitespace-nowrap text-sm ${themeClasses.descriptionText(darkMode)}`}>
                        {formatPermissionType(member.permissionType)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                          member.status === 'ACTIVE' 
                            ? 'bg-green-100 text-green-800' 
                            : member.status === 'PENDING'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {member.status}
                        </span>
                      </td>
                      {!isPrimary && (
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => handleRevokeMember(member.relationshipId || member.id)}
                            className="text-red-600 hover:text-red-900 hover:underline"
                          >
                            Revoke
                          </button>
                        </td>
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FamilySection;