import React, { useEffect, useRef, useState } from 'react';
import { logEvent } from '../../../utils/EventLogger';
import Cookies from 'js-cookie';
import { jwtDecode } from "jwt-decode";
import { useDispatch, useSelector } from 'react-redux';
import { fetchSankeyChartData } from '../../../../../logic/redux/accountsDashboardSlice';
import { 
  FaArrowCircleLeft, 
  FaArrowCircleRight, 
  FaRegCreditCard, 
  FaMoneyBillWave, 
  FaCoins, 
  FaRobot, 
  FaArrowDown, 
  FaTimes, 
  FaRedo 
} from 'react-icons/fa';
import CashflowAIChat from './CashflowAIChat';
import PaymentLoader from '../../load/PaymentLoader';
import { themeClasses } from '../../../utils/tailwindUtils';

const Cashflow = ({ darkMode }) => {
  const dispatch = useDispatch();
  const { sankey<PERSON>hartD<PERSON>, sankey<PERSON>hartLoading, sankeyChartError } = useSelector((state) => state.accounts);

  // State management
  const [month, setMonth] = useState(new Date().getMonth() + 1);
  const [year, setYear] = useState(new Date().getFullYear());
  const [showSubcategories, setShowSubcategories] = useState(false);
  const [yGap, setYGap] = useState(30);
  const [selectedTheme, setSelectedTheme] = useState('emerald');
  const [showThemeDropdown, setShowThemeDropdown] = useState(false);
  const [showAIChat, setShowAIChat] = useState(false);
  // APT-182 fix for incorrect income in summary card
  const [summaryIncome, setSummaryIncome] = useState(0);

  // Constants
  const TOKEN_COOKIE_NAME = 'pennypal_jwt_token';
  const token = Cookies.get(TOKEN_COOKIE_NAME);
  const decodedToken = token ? jwtDecode(token) : null;
  const userId = decodedToken ? decodedToken.userId : 0;
  
  const containerRef = useRef(null);

  // Month data
  const months = [
    { value: 1, label: 'January' },
    { value: 2, label: 'February' },
    { value: 3, label: 'March' },
    { value: 4, label: 'April' },
    { value: 5, label: 'May' },
    { value: 6, label: 'June' },
    { value: 7, label: 'July' },
    { value: 8, label: 'August' },
    { value: 9, label: 'September' },
    { value: 10, label: 'October' },
    { value: 11, label: 'November' },
    { value: 12, label: 'December' }
  ];

  // Color Theme System
  const colorThemes = {
    default: {
      name: "Default Green",
      primary: "#8BC34A",
      secondary: "#7CB342", 
      accent: "#9CCC65",
      success: "#4CAF50"
    },
    ocean: {
      name: "Ocean Blue",
      primary: "#0EA5E9",
      secondary: "#0284C7",
      accent: "#38BDF8", 
      success: "#06B6D4"
    },
    sunset: {
      name: "Sunset Orange",
      primary: "#F97316",
      secondary: "#EA580C",
      accent: "#FB923C",
      success: "#F59E0B"
    },
    purple: {
      name: "Royal Purple",
      primary: "#8B5CF6",
      secondary: "#7C3AED",
      accent: "#A78BFA",
      success: "#6366F1"
    },
    rose: {
      name: "Rose Pink",
      primary: "#EC4899",
      secondary: "#DB2777",
      accent: "#F472B6",
      success: "#F43F5E"
    },
    emerald: {
      name: "Emerald Green",
      primary: "#10B981",
      secondary: "#059669",
      accent: "#34D399",
      success: "#6EE7B7"
    },
    slate: {
      name: "Modern Slate",
      primary: "#64748B",
      secondary: "#475569",
      accent: "#94A3B8",
      success: "#0F172A"
    }
  };

  // Get current theme colors
  const currentTheme = colorThemes[selectedTheme];

  // Enhanced color palette with distinct income/expense colors
  const colorPalettes = {
    light: {
      primary: {
        Income: currentTheme.primary,        // Green for income
        Expenses: '#EF4444',                 // Red for expenses  
        Buffer: currentTheme.accent,         // Accent for savings
        tax: '#F59E0B'                      // Orange for tax
      },
      subcategories: {
        // Income subcategories - Green spectrum
        'Salary': currentTheme.primary,
        'Freelance': '#059669',              // Emerald-600
        'Investment': '#0D9488',             // Teal-600
        'Rental': '#16A34A',                 // Green-600
        'Business': '#15803D',               // Green-700
        'Bonus': '#166534',                  // Green-800
        'Other Income': '#14532D',           // Green-900

        // Expense subcategories - Red spectrum  
        'Housing': '#DC2626',                // Red-600
        'Food': '#B91C1C',                   // Red-700
        'Transportation': '#991B1B',         // Red-800
        'Healthcare': '#7F1D1D',             // Red-900
        'Entertainment': '#EF4444',          // Red-500
        'Shopping': '#F87171',               // Red-400
        'Utilities': '#FCA5A5',              // Red-300
        'Insurance': '#FEE2E2',              // Red-100
        'Education': '#DC2626',              // Red-600
        'Travel': '#B91C1C',                 // Red-700
        'Subscriptions': '#991B1B',          // Red-800
        'Personal Care': '#7F1D1D',          // Red-900
        'Gifts': '#EF4444',                  // Red-500
        'Charity': '#F87171',                // Red-400
        'Debt': '#FCA5A5',                   // Red-300
        'Other Expenses': '#FEE2E2',         // Red-100

        // Buffer subcategories - Cool accent colors
        'Emergency Fund': currentTheme.accent,
        'Savings': currentTheme.primary,
        'Investment Fund': currentTheme.secondary,
        'Retirement': currentTheme.success,
      }
    },
    dark: {
      primary: {
        Income: currentTheme.primary,        // Theme primary for income
        Expenses: '#F87171',                 // Light red for expenses in dark mode
        Buffer: currentTheme.accent,         // Accent for savings  
        tax: '#FBBF24'                      // Light orange for tax
      },
      subcategories: {
        // Income subcategories - Bright green spectrum for dark mode
        'Salary': currentTheme.primary,
        'Freelance': '#34D399',              // Emerald-400
        'Investment': '#2DD4BF',             // Teal-400
        'Rental': '#4ADE80',                 // Green-400
        'Business': '#22C55E',               // Green-500
        'Bonus': '#16A34A',                  // Green-600
        'Other Income': '#15803D',           // Green-700

        // Expense subcategories - Bright red spectrum for dark mode
        'Housing': '#F87171',                // Red-400
        'Food': '#EF4444',                   // Red-500
        'Transportation': '#DC2626',         // Red-600
        'Healthcare': '#B91C1C',             // Red-700
        'Entertainment': '#FCA5A5',          // Red-300
        'Shopping': '#FECACA',               // Red-200
        'Utilities': '#FEE2E2',              // Red-100
        'Insurance': '#F87171',              // Red-400
        'Education': '#EF4444',              // Red-500
        'Travel': '#DC2626',                 // Red-600
        'Subscriptions': '#B91C1C',          // Red-700
        'Personal Care': '#991B1B',          // Red-800
        'Gifts': '#FCA5A5',                  // Red-300
        'Charity': '#FECACA',                // Red-200
        'Debt': '#FEE2E2',                   // Red-100
        'Other Expenses': '#F87171',         // Red-400

        // Buffer subcategories - Bright cool colors for dark mode
        'Emergency Fund': currentTheme.accent,
        'Savings': currentTheme.primary,
        'Investment Fund': currentTheme.secondary,
        'Retirement': currentTheme.success,
      }
    }
  };

  // Enhanced color helper functions
  const getCategoryColor = (category) => {
    const palette = darkMode ? colorPalettes.dark : colorPalettes.light;
    return palette.primary[category] || (darkMode ? '#9CA3AF' : '#6B7280');
  };

  const getSubcategoryColor = (subcategory, parentCategory) => {
    const palette = darkMode ? colorPalettes.dark : colorPalettes.light;
    
    if (palette.subcategories[subcategory]) {
      return palette.subcategories[subcategory];
    }
    
    const baseColor = getCategoryColor(parentCategory);
    return baseColor;
  };

  const isSubcategory = (nodeId) => {
    const mainCategories = ['Income', 'Expenses', 'Buffer', 'tax'];
    return !mainCategories.includes(nodeId);
  };

  const getNodeColor = (node) => {
    if (isSubcategory(node.id)) {
      return getSubcategoryColor(node.id, node.category);
    }
    return getCategoryColor(node.category);
  };

  // Enhanced function to get flow colors based on flow type
  const getFlowColor = (link, sourceNode, targetNode) => {
    // Income flows (from Income node) - use green/income theme colors
    if (link.source === 'Income') {
      return currentTheme.primary; // Main income color
    }
    
    // Expense flows (to Expenses or expense subcategories) - use red/expense colors
    if (link.target === 'Expenses' || (targetNode && targetNode.category === 'Expenses')) {
      return currentTheme.secondary; // Main expense color
    }
    
    // Buffer/Savings flows - use accent colors
    if (link.target === 'Buffer' || (targetNode && targetNode.category === 'Buffer')) {
      return currentTheme.accent;
    }
    
    // Tax flows - use success color
    if (link.target === 'tax' || (targetNode && targetNode.category === 'tax')) {
      return currentTheme.success;
    }
    
    // For subcategory flows, use enhanced subcategory colors
    if (isSubcategory(link.target)) {
      return getSubcategoryColor(link.target, targetNode.category);
    }
    
    // Default to source node color
    return getNodeColor(sourceNode);
  };

  // Event handlers
  const handleSetToToday = () => {
    const today = new Date();
    setMonth(today.getMonth() + 1);
    setYear(today.getFullYear());
  };

  const handleChangeMonth = (delta) => {
    setMonth(prev => {
      let newMonth = prev + delta;
      if (newMonth > 12) {
        setYear(y => y + 1);
        return 1;
      } else if (newMonth < 1) {
        setYear(y => y - 1);
        return 12;
      }
      return newMonth;
    });
  };

  // Effect for fetching data
  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.innerHTML = '';
    }

    logEvent('Cashflow', 'FetchData', { month, year });
    
    dispatch(fetchSankeyChartData({
      userId,
      year,
      month,
      showSubcategories
    }));
  }, [month, year, showSubcategories, dispatch, userId]);

  // SVG Creation Helper Functions
  const createSVGElement = (type, attributes = {}) => {
    const element = document.createElementNS('http://www.w3.org/2000/svg', type);
    Object.entries(attributes).forEach(([key, value]) => {
      element.setAttribute(key, value);
    });
    return element;
  };

  const createBackground = (svg, containerWidth, containerHeight) => {
    const backgroundDef = createSVGElement('defs');
    svg.appendChild(backgroundDef);

    const bgGradient = createSVGElement('radialGradient', {
      id: 'main-background',
      cx: '50%',
      cy: '30%',
      r: '70%'
    });

    // Convert hex to RGB for better gradient control
    const primaryRGB = hexToRgb(currentTheme.primary);
    const secondaryRGB = hexToRgb(currentTheme.secondary);

    const bgStop1 = createSVGElement('stop', {
      offset: '0%',
      'stop-color': darkMode ? currentTheme.primary : currentTheme.accent,
      'stop-opacity': darkMode ? '0.1' : '0.05'
    });

    const bgStop2 = createSVGElement('stop', {
      offset: '100%',
      'stop-color': darkMode ? currentTheme.secondary : currentTheme.primary,
      'stop-opacity': darkMode ? '0.05' : '0.02'
    });

    bgGradient.appendChild(bgStop1);
    bgGradient.appendChild(bgStop2);
    backgroundDef.appendChild(bgGradient);

    const mainBg = createSVGElement('rect', {
      width: '100%',
      height: '100%',
      fill: 'url(#main-background)',
      rx: '12'
    });
    svg.appendChild(mainBg);
  };

  // Helper function to convert hex to RGB
  const hexToRgb = (hex) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  };

  const createSummary = (g, innerWidth, totalIncome) => {
    const summaryGroup = createSVGElement('g', {
      transform: `translate(${innerWidth / 2}, 21)`
    });
    
    const summaryBackground = createSVGElement('rect', {
      x: '-120',
      y: '-15',
      width: '240',
      height: '30',
      fill: darkMode ? currentTheme.primary + '20' : currentTheme.accent + '15',
      stroke: currentTheme.primary,
      'stroke-width': '2',
      rx: '15',
      opacity: '0.95'
    });
    summaryBackground.style.filter = `drop-shadow(0 4px 8px ${currentTheme.primary}20)`;
    
    const summaryText = createSVGElement('text', {
      'font-size': '16px',
      'font-weight': '700',
      'font-family': 'system-ui, -apple-system, sans-serif',
      fill: darkMode ? 'white' : currentTheme.primary,
      'text-anchor': 'middle',
      y: '5'
    });
    // APT-182 fix for incorrect income in summary card 
    // summaryText.textContent = `Total Income: ${totalIncome.toLocaleString()}`;
    summaryText.textContent = `Total Income: $${summaryIncome.toLocaleString()}`;
    
    summaryGroup.appendChild(summaryBackground);
    summaryGroup.appendChild(summaryText);
    g.appendChild(summaryGroup);
  };

  const createLegend = (g, innerWidth, innerHeight) => {
    const legendGroup = createSVGElement('g', {
      transform: `translate(${innerWidth / 2 - 200}, ${innerHeight - 42})`
    });
    g.appendChild(legendGroup);

    const legendBackground = createSVGElement('rect', {
      x: '-20',
      y: '-25',
      width: '400',
      height: '70',
      fill: darkMode ? currentTheme.primary + '15' : currentTheme.accent + '10',
      stroke: currentTheme.primary + '40',
      'stroke-width': '2',
      rx: '16'
    });
    legendBackground.style.filter = darkMode 
      ? 'drop-shadow(0 8px 16px rgba(0, 0, 0, 0.4))' 
      : 'drop-shadow(0 8px 16px rgba(0, 0, 0, 0.15))';
    legendGroup.appendChild(legendBackground);

    const categories = [
      { name: 'Income Flows', color: currentTheme.primary },
      { name: 'Expense Flows', color: darkMode ? '#F87171' : '#EF4444' },
      { name: 'Savings Flows', color: currentTheme.accent }
    ];

    categories.forEach((category, i) => {
      const legendItem = createSVGElement('g', {
        transform: `translate(${i * 120}, -5)`
      });

      const rect = createSVGElement('rect', {
        width: '20',
        height: '20',
        fill: category.color,
        'fill-opacity': '0.9',
        stroke: category.color,
        'stroke-width': '1.5',
        rx: '5'
      });

      const text = createSVGElement('text', {
        x: '30',
        y: '15',
        'font-size': '14px',
        'font-weight': '700',
        'font-family': 'system-ui, -apple-system, sans-serif',
        fill: darkMode ? '#F8FAFC' : currentTheme.primary
      });
      text.textContent = category.name;

      legendItem.appendChild(rect);
      legendItem.appendChild(text);
      legendGroup.appendChild(legendItem);
    });

    // Add subcategory info
    const subcatInfo = createSVGElement('text', {
      x: '200',
      y: '35',
      'text-anchor': 'middle',
      'font-size': '12px',
      'font-weight': '500',
      'font-style': 'italic',
      fill: darkMode ? currentTheme.accent : currentTheme.secondary
    });
    subcatInfo.textContent = 'Flow colors indicate money direction';
    legendGroup.appendChild(subcatInfo);
  };

  // Enhanced effect for rendering the Sankey chart
  useEffect(() => {
    if (
      !containerRef.current ||
      sankeyChartLoading ||
      sankeyChartError ||
      !sankeyChartData.nodes.length ||
      !sankeyChartData.links.length ||
      !Object.keys(sankeyChartData.nodeLevels).length
    ) {
    // APT-110 fix for 500 error
      if (containerRef.current) {
        containerRef.current.innerHTML = '';
      }
      return;
    }

    containerRef.current.innerHTML = '';

    const data = {
      nodes: sankeyChartData.nodes,
      links: sankeyChartData.links
    };

    const nodeLevels = sankeyChartData.nodeLevels;

    // Calculate dynamic height based on nodes and yGap
    const maxLevel = Math.max(...Object.values(nodeLevels), 0);
    const nodesByLevel = {};

    data.nodes.forEach(node => {
      const level = nodeLevels[node.id];
      if (level === undefined) return;
      if (!nodesByLevel[level]) {
        nodesByLevel[level] = [];
      }
      nodesByLevel[level].push(node);
    });

    const nodeValues = {};
    data.nodes.forEach(node => {
      const incoming = data.links.filter(link => link.target === node.id)
        .reduce((sum, link) => sum + link.value, 0);
      const outgoing = data.links.filter(link => link.source === node.id)
        .reduce((sum, link) => sum + link.value, 0);

      const level = nodeLevels[node.id];
      if (level === undefined) return;
      nodeValues[node.id] = level === 0 ? outgoing : level === maxLevel ? incoming : Math.max(incoming, outgoing);
    });

    // Calculate required height
    let requiredHeight = 84;
    Object.keys(nodesByLevel).forEach(level => {
      const nodesInLevel = nodesByLevel[level];
      const totalValue = nodesInLevel.reduce((sum, node) => sum + (nodeValues[node.id] || 0), 0);
      const valueToHeightRatio = totalValue > 0 ? (560 - 84 - ((nodesInLevel.length - 1) * yGap)) / totalValue : 0;
      let levelHeight = 70;
      
      nodesInLevel.forEach(node => {
        const levelFactor = level / maxLevel;
        const minHeightScale = 1.7;
        const maxHeightScale = 1.9;
        const heightScale = minHeightScale + (maxHeightScale - minHeightScale) * levelFactor;
        const baseNodeHeight = (nodeValues[node.id] || 0) * valueToHeightRatio;
        const nodeHeight = Math.max(baseNodeHeight * heightScale, 12);
        levelHeight += nodeHeight + yGap;
      });
      requiredHeight = Math.max(requiredHeight, levelHeight);
    });

    const containerWidth = containerRef.current.clientWidth || 950;
    // APT-79 fix for subcategory data being cut off - changed containerHeight
    // const containerHeight = Math.max(requiredHeight * 0.7, 450);
    const containerHeight = Math.max(requiredHeight, 600);
    const margin = { top: 28, right: 15, bottom: 56, left: 15 };
    const innerWidth = containerWidth - margin.left - margin.right;
    const innerHeight = containerHeight - margin.top - margin.bottom;

    // Create main SVG
    const svg = createSVGElement('svg', {
      width: '100%',
      height: containerHeight,
      viewBox: `0 0 ${containerWidth} ${containerHeight}`,
      preserveAspectRatio: 'xMidYMid meet'
    });
    svg.style.filter = darkMode 
      ? 'drop-shadow(0 8px 25px rgba(0, 0, 0, 0.4))' 
      : 'drop-shadow(0 8px 25px rgba(0, 0, 0, 0.15))';
    containerRef.current.appendChild(svg);

    // Create background
    createBackground(svg, containerWidth, containerHeight);

    const g = createSVGElement('g', {
      transform: `translate(${margin.left},${margin.top})`
    });
    svg.appendChild(g);

    // Calculate node coordinates
    const nodeWidth = 26;
    const totalGaps = maxLevel;
    const totalNodeWidth = (maxLevel + 1) * nodeWidth;
    const availableGapWidth = innerWidth - totalNodeWidth;
    const levelGap = totalGaps > 0 ? Math.max(availableGapWidth / totalGaps, 20) : 0;
    const levelWidth = nodeWidth + levelGap;
    const xOffset = 0;

    let nodeCoordinates = {};
    Object.keys(nodesByLevel).forEach(level => {
      const nodesInLevel = nodesByLevel[level];
      const totalValue = nodesInLevel.reduce((sum, node) => sum + (nodeValues[node.id] || 0), 0);
      const valueToHeightRatio = totalValue > 0
        ? (innerHeight - 84 - ((nodesInLevel.length - 1) * yGap)) / totalValue
        : 0;

      let currentY = 56;

      nodesInLevel.forEach(node => {
        const levelFactor = level / maxLevel;
        const minHeightScale = 0.4;
        const maxHeightScale = 0.7;
        const heightScale = minHeightScale + (maxHeightScale - minHeightScale) * levelFactor;

        const baseNodeHeight = (nodeValues[node.id] || 0) * valueToHeightRatio;
        const nodeHeight = Math.max(baseNodeHeight * heightScale, 14);
        const nodeX = xOffset + parseInt(level) * levelWidth;

        nodeCoordinates[node.id] = {
          x: nodeX,
          y: currentY,
          width: nodeWidth,
          height: nodeHeight
        };

        currentY += nodeHeight + yGap;
      });
    });

    // Create links
    data.links.forEach(link => {
      const source = nodeCoordinates[link.source];
      const target = nodeCoordinates[link.target];

      if (!source || !target) return;

      const sourceNode = data.nodes.find(n => n.id === link.source);
      const targetNode = data.nodes.find(n => n.id === link.target);

      if (!sourceNode || !targetNode) return;

      const sourceOutgoing = data.links
        .filter(l => l.source === link.source)
        .reduce((sum, l) => sum + l.value, 0);

      const targetIncoming = data.links
        .filter(l => l.target === link.target)
        .reduce((sum, link) => sum + link.value, 0);

      const sourceRatio = sourceOutgoing > 0 ? link.value / sourceOutgoing : 0;
      const targetRatio = targetIncoming > 0 ? link.value / targetIncoming : 0;

      const sourcePortionHeight = source.height * sourceRatio;
      const targetPortionHeight = target.height * targetRatio;

      const sourceIndex = data.links
        .filter(l => l.source === link.source)
        .sort((a, b) => a.target.localeCompare(b.target))
        .findIndex(l => l.target === link.target);

      const sourcePrevHeight = data.links
        .filter(l => l.source === link.source)
        .sort((a, b) => a.target.localeCompare(b.target))
        .slice(0, sourceIndex)
        .reduce((sum, l) => sum + (l.value / sourceOutgoing) * source.height, 0);

      const targetIndex = data.links
        .filter(l => l.target === link.target)
        .sort((a, b) => a.source.localeCompare(b.source))
        .findIndex(l => l.source === link.source);

      const targetPrevHeight = data.links
        .filter(l => l.target === link.target)
        .sort((a, b) => a.source.localeCompare(b.source))
        .slice(0, targetIndex)
        .reduce((sum, l) => sum + (l.value / targetIncoming) * target.height, 0);

      const sourceY = source.y + sourcePrevHeight;
      const targetY = target.y + targetPrevHeight;

      const path = createSVGElement('path');

      const sourceX = source.x + source.width;
      const targetX = target.x;
      
      // Enhanced smooth curve calculation
      const distance = targetX - sourceX;
      const curvature = 0.5;
      const controlX1 = sourceX + distance * curvature * 0.7;
      const controlX2 = targetX - distance * curvature * 0.7;
      
      const yDiff = targetY - sourceY;
      const controlY1 = sourceY + yDiff * 0.1;
      const controlY2 = targetY - yDiff * 0.1;

      // Create ultra-smooth cubic bezier path
      path.setAttribute('d', `
        M ${sourceX} ${sourceY}
        C ${controlX1} ${controlY1}, ${controlX2} ${controlY2}, ${targetX} ${targetY}
        L ${targetX} ${targetY + targetPortionHeight}
        C ${controlX2} ${targetY + targetPortionHeight - yDiff * 0.1}, ${controlX1} ${sourceY + sourcePortionHeight + yDiff * 0.1}, ${sourceX} ${sourceY + sourcePortionHeight}
        Z
      `);

      // Use enhanced flow color system
      const linkColor = getFlowColor(link, sourceNode, targetNode);
        
      path.setAttribute('fill', linkColor);
      path.setAttribute('fill-opacity', '0.8');
      path.setAttribute('stroke', linkColor);
      path.setAttribute('stroke-width', '0.5');
      path.setAttribute('stroke-opacity', '0.9');
      path.setAttribute('opacity', '0.9');
      path.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';

      // Store original color for hover reset
      path.setAttribute('data-original-color', linkColor);

      // Enhanced hover effects
      path.addEventListener('mouseenter', () => {
        path.setAttribute('opacity', '1.0');
        path.setAttribute('fill-opacity', '0.95');
        path.setAttribute('stroke-width', '1.5');
        path.setAttribute('stroke', darkMode ? '#FFFFFF' : '#000000');
        path.style.filter = darkMode 
          ? 'drop-shadow(0 4px 8px rgba(255, 255, 255, 0.15)) brightness(1.2)' 
          : 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.25)) brightness(1.1)';

        const sourceRect = containerRef.current.querySelector(`[data-node="${link.source}"] rect`);
        const targetRect = containerRef.current.querySelector(`[data-node="${link.target}"] rect`);
        if (sourceRect) {
          sourceRect.setAttribute('stroke-width', '3');
          sourceRect.setAttribute('stroke', darkMode ? '#FFFFFF' : '#000000');
          sourceRect.style.filter = 'brightness(1.2) drop-shadow(0 2px 6px rgba(0, 0, 0, 0.3))';
        }
        if (targetRect) {
          targetRect.setAttribute('stroke-width', '3');
          targetRect.setAttribute('stroke', darkMode ? '#FFFFFF' : '#000000');
          targetRect.style.filter = 'brightness(1.2) drop-shadow(0 2px 6px rgba(0, 0, 0, 0.3))';
        }
      });

      path.addEventListener('mouseleave', () => {
        const originalColor = path.getAttribute('data-original-color');
        path.setAttribute('opacity', '0.9');
        path.setAttribute('fill-opacity', '0.8');
        path.setAttribute('stroke-width', '0.5');
        path.setAttribute('stroke', originalColor);
        path.style.filter = 'none';

        const sourceRect = containerRef.current.querySelector(`[data-node="${link.source}"] rect`);
        const targetRect = containerRef.current.querySelector(`[data-node="${link.target}"] rect`);
        if (sourceRect) {
          sourceRect.setAttribute('stroke-width', '1.5');
          sourceRect.setAttribute('stroke', darkMode ? '#6B7280' : '#374151');
          sourceRect.style.filter = 'none';
        }
        if (targetRect) {
          targetRect.setAttribute('stroke-width', '1.5');
          targetRect.setAttribute('stroke', darkMode ? '#6B7280' : '#374151');
          targetRect.style.filter = 'none';
        }
      });

      const pathTitle = createSVGElement('title');
      pathTitle.textContent = `${link.source} → ${link.target}: $${link.value.toLocaleString()}`;
      path.appendChild(pathTitle);

      g.appendChild(path);
    });

    // Create nodes
    data.nodes.forEach(node => {
      const coords = nodeCoordinates[node.id];
      if (!coords) return;

      const nodeGroup = createSVGElement('g', { 'data-node': node.id });

      const rect = createSVGElement('rect', {
        x: coords.x,
        y: coords.y,
        width: coords.width,
        height: coords.height,
        rx: '5'
      });
      
      // Use solid color
      const nodeColor = isSubcategory(node.id) ? 
        getSubcategoryColor(node.id, node.category) : 
        getCategoryColor(node.category);
      
      rect.setAttribute('fill', nodeColor);
      rect.setAttribute('fill-opacity', '0.9');
      rect.setAttribute('stroke', darkMode ? '#6B7280' : '#374151');
      rect.setAttribute('stroke-width', '1.5');
      rect.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';

      const originalStroke = darkMode ? '#6B7280' : '#374151';
      const originalStrokeWidth = '1.5';

      // Enhanced node hover effects
      nodeGroup.addEventListener('mouseenter', () => {
        rect.setAttribute('stroke-width', '3');
        rect.setAttribute('stroke', darkMode ? '#FFFFFF' : '#000000');
        rect.setAttribute('fill-opacity', '1.0');
        rect.style.filter = 'brightness(1.15) drop-shadow(0 4px 12px rgba(0, 0, 0, 0.3)) scale(1.02)';
        rect.style.transform = 'scale(1.02)';
        rect.style.transformOrigin = 'center';
        
        document.querySelectorAll('path').forEach(path => {
          const title = path.querySelector('title');
          if (title && title.textContent.includes(node.id)) {
            path.setAttribute('opacity', '1.0');
            path.setAttribute('fill-opacity', '0.95');
            path.setAttribute('stroke-width', '1.5');
            path.setAttribute('stroke', darkMode ? '#FFFFFF' : '#000000');
            path.style.filter = 'brightness(1.2)';
          } else {
            path.setAttribute('opacity', '0.25');
            path.setAttribute('fill-opacity', '0.25');
          }
        });
      });

      nodeGroup.addEventListener('mouseleave', () => {
        rect.setAttribute('stroke-width', originalStrokeWidth);
        rect.setAttribute('stroke', originalStroke);
        rect.setAttribute('fill-opacity', '0.9');
        rect.style.filter = 'none';
        rect.style.transform = 'scale(1)';
        
        document.querySelectorAll('path').forEach(path => {
          const title = path.querySelector('title');
          if (title && title.textContent.includes(node.id)) {
            const originalColor = path.getAttribute('data-original-color');
            path.setAttribute('opacity', '0.9');
            path.setAttribute('fill-opacity', '0.8');
            path.setAttribute('stroke-width', '0.5');
            path.setAttribute('stroke', originalColor);
            path.style.filter = 'none';
          } else {
            path.setAttribute('opacity', '0.9');
            path.setAttribute('fill-opacity', '0.8');
          }
        });
      });

      const rectTitle = createSVGElement('title');
      rectTitle.textContent = `${node.id}: $${(nodeValues[node.id] || 0).toLocaleString()}`;
      rect.appendChild(rectTitle);

      nodeGroup.appendChild(rect);

      // Enhanced text labels
      const isLeftLabel = nodeLevels[node.id] < (maxLevel / 2);
      const textX = isLeftLabel ? coords.x + coords.width + 10 : coords.x - 10;
      const textY = coords.height < 22 ? coords.y + 16 : coords.y + coords.height / 2;

      const text = createSVGElement('text', {
        x: textX,
        y: textY,
        dy: coords.height < 22 ? '-0.35em' : '0.35em',
        'text-anchor': isLeftLabel ? 'start' : 'end',
        'font-size': '12px',
        'font-weight': isSubcategory(node.id) ? '600' : '700',
        'font-family': 'system-ui, -apple-system, BlinkMacSystemFont, sans-serif',
        fill: darkMode ? '#F8FAFC' : '#1E293B',
        'data-selected': 'false'
      });
      text.style.transition = 'all 0.25s ease';
      
      const displayText = `${node.id} ($${(nodeValues[node.id] || 0).toLocaleString()})`;
      text.textContent = displayText;

      text.style.filter = darkMode 
        ? 'drop-shadow(0 1px 3px rgba(0, 0, 0, 0.9))' 
        : 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.4))';

      text.addEventListener('mouseenter', () => {
        text.setAttribute('font-weight', isSubcategory(node.id) ? '700' : '800');
        text.style.filter = darkMode 
          ? 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.9)) brightness(1.1)' 
          : 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5)) brightness(0.9)';
      });

      text.addEventListener('mouseleave', () => {
        if (text.getAttribute('data-selected') === 'false') {
          text.setAttribute('font-weight', isSubcategory(node.id) ? '600' : '700');
          text.style.filter = darkMode 
            ? 'drop-shadow(0 1px 3px rgba(0, 0, 0, 0.9))' 
            : 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.4))';
        }
      });

      text.addEventListener('click', () => {
        containerRef.current.querySelectorAll('text').forEach(t => {
          const isSubcat = !['Income', 'Expenses', 'Buffer', 'tax'].includes(
            t.textContent.split(' (')[0]
          );
          t.setAttribute('font-weight', isSubcat ? '600' : '700');
          t.setAttribute('data-selected', 'false');
          t.style.filter = darkMode 
            ? 'drop-shadow(0 1px 3px rgba(0, 0, 0, 0.9))' 
            : 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.4))';
        });
        text.setAttribute('font-weight', '800');
        text.setAttribute('data-selected', 'true');
        text.style.filter = darkMode 
          ? 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.9)) brightness(1.2)' 
          : 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5)) brightness(0.8)';
      });

      nodeGroup.appendChild(text);
      g.appendChild(nodeGroup);
    });

    // Create legend
    createLegend(g, innerWidth, innerHeight);

    // Create summary
    const totalIncome = data.links
      .filter(link => link.source === "Income")
      .reduce((sum, link) => sum + link.value, 0);

    createSummary(g, innerWidth, totalIncome);

  }, [sankeyChartData, sankeyChartLoading, sankeyChartError, darkMode, yGap, selectedTheme, currentTheme]);

  // APT-182 fix for incorrect income in summary card
  useEffect(() => {
    if (
      sankeyChartData?.links?.length > 0 &&
      sankeyChartData?.nodes?.length > 0
    ) {
      const totalIncome = sankeyChartData.links
        .filter(link => link.target === 'Total Income')
        .reduce((sum, link) => sum + link.value, 0);

      setSummaryIncome(totalIncome);
    }
  }, [sankeyChartData]);

  return (
    <div 
      className={`rounded-2xl p-8 w-full shadow-2xl border transition-all duration-300 ${showAIChat ? 'mr-96' : ''}`}
      style={{
        background: darkMode 
          ? `linear-gradient(135deg, ${currentTheme.primary}08, ${currentTheme.secondary}15, #1e293b)` 
          : `linear-gradient(135deg, ${currentTheme.primary}05, ${currentTheme.accent}10, #ffffff)`,
        borderColor: darkMode ? `${currentTheme.primary}30` : `${currentTheme.primary}20`
      }}
    >
      {showAIChat && (
        <CashflowAIChat
          darkMode={darkMode}
          currentTheme={colorThemes[selectedTheme]}
          setShowAIChat={setShowAIChat}
          showAIChat={showAIChat}
          month={month}
          year={year}
          setMonth={setMonth}
          setYear={setYear}
        />
      )}
      {/* Enhanced Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 space-y-4 lg:space-y-0">
        <div className="flex items-center space-x-4">
          <div 
            className="p-3 rounded-xl shadow-lg border"
            style={{ 
              backgroundColor: darkMode ? `${currentTheme.primary}20` : `${currentTheme.primary}10`,
              borderColor: darkMode ? '#4B5563' : '#E5E7EB'
            }}
          >
            <FaCoins className="text-2xl" style={{ color: currentTheme.primary }} />
          </div>
          <h1 
            className="text-4xl font-bold"
            style={{ 
              background: `linear-gradient(to right, ${currentTheme.primary}, ${currentTheme.secondary})`,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}
          >
            Cashflow
          </h1>
        </div>
        
        <div className="flex space-x-4">
          <button
            className="p-2 rounded-full bg-green-500 text-white shadow-md hover:bg-green-600"
            onClick={() => setShowAIChat(true)}
          >
            <FaRobot className="inline mr-1" /> Ask AI
          </button>
          {/* Theme Selector */}
          <div className="relative">
            <button
              onClick={() => setShowThemeDropdown(!showThemeDropdown)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-xl font-medium transition-all duration-200 ${
                darkMode
                  ? 'bg-slate-800/50 text-white border border-slate-600/50 hover:bg-slate-700/50'
                  : 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50'
              } shadow-lg hover:shadow-xl`}
            >
              <FaRobot style={{ color: currentTheme.primary }} />
              <span>Theme</span>
              <FaArrowDown className={`text-sm transition-transform ${showThemeDropdown ? 'rotate-180' : ''}`} />
            </button>

            {showThemeDropdown && (
              <>
                <div className="fixed inset-0 z-40" onClick={() => setShowThemeDropdown(false)} />
                <div className={`absolute top-full right-0 mt-2 w-80 rounded-2xl shadow-2xl border z-50 ${
                  darkMode ? 'bg-slate-800/95 border-slate-600/50 backdrop-blur-xl' : 'bg-white/95 border-gray-200 backdrop-blur-xl'
                }`}>
                  <div className={`p-4 border-b ${darkMode ? 'border-slate-700' : 'border-gray-200'}`}>
                    <div className="flex items-center justify-between">
                      <h3 className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                        Design Themes
                      </h3>
                      <button onClick={() => setShowThemeDropdown(false)} className={`p-2 rounded-lg transition-colors ${darkMode ? 'hover:bg-slate-700' : 'hover:bg-gray-100'}`}>
                        <FaTimes className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`} />
                      </button>
                    </div>
                  </div>

                  <div className="p-4 max-h-96 overflow-y-auto">
                    <div className="space-y-3">
                      {Object.entries(colorThemes).map(([key, theme]) => (
                        <div
                          key={key}
                          onClick={() => {
                            setSelectedTheme(key);
                            setShowThemeDropdown(false);
                          }}
                          className={`p-3 rounded-xl cursor-pointer transition-all duration-200 border-2 ${
                            selectedTheme === key
                              ? 'border-current shadow-lg'
                              : (darkMode ? 'border-slate-600/50 hover:border-slate-500' : 'border-gray-200 hover:border-gray-300')
                          }`}
                          style={selectedTheme === key ? { borderColor: theme.primary } : {}}
                        >
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className={`font-semibold ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                                {theme.name}
                              </h4>
                              <div className="flex space-x-1 mt-2">
                                {[theme.primary, theme.secondary, theme.accent, theme.success].map((color, index) => (
                                  <div key={index} className="w-6 h-6 rounded-full border border-white shadow-sm" style={{ backgroundColor: color }} />
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Enhanced Month Navigation */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0">
        {/* Subcategories Toggle */}
        <div className="flex justify-start">
          <label 
            className={`flex items-center space-x-3 text-sm font-semibold px-6 py-3 rounded-xl transition-all duration-300 transform hover:scale-[1.02] cursor-pointer ${
              darkMode 
                ? 'bg-slate-700/50 border border-slate-600/50 text-white hover:bg-slate-600/50' 
                : 'bg-white/80 border border-gray-200 text-gray-700 hover:bg-gray-50'
            } shadow-lg hover:shadow-xl backdrop-blur-sm`}
            style={{
              borderColor: showSubcategories ? currentTheme.primary : (darkMode ? '#475569' : '#e5e7eb'),
              backgroundColor: showSubcategories ? `${currentTheme.primary}20` : undefined
            }}
          >
            <input
              type="checkbox"
              checked={showSubcategories}
              onChange={(e) => setShowSubcategories(e.target.checked)}
              className="h-4 w-4 rounded transition-all duration-300 focus:ring-2"
              style={{ 
                accentColor: currentTheme.primary,
                outline: 'none'
              }}
            />
            <span className="flex items-center space-x-2">
              <span>🎨</span>
              <span>Show subcategories</span>
            </span>
          </label>
        </div>
        
        {/* Enhanced Month Selector */}
        <div 
          className={`flex items-center space-x-3 px-6 py-3 rounded-xl shadow-lg backdrop-blur-sm border ${
            darkMode 
              ? 'bg-slate-700/50 border-slate-600/50' 
              : 'bg-white/90 border-gray-200'
          }`}
          style={{
            background: darkMode 
              ? `linear-gradient(135deg, ${currentTheme.primary}15, ${currentTheme.secondary}10)` 
              : `linear-gradient(135deg, ${currentTheme.primary}08, ${currentTheme.accent}05)`,
            borderColor: `${currentTheme.primary}30`
          }}
        >
          {/* Today Button */}
          <button
            onClick={handleSetToToday}
            className={`px-4 py-2 rounded-lg font-semibold text-sm transition-all duration-300 transform hover:scale-105 active:scale-95 ${
              darkMode ? 'text-white' : 'text-gray-700'
            }`}
            style={{
              background: `linear-gradient(135deg, ${currentTheme.primary}, ${currentTheme.secondary})`,
              color: 'white',
              boxShadow: `0 4px 12px ${currentTheme.primary}30`
            }}
            onMouseEnter={(e) => {
              e.target.style.filter = 'brightness(1.1)';
              e.target.style.transform = 'scale(1.05) translateY(-1px)';
            }}
            onMouseLeave={(e) => {
              e.target.style.filter = 'brightness(1)';
              e.target.style.transform = 'scale(1) translateY(0)';
            }}
          >
            📅 Today
          </button>

          {/* Previous Month */}
          <button
            onClick={() => handleChangeMonth(-1)}
            className={`p-3 rounded-lg transition-all duration-300 transform hover:scale-110 active:scale-95 ${
              darkMode 
                ? 'text-gray-300 hover:text-white hover:bg-slate-600/50' 
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
            }`}
            style={{
              color: darkMode ? '#e5e7eb' : '#374151'
            }}
            onMouseEnter={(e) => {
              e.target.style.color = currentTheme.primary;
              e.target.style.backgroundColor = `${currentTheme.primary}15`;
            }}
            onMouseLeave={(e) => {
              e.target.style.color = darkMode ? '#e5e7eb' : '#374151';
              e.target.style.backgroundColor = 'transparent';
            }}
          >
            <FaArrowCircleLeft className="text-xl" />
          </button>

          {/* Month/Year Display */}
          <div 
            className={`px-6 py-2 rounded-lg font-bold text-lg min-w-[200px] text-center ${
              darkMode ? 'text-white' : 'text-gray-800'
            }`}
            style={{
              background: `linear-gradient(135deg, ${currentTheme.primary}20, ${currentTheme.accent}15)`,
              color: darkMode ? 'white' : currentTheme.primary,
              border: `2px solid ${currentTheme.primary}30`
            }}
          >
            {months.find(m => m.value === month)?.label} {year}
          </div>

          {/* Next Month */}
          <button
            onClick={() => handleChangeMonth(1)}
            className={`p-3 rounded-lg transition-all duration-300 transform hover:scale-110 active:scale-95 ${
              darkMode 
                ? 'text-gray-300 hover:text-white hover:bg-slate-600/50' 
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
            }`}
            style={{
              color: darkMode ? '#e5e7eb' : '#374151'
            }}
            onMouseEnter={(e) => {
              e.target.style.color = currentTheme.primary;
              e.target.style.backgroundColor = `${currentTheme.primary}15`;
            }}
            onMouseLeave={(e) => {
              e.target.style.color = darkMode ? '#e5e7eb' : '#374151';
              e.target.style.backgroundColor = 'transparent';
            }}
          >
            <FaArrowCircleRight className="text-xl" />
          </button>
        </div>
      </div>

      {/* Chart Container */}
      <div 
        className="relative w-full rounded-xl overflow-hidden backdrop-blur-sm"
        style={{ 
          minHeight: '450px',
          background: darkMode 
            ? `linear-gradient(135deg, ${currentTheme.primary}05, ${currentTheme.secondary}08)` 
            : `linear-gradient(135deg, ${currentTheme.primary}03, ${currentTheme.accent}05)`,
          border: `1px solid ${currentTheme.primary}20`
        }}
      >
        {sankeyChartLoading && (
          <div 
            className="absolute inset-0 flex flex-col items-center justify-center bg-opacity-95 backdrop-blur-md rounded-xl z-10"
            style={{
              background: darkMode 
                ? `linear-gradient(135deg, ${currentTheme.primary}10, ${currentTheme.secondary}15)` 
                : `linear-gradient(135deg, ${currentTheme.primary}05, ${currentTheme.accent}08)`
            }}
          >
            <PaymentLoader darkMode={darkMode} />
            <p 
              className="mt-6 text-lg font-semibold animate-pulse"
              style={{ color: currentTheme.primary }}
            >
              ⚡ Loading cash flow data...
            </p>
          </div>
        )}
        {sankeyChartError && (
          <div className="flex items-center justify-center h-[300px] p-8">
            <div 
              className="text-xl font-semibold text-center p-6 rounded-2xl shadow-xl border-2"
              style={{
                backgroundColor: darkMode ? '#7F1D1D20' : '#FEF2F2',
                borderColor: darkMode ? '#DC2626' : '#FECACA',
                color: darkMode ? '#FCA5A5' : '#DC2626'
              }}
            >
              ⚠️ {sankeyChartError}
            </div>
          </div>
        )}
        {/* APT-110 fix for 500 error */}
        {!sankeyChartLoading && !sankeyChartError && (
          sankeyChartData?.nodes?.length === 0 || sankeyChartData?.links?.length === 0 ? (
            <div className="flex items-center justify-center h-[300px] p-8">
              <div
                className="text-xl font-semibold text-center p-6 rounded-2xl shadow-xl border-2"
                style={{
                  backgroundColor: darkMode ? '#1E293B' : '#F3F4F6',
                  borderColor: darkMode ? '#374151' : '#D1D5DB',
                  color: darkMode ? '#94A3B8' : '#4B5563'
                }}
              >
                Not enough data to render cashflow
              </div>
            </div>
          ) : null
        )}
        <div
          ref={containerRef}
          className="mx-auto w-full h-full p-4"
        />
      </div>
    </div>
  );
};

export default Cashflow;