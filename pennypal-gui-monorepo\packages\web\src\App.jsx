import React, { useState, useEffect } from 'react';
import { Route, Routes, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { library } from '@fortawesome/fontawesome-svg-core';
import { faArrowUp, faArrowDown, faEquals } from '@fortawesome/free-solid-svg-icons';

//import { store } from '../../logic/redux/store';

// import Sidebar from './components/Sidebar/Sidebar';
import Header from './components/Header/Header';
import Signup from './components/Header/Signup';
import Reports from './components/Reports/Reports';
import TransactionPage from './components/Transactions/TransactionPage';
import TransactionPage1 from './components/Transactions/TransactionPage1';
import RecurringTransactionsView from './components/v1/SideNav/RecurringTransactionsView';
import AccountsDashboard from './components/Accounts/AccountsDashboard';
import Budget5 from './components/Budget/Budget';
import Budget6 from './components/Budget/Budget6';
import BudgetRulePage from './components/Budget/BudgetRulePage';
import SetPassword from './components/auth/setPassword';
import ForgotPassword from './components/auth/forgotPassword';


import Home from './components/v1/SideNav/Home';

import './App.css';

library.add(faArrowUp, faArrowDown, faEquals);

const App = () => {
  
  
  const location = useLocation();

  const [isSidebarMinimized, setIsSidebarMinimized] = useState(false);
  const [isToggledByIcon, setIsToggledByIcon] = useState(false);

  console.log('App Starting --> ');

  let isUserAuthenticated = useSelector((state) => state.auth.isUserAuthenticated);

  // Re-render when forceRerender flag changes
  useEffect(() => {
    // You can add any additional logic you need here
    console.log('App component re-rendered!');
    

    
    
  }, [isUserAuthenticated]);
  console.log("asdasdasdadad");
  console.log('User Authenticated --> ' + isUserAuthenticated);
   //isUserAuthenticated = true;
  const toggleSidebar = () => {
    setIsSidebarMinimized((prevState) => !prevState);
    // setIsToggledByIcon(prevState => !prevState);
  };

  return (
    <>
      {isUserAuthenticated ? (
        <div className={`app ${isSidebarMinimized ? 'sidebar-minimized' : ''}`}>
          <Header
            toggleSidebar={toggleSidebar}
            isSidebarMinimized={isSidebarMinimized}
            setIsToggledByIcon={setIsToggledByIcon}
          />
          <Sidebar
            isMinimized={isSidebarMinimized}
            toggleSidebar={toggleSidebar}
            isToggledByIcon={isToggledByIcon}
          />
          <div className="main-content">
            <Routes>
              {/* <Route path="/dashboard/crm" element={<> <StatsCards /> <Calendar /> <TransactionRevenue /> </>} /> */}
              <Route path="/dashboard/transactions" element={<TransactionPage1 />} />
              <Route path='/dashboard/recurring-transactions' element={<RecurringTransactionsView />} />
              <Route path="/dashboard/accounts" element={<AccountsDashboard />} />
              <Route path="/dashboard/investment" element={<Investment />} />
              <Route path ="/dashboard/setting" element={<Settings/>}/>
              <Route path="/goals/:goalId" element={<Goals />} />
              {/* Default Dashboard */}
              <Route path="/dashboard/reports/*" element={<Reports />} />
              <Route path="/dashboard/investment" element={<Investment />} />
              {/* Automotive Dashboard */}
              {/* Signup */}
              <Route path="/signup" element={<Signup />} />
              <Route path="/dashboard/budget" element={<Budget6 />} />
              <Route path="/dashboard/budget-rules" element={<BudgetRulePage />} />
              {/* <Route path="/" element={<Default />} /> */}
              <Route path="/set-password" element={<SetPassword />} />
              <Route path="/forgot-password" element={<ForgotPassword />} />
            </Routes>
          </div>
          {/* {location.pathname === '/dashboard/crm' && (
            <div className="right-panel">
              <ReadMore />
              <Categories />
              <UserMessage />
            </div>
          )} */}
        </div>
      ) : (
        // <SignIn />
        // <GridDemo />
        //<VerticalLayout />
        <Home />
      )}
    </>
  );
};

export default App;
