// import { ofType } from 'redux-observable';
// import { mergeMap, map, catchError, switchMap, tap, mergeAll, debounceTime, retry, timeout } from 'rxjs/operators';
// import { from, of, defer, EMPTY } from 'rxjs';
// import { axiosInstance } from '../api/axiosConfig';
// import {
//   setBudgetData,
//   setError,
//   updateBudget,
//   addBudget as addBudgetAction,
//   setLoading,
//   prepareBudgetData,
//   deleteSubcategory,
//   clearError,
//   setNetworkError,
//   updateBudgetOptimistic,
//   revertBudgetOptimistic
// } from '../redux/budgetSlice';
// import { combineEpics } from 'redux-observable';

// // Enhanced logging function with performance tracking
// const logEpicAction = (epicName, stage, data, startTime = null) => {
//   const timestamp = new Date().toISOString();
//   const duration = startTime ? Date.now() - startTime : null;
  
//   console.group(`🔄 ${epicName} - ${stage} (${timestamp})${duration ? ` [${duration}ms]` : ''}`);
//   if (data) {
//     if (data instanceof Error) {
//       console.error('Error:', data.message);
//       console.error('Stack:', data.stack);
//       console.error('Response:', data.response?.data);
//     } else {
//       console.log('Data:', data);
//     }
//   }
//   console.groupEnd();
// };

// // Enhanced error handling helper
// const handleApiError = (error, epicName) => {
//   logEpicAction(epicName, 'Error Handler', error);
  
//   if (error.code === 'ECONNABORTED') {
//     return of(setNetworkError('Request timeout. Please check your connection.'));
//   }
  
//   if (error.response?.status === 401) {
//     return of(setError('Authentication expired. Please login again.'));
//   }
  
//   if (error.response?.status === 403) {
//     return of(setError('Access denied. You don\'t have permission for this action.'));
//   }
  
//   if (error.response?.status === 404) {
//     return of(setError('Resource not found.'));
//   }
  
//   if (error.response?.status >= 500) {
//     return of(setError('Server error. Please try again later.'));
//   }
  
//   return of(setError(error.response?.data?.message || error.message || 'An unexpected error occurred'));
// };

// // Helper function for API calls with enhanced error handling and retry logic
// const makeApiRequest = (endpoint, actionType, logPrefix = '', retryCount = 2) => {
//   const startTime = Date.now();
//   console.log(`${logPrefix} About to dispatch ${actionType}`);
  
//   return from(axiosInstance.get(endpoint)).pipe(
//     timeout(10000), // 10 second timeout
//     retry(retryCount),
//     tap(response => logEpicAction('API Request', `${logPrefix} Success`, response.data, startTime)),
//     map(response => ({
//       type: actionType,
//       payload: response.data
//     })),
//     catchError(error => handleApiError(error, `${logPrefix} API Request`))
//   );
// };

// // Enhanced fetch budget data epic with better state management
// export const fetchBudgetDataEpic = (action$, state$) => action$.pipe(
//   ofType('budget/changeMonth', 'budget/setToToday', 'budget/changeYear', 'budget/refreshBudgetData'),
//   debounceTime(300), // Prevent rapid successive calls
//   tap(action => logEpicAction('fetchBudgetDataEpic', 'Initial Action Received', action)),
//   switchMap(() => { // Use switchMap to cancel previous requests
//     const startTime = Date.now();
//     console.group('🔄 Budget Data Fetch Sequence');
//     console.log('Starting fetch sequence...');
    
//     return of({ type: 'budget/setLoading', payload: true }).pipe(
//       mergeMap(() => {
//         const { categories, subcategories, currentMonth, currentYear } = state$.value.budget;
        
//         console.group('🔍 Checking State');
//         console.log('Current categories:', categories?.length || 0);
//         console.log('Current subcategories:', subcategories?.length || 0);
//         console.log('Current month/year:', currentMonth, currentYear);
//         console.groupEnd();

//         const actions = [];
//         const cacheExpiry = 5 * 60 * 1000; // 5 minutes cache
//         const lastFetch = state$.value.budget.lastCategoriesFetch;
//         const shouldRefreshCategories = !lastFetch || (Date.now() - lastFetch) > cacheExpiry;

//         // Enhanced categories logic with caching
//         if (!categories?.length || shouldRefreshCategories) {
//           console.log('🔄 Fetching categories (cache miss or expired)');
//           actions.push(makeApiRequest(
//             '/pennypal/api/v1/category/all',
//             'budget/setCategoriesData',
//             'Categories'
//           ));
//         } else {
//           console.log('✅ Using cached categories data');
//           actions.push(of({ 
//             type: 'budget/setCategoriesData', 
//             payload: categories 
//           }));
//         }

//         // Enhanced subcategories logic with caching
//         if (!subcategories?.length || shouldRefreshCategories) {
//           console.log('🔄 Fetching subcategories (cache miss or expired)');
//           actions.push(makeApiRequest(
//             '/pennypal/api/v1/subCategory/all',
//             'budget/setSubcategoriesData',
//             'Subcategories'
//           ));
//         } else {
//           console.log('✅ Using cached subcategories data');
//           actions.push(of({ 
//             type: 'budget/setSubcategoriesData', 
//             payload: subcategories 
//           }));
//         }

//         return from(actions).pipe(
//           mergeAll(),
//           tap(action => console.log('🎯 Dispatching action:', action.type))
//         );
//       }),

//       // Enhanced budget data fetching with better error handling
//       mergeMap(() => defer(() => {
//         const { currentMonth, currentYear } = state$.value.budget;
//         const monthParam = currentMonth + 1; // Convert to 1-12 range
        
//         logEpicAction('fetchBudgetDataEpic', 'Budget Fetch Parameters', { 
//           currentMonth, 
//           currentYear, 
//           monthParam 
//         });

//         return from(axiosInstance.get(`/pennypal/api/v1/budget/user/1/month/${monthParam}`)).pipe(
//           timeout(15000), // 15 second timeout for budget data
//           retry(1), // Single retry for budget data
//           tap(response => logEpicAction('fetchBudgetDataEpic', 'Budget API Response', {
//             status: response.status,
//             dataLength: response.data?.length || 0
//           }, startTime)),
//           map(response => {
//             if (!response.data) {
//               throw new Error('No data received from budget API');
//             }
            
//             const processedData = prepareBudgetData(response.data);
//             logEpicAction('fetchBudgetDataEpic', 'Processed Budget Data', {
//               originalLength: response.data.length,
//               processedLength: processedData.length
//             });
            
//             return setBudgetData(processedData);
//           }),
//           tap(() => {
//             console.log('✅ Complete fetch sequence finished successfully');
//             console.groupEnd();
//           })
//         );
//       })),

//       catchError(error => {
//         console.error('❌ Error in fetch sequence:', error);
//         console.groupEnd();
//         return handleApiError(error, 'fetchBudgetDataEpic');
//       })
//     );
//   })
// );

// // Enhanced update budget epic with optimistic updates and rollback
// export const updateBudgetEpic = (action$, state$) => action$.pipe(
//   ofType('budget/saveBudget'),
//   tap(action => logEpicAction('updateBudgetEpic', 'Action Received', action.payload)),
//   switchMap(action => {
//     const { budgetId, categoryId, subcategoryId, newBudget, originalBudget } = action.payload;
//     const { currentMonth, currentYear } = state$.value.budget;
//     const startTime = Date.now();

//     // Optimistic update first
//     const optimisticAction = updateBudgetOptimistic({ 
//       categoryId, 
//       subcategoryId, 
//       newBudget,
//       originalBudget 
//     });

//     logEpicAction('updateBudgetEpic', 'Processing Update', {
//       budgetId,
//       categoryId,
//       subcategoryId,
//       newBudget,
//       currentMonth,
//       currentYear
//     });

//     const formattedDate = new Date(currentYear, currentMonth, 1).toLocaleDateString("en-CA");

//     // Find the subcategory to get current values
//     const category = state$.value.budget.budgetData.find(cat => cat.id === categoryId);
//     if (!category) {
//       return of(setError('Category not found'));
//     }

//     const subcategory = category.category.subCategories.find(
//       sub => sub.id === subcategoryId || sub.compositeKey === subcategoryId
//     );
//     if (!subcategory) {
//       return of(setError('Subcategory not found'));
//     }

//     let validBudgetId = subcategory.budgetId || subcategory.id;
//     if (!validBudgetId || validBudgetId === 0) {
//       validBudgetId = subcategory.sub_category_id;
//     }
    
//     const requestBody = {
//       id: validBudgetId,
//       allocated: newBudget,
//       actual: subcategory.spent || 0,
//       isDynamic: subcategory.isDynamic || false,
//       dynamicAllocated: subcategory.dynamicAllocated || 0,
//       date: formattedDate,
//       customSubCategory: subcategory.customSubCategory || subcategory.subCategory
//     };

//     logEpicAction('updateBudgetEpic', 'Request Body', requestBody);

//     return from([optimisticAction]).pipe(
//       mergeMap(() => 
//         from(axiosInstance.put(`/pennypal/api/v1/budget/${validBudgetId}`, requestBody)).pipe(
//           timeout(10000),
//           retry(1),
//           tap(response => logEpicAction('updateBudgetEpic', 'API Response', response.data, startTime)),
//           map(response => {
//             if (response.status !== 200) {
//               throw new Error(`HTTP Error: ${response.status}`);
//             }
//             logEpicAction('updateBudgetEpic', 'Update Success', { categoryId, subcategoryId, newBudget });
//             return clearError(); // Clear any previous errors
//           }),
//           catchError(error => {
//             // Revert optimistic update on error
//             const revertAction = revertBudgetOptimistic({ 
//               categoryId, 
//               subcategoryId, 
//               originalBudget 
//             });
            
//             logEpicAction('updateBudgetEpic', 'Error - Reverting', error);
//             return from([
//               revertAction,
//               ...handleApiError(error, 'updateBudgetEpic')
//             ]);
//           })
//         )
//       )
//     );
//   })
// );

// // Enhanced add budget epic with proper custom subcategory handling
// // Corrected addBudgetEpic
// export const addBudgetEpic = (action$, state$) => action$.pipe(
//   ofType('budget/addBudgetItem'),
//   tap(action => logEpicAction('addBudgetEpic', 'Action Received', action.payload)),
//   switchMap(action => {
//     const {
//       categoryId,
//       subCategoryId,
//       customSubCategory,
//       allocated,
//       isRollover = false,
//       isExcluded = false,
//       icon = 'FaMiscellaneous'
//     } = action.payload;

//     // Enhanced validation
//     if (!categoryId) {
//       return of(setError('Category ID is required'));
//     }

//     if (!customSubCategory?.trim()) {
//       return of(setError('Subcategory name is required'));
//     }

//     if (allocated < 0) {
//       return of(setError('Budget amount cannot be negative'));
//     }

//     const { currentMonth, currentYear } = state$.value.budget;
//     const dateStr = new Date(currentYear, currentMonth, 1).toLocaleDateString("en-CA");
//     const startTime = Date.now();

//     // Check if this is a new custom subcategory (subCategoryId is null or not provided)
//     const isNewCustomSubcategory = !subCategoryId;

//     // Match the backend expected format based on your Postman example
//     const requestBody = {
//       userId: 1, // You might want to get this from auth state
//       categoryId,
//       subCategoryId: subCategoryId || null,
//       customSubCategoryName: customSubCategory.trim(), // Changed from customSubCategory to customSubCategoryName
//       iconKey: icon, // Changed from icon to iconKey
//       allocated,
//       date: dateStr,
//       isRollover,
//       isExcluded
//     };

//     logEpicAction('addBudgetEpic', 'Request Body', requestBody);

//     return from(axiosInstance.post('/pennypal/api/v1/budget/add', requestBody)).pipe(
//       timeout(10000),
//       retry(1),
//       tap(response => logEpicAction('addBudgetEpic', 'API Response', response.data, startTime)),
//       mergeMap(response => {
//         if (response.status !== 200) {
//           throw new Error(`HTTP Error: ${response.status}`);
//         }

//         logEpicAction('addBudgetEpic', 'Add Success', response.data);

//         // Create actions to dispatch
//         const actions = [];

//         // Add the budget to the store with the correct structure
//         const budgetData = {
//           ...response.data,
//           categoryId,
//           customSubCategory: customSubCategory.trim(),
//           icon: icon,
//           iconKey: icon
//         };
        
//         actions.push(addBudgetAction(budgetData));

//         // If this was a new custom subcategory, refresh subcategories list
//         if (isNewCustomSubcategory) {
//           actions.push({ type: 'budget/fetchSubcategories' });
//         }

//         // Clear any errors
//         actions.push(clearError());

//         return from(actions);
//       }),
//       catchError(error => handleApiError(error, 'addBudgetEpic'))
//     );
//   })
// );

// // Enhanced delete subcategory epic with confirmation
// export const deleteSubcategoryEpic = (action$) => action$.pipe(
//   ofType('budget/deleteSubcategoryBudget'),
//   tap(action => logEpicAction('deleteSubcategoryEpic', 'Action Received', action.payload)),
//   switchMap(action => {
//     const { budgetId, categoryId, subcategoryId } = action.payload;
//     const startTime = Date.now();
    
//     if (!budgetId) {
//       return of(setError('Budget ID is required for deletion'));
//     }
    
//     return from(axiosInstance.delete(`/pennypal/api/v1/budget/${budgetId}`)).pipe(
//       timeout(10000),
//       retry(1),
//       tap(() => logEpicAction('deleteSubcategoryEpic', 'Delete Success', { budgetId }, startTime)),
//       map(() => deleteSubcategory({ categoryId, subcategoryId })),
//       catchError(error => handleApiError(error, 'deleteSubcategoryEpic'))
//     );
//   })
// );

// // Epic to fetch all categories
// export const fetchCategoriesEpic = (action$) => action$.pipe(
//   ofType('budget/fetchCategories'),
//   mergeMap(() => makeApiRequest(
//     '/pennypal/api/v1/category/all',
//     'budget/setCategoriesData',
//     'Categories'
//   ))
// );

// // Epic to fetch all subcategories
// export const fetchSubcategoriesEpic = (action$) => action$.pipe(
//   ofType('budget/fetchSubcategories'),
//   mergeMap(() => makeApiRequest(
//     '/pennypal/api/v1/subCategory/all',
//     'budget/setSubcategoriesData',
//     'Subcategories'
//   ))
// );

// // New epic for bulk operations
// export const bulkUpdateBudgetEpic = (action$, state$) => action$.pipe(
//   ofType('budget/bulkUpdateBudgets'),
//   tap(action => logEpicAction('bulkUpdateBudgetEpic', 'Action Received', action.payload)),
//   switchMap(action => {
//     const { updates } = action.payload;
//     const startTime = Date.now();
    
//     if (!Array.isArray(updates) || updates.length === 0) {
//       return of(setError('No updates provided for bulk operation'));
//     }
    
//     const requests = updates.map(update => 
//       axiosInstance.put(`/pennypal/api/v1/budget/${update.budgetId}`, update.data)
//     );
    
//     return from(Promise.all(requests)).pipe(
//       timeout(30000), // Longer timeout for bulk operations
//       tap(() => logEpicAction('bulkUpdateBudgetEpic', 'Bulk Update Success', { count: updates.length }, startTime)),
//       map(() => ({
//         type: 'budget/refreshBudgetData' // Refresh data after bulk update
//       })),
//       catchError(error => handleApiError(error, 'bulkUpdateBudgetEpic'))
//     );
//   })
// );

// // Export all epics
// export const rootBudgetEpic = combineEpics(
//   fetchBudgetDataEpic,
//   updateBudgetEpic,
//   addBudgetEpic,
//   deleteSubcategoryEpic,
//   bulkUpdateBudgetEpic,fetchCategoriesEpic,
//     fetchSubcategoriesEpic,

// );

// export default rootBudgetEpic;
import { ofType } from "redux-observable";
import { mergeMap, map, catchError, switchMap, tap, mergeAll, debounceTime, retry, timeout } from "rxjs/operators";
import { from, of } from "rxjs";
import { axiosInstance } from '../api/axiosConfig';
import { getCurrentUserId } from '../../web/src/utils/AuthUtil';
import {
  setBudgetData,
  setError,
  addBudget,
  updateBudget,
  deleteSubcategory,
  setLoading,
  clearError,
} from "../redux/budgetSlice";
import { combineEpics } from "redux-observable";

const logEpicAction = (epicName, stage, data) => {
  const timestamp = new Date().toISOString();
  console.group(`🔄 ${epicName} - ${stage} (${timestamp})`);
  if (data instanceof Error) {
    console.error("Error:", data.message, data.stack, data.response?.data);
  } else {
    console.log("Data:", data);
  }
  console.groupEnd();
};

const handleApiError = (error, epicName) => {
  logEpicAction(epicName, "Error Handler", error);
  if (error.code === "ECONNABORTED") return setError("Request timeout. Please check your connection.");
  if (error.response?.status === 401) return setError("Authentication expired. Please login again.");
  if (error.response?.status === 403) return setError("Access denied.");
  if (error.response?.status === 404) return setError("Resource not found.");
  if (error.response?.status >= 500) return setError("Server error. Please try again later.");
  return setError(error.response?.data?.message || "An unexpected error occurred");
};

export const fetchBudgetDataEpic = (action$, state$) =>
  action$.pipe(
    ofType("budget/changeMonth", "budget/setToToday"),
    tap((action) => logEpicAction("fetchBudgetDataEpic", "Action Received", action)),
    switchMap(() => {
      const currentUserId = getCurrentUserId();
      if (!currentUserId) return of(setError("User not authenticated. Please login again."));

      return of(setLoading(true)).pipe(
        mergeMap(() => {
          const { currentMonth, currentYear } = state$.value.budget;
          // const cache = state$.value?.cache;
          const monthParam = currentMonth + 1;

          // Check if we can use cached data for current month
          // if (cache?.budgetDataLoaded && cache?.budgetData?.length > 0 &&
          //     cache?.budgetDataParams?.userId === currentUserId &&
          //     currentMonth === new Date().getMonth() &&
          //     currentYear === new Date().getFullYear()) {
          //   console.log('✅ Using cached budget data for current month');
          //   return of(setBudgetData(cache.budgetData));
          // }

          return from(
            axiosInstance.get(
              `/pennypal/api/v1/budget/user/${currentUserId}/year/${currentYear}/month/${monthParam}`
            )
          ).pipe(
            tap(() => {
              // APT-123/124 fix for slow loading
              // Pre-fetch previous and next month's data and store in cache
              // Fire and forget: previous month
              const prevMonthDate = new Date(currentYear, currentMonth - 1);
              const prevMonth = prevMonthDate.getMonth() + 1;
              const prevYear = prevMonthDate.getFullYear();
              axiosInstance.get(
                `/pennypal/api/v1/budget/user/${currentUserId}/year/${prevYear}/month/${prevMonth}`
              ).catch(() => {}); // Ignore error

              // Fire and forget: next month
              const nextMonthDate = new Date(currentYear, currentMonth + 1);
              const nextMonth = nextMonthDate.getMonth() + 1;
              const nextYear = nextMonthDate.getFullYear();
              axiosInstance.get(
                `/pennypal/api/v1/budget/user/${currentUserId}/year/${nextYear}/month/${nextMonth}`
              ).catch(() => {}); // Ignore error
            }),
            // End of APT-123/124 fix
            map((response) => setBudgetData(response.data || [])),
            catchError((error) => of(handleApiError(error, "fetchBudgetDataEpic")))
          );
        })
      );
    })
  );

export const updateBudgetEpic = (action$, state$) =>
  action$.pipe(
    ofType("budget/saveBudget"),
    tap((action) => logEpicAction("updateBudgetEpic", "Action Received", action.payload)),
    switchMap((action) => {
      const { budgetId, categoryId, subcategoryId, newBudget } = action.payload;
      const currentUserId = getCurrentUserId();
      if (!currentUserId) return of(setError("User not authenticated."));
      if (!categoryId || !subcategoryId)
        return of(setError("Budget, category, and subcategory IDs are required."));
      if (newBudget < 0) return of(setError("Budget amount cannot be negative."));

      const category = state$.value.budget.budgetData.find(
        (cat) => cat.categoryId === categoryId
      );
      if (!category) return of(setError("Category not found."));
      const subcategory = category.subcategories.find(
        (sub) => sub.compositeKey === subcategoryId
      );
      if (!subcategory) return of(setError("Subcategory not found."));

      const requestBody = {
        id: budgetId,
        userId: currentUserId,
        categoryId,
        subcategoryId: subcategory.subcategoryId || null,
        customSubCategoryId: subcategory.customSubCategoryId || null,
        customSubCategoryName: subcategory.customSubCategoryName || null,
        allocated: newBudget,
        actual: subcategory.actual || 0,
        date: new Date(state$.value.budget.currentYear, state$.value.budget.currentMonth, 1).toLocaleDateString("en-CA"),
        icon: subcategory.icon || "FaQuestionCircle",
        isRollover: action.payload.isRollover || false,
      };

      return from(axiosInstance.put(`/pennypal/api/v1/budget/${budgetId}`, requestBody)).pipe(
        map(() => updateBudget({ categoryId, subcategoryId, newBudget, isRollover: action.payload.isRollover })),
        catchError((error) => of(handleApiError(error, "updateBudgetEpic")))
      );
    })
  );

export const addBudgetEpic = (action$, state$) =>
  action$.pipe(
    ofType("budget/addBudgetItem"),
    tap((action) => logEpicAction("addBudgetEpic", "Action Received", action.payload)),
    switchMap((action) => {
      const {
        categoryId,
        subCategoryId,
        customSubCategory,
        customSubCategoryId,
        allocated,
        icon,
        isRollover,
      } = action.payload;
      const currentUserId = getCurrentUserId();
      if (!currentUserId) return of(setError("User not authenticated."));
      if (!categoryId) return of(setError("Category ID is required."));
      if (!subCategoryId && !customSubCategory && !customSubCategoryId)
        return of(setError("Subcategory information is required."));
      if (allocated < 0) return of(setError("Budget amount cannot be negative."));

      const requestBody = {
        userId: currentUserId,
        categoryId,
        subcategoryId: subCategoryId || null,
        customSubCategoryName: customSubCategory || null,
        customSubCategoryId: customSubCategoryId || null,
        icon: icon || "FaQuestionCircle",
        allocated: allocated || 0,
        date: new Date(state$.value.budget.currentYear, state$.value.budget.currentMonth, 1).toLocaleDateString("en-CA"),
        isRollover: isRollover || false,
      };

      return from(axiosInstance.post("/pennypal/api/v1/budget/add", requestBody)).pipe(
        map((response) =>
          addBudget({
            ...response.data,
            // APT-126 fix for category/subcategory name not being displayed properly - to update redux state
            categoryId,
            categoryName: response.data.categoryName || "N/A",
            subCategoryId,
            subcategoryName: response.data.subcategoryName || response.data.customSubCategoryName || customSubCategory || "N/A",
            customSubCategory,
            customSubCategoryId,
            icon,
            rollover: isRollover,
          })
        ),
        catchError((error) => of(handleApiError(error, "addBudgetEpic")))
      );
    })
  );

export const deleteSubcategoryEpic = (action$) =>
  action$.pipe(
    ofType("budget/deleteSubcategoryBudget"),
    tap((action) => logEpicAction("deleteSubcategoryEpic", "Action Received", action.payload)),
    switchMap((action) => {
      const { budgetId, categoryId, subcategoryId } = action.payload;
      const currentUserId = getCurrentUserId();
      if (!currentUserId) return of(setError("User not authenticated."));
      if (!budgetId || !categoryId || !subcategoryId)
        return of(setError("Budget, category, and subcategory IDs are required."));

      return from(axiosInstance.delete(`/pennypal/api/v1/budget/${budgetId}`)).pipe(
        map(() => deleteSubcategory({ categoryId, subcategoryId })),
        catchError((error) => of(handleApiError(error, "deleteSubcategoryEpic")))
      );
    })
  );

const makeApiRequest = (endpoint, actionType, logPrefix = '', retryCount = 2) => {
  const startTime = Date.now();
  console.log(`${logPrefix} About to dispatch ${actionType}`);
 
  return from(axiosInstance.get(endpoint)).pipe(
    timeout(10000), // 10 second timeout
    retry(retryCount),
    tap(response => logEpicAction('API Request', `${logPrefix} Success`, response.data, startTime)),
    map(response => ({
      type: actionType,
      payload: response.data
    })),
    catchError(error => of(handleApiError(error, `${logPrefix} API Request`)))
  );
};

// Epic to fetch all categories
export const fetchCategoriesEpic = (action$, state$) => action$.pipe(
  ofType('budget/fetchCategories'),
  mergeMap(() => {
    const cache = state$.value.cache;

    // Check if data is available in cache first
    if (cache?.categoriesLoaded && cache?.categories?.length > 0) {
      console.log('✅ Using cached categories data');
      return of({
        type: 'budget/setCategoriesData',
        payload: cache.categories
      });
    }

    return makeApiRequest(
      '/pennypal/api/v1/category/all',
      'budget/setCategoriesData',
      'Categories'
    );
  })
);

// Epic to fetch all subcategories
export const fetchSubcategoriesEpic = (action$, state$) => action$.pipe(
  ofType('budget/fetchSubcategories'),
  mergeMap(() => {
    const cache = state$.value.cache;

    // Check if data is available in cache first
    if (cache?.subcategoriesLoaded && cache?.subcategories?.length > 0) {
      console.log('✅ Using cached subcategories data');
      return of({
        type: 'budget/setSubcategoriesData',
        payload: cache.subcategories
      });
    }

    return makeApiRequest(
      '/pennypal/api/v1/subCategory/all',
      'budget/setSubcategoriesData',
      'Subcategories'
    );
  })
);

export const rootBudgetEpic = combineEpics(
  fetchBudgetDataEpic,
  updateBudgetEpic,
  addBudgetEpic,
  deleteSubcategoryEpic,
  fetchCategoriesEpic,
  fetchSubcategoriesEpic
);

export default rootBudgetEpic;