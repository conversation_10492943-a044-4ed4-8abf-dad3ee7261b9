import React, { useState, useEffect } from 'react';
import { UilListUl, UilBell, UilUsersAlt, UilCreditCard, UilFileAlt, UilUser } from '@iconscout/react-unicons';
import { useSearchParams } from 'react-router-dom';

// import BudgetRules from './BudgetRules'; // adjust the path
// import NotificationRules from './NotificationRules'; // adjust the path
import BudgetRulePage from '../../../Budget/BudgetRulePage';
import NotificationSettingsModal from '../Settings/NotificationSettingsModal';
import FamilySection from '../../../Settings/FamilySection';
import StripeDashboard from '../../../Dashboard/StripeDashboard'; // Adjust the path to your StripeDashboard component
import InvoicesTransactions from '../../../Dashboard/InvoicesTransactions';
import { themeClasses } from '../../../../utils/tailwindUtils'; // Adjust path as needed
import Profile from '../../../Profile/Profile';
const SettingsPage = ({ darkMode }) => {
  const [searchParams] = useSearchParams();
  // Set initial tab based on URL parameter, default to 'budget' if not specified
  const initialTab = searchParams.get('tab') === 'notifications' ? 'notification' :
                    searchParams.get('tab') === 'profile' ? 'profile' :
                    searchParams.get('tab') === 'family' ? 'family' :
                    searchParams.get('tab') === 'subscription' ? 'subscription' :
                    searchParams.get('tab') === 'invoices' ? 'invoices' : 'budget';
  const [activeTab, setActiveTab] = useState(initialTab);

  useEffect(() => {
    // Update active tab based on URL changes (e.g., back/forward navigation)
    const tabFromURL = searchParams.get('tab');
    if (tabFromURL === 'notifications') {
      setActiveTab('notification');
    }else if (tabFromURL === 'profile') {
    setActiveTab('profile');
  } else if (tabFromURL === 'family') {
      setActiveTab('family');
    } else if (tabFromURL === 'subscription') {
      setActiveTab('subscription');
    } else if (tabFromURL === 'invoices') {
      setActiveTab('invoices');
    } else {
      setActiveTab('budget');
    }
  }, [searchParams]);

  return (
    <div className={`p-6 min-h-screen transition-colors duration-200 ${themeClasses.pageContainer(darkMode)}`}>
      <div className={`flex mb-4 border-b ${themeClasses.tabContainer(darkMode)}`}>
        {/* Budget Rules Tab */}
        <button
          className={`flex items-center space-x-2 px-4 py-2 rounded-t ${themeClasses.tabButton(darkMode)} ${
            activeTab === 'budget' ? themeClasses.tabActive(darkMode) : themeClasses.tabInactive(darkMode)
          }`}
          onClick={() => setActiveTab('budget')}
        >
          <UilListUl />
          <span>Budget Rules</span>
        </button>

        {/* Notification Rules Tab */}
        <button
          className={`flex items-center space-x-2 px-4 py-2 rounded-t ${themeClasses.tabButton(darkMode)} ${
            activeTab === 'notification' ? themeClasses.tabActive(darkMode) : themeClasses.tabInactive(darkMode)
          }`}
          onClick={() => setActiveTab('notification')}
        >
          <UilBell />
          <span>Notification Rules</span>
        </button>
<button
  className={`flex items-center space-x-2 px-4 py-2 rounded-t ${themeClasses.tabButton(darkMode)} ${
    activeTab === 'profile' ? themeClasses.tabActive(darkMode) : themeClasses.tabInactive(darkMode)
  }`}
  onClick={() => setActiveTab('profile')}
>
  <UilUser />
  <span>Profile</span>
</button>
        {/* Family Section Tab */}
        <button
          className={`flex items-center space-x-2 px-4 py-2 rounded-t ${themeClasses.tabButton(darkMode)} ${
            activeTab === 'family' ? themeClasses.tabActive(darkMode) : themeClasses.tabInactive(darkMode)
          }`}
          onClick={() => setActiveTab('family')}
        >
          <UilUsersAlt />
          <span>Family</span>
        </button>

        {/* Subscription Tab */}
        <button
          className={`flex items-center space-x-2 px-4 py-2 rounded-t ${themeClasses.tabButton(darkMode)} ${
            activeTab === 'subscription' ? themeClasses.tabActive(darkMode) : themeClasses.tabInactive(darkMode)
          }`}
          onClick={() => setActiveTab('subscription')}
        >
          <UilCreditCard />
          <span>Subscription</span>
        </button>

        {/* Invoices Transactions Tab */}
        <button
          className={`flex items-center space-x-2 px-4 py-2 rounded-t ${themeClasses.tabButton(darkMode)} ${
            activeTab === 'invoices' ? themeClasses.tabActive(darkMode) : themeClasses.tabInactive(darkMode)
          }`}
          onClick={() => setActiveTab('invoices')}
        >
          <UilFileAlt />
          <span>Invoices Transactions</span>
        </button>
      </div>

      <div className="mt-4">
        {activeTab === 'budget' && <BudgetRulePage darkMode={darkMode} />}
        {activeTab === 'notification' && (
          <NotificationSettingsModal isOpen={true} isPage={true} darkMode={darkMode} onClose={() => {}} />
        )}
        {activeTab === 'profile' && <Profile darkMode={darkMode} />}
        {activeTab === 'family' && <FamilySection darkMode={darkMode} />}
        {activeTab === 'subscription' && <StripeDashboard darkMode={darkMode} />}
        {activeTab === 'invoices' && <InvoicesTransactions darkMode={darkMode} />}
      </div>
    </div>
  );
};

export default SettingsPage;
