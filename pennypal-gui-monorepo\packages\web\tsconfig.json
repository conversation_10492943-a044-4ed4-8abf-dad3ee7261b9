{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "typeRoots": ["./node_modules/@types", "./src/types"]}, "include": ["src", "tests/setupTests.js", "src/components/v1/components/transaction/ReceiptViewTransactionHistoryTabView.jsx"], "references": [{"path": "./tsconfig.node.json"}]}