import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { 
  <PERSON><PERSON><PERSON>, BarChart, AreaChart, 
  Line, Bar, Area, 
  XAxis, YAxis, CartesianGrid, 
  Tooltip, ResponsiveContainer, LabelList 
} from 'recharts';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faChartBar, faChartLine, faChartArea, faExclamationTriangle, faSync
} from '@fortawesome/free-solid-svg-icons';

import { 
  fetchAccountData,
  setChartView,
  setChartType,
  setTimePeriod
} from '@pp-logic/redux/accountChartSlice';

// Import the event logger
import { logEvent } from '@pp-web/utils/EventLogger'; // Adjust path as needed

// Generic mock data for when user has no accounts
const getMockDataForAccountType = (accountType) => {
  // Single generic mock data template for all account types
  const genericMockData = {
    baseValue: 5000,
    variance: 500,
    trend: 0.10,
    data: [
      { name: 'Jan \'24', balance: 4500 },
      { name: 'Feb \'24', balance: 4700 },
      { name: 'Mar \'24', balance: 4900 },
      { name: 'Apr \'24', balance: 5100 },
      { name: 'May \'24', balance: 5300 },
      { name: 'Jun \'24', balance: 5500 },
      { name: 'Jul \'24', balance: 5700 },
      { name: 'Aug \'24', balance: 5900 },
      { name: 'Sep \'24', balance: 6100 },
      { name: 'Oct \'24', balance: 6300 },
      { name: 'Nov \'24', balance: 6500 },
      { name: 'Dec \'24', balance: 6700 }
    ]
  };

  // Return the same generic data for all account types
  return genericMockData.data.map((item, index) => ({
    ...item,
    group_end_date: `2024-${String(index + 1).padStart(2, '0')}-01`,
    dateSortValue: new Date(`2024-${String(index + 1).padStart(2, '0')}-01`).getTime(),
    isMockData: true
  }));
};

// Function to add dummy data points before actual data
const addDummyDataPoints = (data) => {
  if (!data || data.length === 0) return data;
  
  const dataLength = data.length;
  const maxPoints = 8;
  
  // Only add dummy points if we have 7 or fewer data points
  if (dataLength >= maxPoints) return data;
  
  const dummyPointsNeeded = maxPoints - dataLength;
  const dummyPoints = [];
  
  // Generate month names for dummy points
  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  
  // Get the year from the first data point, or use current year as fallback
  let baseYear = '24';
  if (data[0] && data[0].name) {
    const yearMatch = data[0].name.match(/'(\d{2})/);
    if (yearMatch) {
      baseYear = yearMatch[1];
    }
  }
  
  // Create dummy points with balance 0
  for (let i = 0; i < dummyPointsNeeded; i++) {
    const monthIndex = i % 12;
    const dummyPoint = {
      name: `${monthNames[monthIndex]} '${baseYear}`,
      balance: 0,
      group_end_date: `20${baseYear}-${String(monthIndex + 1).padStart(2, '0')}-01`,
      dateSortValue: new Date(`20${baseYear}-${String(monthIndex + 1).padStart(2, '0')}-01`).getTime(),
      isDummyData: true
    };
    dummyPoints.push(dummyPoint);
  }
  
  // Return dummy points followed by actual data
  return [...dummyPoints, ...data];
};

const AccountChart = ({ userId, darkMode }) => {
  const dispatch = useDispatch();
  const { 
    chartData,
    selectedChartView,
    selectedChartType,
    selectedTimePeriod,
    loading,
    error,
    hasNoDataForCurrentType,
    hasNoAccounts
  } = useSelector(state => state.accountChart);

  // Determine if we should show mock data
  const shouldShowMockData = hasNoAccounts || hasNoDataForCurrentType;
  
  // Get chart data - use mock data when user has no accounts or no data for current type
  const getChartData = () => {
    if (shouldShowMockData) {
      return getMockDataForAccountType(selectedChartType);
    }
    
    // Add dummy data points to actual data if needed
    const baseData = chartData || [];
    return addDummyDataPoints(baseData);
  };

  const displayData = getChartData();

  // Theme colors - adjusted for mock data
  const MAIN_COLOR = shouldShowMockData ? '#9ca3af' : '#8bc34a';
  const SECONDARY_COLOR = shouldShowMockData ? '#d1d5db' : '#aed581';
  const HOVER_COLOR = shouldShowMockData ? '#6b7280' : '#689f38';
  
  // Dark mode colors
  const bgColor = darkMode ? 'bg-gray-800' : 'bg-white';
  const textColor = darkMode ? 'text-white' : 'text-gray-800';
  const borderColor = darkMode ? 'border-gray-600' : 'border-gray-300';
  const hoverBgColor = darkMode ? 'hover:bg-gray-700' : 'hover:bg-[#e8f5e9]';

  // Enhanced CustomTooltip with logging
  const CustomTooltip = ({ active, payload, label }) => {
    // Log tooltip interaction
    if (active && payload && payload.length) {
      const isDummy = payload[0].payload.isDummyData;
      
      logEvent('AccountChart', 'tooltip_hover', {
        label,
        value: payload[0].value,
        chartType: selectedChartType,
        chartView: selectedChartView,
        timePeriod: selectedTimePeriod,
        isMockData: shouldShowMockData,
        isDummyData: isDummy
      });

      return (
        <div className={`p-3 border rounded shadow-md ${
          darkMode 
            ? 'bg-gray-700 border-gray-600 text-white' 
            : 'bg-white border-gray-300'
        }`}>
          <p className={`mb-1.5 font-medium text-sm ${
            darkMode ? 'text-gray-300' : 'text-gray-600'
          }`}>{label}</p>
          <p className={`font-semibold text-base ${
            isDummy ? 'text-gray-500' : shouldShowMockData ? 'text-gray-500' : 'text-[#8bc34a]'
          }`}>
            {`Balance: $${Number(payload[0].value).toLocaleString()}`}
          </p>
          {shouldShowMockData && (
            <p className="text-xs text-gray-400 mt-1">Demo data</p>
          )}
          {isDummy && !shouldShowMockData && (
            <p className="text-xs text-gray-400 mt-1">No data available</p>
          )}
        </div>
      );
    }
    return null;
  };

  // Log component mount and key state changes
  useEffect(() => {
    logEvent('AccountChart', 'component_mount', {
      userId,
      darkMode,
      initialChartType: selectedChartType,
      initialChartView: selectedChartView,
      initialTimePeriod: selectedTimePeriod,
      hasNoAccounts,
      hasNoDataForCurrentType
    });
  }, []); // Only on mount

  // Log state changes
  useEffect(() => {
    logEvent('AccountChart', 'state_change', {
      selectedChartType,
      selectedChartView,
      selectedTimePeriod,
      hasNoAccounts,
      hasNoDataForCurrentType,
      shouldShowMockData,
      dataPoints: displayData?.length || 0
    });
  }, [selectedChartType, selectedChartView, selectedTimePeriod, hasNoAccounts, hasNoDataForCurrentType]);

  // Initial and dependent data fetching with logging
  useEffect(() => {
    // logEvent('AccountChart', 'data_fetch_initiated', {
    //   userId,
    //   chartType: selectedChartType,
    //   timePeriod: selectedTimePeriod
    // });

    dispatch(fetchAccountData({
      userId,
      chartType: selectedChartType,
      timePeriod: selectedTimePeriod
    }));
  }, [dispatch, userId, selectedChartType, selectedTimePeriod]);

  // Log loading state changes
  useEffect(() => {
    if (loading) {
      // logEvent('AccountChart', 'loading_start', {
      //   chartType: selectedChartType,
      //   timePeriod: selectedTimePeriod
      // });
    } else {
      // logEvent('AccountChart', 'loading_end', {
      //   chartType: selectedChartType,
      //   timePeriod: selectedTimePeriod,
      //   hasError: !!error,
      //   dataPointsReceived: displayData?.length || 0
      // });
    }
  }, [loading]);

  // Log errors
  useEffect(() => {
    if (error) {
      // logEvent('AccountChart', 'error_occurred', {
      //   errorMessage: error?.message,
      //   errorStatus: error?.status,
      //   chartType: selectedChartType,
      //   timePeriod: selectedTimePeriod
      // });
    }
  }, [error]);

  // Enhanced retry function with logging
  const handleRetry = () => {
    // logEvent('AccountChart', 'retry_button_clicked', {
    //   chartType: selectedChartType,
    //   timePeriod: selectedTimePeriod,
    //   previousError: error?.message
    // });

    dispatch(fetchAccountData({
      userId,
      chartType: selectedChartType,
      timePeriod: selectedTimePeriod
    }));
  };

  // Error Rendering Component
  const ErrorDisplay = () => (
    <div className="chart-error-container p-6 text-center">
      <FontAwesomeIcon 
        icon={faExclamationTriangle} 
        className="text-red-500 text-4xl mb-4"
      />
      <h4 className={`text-xl font-semibold mb-2 ${textColor}`}>Unable to Load Chart</h4>
      
      <p className={`mb-4 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
        {error?.message || 'An unexpected error occurred while fetching account data'}
      </p>
      
      {error?.status && (
        <div className={`error-details mb-4 text-sm ${darkMode ? 'text-gray-500' : 'text-gray-500'}`}>
          Error Code: {error.status}
        </div>
      )}
      
      <button 
        onClick={handleRetry}
        className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 inline-flex items-center"
      >
        <FontAwesomeIcon icon={faSync} className="mr-2" />
        Retry Fetch
      </button>
    </div>
  );

  // Custom Label Renderer - Modified to handle dummy data
  const CustomLabel = (props) => {
    const { x, y, width, value, payload } = props;
    
    // Don't show labels for dummy data points (balance = 0)
    if (payload && payload.isDummyData && value === 0) {
      return null;
    }
    
    // Format the balance with dollar sign and commas
    const formattedValue = `$${Number(value).toLocaleString(undefined, {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    })}`;

    return (
      <text 
        x={x + (width ? width / 2 : 0)} 
        y={y - 10} 
        fill={shouldShowMockData ? (darkMode ? '#6b7280' : '#9ca3af') : (darkMode ? '#e5e7eb' : '#333')} 
        textAnchor="middle" 
        fontSize={10}
        fontWeight="bold"
      >
        {formattedValue}
      </text>
    );
  };

  // Find the maximum value in the data for dynamic domain calculation
  const getMaxValue = () => {
    if (!displayData || displayData.length === 0) return 0;
    const maxValue = Math.max(...displayData.map(item => item.balance));
    return Math.ceil(maxValue * 1.15);
  };

  const calculateTickCount = () => {
    if (!displayData || displayData.length === 0) return 5;
    const dataLength = displayData.length;
    const chartHeight = 400;
    const tickCount = Math.min(Math.ceil(chartHeight / 50), dataLength, 10);
    return tickCount;
  };

  // Enhanced Get Started button handler
  const handleGetStarted = () => {
    // logEvent('AccountChart', 'get_started_clicked', {
    //   currentChartType: selectedChartType,
    //   currentTimePeriod: selectedTimePeriod,
    //   currentChartView: selectedChartView,
    //   hasNoAccounts,
    //   hasNoDataForCurrentType
    // });
    // Add your navigation logic here
  };

  // Beautiful overlay for mock data
  const DemoDataOverlay = () => (
    <div className="absolute inset-0 flex items-center justify-center rounded-md z-10">
      {/* Subtle gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-200/10 to-gray-100/10 backdrop-blur-sm rounded-md"></div>

      <div className={`relative p-8 rounded-2xl border backdrop-blur-md shadow-2xl text-center max-w-sm ${
        darkMode 
          ? 'bg-gray-800/90 border-gray-600/50 text-white' 
          : 'bg-white/90 border-gray-200/50 text-gray-800'
      }`}>
        <div className="mb-4">
          <div className={`w-16 h-16 mx-auto rounded-full flex items-center justify-center ${
            darkMode ? 'bg-blue-500/20' : 'bg-blue-50'
          }`}>
            <FontAwesomeIcon 
              icon={faChartLine} 
              className="text-blue-500 text-2xl"
            />
          </div>
        </div>
        <h4 className="text-xl font-semibold mb-3">Connect Your Account</h4>
        <p className={`text-sm mb-4 leading-relaxed ${
          darkMode ? 'text-gray-300' : 'text-gray-600'
        }`}>
          Link your financial accounts to see real-time data and insights for {selectedTimePeriod.replace('-', ' ')} view
        </p>
        <div 
          onClick={handleGetStarted}
          className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-2 rounded-lg font-medium cursor-pointer hover:from-blue-600 hover:to-blue-700 transition-all duration-200"
        >
          Get Started
        </div>
      </div>
    </div>
  );

  // Enhanced chart view change handler
  const handleChartViewChange = (viewType) => {
    // logEvent('AccountChart', 'chart_view_changed', {
    //   previousView: selectedChartView,
    //   newView: viewType,
    //   chartType: selectedChartType,
    //   timePeriod: selectedTimePeriod,
    //   isMockData: shouldShowMockData
    // });
    dispatch(setChartView(viewType));
  };

  // Enhanced chart type change handler
  const handleChartTypeChange = (e) => {
    const newType = e.target.value;
    // logEvent('AccountChart', 'chart_type_changed', {
    //   previousType: selectedChartType,
    //   newType: newType,
    //   chartView: selectedChartView,
    //   timePeriod: selectedTimePeriod,
    //   isMockData: shouldShowMockData
    // });
    dispatch(setChartType(newType));
  };

  // Enhanced time period change handler
  const handleTimePeriodChange = (e) => {
    const newPeriod = e.target.value;
    // logEvent('AccountChart', 'time_period_changed', {
    //   previousPeriod: selectedTimePeriod,
    //   newPeriod: newPeriod,
    //   chartType: selectedChartType,
    //   chartView: selectedChartView,
    //   isMockData: shouldShowMockData
    // });
    dispatch(setTimePeriod(newPeriod));
  };

  // Enhanced refresh data handler
  const handleRefreshData = () => {
    // logEvent('AccountChart', 'refresh_data_clicked', {
    //   chartType: selectedChartType,
    //   timePeriod: selectedTimePeriod,
    //   chartView: selectedChartView,
    //   currentDataPoints: displayData?.length || 0
    // });

    dispatch(fetchAccountData({
      userId,
      chartType: selectedChartType,
      timePeriod: selectedTimePeriod
    }));
  };

  // Chart rendering logic
  const renderChart = () => {
    if (error && !shouldShowMockData) return <ErrorDisplay />;
    
    if (loading) {
      return (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
        </div>
      );
    }
    
    // Additional data validation
    if (!displayData || displayData.length === 0) {
      return (
        <div className={`text-center p-6 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
          <FontAwesomeIcon 
            icon={faExclamationTriangle} 
            className="text-yellow-500 text-4xl mb-4"
          />
          <p>No data available for the selected parameters</p>
          <button 
            onClick={handleRefreshData}
            className="mt-4 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Refresh Data
          </button>
        </div>
      );
    }

    const chartHeight = 400;
    const chartProps = {
      width: '100%',
      height: chartHeight,
      data: displayData,
      margin: { top: 40, right: 30, left: 20, bottom: 50 }
    };

    const axisFontStyle = {
      fontSize: '12px',
      fill: shouldShowMockData ? (darkMode ? '#6b7280' : '#9ca3af') : (darkMode ? '#9ca3af' : '#666')
    };

    const gradient = (
      <defs>
        <linearGradient id="colorBalance" x1="0" y1="0" x2="0" y2="1">
          <stop offset="5%" stopColor={MAIN_COLOR} stopOpacity={shouldShowMockData ? 0.3 : 0.5}/>
          <stop offset="95%" stopColor={SECONDARY_COLOR} stopOpacity={shouldShowMockData ? 0.4 : 0.6}/>
        </linearGradient>
      </defs>
    );

    // Calculate the maximum domain value to ensure chart isn't cut off
    const maxDomain = getMaxValue();
    const tickCount = calculateTickCount(); 
    const commonElements = (
      <>
        {gradient}
        <CartesianGrid 
          strokeDasharray="3 3" 
          stroke={shouldShowMockData ? (darkMode ? '#374151' : '#d1d5db') : (darkMode ? '#4b5563' : '#E0E0E0')}
          strokeWidth={1.5}
        />
        <XAxis
          dataKey="name"
          tick={{ ...axisFontStyle }}
          axisLine={{ stroke: shouldShowMockData ? (darkMode ? '#4b5563' : '#d1d5db') : (darkMode ? '#6b7280' : '#ddd') }}
          tickCount={tickCount}
        />
        <YAxis
          tick={{ ...axisFontStyle }}
          axisLine={{ stroke: shouldShowMockData ? (darkMode ? '#4b5563' : '#d1d5db') : (darkMode ? '#6b7280' : '#ddd') }}
          domain={[0, maxDomain]}
          tickFormatter={(value) => {
            const absValue = Math.abs(value);
            const prefix = value < 0 ? '-$' : '$';
            
            if (absValue >= 1000000) {
              return `${prefix}${(absValue / 1000000).toFixed(1)}M`;
            } else if (absValue >= 1000) {
              return `${prefix}${(absValue / 1000).toFixed(1)}K`;
            } else {
              return `${prefix}${absValue.toFixed(0)}`;
            }
          }}
          tickCount={tickCount} 
        />
      </>
    );

    // Log chart render with current configuration
    // logEvent('AccountChart', 'chart_rendered', {
    //   chartView: selectedChartView,
    //   chartType: selectedChartType,
    //   timePeriod: selectedTimePeriod,
    //   dataPoints: displayData.length,
    //   isMockData: shouldShowMockData,
    //   maxValue: getMaxValue()
    // });

    // Render appropriate chart based on selectedChartView
    switch (selectedChartView) {
      case 'line':
        return (
          <ResponsiveContainer width="100%" height={chartHeight}>
            <LineChart {...chartProps}>
              {commonElements}
              <Tooltip 
                content={<CustomTooltip />}
                formatter={(value) => [`$${Number(value).toLocaleString()}`, 'Balance']}
              />
              <Line
                type="monotone"
                dataKey="balance"
                stroke={MAIN_COLOR}
                fillOpacity={shouldShowMockData ? 0.2 : 0.3}
                strokeWidth={2.5}
                fill="url(#colorBalance)"
                dot={(props) => {
                  const { payload } = props;
                  // Make dummy data points smaller and more transparent
                  if (payload && payload.isDummyData) {
                    return (
                      <circle
                        cx={props.cx}
                        cy={props.cy}
                        r={2}
                        fill={darkMode ? '#4b5563' : '#d1d5db'}
                        stroke={darkMode ? '#6b7280' : '#9ca3af'}
                        strokeWidth={1}
                        fillOpacity={0.3}
                      />
                    );
                  }
                  return (
                    <circle
                      cx={props.cx}
                      cy={props.cy}
                      r={4}
                      fill={darkMode ? '#374151' : 'white'}
                      stroke={MAIN_COLOR}
                      strokeWidth={2}
                      fillOpacity={shouldShowMockData ? 0.5 : 1}
                    />
                  );
                }}
              >
                <LabelList 
                  dataKey="balance" 
                  content={CustomLabel}
                  position="top"
                />
              </Line>
            </LineChart>
          </ResponsiveContainer>
        );
        
      case 'area':
        return (
          <ResponsiveContainer width="100%" height={chartHeight}>
            <AreaChart {...chartProps}>
              {commonElements}
              <Tooltip 
                content={<CustomTooltip />}
                formatter={(value) => [`$${Number(value).toLocaleString()}`, 'Balance']}
              />
              <Area
                type="monotone"
                dataKey="balance"
                stroke={MAIN_COLOR}
                strokeWidth={2.5}
                fill="url(#colorBalance)"
                fillOpacity={shouldShowMockData ? 0.2 : 0.3}
                dot={(props) => {
                  const { payload } = props;
                  // Make dummy data points smaller and more transparent
                  if (payload && payload.isDummyData) {
                    return (
                      <circle
                        cx={props.cx}
                        cy={props.cy}
                        r={2}
                        fill={darkMode ? '#4b5563' : '#d1d5db'}
                        stroke={darkMode ? '#6b7280' : '#9ca3af'}
                        strokeWidth={1}
                        fillOpacity={0.3}
                      />
                    );
                  }
                  return (
                    <circle
                      cx={props.cx}
                      cy={props.cy}
                      r={4}
                      fill={darkMode ? '#374151' : 'white'}
                      stroke={MAIN_COLOR}
                      strokeWidth={2}
                      fillOpacity={shouldShowMockData ? 0.5 : 1}
                    />
                  );
                }}
              >
                <LabelList 
                  dataKey="balance" 
                  content={CustomLabel}
                  position="top"
                />
              </Area>
            </AreaChart>
          </ResponsiveContainer>
        );
        
      case 'bar':
      default:
        return (
          <ResponsiveContainer width="100%" height={chartHeight}>
            <BarChart {...chartProps}>
              {commonElements}
              <Tooltip 
                content={<CustomTooltip />}
                formatter={(value) => [`$${Number(value).toLocaleString()}`, 'Balance']}
              />
              <Bar
                dataKey="balance"
                strokeWidth={1.5}
              fill="url(#colorBalance)"
                fillOpacity={(entry) => {
                  if (entry && entry.isDummyData) {
                    return 0.2;
                  }
                  return shouldShowMockData ? 0.5 : 0.7;
                }}
                activeBar={{
                  fill: HOVER_COLOR
                }}
              >
                <LabelList 
                  dataKey="balance" 
                  content={CustomLabel}
                  position="top"
                />
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        );
    }
  };

  return (
    <div
      className={`w-full flex flex-col mx-auto rounded-[8px] ${bgColor}`}
      style={{ fontFamily: 'Roboto, sans-serif' }}
    >
      <div className="flex flex-col md:flex-row md:justify-between items-center mb-5 flex-wrap">
        {/* Title */}
        <h1></h1>

        {/* Chart View Switcher */}
        <div className="flex items-center gap-3 my-2 md:my-0">
          {[
            { type: 'bar', icon: faChartBar, label: 'Bar Chart' },
            { type: 'line', icon: faChartLine, label: 'Line Chart' },
            { type: 'area', icon: faChartArea, label: 'Area Chart' },
          ].map((chartType) => {
            const isActive = selectedChartView === chartType.type;
            return (
              <div
                key={chartType.type}
                onClick={() => handleChartViewChange(chartType.type)}
                className={`cursor-pointer rounded-md p-2 transition-all duration-200 flex items-center justify-center w-9 h-9 ${isActive
                    ? `${darkMode ? 'text-black' : 'text-black'} bg-[#8bc34a] shadow`
                    : `text-[#8bc34a] bg-transparent ${hoverBgColor}`
                  }`}
                title={chartType.label}
              >
                <FontAwesomeIcon icon={chartType.icon} className="text-[16px]" />
              </div>
            );
          })}
        </div>

        {/* Dropdown Filters */}
        <div className="flex items-center gap-3 flex-wrap w-full md:w-auto justify-between md:justify-start">
          <select
            value={selectedChartType}
            onChange={handleChartTypeChange}
            className={`p-2 px-3 rounded border text-sm w-[150px] focus:border-green-500 hover:border-green-500 transition-all duration-200 ${darkMode 
                  ? 'border-gray-600 bg-gray-700 text-white hover:bg-gray-600' 
                  : 'border-gray-300 bg-white text-gray-800 hover:bg-gray-50'}
             `}
            style={{ fontFamily:'Roboto, sans-serif' }}
          >
            <option value="cash">Cash Accounts</option>
            <option value="creditCard">Credit Cards</option>
            <option value="loan">Loans</option>
            <option value="investment">Investments</option>
            <option value="liability">Total Liabilities</option>
            <option value="networth">Net Worth</option>
          </select>

          <select
            value={selectedTimePeriod}
            onChange={handleTimePeriodChange}
            disabled={shouldShowMockData}
            className={`p-2 px-3 rounded border text-sm w-[150px] focus:border-green-500 hover:border-green-500 transition-all duration-200 
              ${
              shouldShowMockData 
                ? `cursor-not-allowed opacity-50 ${
                    darkMode 
                      ? 'border-gray-700 bg-gray-800 text-gray-500' 
                      : 'border-gray-200 bg-gray-100 text-gray-400'
                  }`
                : darkMode 
                  ? 'border-gray-600 bg-gray-700 text-white hover:bg-gray-600' 
                  : 'border-gray-300 bg-white text-gray-800 hover:bg-gray-50'
            }`}
            style={{ fontFamily: 'Roboto, sans-serif' }}
          >
            <option value="one-month">One Month</option>
            <option value="three-month">Three Months</option>
            <option value="ytd">Year to Date</option>
            <option value="half-year">Half-Year</option>
            <option value="yearly">Yearly</option>
            <option value="quarterly-aggregate">Quarterly</option>
          </select>
        </div>
      </div>

      {/* Chart Content */}
      <div className={`flex w-full shadow-lg rounded-md ${bgColor} relative`}>
        <div className="w-full relative transition-transform duration-500 min-h-[300px]">
          {renderChart()}
          {shouldShowMockData && <DemoDataOverlay />}
        </div>
      </div>
    </div>
  );
};

export default AccountChart;