import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, <PERSON>, Tooltip } from 'recharts';

// Color constants matching your design
const COLORS = {
  primary: '#7fe029',
  // primaryLight: darkMode ? '#374151' : '#eaf7e0',
  primaryDark: '#5db01e',
  white: '#ffffff',
  lightGray: '#f8f9fa',
  // textPrimary: darkMode ? '#f3f4f6' : '#333333',
  // textSecondary: darkMode ? '#9ca3af' : '#666666',
  success: '#2ecc71',
  danger: '#e74c3c',
  shadow: 'rgba(127, 224, 41, 0.15)'
};

// Spending categories with their colors
const SPENDING_COLORS = ['#FF8042', '#FFBB28', '#00C49F', '#0088FE', '#8884d8', '#82ca9d'];

// Investment categories with their colors
const INVESTMENT_COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#0088FE', '#00C49F', '#FF8042'];

// Spending Card Component
export const SpendingCard = ({ darkMode }) => {
  // Sample spending data
  const spendingData = [
    { name: 'Housing', value: 1200 },
    { name: 'Food', value: 450 },
    { name: 'Transport', value: 300 },
    { name: 'Utilities', value: 200 },
    { name: 'Entertainment', value: 150 },
    { name: 'Other', value: 100 },
  ];

  // Calculate total spending
  const totalSpending = spendingData.reduce((sum, item) => sum + item.value, 0);

  // Period selector state
  const [period, setPeriod] = useState('month');

  return (
    <div className={`h-full flex flex-col ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-roboto" style={{ color: COLORS.textPrimary }}>Spending Breakdown</h2>
        <div className="flex rounded-lg p-1" style={{ backgroundColor: COLORS.primaryLight }}>
          <button
            className="py-1 px-3 rounded-md text-xs font-medium transition-all duration-200"
            style={{
              backgroundColor: period === 'month' ? COLORS.primary : 'transparent',
              color: period === 'month' ? COLORS.white : COLORS.textSecondary
            }}
            onClick={() => setPeriod('month')}
          >
            Month
          </button>
          <button
            className="py-1 px-3 rounded-md text-xs font-medium transition-all duration-200"
            style={{
              backgroundColor: period === 'year' ? COLORS.primary : 'transparent',
              color: period === 'year' ? COLORS.white : COLORS.textSecondary
            }}
            onClick={() => setPeriod('year')}
          >
            Year
          </button>
        </div>
      </div>
      
      <div className="flex flex-col h-full">
        <div className="text-center mb-2">
          <span className="text-3xl font-bold" style={{ color: COLORS.textPrimary }}>
            ${totalSpending.toLocaleString()}
          </span>
          <span className="ml-2 text-sm" style={{ color: COLORS.textSecondary }}>
            this {period}
          </span>
        </div>
        
        <ResponsiveContainer width="100%" height={200}>
          <PieChart>
            <Pie
              data={spendingData}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={80}
              innerRadius={40}
              fill="#8884d8"
              dataKey="value"
            >
              {spendingData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={SPENDING_COLORS[index % SPENDING_COLORS.length]} />
              ))}
            </Pie>
            <Tooltip 
              formatter={(value) => [`$${value}`, 'Amount']}
              contentStyle={{ 
                backgroundColor: darkMode ? '#374151' : COLORS.white,
                borderColor: darkMode ? '#4b5563' : COLORS.primaryLight,
                color: darkMode ? COLORS.textPrimary : COLORS.textSeconda
              }}
            />
            <Legend 
                wrapperStyle={{ 
                  color: darkMode ? COLORS.textPrimary : COLORS.textSecondary,
                  fontSize: '12px'
                }}
            />
          </PieChart>
        </ResponsiveContainer>
        
        <div className="mt-2 overflow-auto">
          {spendingData.map((category, index) => (
            <div key={index} className="flex justify-between items-center py-2">
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: SPENDING_COLORS[index % SPENDING_COLORS.length] }}></div>
                <span className="text-sm" style={{ color: COLORS.textPrimary }}>{category.name}</span>
              </div>
              <div className="flex items-center">
                <span className="text-sm font-medium" style={{ color: COLORS.textPrimary }}>
                  ${category.value.toLocaleString()}
                </span>
                <span className="text-xs ml-2" style={{ color: COLORS.textSecondary }}>
                  ({Math.round((category.value / totalSpending) * 100)}%)
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Investment Card Component
export const InvestmentCard = ({ darkMode }) => {
  // Sample investment data
  const investmentData = [
    { name: 'Stocks', value: 25000 },
    { name: 'Bonds', value: 15000 },
    { name: 'Real Estate', value: 40000 },
    { name: 'Crypto', value: 5000 },
    { name: 'Cash', value: 10000 },
  ];

  // Calculate total investments
  const totalInvestment = investmentData.reduce((sum, item) => sum + item.value, 0);

  // Track performance
  const performance = {
    monthly: 2.4,
    yearly: 8.7
  };

  // Time period state
  const [period, setPeriod] = useState('all');

  return (
    <div className={`h-full flex flex-col ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-roboto" style={{ color: COLORS.textPrimary }}>Investment Portfolio</h2>
        <div className="flex rounded-lg p-1" style={{ backgroundColor: COLORS.primaryLight }}>
          <button
            className="py-1 px-3 rounded-md text-xs font-medium transition-all duration-200"
            style={{
              backgroundColor: period === 'month' ? COLORS.primary : 'transparent',
              color: period === 'month' ? COLORS.white : COLORS.textSecondary
            }}
            onClick={() => setPeriod('month')}
          >
            Month
          </button>
          <button
            className="py-1 px-3 rounded-md text-xs font-medium transition-all duration-200"
            style={{
              backgroundColor: period === 'year' ? COLORS.primary : 'transparent',
              color: period === 'year' ? COLORS.white : COLORS.textSecondary
            }}
            onClick={() => setPeriod('year')}
          >
            Year
          </button>
          <button
            className="py-1 px-3 rounded-md text-xs font-medium transition-all duration-200"
            style={{
              backgroundColor: period === 'all' ? COLORS.primary : 'transparent',
              color: period === 'all' ? COLORS.white : COLORS.textSecondary
            }}
            onClick={() => setPeriod('all')}
          >
            All
          </button>
        </div>
      </div>
      
      <div className="flex justify-between mb-4">
        <div>
          <div className="text-sm" style={{ color: COLORS.textSecondary }}>Total Portfolio</div>
          <div className="text-2xl font-bold" style={{ color: COLORS.textPrimary }}>
            ${totalInvestment.toLocaleString()}
          </div>
        </div>
        <div>
          <div className="text-sm" style={{ color: COLORS.textSecondary }}>Performance</div>
          <div className="flex items-center">
            <span className="text-2xl font-bold" style={{ color: COLORS.success }}>
              +{period === 'month' ? performance.monthly : performance.yearly}%
            </span>
            <span className="ml-1 text-xs" style={{ color: COLORS.textSecondary }}>
              {period === 'month' ? '1M' : period === 'year' ? '1Y' : 'All'}
            </span>
          </div>
        </div>
      </div>
      
      <ResponsiveContainer width="100%" height={200}>
        <PieChart>
          <Pie
            data={investmentData}
            cx="50%"
            cy="50%"
            labelLine={false}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {investmentData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={INVESTMENT_COLORS[index % INVESTMENT_COLORS.length]} />
            ))}
          </Pie>
          <Tooltip 
            formatter={(value) => [`$${value.toLocaleString()}`, 'Amount']}
            contentStyle={{ 
              backgroundColor: darkMode ? '#374151' : COLORS.white,
              borderColor: darkMode ? '#4b5563' : COLORS.primaryLight,
              color: darkMode ? COLORS.textPrimary : COLORS.textSecondary,
              borderRadius: '8px'
            }}
          />
          <Legend 
            layout="horizontal" 
            verticalAlign="bottom" 
            align="center"
            wrapperStyle={{ fontSize: '12px', color: darkMode ? COLORS.textPrimary : COLORS.textSecondary  }}
          />
        </PieChart>
      </ResponsiveContainer>
      
      <div className="mt-2 overflow-auto">
        {investmentData.map((asset, index) => (
          <div key={index} className="flex justify-between items-center py-2">
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: INVESTMENT_COLORS[index % INVESTMENT_COLORS.length] }}></div>
              <span className="text-sm" style={{ color: COLORS.textPrimary }}>{asset.name}</span>
            </div>
            <div className="flex items-center">
              <span className="text-sm font-medium" style={{ color: COLORS.textPrimary }}>
                ${asset.value.toLocaleString()}
              </span>
              <span className="text-xs ml-2" style={{ color: COLORS.textSecondary }}>
                ({Math.round((asset.value / totalInvestment) * 100)}%)
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Export both components to be used in the Dashboard
export default { SpendingCard, InvestmentCard };