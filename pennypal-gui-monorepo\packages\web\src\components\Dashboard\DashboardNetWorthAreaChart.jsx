import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { fetchAccountData, setTimePeriod } from '../../../../logic/redux/accountChartSlice';

const DashboardNetWorthAreaChart = ({ darkMode }) => {
  const dispatch = useDispatch();
  
  // Get state from Redux store
  const { 
    chartData, 
    selectedTimePeriod, 
    loading, 
    error 
  } = useSelector(state => state.accountChart);
  
  // Local state for chart color
  const [chartColor, setChartColor] = useState('#4195d1'); // Default color purple
  
  // Color options
  const colorOptions = [
    { color: '#4195d1', name: 'Blue' },
    { color: '#8884d8', name: 'Purple' },
    { color: '#82ca9d', name: 'Green' },
    { color: '#ff7f0e', name: 'Orange' }
  ];
  
  // Fetch data on component mount and when time period changes
  useEffect(() => {
    dispatch(fetchAccountData({
      userId: 1, // You might want to dynamically get this from user context/state
      chartType: 'networth',
      timePeriod: selectedTimePeriod
    }));
  }, [dispatch, selectedTimePeriod]);

  // Handle loading and error states
  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  // Calculate the max value dynamically for the Y-axis
  const getMaxValue = () => {
    if (!chartData || chartData.length === 0) return 0;
    const maxValue = Math.max(...chartData.map(item => item.balance));
    return Math.ceil(maxValue * 1.15); // Add 15% padding
  };

  // Custom render for data point labels
  const renderDataPointLabel = (props) => {
    const { x, y, value } = props;
    
    return (
      <text 
        x={x} 
        y={y - 10} 
        fill={chartColor} 
        textAnchor="middle" 
        fontSize={12}
        fontWeight="bold"
      >
        ${value.toLocaleString()}
      </text>
    );
  };

  // Handle time period change
  const handleTimePeriodChange = (e) => {
    dispatch(setTimePeriod(e.target.value));
  };

  return (
    <div
    style={{
      color: darkMode ? '#e5e7eb' : '#111827',
      backgroundColor: darkMode ? '#1f2937' : '#ffffff',
      // borderRadius: '10px',
      // padding: '20px'
    }}
  >      {/* Header with title, color dots, and dropdown all on same line */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: '20px' 
      }}>
        {/* NetWorth title on left */}
        <h2 className='text-xl font-roboto'>NetWorth</h2>
        
        {/* Color Selection Dots in center */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center',
          flex: '1'
        }}>
          {colorOptions.map((option) => (
            <div 
              key={option.name}
              onClick={() => setChartColor(option.color)}
              style={{
                width: '20px',
                height: '20px',
                borderRadius: '50%',
                backgroundColor: option.color,
                margin: '0 8px',
                cursor: 'pointer',
                border: chartColor === option.color ? '2px solid #333' : '1px solid #ddd',
                boxShadow: chartColor === option.color ? '0 0 5px rgba(0,0,0,0.3)' : 'none'
              }}
              title={option.name}
            />
          ))}
        </div>

        {/* Time Period Dropdown on right */}
        <div style={{ flex: '1', display: 'flex', justifyContent: 'flex-end' }}>
          <select
            value={selectedTimePeriod}
            onChange={handleTimePeriodChange}
            style={{
              padding: '5px',
              backgroundColor: darkMode ? '#374151' : '#fff',
              color: darkMode ? '#f9fafb' : '#111827',
              border: `1px solid ${darkMode ? '#4b5563' : '#d1d5db'}`,
              borderRadius: '4px'
            }}          >
            <option value="one-month">One Month</option>
            <option value="three-month">Three Months</option>
            <option value="ytd">Year to Date</option>
            <option value="half-year">Half Year</option>
            <option value="yearly">Yearly</option>
            <option value="quarterly-aggregate">Quarterly Aggregate</option>
          </select>
        </div>
      </div>
 
      {/* Responsive Area Chart */}
      <div style={{ height: '400px', width: '100%' }}>
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart 
            data={chartData}
            margin={{ top: 20, right: 0, left: 0, bottom: 10 }} // Added margin for labels
          >
            {/* Removed CartesianGrid for clean background */}
            <XAxis 
              dataKey="name" 
              tick={{ fontSize: 12, fill: darkMode ? '#d1d5db' : '#111827' }}
              height={50} // Increase height for better readability
            />
            <YAxis
             tick={{ fontSize: 12, fontWeight: 'bold', fill: darkMode ? '#d1d5db' : '#111827' }}
             axisLine={{ stroke: darkMode ? '#4b5563' : '#ddd' }}
             domain={[0, getMaxValue()]}
              tickFormatter={(value) => {
                const absValue = Math.abs(value);
                const prefix = value < 0 ? '-$' : '$';
                
                if (absValue >= 1000000) {
                  return `${prefix}${(absValue / 1000000).toFixed(1)}M`;
                } else if (absValue >= 1000) {
                  return `${prefix}${(absValue / 1000).toFixed(1)}K`;
                } else {
                  return `${prefix}${absValue.toFixed(0)}`;
                }
              }}
            />
            <Tooltip 
              formatter={(value) => {
                return [`$${value.toLocaleString()}`, 'Net Worth'];
              }}
              labelFormatter={(label) => `Date: ${label}`}
              contentStyle={{ 
                backgroundColor: darkMode ? '#1f2937' : 'rgba(255, 255, 255, 0.9)',
                border: `2px solid ${chartColor}`,
                color: darkMode ? '#f9fafb' : '#111827',
                borderRadius: '5px',
                padding: '10px'
              }}
              labelStyle={{ color: darkMode ? '#f3f4f6' : '#374151' }}
              itemStyle={{ color: chartColor }}
            />
            <Area
              type="monotone"
              dataKey="balance"
              name="Net Worth"
              stroke={chartColor}
              fill={chartColor}
              fillOpacity={0.3}
              strokeWidth={3}
              dot={{
                stroke: chartColor,
                strokeWidth: 2,
                r: 4,
                fillOpacity: 1
              }}
              activeDot={{
                r: 6,
                stroke: chartColor,
                strokeWidth: 2,
                fill: darkMode ? '#1f2937' : '#fff'
              }}
              label={selectedTimePeriod === 'quarterly-aggregate' || chartData.length < 8 ? renderDataPointLabel : false} // Only show labels when there are few data points
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default DashboardNetWorthAreaChart;