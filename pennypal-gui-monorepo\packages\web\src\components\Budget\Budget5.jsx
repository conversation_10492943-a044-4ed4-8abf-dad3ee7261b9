import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { 
  setBudgetData, 
  changeMonth, 
  setToToday,
  toggleAllTables,
  toggleCategory,
  toggleZeroBudget,
  setEditingSubcategory,
  setEditedBudget,
  formatCurrency,
  getProgress,
  calculateTotals
} from '../../../../logic/redux/budgetSlice';
import { axiosInstance } from '../../../../logic/api/axiosConfig';
import Cookies from 'js-cookie';
import BudgetPopupComponent from './BudgetPopupComponent';
import PaymentLoader from '../load/PaymentLoader'; // Import PaymentLoader
import { useLoading } from '../context/LoadingContext'; // Import useLoading (optional)

import {
  FaWallet,
  FaDollarSign,
  FaCreditCard,
  FaBuilding,
  FaChevronDown,
  FaChevronUp,
  FaArrowCircleRight,
  FaArrowCircleLeft,
  FaPlus,
  FaMinus,
  FaTrashAlt,
  FaQuestionCircle,
  FaTimes,
  FaMoneyBillWave,
  FaCar,
  FaHome,
  FaUtensils,
  FaPlane,
  FaShoppingCart,
  FaChild,
  FaHeart,
  FaBusinessTime,
  FaHospital,
  FaGraduationCap,
  FaFilm,
} from "react-icons/fa";
import * as FaIcons from "react-icons/fa";
import { MdPhone, MdLocalCafe, MdFlightTakeoff, MdMedicalServices } from "react-icons/md";
import {
  //UniBars,
  UniShoppingBasket,
  UniCoffee,
  UniAirplay,
  UniMoneyBill,
  UniWallet,
  UniDollarSign,
  UniCreditCard,
  UniBuilding
} from '../shared/Unicons';

import "./Budget.css";
import { getThemeClasses, themeClasses } from '../../utils/tailwindUtils';

const iconMapping = {
  FaMoneyBillWave: FaIcons.FaMoneyBillWave,
  FaCar: FaIcons.FaCar,
  FaHome: FaIcons.FaHome,
  FaUtensils: FaIcons.FaUtensils,
  FaPlane: FaIcons.FaPlane,
  FaShoppingCart: FaIcons.FaShoppingCart,
  FaChild: FaIcons.FaChild,
  FaHeart: FaIcons.FaHeart,
  FaBusinessTime: FaIcons.FaBusinessTime,
  FaHospital: FaIcons.FaHospital,
  FaGraduationCap: FaIcons.FaGraduationCap,
  FaFilm: FaIcons.FaFilm,
  // Budget-specific icons
  FaWallet: FaWallet,
  FaDollarSign: FaDollarSign,
  FaCreditCard: FaCreditCard,
  FaBuilding: FaBuilding,
  // Fallback icon
  FaMiscellaneous: FaQuestionCircle,
  // React Icons (Md)
  MdPhone: MdPhone,
  MdLocalCafe: MdLocalCafe,
  MdFlightTakeoff: MdFlightTakeoff,
  MdMedicalServices: MdMedicalServices,

  // IconScout icons
  UilShoppingBasket: UniShoppingBasket,
  UilCoffee: UniCoffee,
  UilAirplay: UniAirplay,
  UilMoneyBill: UniMoneyBill,
  UilWallet: UniWallet,
  UilDollarSign: UniDollarSign,
  UilCreditCard: UniCreditCard,
  UilBuilding: UniBuilding,
  //UilBars: UniBars,
};

// Default color mapping for each icon key
const iconColorMapping = {
  FaMoneyBillWave: "#4caf50",
  FaCar: "#2196f3",
  FaHome: "#ff9800",
  FaUtensils: "#9c27b0",
  FaPlane: "#3f51b5",
  FaShoppingCart: "#e91e63",
  FaChild: "#f44336",
  FaHeart: "#e91e63",
  FaBusinessTime: "#009688",
  FaHospital: "#f44336",
  FaGraduationCap: "#673ab7",
  FaFilm: "#795548",
  FaWallet: "#3f51b5",
  FaDollarSign: "#4caf50",
  FaCreditCard: "#009688",
  FaBuilding: "#ff9800",
  FaChevronDown: "#607d8b",
  FaChevronUp: "#607d8b",
  FaArrowCircleRight: "#009688",
  FaArrowCircleLeft: "#009688",
  FaPlus: "#8bc34a",
  FaMinus: "#f44336",
  FaTrashAlt: "#f44336",
  FaMiscellaneous: "#607d8b",
  MdPhone: "#03a9f4",
  MdLocalCafe: "#ff5722",
  MdFlightTakeoff: "#2196f3",
  MdMedicalServices: "#8bc34a",
  UilShoppingBasket: "#795548",
  UilCoffee: "#6f4e37",
  UilAirplay: "#009688",
  UilMoneyBill: "#4caf50",
};

// Renders a colored progress bar based on percentage
function ProgressBar({ spent, budget }) {
  const percentage = getProgress(spent, budget);
  let colorClass = "bg-emerald-500"; // green
  if (percentage > 100) {
    // Over budget
    colorClass = "bg-red-500";
  } else if (percentage > 75) {
    colorClass = "bg-amber-500";
  }

  return (
    <div className="mt-1 mb-3 w-full">
      <div className="flex justify-between text-xs text-gray-500">
        <span>
          ${formatCurrency(spent)} / ${formatCurrency(budget)}
        </span>
        <span>{percentage}%</span>
      </div>
      <div className="h-1 w-full bg-gray-200 rounded-full">
        <div
          className={`h-1 rounded-full ${colorClass}`}
          style={{ width: `${Math.min(percentage, 100)}%` }}
        />
      </div>
    </div>
  );
}

// Define the component
const Budget5 = ({ darkMode }) => {
  const dispatch = useDispatch();
  const { 
    currentMonth, 
    currentYear, 
    budgetData, 
    loading, 
    expandedCategories, 
    showZeroBudget, 
    showAllTables,
    editingSubcategory,
    editedBudget 
  } = useSelector((state) => state.budget);

  // State for the popup form
  const [isPopupOpen, setIsPopupOpen] = useState(false);

  useEffect(() => {
    console.log("Budget UseEffect is called")
    dispatch(setToToday());
  }, [dispatch]);

  // Show PaymentLoader when loading is true
  if (loading) {
    return (
      <div className={`min-h-screen w-full p-5 flex flex-col justify-center items-center ${themeClasses.container(darkMode)}`}>
        <PaymentLoader darkMode={darkMode} />
        {/* <p
          className={`mt-2 text-sm ${
            darkMode ? "text-gray-300" : "text-gray-600"
          }`}
        >
          Loading budget data...
        </p> */}
      </div>
    );
  }
  console.log("Loading ..."+loading);
  console.log("Printing budget data");
  console.log(budgetData);

  // Calculate total actual for Category 1
  const calculateCategory1Actual = () => {
    if (budgetData.length === 0) return 0;

    const category1 = budgetData[0]; // Get Category 1
    let totalActual = category1.category?.actual || 0;

    // Add subcategory actual (spent) values
    category1.category?.subCategories?.forEach((subcategory) => {
      totalActual += subcategory.spent || 0;
    });

    return totalActual;
  };

  const category1Actual = calculateCategory1Actual(); // Get actual total for Category 1

  // Calculate other totals for summary cards (Income, Budget, Actual, Remaining)
  const { totalBudget, totalActual, totalRemaining } = calculateTotals(budgetData);

  // Inline editing functions using the compositeKey.
  const handleBudgetDoubleClick = (subcategory) => {
    const initialValue = subcategory?.budget ?? 0;
    dispatch(setEditedBudget({ 
      key: subcategory.compositeKey, 
      value: String(initialValue) // Convert to string explicitly
    }));
    dispatch(setEditingSubcategory(subcategory.compositeKey));
  };

  const handleBudgetChange = (e) => {
    const newValue = e.target.innerText.trim() || '0';
    dispatch(setEditedBudget({
      ...editedBudget,
      value: newValue
    }));
  };

  const saveBudgetChange = (categoryId, subcategoryId) => {
    // Find the subcategory in Redux state
    const category = budgetData.find((cat) => cat.id === categoryId);
    if (!category) {
      console.error("Category not found!");
      return;
    }

    const subcategory = category.category.subCategories.find((sub) => 
      sub.id === subcategoryId || sub.compositeKey === subcategoryId
    );
    if (!subcategory) {
      console.error("Subcategory not found!");
      return;
    }

    const newAllocatedValue = Number(editedBudget?.value || 0);
    if (isNaN(newAllocatedValue)) {
      console.error("Invalid budget value");
      return;
    }

    // Determine a valid budget ID
    let budgetId = subcategory.budgetId || subcategory.id;
    if (!budgetId || budgetId === 0) {
      budgetId = subcategory.sub_category_id;
    }
    if (!budgetId || budgetId === 0) {
      console.error("Invalid budget ID:", budgetId);
      return;
    }

    // Dispatch the saveBudget action
    dispatch({
      type: 'budget/saveBudget',
      payload: {
        budgetId,
        categoryId,
        subcategoryId,
        newBudget: newAllocatedValue
      }
    });
  };
  
  const handlePopupToggle = () => {
    setIsPopupOpen(!isPopupOpen);
  };

  const handleSaveBudget = (budgetItem) => {
    // Dispatch the addBudgetItem action to be handled by the epic
    dispatch({
      type: 'budget/addBudgetItem',
      payload: budgetItem
    });
    setIsPopupOpen(false);
  };

  const handleCloseSubcategory = (categoryId, subcategoryId) => {
    // Get the subcategory to find the budgetId
    const category = budgetData.find((cat) => cat.id === categoryId);
    if (!category) return;

    const subcategory = category.category.subCategories.find(
      (sub) => sub.id === subcategoryId || sub.compositeKey === subcategoryId
    );
    if (!subcategory) return;

    const budgetId = subcategory.budgetId || subcategory.id;
    
    // Dispatch the delete action to be handled by the epic
    dispatch({
      type: 'budget/deleteSubcategoryBudget',
      payload: {
        budgetId,
        categoryId,
        subcategoryId
      }
    });
  };

  // Convert month index to name
  const monthNames = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];

  const handleToggleCategoryAction = (categoryId) => {
    dispatch(toggleCategory(categoryId));
  };

  const handleToggleZeroBudgetAction = (categoryId) => {
    dispatch(toggleZeroBudget(categoryId));
  };

  const handleToggleAllTablesAction = () => {
    dispatch(toggleAllTables());
  };

  return (
    <div className={`p-5 min-h-screen w-full font-roboto ${themeClasses.container(darkMode)}`}>
      {/* Header */}
      <div className={` ${themeClasses.container(darkMode)}`}>
        <h1 className="text-2xl">Budget</h1>
        <div className="flex justify-end pt-5 mb-5">
          <span
            className={`cursor-pointer mr-2.5 text-lg ${themeClasses.container(darkMode)}`}
            onClick={() => dispatch(setToToday())}
          >
            Today
          </span>
          <span
            className={`cursor-pointer mx-2 text-2xl mt-1 ${themeClasses.container(darkMode)}`}
            onClick={() => dispatch(changeMonth(-1))}
          >
            <FaArrowCircleLeft />
          </span>
          <span className="mx-2 text-lg">
            {monthNames[currentMonth]} {currentYear}
          </span>
          <span
            className={`cursor-pointer mx-2 text-2xl mt-1 ${themeClasses.container(darkMode)}`}
            onClick={() => dispatch(changeMonth(1))}
          >
            <FaArrowCircleRight />
          </span>
        </div>
      </div>

    {/* Summary Cards */}
<div className="flex flex-wrap justify-between mb-1">
  {[
    {
      title: 'Income',
      value: category1Actual,
      icon: <UniWallet size="24" className="text-lg text-white" />,
      iconClass: themeClasses.cardIconIncome(darkMode),
    },
    {
      title: 'Budget',
      value: totalBudget,
      icon: <UniDollarSign size="24" className="text-lg text-white" />,
      iconClass: themeClasses.cardIconBudget(darkMode),
    },
    {
      title: 'Actual',
      value: totalActual,
      icon: <UniCreditCard size="24" className="text-lg text-white" />,
      iconClass: themeClasses.cardIconActual(darkMode),
    },
    {
      title: 'Remaining',
      value: totalRemaining,
      icon: <UniBuilding size="24" className="text-lg text-white" />,
      iconClass: themeClasses.cardIconRemaining(darkMode),
    },
  ].map(({ title, value, icon, iconClass }, index) => (
    <div
      key={index}
      className={`flex flex-col justify-center border shadow rounded-2xl p-5 w-full sm:w-1/2 md:w-[24%] relative mb-4 ${themeClasses.cardBorder(darkMode)} ${themeClasses.cardContainer(darkMode)}`}
    >
      <strong className={`w-full text-left text-xl ${themeClasses.cardContainer(darkMode)}`}>
        {title}
        <div className={`absolute top-6 right-4 ${iconClass} rounded-full p-1`}>
          {icon}
        </div>
      </strong>
      <span
        className={`w-full text-center text-xl font-bold mt-8 ${themeClasses.cardContainer(darkMode)} ${
          title === 'Remaining' && value < 0 ? 'text-red-500' : title === 'Remaining' ? 'text-green-500' : ''
        }`}
      >
        ${formatCurrency(value)}
      </span>
    </div>
  ))}
</div>

   {/* Toggle and Add Button */}
   <div className="flex justify-between items-center mt-2">
        <button
          onClick={handleToggleAllTablesAction}
          className={`bg-transparent border-0 cursor-pointer flex justify-center items-center p-[10px] hover:opacity-80 transition ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}
        >
          {showAllTables ? (
            <FaChevronDown className={`text-[20px] ${darkMode ? 'text-gray-100' : 'text-gray-900'}`} />
          ) : (
            <FaChevronUp className={`text-[20px] ${darkMode ? 'text-gray-100' : 'text-gray-900'}`} />
          )}
        </button>
        <button
          onClick={handlePopupToggle}
          className={`border-0 cursor-pointer py-[8px] px-[16px] hover:opacity-80 transition ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}
        >
          <FaPlus className={`text-[20px] ${darkMode ? 'text-gray-100' : 'text-gray-900'}`} />
        </button>
      </div>


      {/* Popup for adding budget */}
      {isPopupOpen && (
        <BudgetPopupComponent
          budgetData={budgetData}
          onSave={handleSaveBudget}
          onClose={handlePopupToggle}
          darkMode={darkMode}
        />
      )}

      {/* Budget Table */}
      <div className={`mt-5 ${themeClasses.container(darkMode)}`}>
        <div className={`flex font-bold p-4 mt-5 ${themeClasses.tableHeader(darkMode)} ${themeClasses.border(darkMode)} rounded-xl shadow`}>
          <span className="flex-1 text-left pl-3">Category</span>
          <span className="flex-1 text-center">Budget</span>
          <span className="flex-1 text-center">Actual</span>
          <span className="flex-1 text-center">Remaining</span>
        </div>
        {budgetData.length > 0 ? (
          budgetData.map((category) => {
            const nonZeroSubs = category.category?.subCategories?.filter(
              (sub) => sub.budget !== 0
            );
            const zeroSubs = category.category?.subCategories?.filter(
              (sub) => sub.budget === 0
            );
            const isExpanded = expandedCategories[category.id] ?? showAllTables;
            const iconKey = category.category?.categoryIconKey;
            const IconComponent = iconMapping[iconKey] || FaQuestionCircle;

            // Category-level totals
            const categoryBudgetTotal =
              (category.category?.allocated || 0) +
              (category.category?.subCategories?.reduce(
                (acc, sub) => acc + (sub.budget || 0),
                0
              ) || 0);
            const categoryActualTotal =
              (category.category?.actual || 0) +
              (category.category?.subCategories?.reduce(
                (acc, sub) => acc + (sub.spent || 0),
                0
              ) || 0);
            const categoryRemainingTotal =
              (category.category?.remaining || 0) +
              (category.category?.subCategories?.reduce(
                (acc, sub) => acc + (sub.remaining || 0),
                0
              ) || 0);

            return (
              <React.Fragment key={category.id}>
                {/* Category Header */}
                <div
                 className={`flex p-2.5 mt-5 ${themeClasses.tableRowContainer(darkMode)} ${themeClasses.tableRowBorder(darkMode)} rounded-t-xl cursor-pointer border-t border-l border-r shadow-[0_-4px_6px_0_rgba(0,0,0,0.1),0_6px_8px_-4px_rgba(0,0,0,0.1)]`}
                  onClick={() => handleToggleCategoryAction(category.id)}
                >
                  <strong className="w-[24.5%] ml-2.5 pt-1.5 flex items-center">
                    <IconComponent
                      className="mr-2"
                      style={{ color: iconColorMapping[iconKey] || "#000" }}
                    />
                    {category.category?.category || "Unknown"}
                  </strong>
                  <span className="flex-1 text-center">
                    ${formatCurrency(categoryBudgetTotal)}
                  </span>
                  <span className="flex-1 text-center">
                    ${formatCurrency(categoryActualTotal)}
                  </span>
                  <span
                    className={`flex-1 text-center ${
                      categoryRemainingTotal < 0 ? "text-red-500" : "text-green-500"
                    }`}
                  >
                    ${formatCurrency(categoryRemainingTotal)}
                  </span>
                </div>

                 {/* Progress Bar on its own row */}
        {isExpanded && (
          <div
            className={`border-l border-r border-b px-5 pt-1 ${themeClasses.tableProgressContainer(darkMode)} ${themeClasses.tableProgressBorder(darkMode)}`}
          >
                                <ProgressBar spent={categoryActualTotal} budget={categoryBudgetTotal} />
                  </div>
                )}

                {/* Category Details */}
                 {/* Category Details */}
        <div
          className={`border-b border-l border-r shadow-lg rounded-b-xl ${themeClasses.tableRowContainer(darkMode)} ${themeClasses.tableRowBorder(darkMode)}`}
        >
                            {isExpanded && category.category?.subCategories?.length > 0 && (
                    <>
                      {nonZeroSubs?.map((sub) => {
                        const subIconKey = sub.icon_key;
                        const SubIconComponent =
                          iconMapping[subIconKey] || FaQuestionCircle;
                        const isEditing = editingSubcategory === sub.compositeKey;
                        return (
                          <React.Fragment key={sub.compositeKey}>
                              <div
                      className={`flex justify-between p-5 transition-colors duration-300 group ${themeClasses.tableRowContainer(darkMode)} ${themeClasses.tableRowHover(darkMode)}`}
                      tabIndex="0"
                    >
                              <div className="flex-1 text-left flex items-center">
                                <SubIconComponent
                                  className="mr-2"
                                  style={{
                                    color: iconColorMapping[subIconKey] || "#000",
                                  }}
                                />
                                {sub.subCategory || sub.customSubCategory || "N/A"}
                              </div>
                              <div
                        className={`flex-1 text-center cursor-pointer outline-none ${themeClasses.tableRowContainer(darkMode)}`}
                        onDoubleClick={() => handleBudgetDoubleClick(sub)}
                                contentEditable={isEditing}
                                suppressContentEditableWarning={true}
                                onInput={handleBudgetChange}
                                onBlur={() => saveBudgetChange(category.id, sub.compositeKey)}
                                onKeyDown={(e) => {
                                  if (e.key === "Enter") {
                                    e.preventDefault();
                                    saveBudgetChange(category.id, sub.compositeKey);
                                  }
                                }}
                                style={{
                                  outline: isEditing ? "1px solid blue" : "none",
                                }}
                              >
                                {isEditing 
                                  ? (editedBudget?.value ?? '0')
                                  : formatCurrency(sub.budget ?? 0)}
                              </div>
                              <div className="flex-1 text-center">
                                ${formatCurrency(sub.spent || 0)}
                              </div>
                              <div
                                className={`flex-1 text-center ${
                                  sub.budget - (sub.spent || 0) < 0
                                    ? "text-red-500"
                                    : "text-green-500"
                                }`}
                              >
                                ${formatCurrency(sub.budget - (sub.spent || 0))}
                              </div>
                              {sub.budget !== 0 && (
                                <div
                                  className="text-center cursor-pointer hidden group-hover:block"
                                  onClick={() =>
                                    handleCloseSubcategory(category.id, sub.compositeKey)
                                  }
                                >
                                  <FaTrashAlt />
                                </div>
                              )}
                            </div>

                            {/* Subcategory Progress Bar */}
                            <div className="px-5 pt-1 ">
                              <ProgressBar
                                spent={sub.spent || 0}
                                budget={sub.budget || 0}
                              />
                            </div>
                          </React.Fragment>
                        );
                      })}
                      {zeroSubs?.length > 0 && !showZeroBudget[category.id] && (
                        <div
                  className={`flex justify-center items-center ${themeClasses.toggleText(darkMode)} italic cursor-pointer`}
                  onClick={() => handleToggleZeroBudgetAction(category.id)}
                >
                          <FaPlus className="mr-2 mb-5" />
                        </div>
                      )}
                      {zeroSubs?.length > 0 && showZeroBudget[category.id] && (
                        <>
                          {zeroSubs.map((sub) => {
                            const subIconKey = sub.icon_key;
                            const SubIconComponent =
                              iconMapping[subIconKey] || FaQuestionCircle;
                            const isEditing = editingSubcategory === sub.compositeKey;
                            return (
                              <React.Fragment key={sub.compositeKey}>
                               <div
                          className={`flex justify-between p-5 transition-colors duration-300 group ${themeClasses.tableRowContainer(darkMode)} ${themeClasses.tableRowHover(darkMode)}`}
                          tabIndex="0"
                        >
                                  <div className="flex-1 text-left flex items-center">
                                    <SubIconComponent
                                      className="mr-2"
                                      style={{
                                        color: iconColorMapping[subIconKey] || "#000",
                                      }}
                                    />
                                    {sub.subCategory || sub.customSubCategory || "N/A"}
                                  </div>
                                  <div
                            className={`flex-1 text-center cursor-pointer outline-none ${themeClasses.tableRowContainer(darkMode)}`}
                            onDoubleClick={() => handleBudgetDoubleClick(sub)}
                                    contentEditable={isEditing}
                                    suppressContentEditableWarning={true}
                                    onInput={handleBudgetChange}
                                    onBlur={() => saveBudgetChange(category.id, sub.compositeKey)}
                                    onKeyDown={(e) => {
                                      if (e.key === "Enter") {
                                        e.preventDefault();
                                        saveBudgetChange(category.id, sub.compositeKey);
                                      }
                                    }}
                                    style={{
                                      outline: isEditing ? "1px solid blue" : "none",
                                    }}
                                  >
                                    {isEditing 
                                      ? (editedBudget?.value ?? '0')
                                      : formatCurrency(sub.budget ?? 0)}
                                  </div>
                                  <div className="flex-1 text-center">
                                    ${formatCurrency(sub.spent || 0)}
                                  </div>
                                  <div
                                    className={`flex-1 text-center ${
                                      sub.budget - (sub.spent || 0) < 0
                                        ? "text-red-500"
                                        : "text-green-500"
                                    }`}
                                  >
                                    ${formatCurrency(sub.budget - (sub.spent || 0))}
                                  </div>
                                  {sub.budget !== 0 && (
                                    <div
                                      className="text-center cursor-pointer hidden group-hover:block"
                                      onClick={() =>
                                        handleCloseSubcategory(
                                          category.id,
                                          sub.compositeKey
                                        )
                                      }
                                    >
                                      <FaTrashAlt />
                                    </div>
                                  )}
                                </div>

                                {/* Subcategory Progress Bar */}
                                <div className="ml-8">
                                  <ProgressBar
                                    spent={sub.spent || 0}
                                    budget={sub.budget || 0}
                                  />
                                </div>
                              </React.Fragment>
                            );
                          })}
                          <div
                    className={`flex justify-center items-center ${themeClasses.toggleText(darkMode)} italic cursor-pointer`}
                    onClick={() => handleToggleZeroBudgetAction(category.id)}
                  >
                            <FaMinus className="mr-2" />
                          </div>
                        </>
                      )}
                    </>
                  )}
                </div>
              </React.Fragment>
            );
          })
        ) : (
          <p>No budget data available.</p>
        )}
      </div>
    </div>
  );
}

// Make sure to add this explicit default export
export default Budget5;
