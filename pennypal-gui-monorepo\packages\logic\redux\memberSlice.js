import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  inviteStatus: 'idle', // 'idle', 'loading', 'succeeded', 'failed'
  inviteError: null,
  inviteResponse: null,
  validateStatus: 'idle',
  validateError: null,
  validateResponse: null,
  signupStatus: 'idle',
  signupError: null,
  signupResponse: null,
  familyMembersStatus: 'idle',
  familyMembersError: null,
  familyMembers: [],
  revokeStatus: 'idle',
  revokeError: null,
  revokeResponse: null,
};

const memberSlice = createSlice({
  name: 'membership',
  initialState,
  reducers: {
    // Invite family member actions
    inviteFamilyMemberRequest: (state) => {
      state.inviteStatus = 'loading';
      state.inviteError = null;
      state.inviteResponse = null;
    },
    inviteFamilyMemberSuccess: (state, action) => {
      state.inviteStatus = 'succeeded';
      state.inviteResponse = action.payload;
    },
    inviteFamilyMemberFailure: (state, action) => {
      state.inviteStatus = 'failed';
      state.inviteError = action.payload;
    },
    
    // Validate invite link actions
    validateInviteLinkRequest: (state) => {
      state.validateStatus = 'loading';
      state.validateError = null;
      state.validateResponse = null;
    },
    validateInviteLinkSuccess: (state, action) => {
      state.validateStatus = 'succeeded';
      state.validateResponse = action.payload;
    },
    validateInviteLinkFailure: (state, action) => {
      state.validateStatus = 'failed';
      state.validateError = action.payload;
    },
    
    // Complete signup actions
    completeSignupRequest: (state) => {
      state.signupStatus = 'loading';
      state.signupError = null;
      state.signupResponse = null;
    },
    completeSignupSuccess: (state, action) => {
      state.signupStatus = 'succeeded';
      state.signupResponse = action.payload;
    },
    completeSignupFailure: (state, action) => {
      state.signupStatus = 'failed';
      state.signupError = action.payload;
    },
    
    // Get family members actions
    getFamilyMembersRequest: (state) => {
      state.familyMembersStatus = 'loading';
      state.familyMembersError = null;
    },
    getFamilyMembersSuccess: (state, action) => {
      state.familyMembersStatus = 'succeeded';
      state.familyMembers = action.payload;
    },
    getFamilyMembersFailure: (state, action) => {
      state.familyMembersStatus = 'failed';
      state.familyMembersError = action.payload;
    },
    
    // Revoke family member actions
    revokeFamilyMemberRequest: (state) => {
      state.revokeStatus = 'loading';
      state.revokeError = null;
      state.revokeResponse = null;
    },
    revokeFamilyMemberSuccess: (state, action) => {
      state.revokeStatus = 'succeeded';
      state.revokeResponse = action.payload;
      // Remove the revoked member from the list if the operation was successful
      if (action.payload.success) {
        state.familyMembers = state.familyMembers.filter(
          member => member.relationshipId !== action.payload.relationshipId
        );
      }
    },
    revokeFamilyMemberFailure: (state, action) => {
      state.revokeStatus = 'failed';
      state.revokeError = action.payload;
    },
    
    // Reset states
    resetInviteState: (state) => {
      state.inviteStatus = 'idle';
      state.inviteError = null;
      state.inviteResponse = null;
    },
    resetValidateState: (state) => {
      state.validateStatus = 'idle';
      state.validateError = null;
      state.validateResponse = null;
    },
    resetSignupState: (state) => {
      state.signupStatus = 'idle';
      state.signupError = null;
      state.signupResponse = null;
    },
    resetRevokeState: (state) => {
      state.revokeStatus = 'idle';
      state.revokeError = null;
      state.revokeResponse = null;
    },
    resetFamilyMembersState: (state) => {
      state.familyMembersStatus = 'idle';
      state.familyMembersError = null;
    },
  }
});

export const {
  inviteFamilyMemberRequest,
  inviteFamilyMemberSuccess,
  inviteFamilyMemberFailure,
  validateInviteLinkRequest,
  validateInviteLinkSuccess,
  validateInviteLinkFailure,
  completeSignupRequest,
  completeSignupSuccess,
  completeSignupFailure,
  getFamilyMembersRequest,
  getFamilyMembersSuccess,
  getFamilyMembersFailure,
  revokeFamilyMemberRequest,
  revokeFamilyMemberSuccess,
  revokeFamilyMemberFailure,
  resetInviteState,
  resetValidateState,
  resetSignupState,
  resetRevokeState,
  resetFamilyMembersState
} = memberSlice.actions;

// Selectors
export const selectInviteStatus = state => state.membership.inviteStatus;
export const selectInviteError = state => state.membership.inviteError;
export const selectInviteResponse = state => state.membership.inviteResponse;

export const selectValidateStatus = state => state.membership.validateStatus;
export const selectValidateError = state => state.membership.validateError;
export const selectValidateResponse = state => state.membership.validateResponse;

export const selectSignupStatus = state => state.membership.signupStatus;
export const selectSignupError = state => state.membership.signupError;
export const selectSignupResponse = state => state.membership.signupResponse;

export const selectFamilyMembersStatus = state => state.membership.familyMembersStatus;
export const selectFamilyMembersError = state => state.membership.familyMembersError;
export const selectFamilyMembers = state => state.membership.familyMembers;

export const selectRevokeStatus = state => state.membership.revokeStatus;
export const selectRevokeError = state => state.membership.revokeError;
export const selectRevokeResponse = state => state.membership.revokeResponse;

export default memberSlice.reducer;