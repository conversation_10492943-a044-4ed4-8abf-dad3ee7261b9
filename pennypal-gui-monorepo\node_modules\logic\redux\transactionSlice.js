import { createSlice,createAction } from '@reduxjs/toolkit';

export const addTransactionRequest = createAction('transactions/addTransaction');

const initialState = {
  transactions: [],
  reconciledTransactionsById: {},
  hiddenTransactions: [],
  allTransactions: [],
  selectedTransaction: null,
  openModal: false,
  isCardVisible: false,
  startDate: '',
  endDate: '',
  searchName: '',
  customSearchText: '',
  searchDate: '',
  selectedDateRange: '',
  dateFilterOpen: false,
  sortOrder: {
    date: 'desc',
    name: 'asc',
    category: 'asc',
  },
  loading: false,
  error: null,
  selectedTransactions: [],
  allChecked: false,
  selectedAccount: null,
  isAddTransactionOpen: false,
  newTransaction: {
    date: '',
    description: '',
    category: '',
    account: '',
    amount: '',
  },
  accounts: [], // Add accounts array to store fetched account IDs
    loadingAccounts: false,
    error: null,
   transactionDetails: {},
    summary: null,
  loading: false,
  error: null,
  currentTab: 'receipt', // Make sure this is set
  jsonResponse: {},
  hideFromBudgetSuccess: false, // New state
  hideFromBudgetError: null,
  page: 0,
  pageSize: 250,
  totalElements: 0,
  totalPages: 0,
  append: true,
  hasMore: true,
   categoryMonthlyExpenses: [],     //  Store the fetched data
  loadingCategoryExpenses: false,  //  Loading state
  errorCategoryExpenses: null, 
  selectedCategory: null,
  openCategoryModal: false,
  selectedSubCategoryId: null, 
  selectedCategoryId: null, // Add this
  subCategoryIcons: {},
  icons: [],
  iconMap: {},
  loading: false,
  loaded: false,
  error: null,
  categories: [],
  subcategories: [],
};

const transactionSlice = createSlice({
  name: 'transactions',
  initialState,
  reducers: {
    setSelectedAccount: (state, action) => {
  state.selectedAccount = action.payload;
},
    fetchAllIconsStart: (state) => {
      state.loading = true;
    },
    fetchAllIconsSuccess: (state, action) => {
      state.icons = action.payload.icons;
      state.iconMap = action.payload.iconMap;
      state.loading = false;
      state.loaded = true;
    },
    fetchAllIconsFailure: (state, action) => {
      state.error = action.payload;
      state.loading = false;
    },
    clearIcons: (state) => {
      // Revoke all object URLs
      Object.values(state.iconMap).forEach(url => URL.revokeObjectURL(url));
      return initialState;
    },
    setSubcategoriesData: (state, action) => {
      state.subcategories = action.payload;
    },
    fetchAccountsRequest(state) {
      state.loadingAccounts = true;
      state.error = null;
    },
    fetchAccountsSuccess(state, action) {
      state.accounts = action.payload;
      state.loadingAccounts = false;
    },
    fetchAccountsFailure(state, action) {
      state.error = action.payload;
      state.loadingAccounts = false;
    },
     fetchTransactionSummaryStart(state) {
      state.loading = true;
      state.error = null;
    },
     fetchTransactionSummarySuccess(state, action) {
      state.summary  = action.payload;
      state.loading = false;
    },
    fetchTransactionSummaryFailure(state, action) {
      state.error = action.payload;
      state.loading = false;
    },
 fetchHiddenTransactionsStart(state) {
  state.loading = true;
  state.error = null;
},

fetchSubCategoryIconStart(state, action) {
      state.loading = true;
      state.error = null;
    },
     fetchSubCategoryIconSuccess: (state, action) => {
    const { subCategoryId, iconUrl } = action.payload;
    if (!state.subCategoryIcons) {
      state.subCategoryIcons = {};
    }
    state.subCategoryIcons[subCategoryId] = iconUrl;
    state.loading = false;
  },
  fetchSubCategoryIconFailure: (state, action) => {
    state.loading = false;
    state.error = action.payload.error;
  },
  clearSubCategoryIcon: (state, action) => {
      const { subCategoryId } = action.payload;
      if (state.subCategoryIcons?.[subCategoryId]) {
        URL.revokeObjectURL(state.subCategoryIcons[subCategoryId]);
        delete state.subCategoryIcons[subCategoryId];
      }
    },
  
fetchHiddenTransactionsSuccess(state, action) {
  state.loading = false;
  state.hiddenTransactions = action.payload;
},

fetchHiddenTransactionsFailure(state, action) {
  state.loading = false;
  state.error = action.payload;
},
setPage(state, action) {
      state.page = action.payload;
    },
    setPageSize(state, action) {
      state.pageSize = action.payload;
    },
    setAppendMode(state, action) {
  state.appendMode = action.payload;
},

hideFromBudgetSuccess(state, action) {
      state.loading = false;
      state.hideFromBudgetSuccess = true;
      state.hideFromBudgetError = null;
      // Update transactions in state
      state.transactions = state.transactions.map(tx =>
        action.payload.includes(tx.transaction_id)
          ? { ...tx, hideFromBudget: true }
          : tx
      );
      state.allTransactions = state.allTransactions.map(tx =>
        action.payload.includes(tx.transaction_id)
          ? { ...tx, hideFromBudget: true }
          : tx
      );
    },
    hideFromBudgetFailure(state, action) {
      state.loading = false;
      state.hideFromBudgetError = action.payload;
    },
    resetHideFromBudgetStatus(state) {
      state.hideFromBudgetSuccess = false;
      state.hideFromBudgetError = null;
    },
  
//update transaction(modify trans detail)
 updateTransactionStart: (state) => {
      state.loading = true;
      state.error = null;
    },
    updateTransactionSuccess: (state, action) => {
      state.loading = false;
      // Update the transaction in the list
      const index = state.transactions.findIndex(
        t => t.transaction_id === action.payload.transaction_id
      );
      if (index !== -1) {
        state.transactions[index] = action.payload;
      }
      // Also update the selected transaction
      state.selectedTransaction = action.payload;
    },
    updateTransactionFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },

    // delete trans
deleteTransactionSuccess: (state, action) => {
  const { id, removed } = action.payload;
  
  const index = state.transactions.findIndex(t => t.transaction_id === id);
if (index !== -1) {
  state.transactions[index].removed = true;
}

if (state.selectedTransaction?.transaction_id === id) {
  state.selectedTransaction = null;
}
},
deleteTransactionFailure: (state, action) => {
  state.error = action.payload;
  state.loading = false;
},

   // Add these to your slice reducers
hideTransactions: (state, action) => {
  state.error = null; 
  state.isProcessing = true;
},
hideTransactionsSuccess: (state, action) => {
  state.isProcessing = false;
  // Mark transactions as removed rather than filtering them out
  state.transactions = state.transactions.map(t => 
    action.payload.includes(t.transaction_id) ? {...t, removed: true} : t
  );
  state.selectedTransactions = state.selectedTransactions.filter(
    id => !action.payload.includes(id)
  );
},
hideTransactionsFailure: (state, action) => {
  state.isProcessing = false;
  state.error = action.payload;
},
    // Fetch transactions
    fetchTransactionsStart(state) {
      state.loading = true;
      state.error = null;
    },
fetchTransactionsSuccess(state, action) {
  state.loading = false;
  state.totalElements = action.payload.totalElements;
  state.totalPages = action.payload.totalPages;
  
  if (action.payload.append) {
    state.transactions = [...state.transactions, ...action.payload.content];
    state.allTransactions = [...state.allTransactions, ...action.payload.content];
  } else {
    state.transactions = action.payload.content;
    state.allTransactions = action.payload.content;
  }
},

    fetchTransactionsFailure(state, action) {
      state.loading = false;
      state.error = action.payload;
    },

    // Select a transaction for detail view
    setSelectedTransaction(state, action) {
      state.selectedTransaction = action.payload;
    },
    updateTransactionRequest: (state, action) => {
      const index = state.transactions.findIndex(
        t => t.transaction_id === action.payload.transaction_id
      );
      if (index !== -1) {
        state.transactions[index] = action.payload;
      }
      state.selectedTransaction = action.payload;
    },

    // Toggle modal for transaction details
    setOpenModal(state, action) {
      state.openModal = action.payload;
    },

    // Toggle card visibility
    toggleCardVisibility(state) {
      state.isCardVisible = !state.isCardVisible;
    },

    // Update filter/search parameters
    setSearchName(state, action) {
      state.searchName = action.payload;
    },
    setStartDate(state, action) {
      state.startDate = action.payload;
    },
    setEndDate(state, action) {
      state.endDate = action.payload;
    },
    setSearchDate(state, action) {
      state.searchDate = action.payload;
    },
    setCustomSearchText(state, action) {
  state.customSearchText = action.payload;
},

    setSelectedDateRange(state, action) {
      state.selectedDateRange = action.payload;
    },
    toggleDateFilter(state) {
      state.dateFilterOpen = !state.dateFilterOpen;
    },
    setSortOrder(state, action) {
      state.sortOrder = action.payload;
    },
 selectAllTransactions: (state, action) => {
    const transactionIds = action.payload;
    state.selectedTransactions = [...new Set([...state.selectedTransactions, ...transactionIds])];
  },
  
  deselectAllTransactions: (state, action) => {
    const transactionIds = action.payload;
    state.selectedTransactions = state.selectedTransactions.filter(
      id => !transactionIds.includes(id)
    );
  },
    // Select transactions (checkboxes)
    toggleSelectTransaction(state, action) {
      const transactionId = action.payload;
      if (state.selectedTransactions.includes(transactionId)) {
        state.selectedTransactions = state.selectedTransactions.filter(id => id !== transactionId);
      } else {
        state.selectedTransactions = [...state.selectedTransactions, transactionId];
      }
    },
    toggleSelectAll(state) {
      if (state.allChecked) {
        state.selectedTransactions = [];
      } else {
        state.selectedTransactions = state.transactions.map(transaction => transaction.transactionId);
      }
      state.allChecked = !state.allChecked;
    },

    // Add transaction modal actions
    toggleAddTransactionModal(state, action) {
      state.isAddTransactionOpen = action.payload ?? !state.isAddTransactionOpen;
    },
    updateNewTransaction(state, action) {
      state.newTransaction = { ...state.newTransaction, ...action.payload };
    },
    resetNewTransaction(state) {
      state.newTransaction = initialState.newTransaction;
    },

    // Apply filters
    applyFilters(state) {
  let filteredTransactions = [...state.allTransactions];
// In your applyFilters reducer
 if (state.selectedAccount) {
    filteredTransactions = filteredTransactions.filter(transaction => {
      const accountIds = [
        transaction.accountId,
        transaction.bankAccountId,
        transaction.account_id,
        transaction.bank?.id
      ].map(id => id?.toString()); // Convert all to strings for comparison
      
      return accountIds.includes(state.selectedAccount.toString());
    });
  }


  // 1. Filter by date (if any)
  if (state.selectedDateRange) {
    let startDate = null, endDate = null;

    switch (state.selectedDateRange) {
      case 'last7days':
        startDate = new Date();
        startDate.setDate(startDate.getDate() - 6);
        endDate = new Date();
        break;
      case 'currentMonth':
        startDate = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
        endDate = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0);
        break;
      case 'previousMonth':
        startDate = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);
        endDate = new Date(new Date().getFullYear(), new Date().getMonth(), 0);
        break;
      case 'last6months': // New option
        startDate = new Date();
        startDate.setMonth(startDate.getMonth() - 6);
        startDate.setDate(1);
        endDate = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0);
        break;
      case 'custom':
        if (state.searchDate?.start && state.searchDate?.end) {
          startDate = new Date(state.searchDate.start);
          endDate = new Date(state.searchDate.end);
        }
        break;
    }

    if (startDate || endDate) {
      filteredTransactions = filteredTransactions.filter(transaction => {
        const txDate = new Date(transaction.transactionDate);
        return (!startDate || txDate >= startDate) &&
               (!endDate || txDate <= endDate);
      });
    }
  }

  // 2. Filter by custom search (e.g., "Spotify")
  if (state.customSearchText) {
    const search = state.customSearchText.toLowerCase();
    filteredTransactions = filteredTransactions.filter(transaction =>
      transaction.name?.toLowerCase().includes(search) ||
      transaction.description?.toLowerCase().includes(search) // optionally include description
    );
  }

  state.transactions = filteredTransactions;
},

  setSelectedCategory(state, action) {
      state.selectedCategory = action.payload;
    },
    resetCategoryMonthlyExpenses(state) {
            state.categoryMonthlyExpenses = [];
            state.loadingCategoryExpenses = false;
        },
    setSelectedSubCategoryId(state, action) { // Add this reducer
      state.selectedSubCategoryId = action.payload;
    },
    setSelectedCategoryId(state, action) {
      state.selectedCategoryId = action.payload;
    },
    setOpenCategoryModal(state, action) {
      state.openCategoryModal = action.payload;
    },
fetchCategoryMonthlyExpensesStart(state) {
    state.loadingCategoryExpenses = true;
    state.errorCategoryExpenses = null;
  },
  fetchCategoryMonthlyExpensesSuccess(state, action) {
    state.categoryMonthlyExpenses = action.payload;
    state.loadingCategoryExpenses = false;
  },
  fetchCategoryMonthlyExpensesFailure(state, action) {
    state.loadingCategoryExpenses = false;
    state.errorCategoryExpenses = action.payload;
  },

    // Add new transaction
    addTransactionSuccess(state, action) {
      state.transactions = [...state.transactions, action.payload];
      state.allTransactions = [...state.allTransactions, action.payload];
    },
    addTransaction(state, action) {
      state.newTransaction = action.payload;
    },
    setTransactions: (state, action) => {
      state.transactions = action.payload;
      state.loading = false;
      state.error = null;
    },
    setError: (state, action) => {
      state.error = action.payload;
      state.loading = false;
    },
  },
 extraReducers: (builder) => {
    builder.addCase('transactions/getReconciledTransactionsSuccess', (state, action) => {
      console.log('Updating state with reconciled data for:', action.payload.reconcileId);
      console.log('Data received:', action.payload.transactions);
      
      // Make sure we're not overwriting existing data
      state.reconciledTransactionsById = {
        ...state.reconciledTransactionsById,
        [action.payload.reconcileId]: action.payload.transactions
      };
    });
  }
});

export const {
  fetchTransactionsStart,
  fetchTransactionsSuccess,
  fetchTransactionsFailure,
  setSelectedTransaction,
  updateTransactionRequest,
  setOpenModal,
  toggleCardVisibility,
  setSearchName,
  setCustomSearchText,
  setStartDate,
  setEndDate,
  setSearchDate,
  setSelectedDateRange,
  toggleDateFilter,
  setSortOrder,
  toggleSelectTransaction,
  toggleSelectAll,
   selectAllTransactions,
  deselectAllTransactions,
  toggleAddTransactionModal,
  updateNewTransaction,
  resetNewTransaction,
  applyFilters,
  addTransactionSuccess,
  addTransaction,
  fetchAccountsRequest,
  fetchAccountsSuccess,
  fetchAccountsFailure,
   fetchHiddenTransactionsStart,
  fetchHiddenTransactionsSuccess,
  fetchHiddenTransactionsFailure,
  fetchTransactionSummarySuccess,
  fetchTransactionSummaryFailure,
  fetchTransactionSummaryStart,
  updateTransactionStart,
  updateTransactionSuccess,
  updateTransactionFailure,
  deleteTransactionSuccess,
  deleteTransactionFailure,
  hideFromBudgetSuccess,
  hideFromBudgetFailure,
  resetHideFromBudgetStatus,
  setPage,
  setPageSize,
   fetchCategoryMonthlyExpensesStart,
  fetchCategoryMonthlyExpensesSuccess,
  fetchCategoryMonthlyExpensesFailure,
   setOpenCategoryModal,
    setSelectedCategory,
    setSelectedSubCategoryId, 
    setSelectedCategoryId,
    resetCategoryMonthlyExpenses,
    fetchSubCategoryIconStart,
  fetchSubCategoryIconSuccess,
  fetchSubCategoryIconFailure,
  clearSubCategoryIcon,
  fetchAllIconsStart,
  fetchAllIconsSuccess,
  fetchAllIconsFailure,
  clearIcons,
  setSelectedAccount,
  setAppendMode,
  setTransactions,
  setError
} = transactionSlice.actions;

export default transactionSlice.reducer;
