import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { fetchBudgetDashboardData } from '../../../../logic/redux/budgetDashboardSlice';
import { useCacheStatus } from '../../../../logic/hooks/useCacheStatus';

const BudgetDashboard = ({ darkMode }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Get current date parameters
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1;
  const testUserId = 1; // Using test user ID as in original

  // Simple cache status check
  const cacheStatus = useCacheStatus('budgetSummary');

  // Fallback to component state
  const { summary: componentSummary, loading: componentLoading, error: componentError } = useSelector((state) => state.budgetDashboard || {});

  // Use cached data if available, otherwise use component data
  const summary = cacheStatus.isLoaded ? cacheStatus.data : componentSummary;
  const loading = cacheStatus.isLoading || componentLoading;
  const error = cacheStatus.hasError ? cacheStatus.error : componentError;

  // Cache-aware data fetching
  useEffect(() => {
    if (cacheStatus.isLoaded) {
      console.log('✅ Using cached budget summary data');
      return; // Already have cached data
    }

    if (!cacheStatus.isLoading && !componentLoading) {
      console.log('🔄 Budget summary not cached, fetching data');
      dispatch(fetchBudgetDashboardData({
        userId: testUserId,
        year: currentYear,
        month: currentMonth
      }));
    }
  }, [dispatch, cacheStatus.isLoaded, cacheStatus.isLoading, componentLoading, testUserId, currentYear, currentMonth]);

  const formatCurrency = (amount) =>
    new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount || 0);

  const getCurrentMonthName = () => {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[new Date().getMonth()]; // Use current month
  };
  
  const handleComponentClick = () => {
    navigate('/dashboard/budget');
  };

  // Calculate percentages for progress bars
  const calculateBudgetPercentage = () => {
    if (!summary || !summary.totalBudget || summary.totalBudget === 0) return 0;
    return Math.min(100, (summary.actualBudget / summary.totalBudget) * 100);
  };

  const calculateIncomePercentage = () => {
    if (!summary || !summary.actualIncome || !summary.actualBudget) return 0;
    const targetIncome = summary.actualBudget * 1.5; // Example target
    return Math.min(100, (summary.actualIncome / targetIncome) * 100);
  };

  if (loading) {
    return <div className="h-full flex items-center justify-center">Loading budget data...</div>;
  }

  if (error) {
    return <div className="h-full flex items-center justify-center text-red-500">{error}</div>;
  }

  if (!summary) {
    return (
      <div className="h-full flex flex-col items-center justify-center">
        <div className={`${ darkMode ? 'text-white' 
              : ' text-black'}`}>No budget data available</div>
        <button
          onClick={() => {
            const currentDate = new Date();
            const currentYear = currentDate.getFullYear();
            const currentMonth = currentDate.getMonth() + 1;
            dispatch(fetchBudgetDashboardData({ userId: 1, year: currentYear, month: currentMonth }));
          }}
          className={`${
            darkMode
              ? 'bg-gray-700 hover:bg-gray-600 text-white'
              : 'bg-[#8bc34a] hover:bg-[#6ec122] text-white'
          } py-2 px-4 rounded flex items-center`}        >
          Retry
        </button>
      </div>
    );
  }

  const budgetUsagePercentage = calculateBudgetPercentage();
  const incomePercentage = calculateIncomePercentage();

  return (
    <div className={`h-full flex flex-col ${darkMode ? 'text-gray-100 bg-gray-800' : 'text-gray-900 bg-gray-100'}`}>
      <div className="flex justify-between items-center mb-4">
        <div className="text-lg font-semibold">{getCurrentMonthName()} Budget</div>
        {/* <button 
          onClick={(e) => {
            e.stopPropagation(); // Prevent triggering the parent onClick
            handleComponentClick();
          }}
          className="text-sm text-blue-500 hover:text-blue-700 focus:outline-none"
        >
          View details
        </button> */}
      </div>
      <div  onClick={handleComponentClick} style={{ cursor: 'pointer' }}>

      <div className={`flex-grow bg-white rounded-lg shadow ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
        {/* Budget Progress Section */}
        <div className={`px-4 py-3 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className={`text-sm mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>Budget Overview</div>
          <div className={`w-full rounded-full h-2.5 ${darkMode ? 'bg-gray-700' : 'bg-gray-200'}`}>{formatCurrency(summary.totalBudget)}</div>
          
          <div className="flex justify-between mt-3 mb-2">
            <div className="text-sm text-gray-500">Budget Usage</div>
            <div className="text-sm font-medium">
              {budgetUsagePercentage.toFixed(1)}%
            </div>
          </div>
          
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className={`h-2.5 rounded-full ${
                budgetUsagePercentage > 85
                  ? 'bg-red-500'
                  : budgetUsagePercentage > 65
                  ? 'bg-yellow-500'
                  : 'bg-green-500'
              }`}
              style={{ width: `${budgetUsagePercentage}%` }}
            ></div>
          </div>
          
          <div className="flex justify-between mt-2 text-xs text-gray-500">
            <span>{formatCurrency(summary.actualBudget)} used</span>
            <span>{formatCurrency(summary.totalBudget)} total</span>
          </div>
        </div>

        {/* Income Progress Section */}
        <div className={`px-4 py-3 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="flex justify-between mb-2">
            <div className="text-sm text-gray-500">Income Progress</div>
            <div className="text-sm font-medium">
              {incomePercentage.toFixed(1)}%
            </div>
          </div>
          
          <div className={`w-full rounded-full h-2.5 ${darkMode ? 'bg-gray-700' : 'bg-gray-200'}`}>
            <div
              className="h-2.5 rounded-full bg-blue-500"
              style={{ width: `${incomePercentage}%` }}
            ></div>
          </div>
          
          <div className="flex justify-between mt-2 text-xs text-gray-500">
            <span>{formatCurrency(summary.actualIncome)} received</span>
            <span>Target: {formatCurrency(summary.actualBudget * 1.5)}</span>
          </div>
        </div>
</div>
      </div>
    </div>
  );
};

export default BudgetDashboard;