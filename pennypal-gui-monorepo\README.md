# README #

This README would normally document whatever steps are necessary to get your application up and running.

### What is this repository for? ###

* Quick summary
* Version
* [Learn Markdown](https://bitbucket.org/tutorials/markdowndemo)

### How do I get set up? ###

* Summary of set up
* Configuration
* Dependencies
* Database configuration
* How to run tests
* Deployment instructions

### Contribution guidelines ###

* Writing tests
* Code review
* Other guidelines

### Who do I talk to? ###

* Repo owner or admin
* Other community or team contact


VITEST coverage report: 

packages/web/coverage/
├── index.html          # Main HTML report (open this in browser)
├── coverage-final.json # JSON format for tools
└── lcov-report/        # LCOV format files
    ├── index.html
    ├── base.css
    └── ... (other assets)
9. View HTML Coverage Report
bash# After running coverage, open the HTML report
open packages/web/coverage/index.html

# Or on Linux/Windows
xdg-open packages/web/coverage/index.html
# or
start packages/web/coverage/index.html
The HTML report will show you:

File-by-file coverage percentages
Line-by-line highlighting (covered/uncovered)
Branch coverage details
Function coverage information

Try running npm run test:coverage now and check out the HTML report! 📊

