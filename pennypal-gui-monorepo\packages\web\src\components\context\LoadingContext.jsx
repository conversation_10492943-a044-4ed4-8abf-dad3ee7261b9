// src/context/LoadingContext.jsx
import React, { createContext, useContext, useState } from 'react';

const LoadingContext = createContext();

export const LoadingProvider = ({ children }) => {
  const [loadingStates, setLoadingStates] = useState({});

  const setLoading = (pageId, isLoading) => {
    setLoadingStates((prev) => ({
      ...prev,
      [pageId]: isLoading,
    }));
  };

  return (
    <LoadingContext.Provider value={{ loadingStates, setLoading }}>
      {children}
    </LoadingContext.Provider>
  );
};

export const useLoading = () => {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
};