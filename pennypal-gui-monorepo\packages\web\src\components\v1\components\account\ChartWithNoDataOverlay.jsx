// ChartWithNoDataOverlay.jsx
import React from "react";
import { useSelector } from "react-redux";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faChartLine } from "@fortawesome/free-solid-svg-icons";
import PortfolioChart from "./PortfolioChart";

const ChartWithNoDataOverlay = ({
  darkMode,
  currentTheme,
  accounts,
  selectedTimePeriod,
  selectedChartType,
  chartView,         // <-- Accept this prop
  userId,            // <-- Accept this prop
  onGetStarted,      // <-- add this prop
  ...props
}) => {
  const hasNoAccounts = useSelector(state => state.accountChart.hasNoAccounts);
  const hasNoDataForCurrentType = useSelector(state => state.accountChart.hasNoDataForCurrentType);
  const shouldShowMockData = hasNoAccounts || hasNoDataForCurrentType;

  // Overlay for demo/mock data
  const DemoDataOverlay = () => (
    <div className="absolute inset-0 flex items-center justify-center rounded-md z-10">
      <div className="absolute inset-0 bg-gradient-to-br from-gray-200/10 to-gray-100/10 backdrop-blur-sm rounded-md"></div>
      <div className={`relative p-8 rounded-2xl border backdrop-blur-md shadow-2xl text-center max-w-sm ${
        darkMode 
          ? 'bg-gray-800/90 border-gray-600/50 text-white' 
          : 'bg-white/90 border-gray-200/50 text-gray-800'
      }`}>
        <div className="mb-4">
          <div className={`w-16 h-16 mx-auto rounded-full flex items-center justify-center ${
            darkMode ? 'bg-blue-500/20' : 'bg-blue-50'
          }`}>
            <FontAwesomeIcon 
              icon={faChartLine} 
              className="text-blue-500 text-2xl"
            />
          </div>
        </div>
        <h4 className="text-xl font-semibold mb-3">No Data Available</h4>
        <p className={`text-sm mb-4 leading-relaxed ${
          darkMode ? 'text-gray-300' : 'text-gray-600'
        }`}>
          No accounts found or not enough data for the selected time period.<br />
          Link your financial accounts to see real-time data and insights.
        </p>
        <div 
          onClick={onGetStarted} // <-- use the handler here
          className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-2 rounded-lg font-medium cursor-pointer hover:from-blue-600 hover:to-blue-700 transition-all duration-200"
        >
          Get Started
        </div>
      </div>
    </div>
  );

  return (
    <div className="relative min-h-[320px]">
      {/* Blurred chart as background if mock data */}
      {shouldShowMockData && (
        <div style={{ filter: 'blur(6px)', opacity: 0.7, pointerEvents: 'none' }}>
          <PortfolioChart
            darkMode={darkMode}
            accounts={accounts}
            selectedTimePeriod={selectedTimePeriod}
            accountType={selectedChartType}
            chartView={chartView}           // <-- Use the prop
            currentTheme={currentTheme}
            userId={userId}
            {...props}
          />
        </div>
      )}
      {/* Main chart (always rendered, but overlay covers it if mock) */}
      <div className="absolute inset-0">
        {shouldShowMockData && <DemoDataOverlay />}
      </div>
      {/* If you want to show the real chart when data exists, you can render it here as well */}
      {!shouldShowMockData && (
        <PortfolioChart
          darkMode={darkMode}
          accounts={accounts}
          selectedTimePeriod={selectedTimePeriod}
          accountType={selectedChartType}
          chartView={chartView}
          currentTheme={currentTheme}
          userId={userId}
          {...props}
        />
      )}
    </div>
  );
};

export default ChartWithNoDataOverlay;