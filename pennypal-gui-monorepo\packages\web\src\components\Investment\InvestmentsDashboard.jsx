import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { TrendingUp,TrendingDown,DollarSign,PieChart,Activity,RefreshCw,Calendar,Search,
  Filter,ArrowUpRight, ArrowDownRight, Eye,Wallet,Target,BarChart3,Clock,AlertCircle} from 'lucide-react';
import {syncInvestmentDataRequest,fetchUserInvestmentsRequest,fetchDailyInvestmentStocksRequest,
  fetchPortfolioSummaryRequest,fetchInvestmentByTickerRequest,resetInvestmentStatus,setFilters
} from '../../../../logic/redux/investmentsSlice';
import InvestmentsChart from './InvestmentsChart'; 
import BankIcon from '../../components/v1/components/account/BankIcon'
const InvestmentDashboard = () => {
  console.log("screen is investmentdashboard")
  const dispatch = useDispatch();
  const {userInvestments,dailyInvestmentStocks,portfolioSummary,
    investmentByTicker,loading,syncLoading,error,success,message,filters
  } = useSelector((state) => state.investment);

  // Local state for UI
  const [selectedTab, setSelectedTab] = useState('overview');
  const [searchTicker, setSearchTicker] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Initialize dashboard data
  useEffect(() => {
    dispatch(fetchPortfolioSummaryRequest());
    dispatch(fetchUserInvestmentsRequest());
    dispatch(fetchDailyInvestmentStocksRequest());
  },[]);

  // Handle sync
  const handleSync = () => {
    dispatch(syncInvestmentDataRequest());
  };
const STOCK_ICON_MAP = {
  'aapl': 'apple',
  'tsla': 'tesla',
  'msft': 'microsoft',
  'googl': 'google',
  'amzn': 'amazon',
  'mcd': 'McDonalds',
  'meta': 'facebook',
  'fb': 'facebook',
  'nflx': 'netflix',
  'spot': 'spotify',
  'pypl':'PayPal',
  'uber': 'uber',
  'lyft': 'lyft',
  'aal':'american airlines',
  'nvda': 'nvidia', // You might need to add this to your icon list

};

  // Handle search
  const handleTickerSearch = () => {
    if (searchTicker.trim()) {
      dispatch(fetchInvestmentByTickerRequest(searchTicker.toUpperCase()));
    }
  };
const getStockIcon = (ticker) => {
  if (!ticker) return null;
  
  const normalizedTicker = ticker.toLowerCase();
  const iconName = STOCK_ICON_MAP[normalizedTicker];
  
  if (iconName) {
    return (
      <BankIcon
        institutionName={iconName}
        accountType="investment"
        sizeClass="md"
        className="rounded-xl"
        trackingId={`stock-${ticker}`}
      />
    );
  }
  
  // Fallback to text-based icon
  return (
    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center text-white font-bold">
      {ticker.substring(0, 2)}
    </div>
  );
};

  // Handle filter apply
  const handleApplyFilters = () => {
    dispatch(setFilters({ date: dateFilter, ticker: searchTicker }));
    dispatch(fetchDailyInvestmentStocksRequest({ date: dateFilter, ticker: searchTicker }));
    setShowFilters(false);
  };

  // Format currency
  const formatCurrency = (amount) => {
    if (amount === null || amount === undefined) return '$0.00';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Format percentage
  const formatPercentage = (value) => {
    if (value === null || value === undefined) return '0.00%';
    return `${value.toFixed(2)}%`;
  };

  // Get trend color
  const getTrendColor = (value) => {
    if (value === null || value === undefined) return 'text-slate-500';
    return value >= 0 ? 'text-green-500' : 'text-red-500';
  };

  // Get trend icon
  const getTrendIcon = (value) => {
    if (value === null || value === undefined) {
      return <Activity className="w-4 h-4 text-slate-500" />;
    }
    return value >= 0 ? 
      <ArrowUpRight className="w-4 h-4 text-green-500" /> : 
      <ArrowDownRight className="w-4 h-4 text-red-500" />;
  };

  // Safe access to portfolio summary
  const safePortfolioSummary = portfolioSummary || {};
  const safeUserInvestments = Array.isArray(userInvestments) ? userInvestments : [];
  const safeDailyStocks = Array.isArray(dailyInvestmentStocks) ? dailyInvestmentStocks : [];

  // Debug logs
  console.log('Dashboard Render - userInvestments:', userInvestments);
  console.log('Dashboard Render - dailyInvestmentStocks:', dailyInvestmentStocks);
  console.log('Dashboard Render - portfolioSummary:', portfolioSummary);
  console.log('Dashboard Render - safeUserInvestments length:', safeUserInvestments.length);
  console.log('Dashboard Render - safeDailyStocks length:', safeDailyStocks.length);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-8">
      <div>
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-xl p-6 mb-6 border border-slate-200">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold text-slate-800 flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                  <BarChart3 className="w-6 h-6 text-white" />
                </div>
                Investment Dashboard
              </h1>
              <p className="text-slate-600 mt-1">Track and manage your investment portfolio</p>
            </div>
            
            <div className="flex items-center gap-3">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2 px-4 py-2 bg-slate-100 hover:bg-slate-200 rounded-xl transition-colors"
              >
                <Filter className="w-4 h-4" />
                Filters
              </button>
              
              <button
                onClick={handleSync}
                disabled={syncLoading}
                className="flex items-center gap-2 px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all shadow-lg disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 ${syncLoading ? 'animate-spin' : ''}`} />
                {syncLoading ? 'Syncing...' : 'Sync Data'}
              </button>
            </div>
          </div>

          {/* Filters Panel */}
          {showFilters && (
            <div className="mt-6 p-4 bg-slate-50 rounded-xl border border-slate-200">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Search Ticker
                  </label>
                  <div className="flex gap-2">
                    <input
                      type="text"
                      value={searchTicker}
                      onChange={(e) => setSearchTicker(e.target.value)}
                      placeholder="e.g., AAPL, TSLA"
                      className="flex-1 px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <button
                      onClick={handleTickerSearch}
                      className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                    >
                      <Search className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Date Filter
                  </label>
                  <input
                    type="date"
                    value={dateFilter}
                    onChange={(e) => setDateFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div className="flex items-end">
                  <button
                    onClick={handleApplyFilters}
                    className="w-full px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                  >
                    Apply Filters
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Status Messages */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-6 flex items-center gap-3">
            <AlertCircle className="w-5 h-5 text-red-500" />
            <span className="text-red-700">{error}</span>
          </div>
        )}

        {/* {success && message && (
          <div className="bg-green-50 border border-green-200 rounded-xl p-4 mb-6 flex items-center gap-3">
            <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
              <div className="w-2 h-2 bg-white rounded-full"></div>
            </div>
            <span className="text-green-700">{message}</span>
          </div>
        )} */}

        {/* Portfolio Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-2xl shadow-lg p-6 border border-slate-200 hover:shadow-xl transition-all">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                <Wallet className="w-6 h-6 text-green-600" />
              </div>
              {getTrendIcon(safePortfolioSummary.totalGainLoss)}
            </div>
            <h3 className="text-slate-600 text-sm font-medium">Total Value</h3>
            <p className="text-2xl font-bold text-slate-800">
              {formatCurrency(safePortfolioSummary.totalValue)}
            </p>
            <p className={`text-sm ${getTrendColor(safePortfolioSummary.totalGainLoss)}`}>
              {formatCurrency(safePortfolioSummary.totalGainLoss)} 
              ({formatPercentage(safePortfolioSummary.totalGainLossPercent)})
            </p>
          </div>

          <div className="bg-white rounded-2xl shadow-lg p-6 border border-slate-200 hover:shadow-xl transition-all">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <Target className="w-6 h-6 text-blue-600" />
              </div>
              <TrendingUp className="w-5 h-5 text-blue-500" />
            </div>
            <h3 className="text-slate-600 text-sm font-medium">Total Cost Basis</h3>
            <p className="text-2xl font-bold text-slate-800">
              {formatCurrency(safePortfolioSummary.totalCostBasis)}
            </p>
            <p className="text-sm text-slate-500">Principal amount</p>
          </div>

          <div className="bg-white rounded-2xl shadow-lg p-6 border border-slate-200 hover:shadow-xl transition-all">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                <PieChart className="w-6 h-6 text-purple-600" />
              </div>
              <Activity className="w-5 h-5 text-purple-500" />
            </div>
            <h3 className="text-slate-600 text-sm font-medium">Total Positions</h3>
            <p className="text-2xl font-bold text-slate-800">{safePortfolioSummary.totalPositions || safeUserInvestments.length}</p>
            <p className="text-sm text-slate-500">Active investments</p>
          </div>

          <div className="bg-white rounded-2xl shadow-lg p-6 border border-slate-200 hover:shadow-xl transition-all">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                <Clock className="w-6 h-6 text-orange-600" />
              </div>
              <Activity className="w-5 h-5 text-orange-500" />
            </div>
            <h3 className="text-slate-600 text-sm font-medium">Daily Stocks</h3>
            <p className="text-2xl font-bold text-slate-800">{safeDailyStocks.length}</p>
            <p className="text-sm text-slate-500">Today's tracked stocks</p>
          </div>
        </div>
<div className="mb-8">
  <InvestmentsChart />
</div>
        {/* Navigation Tabs */}
        <div className="bg-white rounded-2xl shadow-lg mb-6 border border-slate-200">
          <div className="flex flex-wrap gap-1 p-2">
            {[
              { id: 'overview', label: 'Portfolio Overview', icon: BarChart3 },
              { id: 'holdings', label: 'My Holdings', icon: Wallet },
              { id: 'daily', label: 'Daily Stocks', icon: Clock },
              { id: 'search', label: 'Stock Search', icon: Search }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-3 rounded-xl transition-all ${
                  selectedTab === tab.id
                    ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg'
                    : 'text-slate-600 hover:bg-slate-100'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Content Area */}
        <div className="bg-white rounded-2xl shadow-lg p-6 border border-slate-200">
          {selectedTab === 'overview' && (
            <div>
              <h2 className="text-xl font-bold text-slate-800 mb-6 flex items-center gap-2">
                <BarChart3 className="w-5 h-5 text-blue-500" />
                Portfolio Overview
              </h2>
              
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                  <span className="ml-3 text-slate-600">Loading portfolio data...</span>
                </div>
              ) : (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="font-semibold text-slate-700">Portfolio Summary</h3>
                    {safePortfolioSummary && Object.keys(safePortfolioSummary).length > 0 ? (
                      <div className="space-y-3">
                        <div className="flex justify-between items-center p-3 bg-slate-50 rounded-lg">
                          <span className="text-slate-600">Total Cost Basis</span>
                          <span className="font-semibold">{formatCurrency(safePortfolioSummary.totalCostBasis)}</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-slate-50 rounded-lg">
                          <span className="text-slate-600">Current Value</span>
                          <span className="font-semibold">{formatCurrency(safePortfolioSummary.totalValue)}</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-slate-50 rounded-lg">
                          <span className="text-slate-600">Total Gain/Loss</span>
                          <span className={`font-semibold ${getTrendColor(safePortfolioSummary.totalGainLoss)}`}>
                            {formatCurrency(safePortfolioSummary.totalGainLoss)}
                          </span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-slate-50 rounded-lg">
                          <span className="text-slate-600">Gain/Loss %</span>
                          <span className={`font-semibold ${getTrendColor(safePortfolioSummary.totalGainLossPercent)}`}>
                            {formatPercentage(safePortfolioSummary.totalGainLossPercent)}
                          </span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-slate-50 rounded-lg">
                          <span className="text-slate-600">Total Positions</span>
                          <span className="font-semibold">{safePortfolioSummary.totalPositions || 0}</span>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8 text-slate-500">
                        <PieChart className="w-12 h-12 mx-auto mb-3 opacity-50" />
                        <p>No portfolio data available</p>
                        <p className="text-sm">Sync your data to see portfolio summary</p>
                      </div>
                    )}
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="font-semibold text-slate-700">Quick Stats</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-lg">
                        <div className="text-2xl font-bold text-green-700">{safeUserInvestments.length}</div>
                        <div className="text-sm text-green-600">Total Holdings</div>
                      </div>
                      <div className="p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg">
                        <div className="text-2xl font-bold text-blue-700">{safeDailyStocks.length}</div>
                        <div className="text-sm text-blue-600">Daily Stocks</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {selectedTab === 'holdings' && (
            <div>
              <h2 className="text-xl font-bold text-slate-800 mb-6 flex items-center gap-2">
                <Wallet className="w-5 h-5 text-blue-500" />
                My Holdings ({safeUserInvestments.length})
              </h2>
              
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                  <span className="ml-3 text-slate-600">Loading holdings...</span>
                </div>
              ) : safeUserInvestments.length > 0 ? (
                <div className="space-y-4">
                  {safeUserInvestments.map((investment, index) => (
                    <div key={investment.id || index} className="p-4 border border-slate-200 rounded-xl hover:shadow-md transition-all">
                      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                        <div className="flex items-center gap-4">
                           {getStockIcon(investment.ticker || investment.formattedTicker)}
                          {/* <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center text-white font-bold">
                            {(investment.ticker || investment.formattedTicker || 'ST').substring(0, 2)}
                          </div> */}
                          <div>
                            <h3 className="font-semibold text-slate-800">{investment.ticker || investment.formattedTicker || 'Unknown'}</h3>
                            <p className="text-sm text-slate-600">{investment.security_name || investment.securityName || 'Company Name'}</p>
                            <p className="text-xs text-slate-500">{investment.securityTypeDisplayName || investment.security_type || 'Security Type'}</p>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                          <div>
                            <div className="text-slate-600">Shares</div>
                            <div className="font-semibold">{investment.quantity || 0}</div>
                          </div>
                          <div>
                            <div className="text-slate-600">Cost Basis</div>
                            <div className="font-semibold">{formatCurrency(investment.cost_basis || investment.costBasis)}</div>
                          </div>
                          <div>
                            <div className="text-slate-600">Current Price</div>
                            <div className="font-semibold">{formatCurrency(investment.current_price || investment.currentPrice)}</div>
                          </div>
                          <div>
                            <div className="text-slate-600">Market Value</div>
                            <div className="font-semibold">{formatCurrency(investment.value || investment.marketValue)}</div>
                          </div>
                          <div>
                            <div className="text-slate-600">Gain/Loss</div>
                            <div className={`font-semibold ${getTrendColor(investment.total_gain || investment.unrealizedGainLoss || investment.gainLoss)}`}>
                              {formatCurrency(investment.total_gain || investment.unrealizedGainLoss || investment.gainLoss)}
                              <div className="text-xs">
                                ({formatPercentage(investment.total_gain_percent || investment.unrealizedGainLossPercent || investment.gainLossPercent)})
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 text-slate-500">
                  <Wallet className="w-16 h-16 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium">No holdings found</p>
                  <p>Your investment holdings will appear here once you sync your data</p>
                </div>
              )}
            </div>
          )}

       
{selectedTab === 'daily' && (
  <div>
    <h2 className="text-xl font-bold text-slate-800 mb-6 flex items-center gap-2">
      <Clock className="w-5 h-5 text-blue-500" />
      Daily Investment Stocks ({safeDailyStocks.length})
    </h2>
    
    {loading ? (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-slate-600">Loading daily stocks...</span>
      </div>
    ) : safeDailyStocks.length > 0 ? (
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-slate-200">
              <th className="text-left py-3 px-4 font-semibold text-slate-700">Ticker</th>
              <th className="text-left py-3 px-4 font-semibold text-slate-700">Date</th>
              <th className="text-right py-3 px-4 font-semibold text-slate-700">Price</th>
              <th className="text-right py-3 px-4 font-semibold text-slate-700">Change %</th>
              <th className="text-right py-3 px-4 font-semibold text-slate-700">Volume</th>
            </tr>
          </thead>
          <tbody>
            {safeDailyStocks.map((stock, index) => (
              <tr key={stock.id || index} className="border-b border-slate-100 hover:bg-slate-50 transition-colors">
                <td className="py-3 px-4">
                  <div className="flex items-center gap-3">
                  {getStockIcon(stock.ticker) || (
      <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white text-xs font-bold">
        {(stock.ticker || 'ST').substring(0, 2)}
      </div>
    )}
                    <span className="font-medium">{stock.ticker}</span>
                  </div>
                </td>
                <td className="py-3 px-4 text-slate-600">
                  {/* Use timestamp field for date */}
                  {stock.timestamp ? new Date(stock.timestamp).toLocaleDateString() : 
                   stock.date ? new Date(stock.date).toLocaleDateString() : 'N/A'}
                </td>
                <td className="py-3 px-4 text-right font-medium">
                  {formatCurrency(stock.price || stock.close)}
                </td>
                <td className={`py-3 px-4 text-right font-medium ${getTrendColor(stock.percentChange || stock.change || stock.changePercent)}`}>
                  {/* Use percentChange field for change percentage */}
                  {(stock.percentChange !== null && stock.percentChange !== undefined) ? 
                    `${stock.percentChange > 0 ? '+' : ''}${stock.percentChange.toFixed(2)}%` :
                    (stock.change || stock.changePercent) ? 
                      `${(stock.change || stock.changePercent) > 0 ? '+' : ''}${(stock.change || stock.changePercent).toFixed(2)}%` : 
                      'N/A'
                  }
                </td>
                <td className="py-3 px-4 text-right text-slate-600">
                  {/* Use quantity field for volume */}
                  {stock.quantity ? stock.quantity.toLocaleString() : 
                   stock.volume ? stock.volume.toLocaleString() : 'N/A'}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    ) : (
      <div className="text-center py-12 text-slate-500">
        <Clock className="w-16 h-16 mx-auto mb-4 opacity-50" />
        <p className="text-lg font-medium">No daily stocks data</p>
        <p>Daily stock information will appear here</p>
      </div>
    )}
  </div>
)}

          {selectedTab === 'search' && (
            <div>
              <h2 className="text-xl font-bold text-slate-800 mb-6 flex items-center gap-2">
                <Search className="w-5 h-5 text-blue-500" />
                Stock Search
              </h2>
              
              <div className="mb-6">
                <div className="flex gap-3 max-w-md">
                  <input
                    type="text"
                    value={searchTicker}
                    onChange={(e) => setSearchTicker(e.target.value)}
                    placeholder="Enter ticker symbol (e.g., AAPL, TSLA)"
                    className="flex-1 px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    onKeyPress={(e) => e.key === 'Enter' && handleTickerSearch()}
                  />
                  <button
                    onClick={handleTickerSearch}
                    disabled={loading}
                    className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all shadow-lg disabled:opacity-50"
                  >
                    <Search className="w-5 h-5" />
                  </button>
                </div>
              </div>

              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                  <span className="ml-3 text-slate-600">Searching...</span>
                </div>
              ) : investmentByTicker ? (
                <div className="p-6 border border-slate-200 rounded-xl bg-slate-50">
                  <div className="flex items-center gap-4 mb-6">
                   {getStockIcon(investmentByTicker.ticker || investmentByTicker.formattedTicker) || (
    <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center text-white font-bold text-xl">
      {(investmentByTicker.ticker || investmentByTicker.formattedTicker || 'ST').substring(0, 2)}
    </div>
  )}
                    <div>
                      <h3 className="text-2xl font-bold text-slate-800">{investmentByTicker.ticker || investmentByTicker.formattedTicker}</h3>
                      <p className="text-slate-600">{investmentByTicker.security_name || investmentByTicker.securityName || 'Company Name'}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="p-4 bg-white rounded-lg">
                      <div className="text-sm text-slate-600 mb-1">Shares Owned</div>
                      <div className="text-xl font-bold text-slate-800">{investmentByTicker.quantity || 0}</div>
                    </div>
                    <div className="p-4 bg-white rounded-lg">
                      <div className="text-sm text-slate-600 mb-1">Cost Basis</div>
                      <div className="text-xl font-bold text-slate-800">{formatCurrency(investmentByTicker.cost_basis || investmentByTicker.costBasis)}</div>
                    </div>
                    <div className="p-4 bg-white rounded-lg">
                      <div className="text-sm text-slate-600 mb-1">Current Price</div>
                      <div className="text-xl font-bold text-slate-800">{formatCurrency(investmentByTicker.current_price || investmentByTicker.currentPrice)}</div>
                    </div>
                    <div className="p-4 bg-white rounded-lg">
                      <div className="text-sm text-slate-600 mb-1">Market Value</div>
                      <div className="text-xl font-bold text-slate-800">{formatCurrency(investmentByTicker.value || investmentByTicker.marketValue)}</div>
                    </div>
                    <div className="p-4 bg-white rounded-lg">
                      <div className="text-sm text-slate-600 mb-1">Total Gain/Loss</div>
                      <div className={`text-xl font-bold ${getTrendColor(investmentByTicker.total_gain || investmentByTicker.unrealizedGainLoss || investmentByTicker.gainLoss)}`}>
                        {formatCurrency(investmentByTicker.total_gain || investmentByTicker.unrealizedGainLoss || investmentByTicker.gainLoss)}
                      </div>
                    </div>
                    <div className="p-4 bg-white rounded-lg">
                      <div className="text-sm text-slate-600 mb-1">Gain/Loss %</div>
                      <div className={`text-xl font-bold ${getTrendColor(investmentByTicker.total_gain_percent || investmentByTicker.unrealizedGainLossPercent || investmentByTicker.gainLossPercent)}`}>
                        {formatPercentage(investmentByTicker.total_gain_percent || investmentByTicker.unrealizedGainLossPercent || investmentByTicker.gainLossPercent)}
                      </div>
                    </div>
                    <div className="p-4 bg-white rounded-lg">
                      <div className="text-sm text-slate-600 mb-1">Security Type</div>
                      <div className="text-xl font-bold text-slate-800">{investmentByTicker.securityTypeDisplayName || investmentByTicker.security_type || 'N/A'}</div>
                    </div>
                    <div className="p-4 bg-white rounded-lg">
                      <div className="text-sm text-slate-600 mb-1">Last Updated</div>
                      <div className="text-xl font-bold text-slate-800">
                        {investmentByTicker.last_updated ? new Date(investmentByTicker.last_updated).toLocaleDateString() : 'N/A'}
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12 text-slate-500">
                  <Search className="w-16 h-16 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium">Search for a stock</p>
                  <p>Enter a ticker symbol above to search for investment details</p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="mt-8 text-center text-slate-500 text-sm">
          <p>Investment Dashboard • Last updated: {new Date().toLocaleString()}</p>
        </div>
      </div>
    </div>
  );
};

export default InvestmentDashboard;
