/* FamilySection-button.css */
/* Specific styles for the Send Invite button */

.invite-button {
    width: 100%;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    font-weight: 600;
    background-color: #8bc34a !important; /* blue-500 */
    color: #ffffff; /* white text for visibility */
    text-align: center;
    border: none;
    margin-top: 1rem;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }
  
  .invite-button:hover {
    background-color: #8bc34a !important; /* blue-500 */

  }
  
  .invite-button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5); /* blue focus ring */
  }
  
  .invite-button:disabled {
    background-color: #9ca3af; /* gray-400 */
    cursor: not-allowed;
    color: #8bc34a;
  }
  
  /* If you're having issues with text visibility, add these high-contrast overrides */
  .invite-button {
    color: #8bc34a !important; /* Force white text */
    text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.2); /* Add subtle text shadow for better readability */
  }