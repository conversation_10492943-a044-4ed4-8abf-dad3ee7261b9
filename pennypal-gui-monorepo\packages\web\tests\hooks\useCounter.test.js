import React from 'react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'

// Import your actual hooks
// Example: import { useCounter, useLocalStorage } from '../../src/hooks'

// Mock hook for demonstration
const useCounter = (initialValue = 0) => {
  const [count, setCount] = React.useState(initialValue)
  
  const increment = React.useCallback(() => {
    setCount(prev => prev + 1)
  }, [])
  
  const decrement = React.useCallback(() => {
    setCount(prev => prev - 1)
  }, [])
  
  const reset = React.useCallback(() => {
    setCount(initialValue)
  }, [initialValue])
  
  const setValue = React.useCallback((value) => {
    setCount(value)
  }, [])
  
  return {
    count,
    increment,
    decrement,
    reset,
    setValue
  }
}

describe('useCounter Hook', () => {
  describe('Initialization', () => {
    it('should initialize with default value', () => {
      const { result } = renderHook(() => useCounter())
      expect(result.current.count).toBe(0)
    })

    it('should initialize with custom value', () => {
      const { result } = renderHook(() => useCounter(10))
      expect(result.current.count).toBe(10)
    })
  })

  describe('Increment', () => {
    it('should increment count', () => {
      const { result } = renderHook(() => useCounter(0))
      
      act(() => {
        result.current.increment()
      })
      
      expect(result.current.count).toBe(1)
    })

    it('should increment multiple times', () => {
      const { result } = renderHook(() => useCounter(0))
      
      act(() => {
        result.current.increment()
        result.current.increment()
        result.current.increment()
      })
      
      expect(result.current.count).toBe(3)
    })
  })

  describe('Decrement', () => {
    it('should decrement count', () => {
      const { result } = renderHook(() => useCounter(5))
      
      act(() => {
        result.current.decrement()
      })
      
      expect(result.current.count).toBe(4)
    })

    it('should allow negative values', () => {
      const { result } = renderHook(() => useCounter(0))
      
      act(() => {
        result.current.decrement()
      })
      
      expect(result.current.count).toBe(-1)
    })
  })

  describe('Reset', () => {
    it('should reset to initial value', () => {
      const { result } = renderHook(() => useCounter(5))
      
      act(() => {
        result.current.increment()
        result.current.increment()
      })
      
      expect(result.current.count).toBe(7)
      
      act(() => {
        result.current.reset()
      })
      
      expect(result.current.count).toBe(5)
    })

    it('should reset to new initial value when prop changes', () => {
      let initialValue = 0
      const { result, rerender } = renderHook(() => useCounter(initialValue))
      
      act(() => {
        result.current.increment()
      })
      
      expect(result.current.count).toBe(1)
      
      // Change initial value and rerender
      initialValue = 10
      rerender()
      
      act(() => {
        result.current.reset()
      })
      
      expect(result.current.count).toBe(10)
    })
  })

  describe('Set Value', () => {
    it('should set specific value', () => {
      const { result } = renderHook(() => useCounter(0))
      
      act(() => {
        result.current.setValue(42)
      })
      
      expect(result.current.count).toBe(42)
    })

    it('should overwrite previous value', () => {
      const { result } = renderHook(() => useCounter(0))
      
      act(() => {
        result.current.increment()
        result.current.setValue(100)
      })
      
      expect(result.current.count).toBe(100)
    })
  })

  describe('Stability', () => {
    it('should have stable function references', () => {
      const { result, rerender } = renderHook(() => useCounter(0))
      
      const firstRender = {
        increment: result.current.increment,
        decrement: result.current.decrement,
        reset: result.current.reset,
        setValue: result.current.setValue
      }
      
      rerender()
      
      expect(result.current.increment).toBe(firstRender.increment)
      expect(result.current.decrement).toBe(firstRender.decrement)
      expect(result.current.reset).toBe(firstRender.reset)
      expect(result.current.setValue).toBe(firstRender.setValue)
    })
  })
})

// Example of testing a more complex hook
const useLocalStorage = (key, initialValue) => {
  const [storedValue, setStoredValue] = React.useState(() => {
    try {
      const item = window.localStorage.getItem(key)
      return item ? JSON.parse(item) : initialValue
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error)
      return initialValue
    }
  })

  const setValue = React.useCallback((value) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value
      setStoredValue(valueToStore)
      window.localStorage.setItem(key, JSON.stringify(valueToStore))
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error)
    }
  }, [key, storedValue])

  return [storedValue, setValue]
}

describe('useLocalStorage Hook', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    window.localStorage.clear()
    vi.clearAllMocks()
  })

  it('should return initial value when localStorage is empty', () => {
    const { result } = renderHook(() => useLocalStorage('test-key', 'initial'))
    expect(result.current[0]).toBe('initial')
  })

  it('should store and retrieve values', () => {
    const { result } = renderHook(() => useLocalStorage('test-key', ''))
    
    act(() => {
      result.current[1]('stored value')
    })
    
    expect(result.current[0]).toBe('stored value')
    expect(window.localStorage.setItem).toHaveBeenCalledWith(
      'test-key', 
      JSON.stringify('stored value')
    )
  })

  it('should handle functional updates', () => {
    const { result } = renderHook(() => useLocalStorage('counter', 0))
    
    act(() => {
      result.current[1](prev => prev + 1)
    })
    
    expect(result.current[0]).toBe(1)
  })
})