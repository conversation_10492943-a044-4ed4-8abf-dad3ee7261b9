// redux/accountDeltaSlice.js
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  monthlyDeltas: [],
  monthlyDeltasByAccountId: [],
  loading: false,
  error: null,
  lastFetched: null,
  selectedPeriod: {
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1,
  },
};

const accountDeltaSlice = createSlice({
  name: 'accountDelta',
  initialState,
  reducers: {
    // Monthly deltas actions
    fetchAccountBalanceMonthlyDeltas: (state, action) => {
      state.loading = true;
      state.error = null;
    },
    fetchAccountBalanceMonthlyDeltasSuccess: (state, action) => {
      state.loading = false;
      state.monthlyDeltas = action.payload;
      state.error = null;
      state.lastFetched = new Date().toISOString();
    },
    fetchAccountBalanceMonthlyDeltasFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
      state.monthlyDeltas = [];
    },
    
    // Monthly deltas by account ID actions
    fetchAccountBalanceMonthlyDeltasByAccountId: (state, action) => {
      state.loading = true;
      state.error = null;
    },
    fetchAccountBalanceMonthlyDeltasByAccountIdSuccess: (state, action) => {
      state.loading = false;
      state.monthlyDeltasByAccountId = action.payload;
      state.error = null;
      state.lastFetched = new Date().toISOString();
    },
    fetchAccountBalanceMonthlyDeltasByAccountIdFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
      state.monthlyDeltasByAccountId = [];
    },
    
    // UI state management
    setSelectedPeriod: (state, action) => {
      state.selectedPeriod = action.payload;
    },
    
    clearError: (state) => {
      state.error = null;
    },
    
    resetState: (state) => {
      return initialState;
    }
  },
});

export const {
  fetchAccountBalanceMonthlyDeltas,
  fetchAccountBalanceMonthlyDeltasSuccess,
  fetchAccountBalanceMonthlyDeltasFailure,
  fetchAccountBalanceMonthlyDeltasByAccountId,
  fetchAccountBalanceMonthlyDeltasByAccountIdSuccess,
  fetchAccountBalanceMonthlyDeltasByAccountIdFailure,
  setSelectedPeriod,
  clearError,
  resetState
} = accountDeltaSlice.actions;

export default accountDeltaSlice.reducer;

// Selectors
export const selectAccountDeltaState = (state) => state.accountDelta;
export const selectMonthlyDeltas = (state) => state.accountDelta.monthlyDeltas;
export const selectMonthlyDeltasByAccountId = (state) => state.accountDelta.monthlyDeltasByAccountId;
export const selectAccountDeltaLoading = (state) => state.accountDelta.loading;
export const selectAccountDeltaError = (state) => state.accountDelta.error;
export const selectSelectedPeriod = (state) => state.accountDelta.selectedPeriod;