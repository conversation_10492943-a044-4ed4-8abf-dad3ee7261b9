import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { 
  FaTimes,
  FaMoneyBillWave,
  FaCar,
  FaHome,
  FaUtensils,
  FaPlane,
  FaShoppingCart,
  FaChild,
  FaHeart,
  FaBusinessTime,
  FaHospital,
  FaGraduationCap,
  FaFilm,
  FaWallet,
  FaDollarSign,
  FaCreditCard,
  FaBuilding,
  FaEllipsisH,
  FaCheck
} from "react-icons/fa";

const EditBudgetPopupComponent = ({ budgetItem, onSave, onClose }) => {
  const dispatch = useDispatch();
  const { loading, error, networkError, categories, subcategories } = useSelector((state) => state.budget);
  
  // Icon mapping object
  const iconMap = {
    "FaMoneyBillWave": FaMoneyBillWave,
    "FaCar": FaCar,
    "FaHome": FaHome,
    "FaUtensils": FaUtensils,
    "FaPlane": FaPlane,
    "FaShoppingCart": FaShoppingCart,
    "FaChild": FaChild,
    "FaHeart": FaHeart,
    "FaBusinessTime": FaBusinessTime,
    "FaHospital": FaHospital,
    "FaGraduationCap": FaGraduationCap,
    "FaFilm": FaFilm,
    "FaWallet": FaWallet,
    "FaDollarSign": FaDollarSign,
    "FaCreditCard": FaCreditCard,
    "FaBuilding": FaBuilding,
    "FaMiscellaneous": FaEllipsisH
  };

  // State declarations
  const [newBudgetAmount, setNewBudgetAmount] = useState(budgetItem.allocated || 0);
  const [isRollover, setIsRollover] = useState(budgetItem.rollover ?? false); // Use nullish coalescing to handle undefined
  const [isExcluded, setIsExcluded] = useState(budgetItem.isExcluded ?? false);
  const [isEditingBudget, setIsEditingBudget] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // Helper function to render icon
  const renderIcon = (iconKey, className = "") => {
    const IconComponent = iconMap[iconKey] || iconMap["FaMiscellaneous"];
    return <IconComponent className={className} />;
  };

  // Fetch categories and subcategories if not loaded
  useEffect(() => {
    if (!categories || categories.length === 0) {
      dispatch({ type: 'budget/fetchCategories' });
    }
    if (!subcategories || subcategories.length === 0) {
      dispatch({ type: 'budget/fetchSubcategories' });
    }
  }, [dispatch, categories, subcategories]);

  // Get category and subcategory details
  const category = categories.find((cat) => cat.id?.toString() === budgetItem.categoryId?.toString());
  const subcategory = subcategories.find((sub) => sub.id?.toString() === (budgetItem.subcategoryId || "").toString());

  // Fallback names
  const categoryName = category?.category || category?.name || budgetItem.categoryName || "Unknown Category";
  const subcategoryName = subcategory?.subCategory || subcategory?.name || budgetItem.subcategoryName || budgetItem.customSubCategoryName || "Unknown Subcategory";
  const categoryIcon = category?.categoryIconKey || budgetItem.icon || "FaMiscellaneous";
  const subcategoryIcon = subcategory?.iconKey || budgetItem.icon || "FaMiscellaneous";

  // Slider change handler
  const handleSliderChange = (e) => {
    setNewBudgetAmount(parseInt(e.target.value, 10) || 0);
  };

  // Save the updated budget
  const handleUpdateBudget = () => {
    if (newBudgetAmount < 0) {
      return;
    }

    const updatedBudgetItem = {
      budgetId: budgetItem.id,
      categoryId: budgetItem.categoryId,
      subcategoryId: budgetItem.compositeKey,
      newBudget: newBudgetAmount,
      isRollover: isRollover,
      isExcluded,
    };

    setIsSubmitting(true);
    setShowSuccess(false);

    dispatch({ type: 'budget/saveBudget', payload: updatedBudgetItem });
  };

  // Monitor for successful budget update
  useEffect(() => {
    if (!loading && !error && !networkError && isSubmitting) {
      setShowSuccess(true);
      setIsSubmitting(false);
      
      setTimeout(() => {
        onClose();
      }, 1500);
    }
  }, [loading, error, networkError, isSubmitting, onClose]);

  // Show success message
  if (showSuccess) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center popup-overlay z-50">
        <div className="bg-white rounded-lg shadow-2xl w-full max-w-md p-8 text-center">
          <div className="text-green-500 text-6xl mb-4">
            <FaCheck />
          </div>
          <h3 className="text-xl font-bold mb-2 text-gray-800">Success!</h3>
          <p className="text-gray-600">Budget item has been updated successfully.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center popup-overlay z-50">
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-md max-h-screen overflow-hidden relative popup-container">
        {/* Header */}
        <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 relative">
          <h3 className="text-xl font-bold m-0 popup-title">
            Edit Budget
          </h3>
          <div 
            className="absolute top-4 right-4 cursor-pointer text-white text-xl hover:text-gray-200 transition-colors" 
            onClick={onClose}
            disabled={isSubmitting}
          >
            <FaTimes />
          </div>
        </div>

        {/* Error Display */}
        {(error || networkError) && (
          <div className="bg-red-50 border-l-4 border-red-500 p-4 mx-4 mt-4">
            <div className="text-red-700 text-sm">
              {error || networkError}
            </div>
          </div>
        )}

        {/* Content */}
        <div className="p-6 relative overflow-y-auto max-h-96">
          {/* Category (Non-editable) */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2 text-gray-700">Category</label>
            <div className="w-full p-3 border border-gray-300 rounded-md text-sm flex items-center bg-gray-100">
              <div className="flex items-center">
                {renderIcon(categoryIcon, "text-green-600 mr-2")}
                {categoryName}
              </div>
            </div>
          </div>

          {/* Subcategory (Non-editable) */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2 text-gray-700">Subcategory</label>
            <div className="w-full p-3 border border-gray-300 rounded-md text-sm flex items-center bg-gray-100">
              <div className="flex items-center">
                {renderIcon(subcategoryIcon, "text-green-600 mr-2")}
                {subcategoryName}
              </div>
            </div>
          </div>

          {/* Budget Amount Slider */}
          <div className="mb-6 flex flex-col">
            <div className="flex items-center justify-between mb-3">
              <label className="block text-sm font-medium text-gray-700">Budget Amount</label>
              {isEditingBudget ? (
                <input
                  type="number"
                  value={newBudgetAmount}
                  onChange={(e) =>
                    setNewBudgetAmount(Number(e.target.value) || 0)
                  }
                  onBlur={() => setIsEditingBudget(false)}
                  onKeyDown={(e) => e.key === 'Enter' && setIsEditingBudget(false)}
                  disabled={isSubmitting}
                  className={`text-right border border-gray-300 rounded p-2 w-28 font-medium ${!isSubmitting ? 'cursor-pointer focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200' : 'cursor-not-allowed opacity-50'}`}
                  autoFocus
                />
              ) : (
                <span
                  className={`${!isSubmitting ? 'cursor-pointer hover:text-green-700 hover:bg-green-50' : 'cursor-not-allowed opacity-50'} font-medium text-green-600 transition-colors px-2 py-1 rounded`}
                  onClick={() => !isSubmitting && setIsEditingBudget(true)}
                >
                  ${newBudgetAmount.toLocaleString()}
                </span>
              )}
            </div>
            <div className="relative">
              <input
                type="range"
                min="0"
                max="10000"
                step="100"
                value={newBudgetAmount}
                onChange={handleSliderChange}
                disabled={isSubmitting}
                className={`w-full h-2 bg-gray-200 rounded-lg appearance-none outline-none slider ${isSubmitting ? 'cursor-not-allowed opacity-50' : ''}`}
                style={{
                  background: `linear-gradient(to right, #10b981 0%, #10b981 ${(newBudgetAmount / 10000) * 100}%, #e5e7eb ${(newBudgetAmount / 10000) * 100}%, #e5e7eb 100%)`
                }}
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>$0</span>
                <span>$10,000</span>
              </div>
            </div>
          </div>

          {/* Rollover & Exclusion Toggles */}
          <div className="mb-4 flex items-center justify-between py-2">
            <label className="block text-sm font-medium text-gray-700">Make this a rollover budget</label>
            <div className="relative inline-block w-12 h-6">
              <input
                type="checkbox"
                checked={isRollover}
                onChange={() => !isSubmitting && setIsRollover(!isRollover)}
                disabled={isSubmitting}
                className="opacity-0 w-0 h-0"
              />
              <span
                className={`absolute cursor-pointer top-0 left-0 right-0 bottom-0 transition-all duration-300 rounded-full ${
                  isRollover ? 'bg-green-500' : 'bg-gray-300'
                } hover:${isRollover ? 'bg-green-600' : 'bg-gray-400'}`}
                onClick={() => setIsRollover(!isRollover)}
              >
                <span
                  className={`absolute h-5 w-5 left-0.5 bottom-0.5 bg-white transition-all duration-300 rounded-full transform shadow-sm ${
                    isRollover ? 'translate-x-6' : 'translate-x-0'
                  }`}
                />
              </span>
            </div>
          </div>

          <div className="mb-4 flex items-center justify-between py-2">
            <label className="block text-sm font-medium text-gray-700">Exclude from budget</label>
            <div className="relative inline-block w-12 h-6">
              <input
                type="checkbox"
                checked={isExcluded}
                onChange={() => !isSubmitting && setIsExcluded(!isExcluded)}
                disabled={isSubmitting}
                className="opacity-0 w-0 h-0"
              />
              <span
                className={`absolute cursor-pointer top-0 left-0 right-0 bottom-0 transition-all duration-300 rounded-full ${
                  isExcluded ? 'bg-green-500' : 'bg-gray-300'
                } hover:${isExcluded ? 'bg-green-600' : 'bg-gray-400'}`}
                onClick={() => setIsExcluded(!isExcluded)}
              >
                <span
                  className={`absolute h-5 w-5 left-0.5 bottom-0.5 bg-white transition-all duration-300 rounded-full transform shadow-sm ${
                    isExcluded ? 'translate-x-6' : 'translate-x-0'
                  }`}
                />
              </span>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <button 
            className="w-full p-3 border-0 rounded-md bg-gradient-to-r from-green-500 to-green-600 text-white cursor-pointer text-center font-medium hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-[1.02]"
            onClick={handleUpdateBudget}
            disabled={isSubmitting}
          >
            Update Budget
          </button>
        </div>
      </div>
    </div>
  );
};

export default EditBudgetPopupComponent;