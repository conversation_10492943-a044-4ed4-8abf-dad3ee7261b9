// ratingEpics.js
import { ofType } from 'redux-observable';
import { from, of } from 'rxjs';
import { mergeMap, map, catchError, switchMap } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig'; // Adjust path as needed
import { getCurrentUserId } from '../../web/src/utils/AuthUtil'; // Adjust path as needed
import {
  submitRatingRequest,
  submitRatingSuccess,
  submitRatingFailure,
  getUserRatingRequest,
  getUserRatingSuccess,
  getUserRatingFailure,
  getRatingStatsRequest,
  getRatingStatsSuccess,
  getRatingStatsFailure,
  updateRatingRequest,
  updateRatingSuccess,
  updateRatingFailure,
  deleteRatingRequest,
  deleteRatingSuccess,
  deleteRatingFailure,
} from '../redux/ratingSlice'; 

// Submit Rating Epic
export const submitRatingEpic = (action$) =>
  action$.pipe(
    ofType(submitRatingRequest.type),
    mergeMap((action) =>
      from(
        axiosInstance.post('/pennypal/api/ratings/submit', action.payload)
      ).pipe(
        map((response) => {
          console.log('Rating submitted successfully:', response.data);
          return submitRatingSuccess(response.data.data);
        }),
        catchError((error) => {
          console.error('Error submitting rating:', error);
          const errorMessage = error.response?.data?.message || 'Failed to submit rating';
          return of(submitRatingFailure(errorMessage));
        })
      )
    )
  );

// Get User Rating Epic
export const getUserRatingEpic = (action$) =>
  action$.pipe(
    ofType(getUserRatingRequest.type),
    switchMap((action) => {
      const userId = action.payload || getCurrentUserId();
      return from(
        axiosInstance.get(`/pennypal/api/ratings/user/${userId}`)
      ).pipe(
        map((response) => {
          console.log('User rating retrieved successfully:', response.data);
          return getUserRatingSuccess(response.data.data);
        }),
        catchError((error) => {
          console.error('Error getting user rating:', error);
          const errorMessage = error.response?.data?.message || 'Failed to get user rating';
          return of(getUserRatingFailure(errorMessage));
        })
      );
    })
  );

// Get Rating Stats Epic
export const getRatingStatsEpic = (action$) =>
  action$.pipe(
    ofType(getRatingStatsRequest.type),
    switchMap((action) => {
      const userId = action.payload || getCurrentUserId();
      return from(
        axiosInstance.get(`/pennypal/api/ratings/stats/${userId}`)
      ).pipe(
        map((response) => {
          console.log('Rating stats retrieved successfully:', response.data);
          return getRatingStatsSuccess(response.data.data);
        }),
        catchError((error) => {
          console.error('Error getting rating stats:', error);
          const errorMessage = error.response?.data?.message || 'Failed to get rating stats';
          return of(getRatingStatsFailure(errorMessage));
        })
      );
    })
  );

// Update Rating Epic
export const updateRatingEpic = (action$) =>
  action$.pipe(
    ofType(updateRatingRequest.type),
    mergeMap((action) =>
      from(
        axiosInstance.put('/pennypal/api/ratings/update', action.payload)
      ).pipe(
        map((response) => {
          console.log('Rating updated successfully:', response.data);
          return updateRatingSuccess(response.data.data);
        }),
        catchError((error) => {
          console.error('Error updating rating:', error);
          const errorMessage = error.response?.data?.message || 'Failed to update rating';
          return of(updateRatingFailure(errorMessage));
        })
      )
    )
  );

// Delete Rating Epic
export const deleteRatingEpic = (action$) =>
  action$.pipe(
    ofType(deleteRatingRequest.type),
    switchMap((action) => {
      const userId = action.payload || getCurrentUserId();
      return from(
        axiosInstance.delete(`/pennypal/api/ratings/user/${userId}`)
      ).pipe(
        map((response) => {
          console.log('Rating deleted successfully:', response.data);
          return deleteRatingSuccess(response.data.data);
        }),
        catchError((error) => {
          console.error('Error deleting rating:', error);
          const errorMessage = error.response?.data?.message || 'Failed to delete rating';
          return of(deleteRatingFailure(errorMessage));
        })
      );
    })
  );

// Combine all epics
export const ratingEpics = [
  submitRatingEpic,
  getUserRatingEpic,
  getRatingStatsEpic,
  updateRatingEpic,
  deleteRatingEpic,
];