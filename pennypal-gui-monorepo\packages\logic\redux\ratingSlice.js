// ratingSlice.js
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  // Rating submission state
  isSubmitting: false,
  submitError: null,
  submitSuccess: false,
  
  // User rating state
  userRating: null,
  isLoadingUserRating: false,
  userRatingError: null,
  
  // Rating stats state
  ratingStats: null,
  isLoadingStats: false,
  statsError: null,
  
  // Update rating state
  isUpdating: false,
  updateError: null,
  updateSuccess: false,
  
  // Delete rating state
  isDeleting: false,
  deleteError: null,
  deleteSuccess: false,
};

const ratingSlice = createSlice({
  name: 'rating',
  initialState,
  reducers: {
    // Submit Rating Actions
    submitRatingRequest: (state) => {
      state.isSubmitting = true;
      state.submitError = null;
      state.submitSuccess = false;
    },
    submitRatingSuccess: (state, action) => {
      state.isSubmitting = false;
      state.submitError = null;
      state.submitSuccess = true;
      // Optionally store the response data
      state.userRating = action.payload;
    },
    submitRatingFailure: (state, action) => {
      state.isSubmitting = false;
      state.submitError = action.payload;
      state.submitSuccess = false;
    },
    
    // Get User Rating Actions
    getUserRatingRequest: (state) => {
      state.isLoadingUserRating = true;
      state.userRatingError = null;
    },
    getUserRatingSuccess: (state, action) => {
      state.isLoadingUserRating = false;
      state.userRatingError = null;
      state.userRating = action.payload;
    },
    getUserRatingFailure: (state, action) => {
      state.isLoadingUserRating = false;
      state.userRatingError = action.payload;
    },
    
    // Get Rating Stats Actions
    getRatingStatsRequest: (state) => {
      state.isLoadingStats = true;
      state.statsError = null;
    },
    getRatingStatsSuccess: (state, action) => {
      state.isLoadingStats = false;
      state.statsError = null;
      state.ratingStats = action.payload;
    },
    getRatingStatsFailure: (state, action) => {
      state.isLoadingStats = false;
      state.statsError = action.payload;
    },
    
    // Update Rating Actions
    updateRatingRequest: (state) => {
      state.isUpdating = true;
      state.updateError = null;
      state.updateSuccess = false;
    },
    updateRatingSuccess: (state, action) => {
      state.isUpdating = false;
      state.updateError = null;
      state.updateSuccess = true;
      state.userRating = action.payload;
    },
    updateRatingFailure: (state, action) => {
      state.isUpdating = false;
      state.updateError = action.payload;
      state.updateSuccess = false;
    },
    
    // Delete Rating Actions
    deleteRatingRequest: (state) => {
      state.isDeleting = true;
      state.deleteError = null;
      state.deleteSuccess = false;
    },
    deleteRatingSuccess: (state) => {
      state.isDeleting = false;
      state.deleteError = null;
      state.deleteSuccess = true;
      state.userRating = null; // Clear user rating after deletion
    },
    deleteRatingFailure: (state, action) => {
      state.isDeleting = false;
      state.deleteError = action.payload;
      state.deleteSuccess = false;
    },
    
    // Reset Actions
    resetSubmitState: (state) => {
      state.submitError = null;
      state.submitSuccess = false;
    },
    resetUpdateState: (state) => {
      state.updateError = null;
      state.updateSuccess = false;
    },
    resetDeleteState: (state) => {
      state.deleteError = null;
      state.deleteSuccess = false;
    },
    clearRatingData: (state) => {
      state.userRating = null;
      state.ratingStats = null;
    },
  },
});

export const {
  submitRatingRequest,
  submitRatingSuccess,
  submitRatingFailure,
  getUserRatingRequest,
  getUserRatingSuccess,
  getUserRatingFailure,
  getRatingStatsRequest,
  getRatingStatsSuccess,
  getRatingStatsFailure,
  updateRatingRequest,
  updateRatingSuccess,
  updateRatingFailure,
  deleteRatingRequest,
  deleteRatingSuccess,
  deleteRatingFailure,
  resetSubmitState,
  resetUpdateState,
  resetDeleteState,
  clearRatingData,
} = ratingSlice.actions;

export default ratingSlice.reducer;