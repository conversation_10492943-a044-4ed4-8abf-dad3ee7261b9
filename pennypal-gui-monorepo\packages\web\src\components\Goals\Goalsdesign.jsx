import { useState } from 'react';
import { Plus, Home, ChevronRight, Wallet, Briefcase, BookOpen, Target, X, ArrowUp, ArrowDown, Calendar, DollarSign, <PERSON>Chart, BarChart2 } from 'lucide-react';

export default function Goals() {
  const [goals, setGoals] = useState([
    {
      id: 1,
      title: "Future Investment House",
      category: "investment",
      target: 1000,
      progress: 250,
      icon: <Home />,
      color: "bg-blue-100",
      transactions: [
        { id: 1, amount: 100, date: "2025-04-15", type: "deposit" },
        { id: 2, amount: 150, date: "2025-05-01", type: "deposit" }
      ],
      description: "Saving for a down payment on an investment property in a growing neighborhood.",
      timeline: "Expected completion by December 2025",
      returnRate: "Expected 5-7% annual return"
    },
    {
      id: 2,
      title: "Emergency Fund",
      category: "savings",
      target: 1000,
      progress: 600,
      icon: <Wallet />,
      color: "bg-green-100",
      transactions: [
        { id: 1, amount: 300, date: "2025-03-10", type: "deposit" },
        { id: 2, amount: 300, date: "2025-04-10", type: "deposit" }
      ],
      description: "Building a 3-month emergency fund for unexpected expenses.",
      timeline: "Expected completion by August 2025",
      returnRate: "Secure savings account with 1.5% interest"
    },
    {
      id: 3,
      title: "Career Development",
      category: "education",
      target: 1000,
      progress: 400,
      icon: <Briefcase />,
      color: "bg-yellow-100",
      transactions: [
        { id: 1, amount: 200, date: "2025-03-15", type: "deposit" },
        { id: 2, amount: 200, date: "2025-04-15", type: "deposit" }
      ],
      description: "Funding for professional certifications and courses.",
      timeline: "Ongoing investment in skills",
      returnRate: "Expected career advancement and salary increase"
    },
    {
      id: 4,
      title: "Skill Building",
      category: "personal",
      target: 1000,
      progress: 150,
      icon: <BookOpen />,
      color: "bg-purple-100",
      transactions: [
        { id: 1, amount: 75, date: "2025-04-01", type: "deposit" },
        { id: 2, amount: 75, date: "2025-05-01", type: "deposit" }
      ],
      description: "Learning new skills through online courses and workshops.",
      timeline: "Ongoing personal development",
      returnRate: "Knowledge investment with immeasurable returns"
    }
  ]);

  const [modalOpen, setModalOpen] = useState(false);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [selectedGoal, setSelectedGoal] = useState(null);
  const [newGoal, setNewGoal] = useState({
    title: "",
    category: "investment",
    target: 1000,
    progress: 0,
    description: "",
    timeline: "",
    returnRate: "",
    transactions: []
  });
  
  const [newTransaction, setNewTransaction] = useState({
    amount: 0,
    type: "deposit"
  });

  const handleAddGoal = () => {
    const iconMap = {
      investment: <Home />,
      savings: <Wallet />,
      education: <Briefcase />,
      personal: <BookOpen />
    };

    const colorMap = {
      investment: "bg-blue-100",
      savings: "bg-green-100",
      education: "bg-yellow-100",
      personal: "bg-purple-100"
    };

    setGoals([...goals, {
      id: goals.length + 1,
      ...newGoal,
      icon: iconMap[newGoal.category],
      color: colorMap[newGoal.category],
      transactions: []
    }]);
    
    setModalOpen(false);
    setNewGoal({
      title: "",
      category: "investment",
      target: 1000,
      progress: 0,
      description: "",
      timeline: "",
      returnRate: "",
      transactions: []
    });
  };

  const openDetails = (goal) => {
    setSelectedGoal(goal);
    setDetailsOpen(true);
  };

  const handleAddTransaction = () => {
    if (!newTransaction.amount) return;
    
    const updatedGoals = goals.map(goal => {
      if (goal.id === selectedGoal.id) {
        const newTransactionObj = {
          id: goal.transactions.length + 1,
          amount: Number(newTransaction.amount),
          date: new Date().toISOString().slice(0, 10),
          type: newTransaction.type
        };
        
        let updatedProgress = goal.progress;
        if (newTransaction.type === "deposit") {
          updatedProgress += Number(newTransaction.amount);
        } else if (newTransaction.type === "withdrawal") {
          updatedProgress -= Number(newTransaction.amount);
          if (updatedProgress < 0) updatedProgress = 0;
        }
        
        const updatedGoal = {
          ...goal,
          transactions: [...goal.transactions, newTransactionObj],
          progress: updatedProgress
        };
        
        setSelectedGoal(updatedGoal);
        return updatedGoal;
      }
      return goal;
    });
    
    setGoals(updatedGoals);
    setNewTransaction({ amount: 0, type: "deposit" });
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <div className="flex-1 max-w-5xl mx-auto p-6">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800">My Financial Goals</h1>
          <button 
            onClick={() => setModalOpen(true)}
            className="flex items-center px-4 py-2 bg-blue-600 text-black rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus size={20} className="mr-2" />
            Add Goal
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {goals.map((goal) => (
            <div key={goal.id} className="bg-white rounded-xl shadow-md overflow-hidden">
              <div className="flex p-6">
                <div className={`${goal.color} p-4 rounded-lg mr-4`}>
                  {goal.icon}
                </div>
                <div className="flex-1">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-lg font-semibold">{goal.title}</h3>
                    <span className="text-sm text-gray-500 capitalize">{goal.category}</span>
                  </div>
                  <div className="mb-2">
                    <div className="flex justify-between text-sm mb-1">
                      <span>${goal.progress}</span>
                      <span>${goal.target}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 rounded-full h-2" 
                        style={{ width: `${(goal.progress / goal.target) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">{Math.round((goal.progress / goal.target) * 100)}% Complete</span>
                    <button 
                      onClick={() => openDetails(goal)}
                      className="text-blue-600 hover:text-blue-800 flex items-center text-sm"
                    >
                      Details <ChevronRight size={16} />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Add Goal Modal */}
      {modalOpen && (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
    <div className="bg-white rounded-xl shadow-lg p-6 w-full max-w-4xl">
      <h2 className="text-xl font-bold mb-6 text-center">Add New Goal</h2>
      
      <div className="grid grid-cols-2 gap-4">
        {/* Left Column */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Goal Title</label>
            <input
              type="text"
              value={newGoal.title}
              onChange={(e) => setNewGoal({...newGoal, title: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="e.g. Future Investment House"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
            <select
              value={newGoal.category}
              onChange={(e) => setNewGoal({...newGoal, category: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="investment">Investment</option>
              <option value="savings">Savings</option>
              <option value="education">Education</option>
              <option value="personal">Personal</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Target Amount ($)</label>
            <input
              type="number"
              value={newGoal.target}
              onChange={(e) => setNewGoal({...newGoal, target: parseInt(e.target.value)})}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="1000"
            />
          </div>
        </div>
        
        {/* Right Column */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Timeline</label>
            <input
              type="text"
              value={newGoal.timeline}
              onChange={(e) => setNewGoal({...newGoal, timeline: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="e.g. Expected completion by December 2025"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Expected Return</label>
            <input
              type="text"
              value={newGoal.returnRate}
              onChange={(e) => setNewGoal({...newGoal, returnRate: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="e.g. Expected 5-7% annual return"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              value={newGoal.description}
              onChange={(e) => setNewGoal({...newGoal, description: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="Brief description of your goal"
              rows="2"
            />
          </div>
        </div>
      </div>
      
      {/* Buttons - Full Width Row */}
      <div className="mt-6 flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <button
          onClick={() => setModalOpen(false)}
          className="px-4 py-2 border border-gray-300 bg-white text-gray-700 rounded-md hover:bg-gray-50 font-medium"
        >
          Cancel
        </button>
        <button
          onClick={handleAddGoal}
          className="px-4 py-2 bg-blue-600 text-black rounded-md hover:bg-blue-700 font-medium"
        >
          Add Goal
        </button>
      </div>
    </div>
  </div>
)}

      {/* Goal Details Modal */}
      {detailsOpen && selectedGoal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-lg w-full max-w-4xl max-h-full overflow-auto">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center">
                <div className={`${selectedGoal.color} p-4 rounded-lg mr-4`}>
                  {selectedGoal.icon}
                </div>
                <div>
                  <h2 className="text-2xl font-bold">{selectedGoal.title}</h2>
                  <span className="text-sm text-gray-500 capitalize">{selectedGoal.category}</span>
                </div>
              </div>
              <button 
                onClick={() => setDetailsOpen(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={24} />
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-6">
              {/* Progress Section */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold mb-3">Progress Tracking</h3>
                <div className="mb-4">
                  <div className="flex justify-between text-sm mb-1">
                    <span>${selectedGoal.progress}</span>
                    <span>${selectedGoal.target}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div 
                      className="bg-blue-600 rounded-full h-3" 
                      style={{ width: `${(selectedGoal.progress / selectedGoal.target) * 100}%` }}
                    ></div>
                  </div>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span>{Math.round((selectedGoal.progress / selectedGoal.target) * 100)}% Complete</span>
                  <span>Remaining: ${selectedGoal.target - selectedGoal.progress}</span>
                </div>
              </div>

              {/* Goal Info */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold mb-3">Goal Information</h3>
                <div className="space-y-3">
                  <div className="flex items-start">
                    <div className="text-blue-600 mr-3 mt-1"><Target size={18} /></div>
                    <div>
                      <p className="text-sm font-medium">Description</p>
                      <p className="text-sm text-gray-600">{selectedGoal.description}</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="text-blue-600 mr-3 mt-1"><Calendar size={18} /></div>
                    <div>
                      <p className="text-sm font-medium">Timeline</p>
                      <p className="text-sm text-gray-600">{selectedGoal.timeline}</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="text-blue-600 mr-3 mt-1"><PieChart size={18} /></div>
                    <div>
                      <p className="text-sm font-medium">Expected Return</p>
                      <p className="text-sm text-gray-600">{selectedGoal.returnRate}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Add Transaction */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold mb-3">Add Transaction</h3>
                <div className="flex space-x-2">
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Amount ($)</label>
                    <input
                      type="number"
                      value={newTransaction.amount}
                      onChange={(e) => setNewTransaction({...newTransaction, amount: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      placeholder="Enter amount"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
                    <select
                      value={newTransaction.type}
                      onChange={(e) => setNewTransaction({...newTransaction, type: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="deposit">Deposit</option>
                      <option value="withdrawal">Withdrawal</option>
                    </select>
                  </div>
                </div>
                <button
                  onClick={handleAddTransaction}
                  className="mt-3 w-full px-4 py-2 bg-blue-600 text-black rounded-md hover:bg-blue-700"
                >
                  Add Transaction
                </button>
              </div>

              {/* Transaction History */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold mb-3">Transaction History</h3>
                <div className="max-h-60 overflow-y-auto">
                  {selectedGoal.transactions.length > 0 ? (
                    <table className="min-w-full">
                      <thead>
                        <tr className="text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          <th className="px-3 py-2">Date</th>
                          <th className="px-3 py-2">Amount</th>
                          <th className="px-3 py-2">Type</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200">
                        {selectedGoal.transactions.map((transaction) => (
                          <tr key={transaction.id}>
                            <td className="px-3 py-2 text-sm">{transaction.date}</td>
                            <td className="px-3 py-2 text-sm">${transaction.amount}</td>
                            <td className="px-3 py-2 text-sm">
                              <span className={`inline-flex items-center ${transaction.type === 'deposit' ? 'text-green-600' : 'text-red-600'}`}>
                                {transaction.type === 'deposit' ? <ArrowDown size={14} className="mr-1" /> : <ArrowUp size={14} className="mr-1" />}
                                {transaction.type}
                              </span>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  ) : (
                    <div className="text-center text-gray-500 py-4">No transactions yet</div>
                  )}
                </div>
              </div>
            </div>

            {/* Footer with call-to-action buttons */}
            <div className="p-6 border-t border-gray-200">
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setDetailsOpen(false)}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  Close
                </button>
                <button
                  className="px-4 py-2 bg-blue-600 text-black rounded-md hover:bg-blue-700"
                >
                  Update Goal
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}