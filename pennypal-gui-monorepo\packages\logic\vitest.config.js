/// <reference types="vitest" />
import { defineConfig } from 'vitest/config'

export default defineConfig({
  test: {
    globals: true,
    environment: 'node', // Node environment for pure logic
    setupFiles: ['./tests/setupTests.js'],
    // Include patterns for test files in tests folder
    include: ['tests/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts}'],
    // Exclude patterns
    exclude: ['node_modules', 'dist', '.idea', '.git', '.cache'],
    // Coverage configuration - cover source files, not test files
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: './coverage',
      // Include source files for coverage (adjust paths based on your structure)
      include: ['src/**/*.{js,ts}', 'lib/**/*.{js,ts}', '*.{js,ts}'],
      exclude: [
        'coverage/**',
        'dist/**',
        'tests/**',
        '**/*.d.ts',
        '**/*.config.{js,ts}',
        'node_modules/**'
      ],
      thresholds: {
        global: {
          branches: 70,
          functions: 70,
          lines: 70,
          statements: 70
        }
      }
    }
  },
  resolve: {
    alias: {
      '@': new URL('./', import.meta.url).pathname,
    },
  },
})