// ReduxSpendingDashboard.jsx
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';
import { fetchMonthlyExpenses, fetchYearlyExpenses } from '../../../../logic/epics/spendingDashboardEpic';

const COLORS = {
  primary: '#7fe029',
  primaryLight: '#eaf7e0',
  primaryDark: '#5db01e',
  white: '#ffffff',
  lightGray: '#f8f9fa',
  textPrimary: '#333333',
  textSecondary: '#666666',
  success: '#2ecc71',
  danger: '#e74c3c',
  shadow: 'rgba(127, 224, 41, 0.15)',
};

const SPENDING_COLORS = ['#FF8042', '#FFBB28', '#00C49F', '#0088FE', '#8884d8', '#82ca9d', '#a569bd', '#ffa07a', '#20b2aa', '#f08080'];

const SpendingDashboard = () => {
  const dispatch = useDispatch();
  const [period, setPeriod] = useState('month');
  
  // Get data from Redux store
  const { monthlyExpenses, yearlyExpenses, isLoading, error } = useSelector(
    (state) => state.spendingDashboard
  );
  
  const userId = 1; // You might want to get this from auth state or props

  // Select expenses based on current period
  const expenses = period === 'month' ? monthlyExpenses : yearlyExpenses;

  // Fetch data when component mounts or period changes
  useEffect(() => {
    if (period === 'month') {
      dispatch(fetchMonthlyExpenses(userId));
    } else {
      dispatch(fetchYearlyExpenses(userId));
    }
  }, [period, userId, dispatch]);

  // Process data for the pie chart
  const processChartData = () => {
    if (!expenses || expenses.length === 0) return [];
    
    // Group by category and sum expenses
    const categoryMap = {};
    
    expenses.forEach(item => {
      const categoryName = item.categoryName;
      if (!categoryMap[categoryName]) {
        categoryMap[categoryName] = 0;
      }
      categoryMap[categoryName] += item.actualExpense;
    });
    
    // Convert to array format for recharts
    return Object.keys(categoryMap)
      .filter(category => categoryMap[category] > 0) // Only include categories with expenses
      .map(category => ({
        name: category,
        value: categoryMap[category]
      }))
      .sort((a, b) => b.value - a.value); // Sort by value descending
  };

  const chartData = processChartData();
  const totalSpending = chartData.reduce((sum, item) => sum + item.value, 0);

  // Handle period change
  const handlePeriodChange = (newPeriod) => {
    setPeriod(newPeriod);
  };

  return (
    <div className="h-full flex flex-col bg-white rounded-lg shadow p-6" style={{ boxShadow: `0 4px 12px ${COLORS.shadow}` }}>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold" style={{ color: COLORS.textPrimary }}>Spending Breakdown</h2>
        <div className="flex rounded-lg p-1" style={{ backgroundColor: COLORS.primaryLight }}>
          {['month', 'year'].map(p => (
            <button
              key={p}
              className="py-1 px-3 rounded-md text-xs font-medium transition-all duration-200"
              style={{
                backgroundColor: period === p ? COLORS.primary : 'transparent',
                color: period === p ? COLORS.white : COLORS.textSecondary
              }}
              onClick={() => handlePeriodChange(p)}
            >
              {p.charAt(0).toUpperCase() + p.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {isLoading ? (
        <div className="flex-1 flex items-center justify-center">
          <p>Loading expenses...</p>
        </div>
      ) : error ? (
        <div className="flex-1 flex items-center justify-center text-red-500">
          <p>Error: {error}</p>
        </div>
      ) : chartData.length === 0 ? (
        <div className="flex-1 flex items-center justify-center">
          <p>No expense data available for this {period}</p>
        </div>
      ) : (
        <div className="flex flex-col flex-1">
          <div className="text-center mb-2">
            <span className="text-3xl font-bold" style={{ color: COLORS.textPrimary }}>
              ${totalSpending.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </span>
            <span className="ml-2 text-sm" style={{ color: COLORS.textSecondary }}>
              this {period}
            </span>
          </div>

          <ResponsiveContainer width="100%" height={240} className="mt-2">
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={80}
                innerRadius={40}
                fill="#8884d8"
                dataKey="value"
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={SPENDING_COLORS[index % SPENDING_COLORS.length]} />
                ))}
              </Pie>
              <Tooltip
                formatter={(value) => [
                  `$${value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`, 
                  'Amount'
                ]}
                contentStyle={{
                  backgroundColor: COLORS.white,
                  borderColor: COLORS.primaryLight,
                  borderRadius: '8px'
                }}
              />
              <Legend
                layout="horizontal"
                verticalAlign="bottom"
                align="center"
                wrapperStyle={{ fontSize: '12px' }}
              />
            </PieChart>
          </ResponsiveContainer>

          <div className="mt-4 overflow-auto">
            {chartData.map((category, index) => (
              <div key={index} className="flex justify-between items-center py-2">
                <div className="flex items-center">
                  <div 
                    className="w-3 h-3 rounded-full mr-2" 
                    style={{ backgroundColor: SPENDING_COLORS[index % SPENDING_COLORS.length] }}
                  ></div>
                  <span className="text-sm" style={{ color: COLORS.textPrimary }}>{category.name}</span>
                </div>
                <div className="flex items-center">
                  <span className="text-sm font-medium" style={{ color: COLORS.textPrimary }}>
                    ${category.value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </span>
                  <span className="text-xs ml-2" style={{ color: COLORS.textSecondary }}>
                    ({Math.round((category.value / totalSpending) * 100)}%)
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SpendingDashboard;
// import React, { useState, useEffect } from 'react';
// import { useDispatch, useSelector } from 'react-redux';
// import { PieChart, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';
// import { 
//   fetchExpensesStart,
//   fetchCurrentMonthExpensesSuccess,
//   fetchPast12MonthsExpensesSuccess,
//   fetchExpensesFailure
// } from '../../../../logic/redux/spendingDashboardSlice';
// import { 
//   fetchCurrentMonthExpenses, 
//   fetchPast12MonthsExpenses 
// } from '../../../../logic/epics/spendingDashboardEpic';

// const COLORS = {
//   primary: '#7fe029',
//   primaryLight: '#eaf7e0',
//   primaryDark: '#5db01e',
//   white: '#ffffff',
//   lightGray: '#f8f9fa',
//   textPrimary: '#333333',
//   textSecondary: '#666666',
//   success: '#2ecc71',
//   danger: '#e74c3c',
//   shadow: 'rgba(127, 224, 41, 0.15)',
// };

// const SPENDING_COLORS = ['#FF8042', '#FFBB28', '#00C49F', '#0088FE', '#8884d8', '#82ca9d'];

// const SpendingCard = ({ userId = 1 }) => {
//   const dispatch = useDispatch();
//   const [period, setPeriod] = useState('month');
  
//   const { 
//     currentMonthExpenses, 
//     past12MonthsExpenses, 
//     isLoading, 
//     error 
//   } = useSelector((state) => state.spendingDashboard);

//   // Determine which dataset to use based on selected period
//   const spendingData = period === 'month' ? currentMonthExpenses : past12MonthsExpenses;
  
//   // Calculate total spending
//   const totalSpending = spendingData.reduce((sum, item) => sum + item.value, 0);

//   // Fetch data based on selected period when component mounts or period changes
//   useEffect(() => {
//     if (period === 'month') {
//       dispatch(fetchCurrentMonthExpenses(userId));
//     } else {
//       dispatch(fetchPast12MonthsExpenses(userId));
//     }
//   }, [dispatch, period, userId]);

//   const handlePeriodChange = (newPeriod) => {
//     setPeriod(newPeriod);
//   };

//   return (
//     <div className="h-full flex flex-col">
//       <div className="flex justify-between items-center mb-4">
//         <h2 className="text-xl font-semibold" style={{ color: COLORS.textPrimary }}>Spending Breakdown</h2>
//         <div className="flex rounded-lg p-1" style={{ backgroundColor: COLORS.primaryLight }}>
//           {['month', 'year'].map(p => (
//             <button
//               key={p}
//               className="py-1 px-3 rounded-md text-xs font-medium transition-all duration-200"
//               style={{
//                 backgroundColor: period === p ? COLORS.primary : 'transparent',
//                 color: period === p ? COLORS.white : COLORS.textSecondary
//               }}
//               onClick={() => handlePeriodChange(p)}
//             >
//               {p.charAt(0).toUpperCase() + p.slice(1)}
//             </button>
//           ))}
//         </div>
//       </div>

//       {isLoading ? (
//         <div className="flex items-center justify-center h-64">
//           <p style={{ color: COLORS.textSecondary }}>Loading spending data...</p>
//         </div>
//       ) : error ? (
//         <div className="flex items-center justify-center h-64">
//           <p style={{ color: COLORS.danger }}>Error loading data. Please try again.</p>
//         </div>
//       ) : spendingData.length === 0 ? (
//         <div className="flex items-center justify-center h-64">
//           <p style={{ color: COLORS.textSecondary }}>No spending data available.</p>
//         </div>
//       ) : (
//         <div className="flex flex-col h-full">
//           <div className="text-center mb-2">
//             <span className="text-3xl font-bold" style={{ color: COLORS.textPrimary }}>
//               ${totalSpending.toLocaleString()}
//             </span>
//             <span className="ml-2 text-sm" style={{ color: COLORS.textSecondary }}>
//               this {period}
//             </span>
//           </div>

//           <ResponsiveContainer width="100%" height={200}>
//             <PieChart>
//               <Pie
//                 data={spendingData}
//                 cx="50%"
//                 cy="50%"
//                 labelLine={false}
//                 outerRadius={80}
//                 innerRadius={40}
//                 fill="#8884d8"
//                 dataKey="value"
//               >
//                 {spendingData.map((entry, index) => (
//                   <Cell key={`cell-${index}`} fill={SPENDING_COLORS[index % SPENDING_COLORS.length]} />
//                 ))}
//               </Pie>
//               <Tooltip
//                 formatter={(value) => [`$${value}`, 'Amount']}
//                 contentStyle={{
//                   backgroundColor: COLORS.white,
//                   borderColor: COLORS.primaryLight,
//                   borderRadius: '8px'
//                 }}
//               />
//               <Legend
//                 layout="horizontal"
//                 verticalAlign="bottom"
//                 align="center"
//                 wrapperStyle={{ fontSize: '12px' }}
//               />
//             </PieChart>
//           </ResponsiveContainer>

//           <div className="mt-2 overflow-auto">
//             {spendingData.map((category, index) => (
//               <div key={index} className="flex justify-between items-center py-2">
//                 <div className="flex items-center">
//                   <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: SPENDING_COLORS[index % SPENDING_COLORS.length] }}></div>
//                   <span className="text-sm" style={{ color: COLORS.textPrimary }}>{category.name}</span>
//                 </div>
//                 <div className="flex items-center">
//                   <span className="text-sm font-medium" style={{ color: COLORS.textPrimary }}>
//                     ${category.value.toLocaleString()}
//                   </span>
//                   <span className="text-xs ml-2" style={{ color: COLORS.textSecondary }}>
//                     ({Math.round((category.value / totalSpending) * 100)}%)
//                   </span>
//                 </div>
//               </div>
//             ))}
//           </div>
//         </div>
//       )}
//     </div>
//   );
// };

// export default SpendingCard;