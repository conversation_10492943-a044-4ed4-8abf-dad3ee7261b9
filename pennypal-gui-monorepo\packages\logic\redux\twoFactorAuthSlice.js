import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  // Data states
  twoFactorStatus: null,
  setupData: null,
  
  // Loading states
  loading: false,
  setupLoading: false,
  enableLoading: false,
  disableLoading: false,
  verifyLoading: false,
  statusLoading: false,
  
  // Error and success states
  error: null,
  success: false,
  message: '',
  
  // Verification states
  isVerified: false,
  isEnabled: false,
};

export const twoFactorAuthSlice = createSlice({
  name: 'twoFactorAuth',
  initialState,
  reducers: {
    // Setup Two-Factor Auth
    setupTwoFactorAuthRequest: (state) => {
      console.log('Redux: setupTwoFactorAuthRequest');
      state.setupLoading = true;
      state.loading = true;
      state.error = null;
      state.message = '';
      state.success = false;
    },
    setupTwoFactorAuthSuccess: (state, action) => {
      console.log('Redux: setupTwoFactorAuthSuccess', action.payload);
      state.setupLoading = false;
      state.loading = false;
      state.setupData = action.payload?.data || null;
      state.success = true;
      state.message = action.payload?.message || '2FA setup successful';
      state.error = null;
    },
    
    // Enable Two-Factor Auth
    enableTwoFactorAuthRequest: (state) => {
      console.log('Redux: enableTwoFactorAuthRequest');
      state.enableLoading = true;
      state.loading = true;
      state.error = null;
      state.message = '';
      state.success = false;
    },
    enableTwoFactorAuthSuccess: (state, action) => {
      console.log('Redux: enableTwoFactorAuthSuccess', action.payload);
      state.enableLoading = false;
      state.loading = false;
      state.isEnabled = true;
      state.success = true;
      state.message = action.payload?.message || '2FA enabled successfully';
      state.error = null;
      // Update status if we have it
      if (state.twoFactorStatus) {
        state.twoFactorStatus.enabled = true;
      }
    },
    
    // Disable Two-Factor Auth
    disableTwoFactorAuthRequest: (state) => {
      console.log('Redux: disableTwoFactorAuthRequest');
      state.disableLoading = true;
      state.loading = true;
      state.error = null;
      state.message = '';
      state.success = false;
    },
    disableTwoFactorAuthSuccess: (state, action) => {
      console.log('Redux: disableTwoFactorAuthSuccess', action.payload);
      state.disableLoading = false;
      state.loading = false;
      state.isEnabled = false;
      state.success = true;
      state.message = action.payload?.message || '2FA disabled successfully';
      state.error = null;
      // Update status if we have it
      if (state.twoFactorStatus) {
        state.twoFactorStatus.enabled = false;
      }
    },
    
    // Verify Two-Factor Code
    verifyTwoFactorCodeRequest: (state) => {
      console.log('Redux: verifyTwoFactorCodeRequest');
      state.verifyLoading = true;
      state.loading = true;
      state.error = null;
      state.message = '';
      state.success = false;
      state.isVerified = false;
    },
    verifyTwoFactorCodeSuccess: (state, action) => {
      console.log('Redux: verifyTwoFactorCodeSuccess', action.payload);
      state.verifyLoading = false;
      state.loading = false;
      state.isVerified = action.payload?.verified || false;
      state.success = true;
      state.message = action.payload?.message || 
        (action.payload?.verified ? 'Code verified successfully' : 'Invalid verification code');
      state.error = null;
    },
    
    // Get Two-Factor Status
    getTwoFactorStatusRequest: (state) => {
      console.log('Redux: getTwoFactorStatusRequest');
      state.statusLoading = true;
      state.loading = true;
      state.error = null;
      state.message = '';
      state.success = false;
    },
    getTwoFactorStatusSuccess: (state, action) => {
      console.log('Redux: getTwoFactorStatusSuccess', action.payload);
      state.statusLoading = false;
      state.loading = false;
      state.twoFactorStatus = action.payload?.data || null;
      state.isEnabled = action.payload?.data?.enabled || false;
      state.success = true;
      state.message = action.payload?.message || 'Status retrieved successfully';
      state.error = null;
    },
    
    // Failure action
    twoFactorAuthActionFailure: (state, action) => {
      console.log('Redux: twoFactorAuthActionFailure', action.payload);
      state.loading = false;
      state.setupLoading = false;
      state.enableLoading = false;
      state.disableLoading = false;
      state.verifyLoading = false;
      state.statusLoading = false;
      state.error = action.payload;
      state.success = false;
      state.message = '';
      state.isVerified = false;
    },
    
    // Reset states
    resetTwoFactorAuthStatus: (state) => {
      console.log('Redux: resetTwoFactorAuthStatus');
      state.loading = false;
      state.setupLoading = false;
      state.enableLoading = false;
      state.disableLoading = false;
      state.verifyLoading = false;
      state.statusLoading = false;
      state.success = false;
      state.error = null;
      state.message = '';
      state.isVerified = false;
    },
    
    // Clear setup data
    clearSetupData: (state) => {
      console.log('Redux: clearSetupData');
      state.setupData = null;
    },
    
    // Clear verification status
    clearVerificationStatus: (state) => {
      console.log('Redux: clearVerificationStatus');
      state.isVerified = false;
    },
    
    // Clear all 2FA data
    clearAllTwoFactorAuthData: (state) => {
      console.log('Redux: clearAllTwoFactorAuthData');
      state.twoFactorStatus = null;
      state.setupData = null;
      state.isVerified = false;
      state.isEnabled = false;
      state.error = null;
      state.success = false;
      state.message = '';
      state.loading = false;
      state.setupLoading = false;
      state.enableLoading = false;
      state.disableLoading = false;
      state.verifyLoading = false;
      state.statusLoading = false;
    }
  }
});

export const {
  // Setup actions
  setupTwoFactorAuthRequest,
  setupTwoFactorAuthSuccess,
  
  // Enable/Disable actions
  enableTwoFactorAuthRequest,
  enableTwoFactorAuthSuccess,
  disableTwoFactorAuthRequest,
  disableTwoFactorAuthSuccess,
  
  // Verify actions
  verifyTwoFactorCodeRequest,
  verifyTwoFactorCodeSuccess,
  
  // Status actions
  getTwoFactorStatusRequest,
  getTwoFactorStatusSuccess,
  
  // Utility actions
  twoFactorAuthActionFailure,
  resetTwoFactorAuthStatus,
  clearSetupData,
  clearVerificationStatus,
  clearAllTwoFactorAuthData,
} = twoFactorAuthSlice.actions;

export default twoFactorAuthSlice.reducer;