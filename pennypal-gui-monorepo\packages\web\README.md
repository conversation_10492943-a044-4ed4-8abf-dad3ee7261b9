# React + Vite

This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react/README.md) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh


packages/web/
├── tests/
│   ├── setupTests.js
│   ├── components/
│   │   └── App.test.jsx
│   ├── pages/
│   │   └── Dashboard.test.jsx
│   ├── hooks/
│   │   └── useCounter.test.js
│   ├── utils/
│   │   └── formatters.test.js
│   └── store/
│       └── store.test.js
├── src/
│   ├── [your source files...]
│   └── [no more test files here]
├── vitest.config.js
├── package.json
├── .gitignore
└── [other config files...]
