import { useDispatch } from 'react-redux';
import {
  invalidateTransactionCache,
  invalidateRecurringTransactionCache,
  invalidateBudgetCache,
  invalidateAllTransactionRelatedCache,
  invalidateReceiptCache,
  invalidateChatbotCache,
  clearCache
} from '../redux/cacheSlice';

/**
 * Utility functions for manual cache invalidation
 * Use these in components when you need to manually invalidate cache
 */

/**
 * Invalidate transaction cache only
 * Use when: Individual transactions are modified but budget data is unchanged
 */
export const invalidateTransactions = (dispatch) => {
  console.log('🗑️ Manually invalidating transaction cache');
  dispatch(invalidateTransactionCache());
};

/**
 * Invalidate recurring transaction cache only
 * Use when: Recurring transactions are modified
 */
export const invalidateRecurringTransactions = (dispatch) => {
  console.log('🗑️ Manually invalidating recurring transaction cache');
  dispatch(invalidateRecurringTransactionCache());
};

/**
 * Invalidate budget cache only
 * Use when: Budget data is modified but transactions are unchanged
 */
export const invalidateBudget = (dispatch) => {
  console.log('🗑️ Manually invalidating budget cache');
  dispatch(invalidateBudgetCache());
};

/**
 * Invalidate all transaction and budget related cache
 * Use when: Major data changes that affect both transactions and budgets
 * Examples: Account sync, bulk operations, data imports
 */
export const invalidateAllTransactionData = (dispatch) => {
  console.log('🗑️ Manually invalidating all transaction-related cache');
  dispatch(invalidateAllTransactionRelatedCache());
};

/**
 * Invalidate receipt cache only
 * Use when: Receipts are uploaded, saved, or modified
 */
export const invalidateReceipts = (dispatch) => {
  console.log('🗑️ Manually invalidating receipt cache');
  dispatch(invalidateReceiptCache());
};

/**
 * Invalidate chatbot cache only
 * Use when: New chatbot queries are made
 */
export const invalidateChatbot = (dispatch) => {
  console.log('🗑️ Manually invalidating chatbot cache');
  dispatch(invalidateChatbotCache());
};

/**
 * Clear entire cache (nuclear option)
 * Use when: User logs out, major app state reset, or troubleshooting
 */
export const clearAllCache = (dispatch) => {
  console.log('🗑️ Manually clearing entire cache');
  dispatch(clearCache());
};

/**
 * Comprehensive logout cache clearing
 * Use when: User logs out - clears all cache and provides logging
 */
export const clearCacheOnLogout = (dispatch) => {
  console.log('🚪 Clearing all cache data on user logout...');

  // Clear Redux cache
  dispatch(clearCache());

  // Clear browser storage (additional safety)
  try {
    localStorage.removeItem('pennypal_cache');
    sessionStorage.removeItem('pennypal_cache');
    console.log('✅ Browser storage cache cleared');
  } catch (error) {
    console.warn('⚠️ Could not clear browser storage cache:', error);
  }

  console.log('✅ All cache data cleared on logout');
};

/**
 * Hook for easy cache invalidation in React components
 * Usage: const { invalidateTransactions, invalidateBudget, ... } = useCacheInvalidation();
 */
export const useCacheInvalidation = () => {
  const dispatch = useDispatch();

  return {
    invalidateTransactions: () => invalidateTransactions(dispatch),
    invalidateRecurringTransactions: () => invalidateRecurringTransactions(dispatch),
    invalidateBudget: () => invalidateBudget(dispatch),
    invalidateAllTransactionData: () => invalidateAllTransactionData(dispatch),
    invalidateReceipts: () => invalidateReceipts(dispatch),
    invalidateChatbot: () => invalidateChatbot(dispatch),
    clearAllCache: () => clearAllCache(dispatch),
    clearCacheOnLogout: () => clearCacheOnLogout(dispatch)
  };
};

export {
  invalidateTransactionCache,
  invalidateRecurringTransactionCache,
  invalidateBudgetCache,
  invalidateAllTransactionRelatedCache,
  invalidateReceiptCache,
  invalidateChatbotCache,
  clearCache
} from '../redux/cacheSlice';