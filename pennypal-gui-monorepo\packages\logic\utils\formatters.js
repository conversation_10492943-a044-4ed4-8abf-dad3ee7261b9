/**
 * Formats a number as currency with specified currency code
 * @param {number} value - The value to format
 * @param {string} currencyCode - The currency code (e.g., 'USD', 'EUR')
 * @param {number} [decimals=2] - Number of decimal places
 * @returns {string} Formatted currency string
 */
export const formatCurrency = (value, currencyCode = 'USD', decimals = 2) => {
    if (value === null || value === undefined) {
      return '-';
    }
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    }).format(value);
  };
  
  /**
   * Formats a number as percentage
   * @param {number} value - The value to format (e.g., 0.05 for 5%)
   * @param {number} [decimals=2] - Number of decimal places
   * @returns {string} Formatted percentage string
   */
  export const formatPercentage = (value, decimals = 2) => {
    if (value === null || value === undefined) {
      return '-';
    }
    
    return new Intl.NumberFormat('en-US', {
      style: 'percent',
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    }).format(value);
  };
  
  /**
   * Formats a date string or Date object
   * @param {string|Date} dateValue - The date to format
   * @param {object} [options] - Formatting options
   * @returns {string} Formatted date string
   */
  export const formatDate = (dateValue, options = {}) => {
    if (!dateValue) {
      return '-';
    }
    
    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;
    
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      ...options
    }).format(date);
  };