import React, { useState, useEffect, useMemo } from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Area, AreaChart } from 'recharts';
import { useDispatch, useSelector } from 'react-redux';
import { 
  fetchAllStocksHistoryRequest, 
  fetchStockHistoryRequest,
  fetchUserInvestmentsRequest 
} from '../../../../logic/redux/investmentsSlice';

// Modern Icons (you can replace with your preferred icon library)
const TrendingUpIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
  </svg>
);

const TrendingDownIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
  </svg>
);

const ChartIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
  </svg>
);

const CalendarIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
);

const WalletIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
  </svg>
);

const InvestmentsChart = () => {
  const dispatch = useDispatch();
  const { 
    allStocksHistory, 
    stockHistory, 
    userInvestments,
    allStocksHistoryLoading, 
    stockHistoryLoading,
    loading 
  } = useSelector(state => state.investment);

  // State for dropdowns and chart type
  const [selectedTimePeriod, setSelectedTimePeriod] = useState(3);
  const [selectedHolding, setSelectedHolding] = useState('all');
  const [chartType, setChartType] = useState('line'); // 'line' or 'area'

  // Time period options with modern styling
  const timePeriodOptions = [
    { value: 1, label: '1M', fullLabel: '1 Month' },
    { value: 3, label: '3M', fullLabel: '3 Months' },
    { value: 6, label: '6M', fullLabel: '6 Months' },
    { value: 12, label: '1Y', fullLabel: '1 Year' }
  ];

  // Get unique holdings from user investments
  const holdingOptions = [
    { value: 'all', label: 'All Holdings', icon: '🏦' },
    ...userInvestments
      .filter(investment => investment.ticker && investment.ticker.trim() !== '')
      .map((investment, index) => ({
        value: investment.ticker,
        label: `${investment.ticker}`,
        fullLabel: `${investment.ticker} - ${investment.companyName || investment.ticker}`,
        icon: '📈',
        key: `holding-${investment.ticker}-${index}`
      }))
      .filter((option, index, self) => 
        index === self.findIndex(o => o.value === option.value)
      )
  ];

  // Load user investments on component mount
  useEffect(() => {
    dispatch(fetchUserInvestmentsRequest());
  }, [dispatch]);

  // Load initial data
  useEffect(() => {
    if (selectedHolding === 'all') {
      dispatch(fetchAllStocksHistoryRequest({ months: selectedTimePeriod }));
    } else {
      dispatch(fetchStockHistoryRequest({ 
        ticker: selectedHolding, 
        months: selectedTimePeriod 
      }));
    }
  }, [dispatch, selectedHolding, selectedTimePeriod]);

  // Transform API data into chart format
  const transformAllStocksData = (allStocksData) => {
    if (!allStocksData || !allStocksData.stockData) {
      return [];
    }

    const allDates = new Set();
    Object.values(allStocksData.stockData).forEach(stockArray => {
      if (Array.isArray(stockArray)) {
        stockArray.forEach(dataPoint => {
          if (dataPoint.date) {
            allDates.add(dataPoint.date);
          }
        });
      }
    });

    const sortedDates = Array.from(allDates).sort();

    const chartData = sortedDates.map((date, index) => {
      let totalValue = 0;
      let totalShares = 0;

      Object.entries(allStocksData.stockData).forEach(([ticker, stockArray]) => {
        if (Array.isArray(stockArray)) {
          const dataPoint = stockArray.find(point => point.date === date);
          if (dataPoint) {
            totalValue += dataPoint.totalValue || 0;
            totalShares += dataPoint.totalQuantity || 0;
          }
        }
      });

      return {
        x: index,
        y: totalValue,
        date: date,
        totalValue: totalValue,
        totalShares: totalShares
      };
    });

    return chartData;
  };

  // Transform individual stock data
  const transformStockData = (stockData, ticker) => {
    let dataPoints = null;
    
    if (Array.isArray(stockData)) {
      dataPoints = stockData;
    } else if (stockData && Array.isArray(stockData.dataPoints)) {
      dataPoints = stockData.dataPoints;
    } else if (stockData && stockData.ticker === ticker && Array.isArray(stockData.dataPoints)) {
      dataPoints = stockData.dataPoints;
    }
    
    if (!dataPoints || !Array.isArray(dataPoints) || dataPoints.length === 0) {
      return [];
    }

    const chartData = dataPoints.map((dataPoint, index) => ({
      x: index,
      y: dataPoint.totalValue || 0,
      date: dataPoint.date,
      totalValue: dataPoint.totalValue,
      totalShares: dataPoint.totalQuantity,
      price: dataPoint.avgPrice,
      gainLossPercent: dataPoint.gainLossPercent
    }));

    return chartData;
  };

  // Memoized chart data calculation
  const chartData = useMemo(() => {
    if (selectedHolding === 'all') {
      return transformAllStocksData(allStocksHistory);
    } else {
      const stockData = stockHistory[selectedHolding];
      if (stockData) {
        return transformStockData(stockData, selectedHolding);
      }
      return [];
    }
  }, [allStocksHistory, stockHistory, selectedHolding]);

  // Calculate performance metrics
  const performanceMetrics = useMemo(() => {
    if (chartData.length < 2) return null;

    const firstValue = chartData[0]?.y || 0;
    const lastValue = chartData[chartData.length - 1]?.y || 0;
    const change = lastValue - firstValue;
    const changePercent = firstValue !== 0 ? (change / firstValue) * 100 : 0;

    return {
      currentValue: lastValue,
      change,
      changePercent,
      isPositive: change >= 0
    };
  }, [chartData]);

  // Handle time period change
  const handleTimePeriodChange = (months) => {
    setSelectedTimePeriod(months);
    
    if (selectedHolding === 'all') {
      dispatch(fetchAllStocksHistoryRequest({ months }));
    } else {
      dispatch(fetchStockHistoryRequest({ 
        ticker: selectedHolding, 
        months 
      }));
    }
  };

  // Handle holding change
  const handleHoldingChange = (event) => {
    const holding = event.target.value;
    setSelectedHolding(holding);
    
    if (holding === 'all') {
      dispatch(fetchAllStocksHistoryRequest({ months: selectedTimePeriod }));
    } else {
      dispatch(fetchStockHistoryRequest({ 
        ticker: holding, 
        months: selectedTimePeriod 
      }));
    }
  };

  // Custom Tooltip
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white/95 backdrop-blur-sm p-4 border border-gray-200/50 rounded-xl shadow-2xl">
          <p className="text-sm font-semibold text-gray-900 mb-2">
            {new Date(data.date).toLocaleDateString('en-US', {
              weekday: 'short',
              year: 'numeric',
              month: 'short',
              day: 'numeric'
            })}
          </p>
          <div className="space-y-1">
            <p className="text-lg font-bold text-blue-600">
              ${data.totalValue?.toLocaleString('en-US', { minimumFractionDigits: 2 }) || '0.00'}
            </p>
            {data.totalShares && (
              <p className="text-sm text-gray-600">
                Shares: {data.totalShares?.toLocaleString()}
              </p>
            )}
            {data.price && (
              <p className="text-sm text-emerald-600">
                Price: ${data.price?.toFixed(2)}
              </p>
            )}
            {data.gainLossPercent !== undefined && (
              <p className={`text-sm font-medium ${data.gainLossPercent >= 0 ? 'text-emerald-600' : 'text-red-500'}`}>
                {data.gainLossPercent >= 0 ? '+' : ''}{data.gainLossPercent.toFixed(2)}%
              </p>
            )}
          </div>
        </div>
      );
    }
    return null;
  };

  const isLoading = selectedHolding === 'all' ? allStocksHistoryLoading : stockHistoryLoading;

  return (
    <div className="">
      {/* Main Card */}
      <div className="bg-gradient-to-br from-white to-gray-50/50 backdrop-blur-sm rounded-3xl shadow-xl border border-gray-200/50 overflow-hidden">
        {/* Header Section */}
        <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-700 p-8 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-white/20 rounded-2xl backdrop-blur-sm">
                <ChartIcon />
              </div>
              <div>
                <h1 className="text-3xl font-bold mb-2">Portfolio Analytics</h1>
                <p className="text-blue-100 text-lg">Track your investment performance with real-time insights</p>
              </div>
            </div>
            
            {/* Performance Metrics */}
            {performanceMetrics && (
              <div className="text-right">
                <div className="text-3xl font-bold mb-1">
                  ${performanceMetrics.currentValue.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                </div>
                <div className={`flex items-center justify-end space-x-2 text-lg font-semibold ${
                  performanceMetrics.isPositive ? 'text-emerald-300' : 'text-red-300'
                }`}>
                  {performanceMetrics.isPositive ? <TrendingUpIcon /> : <TrendingDownIcon />}
                  <span>
                    {performanceMetrics.isPositive ? '+' : ''}
                    {performanceMetrics.changePercent.toFixed(2)}%
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Controls Section */}
        <div className="p-6 bg-gray-50/50 border-b border-gray-200/50">
          <div className="flex flex-col lg:flex-row gap-6 items-start lg:items-center justify-between">
            {/* Time Period Pills */}
            <div className="flex items-center space-x-2">
              <CalendarIcon />
              <span className="text-sm font-medium text-gray-700 mr-3">Period:</span>
              <div className="flex bg-white rounded-xl p-1 shadow-sm border border-gray-200">
                {timePeriodOptions.map(option => (
                  <button
                    key={`time-${option.value}`}
                    onClick={() => handleTimePeriodChange(option.value)}
                    disabled={isLoading}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                      selectedTimePeriod === option.value
                        ? 'bg-blue-500 text-white shadow-sm'
                        : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
                    } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Holdings Selector */}
            <div className="flex items-center space-x-3">
              <WalletIcon />
              <span className="text-sm font-medium text-gray-700">Holdings:</span>
              <select
                value={selectedHolding}
                onChange={handleHoldingChange}
                disabled={isLoading || loading}
                className="bg-white border border-gray-200 rounded-xl px-4 py-2 pr-10 text-sm font-medium text-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 min-w-48"
              >
                {holdingOptions.map((option, index) => (
                  <option 
                    key={option.key || `holding-${option.value}-${index}`} 
                    value={option.value}
                  >
                    {option.icon} {option.fullLabel || option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Chart Type Toggle */}
            <div className="flex bg-white rounded-xl p-1 shadow-sm border border-gray-200">
              <button
                onClick={() => setChartType('line')}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                  chartType === 'line'
                    ? 'bg-purple-500 text-white shadow-sm'
                    : 'text-gray-600 hover:text-purple-600 hover:bg-purple-50'
                }`}
              >
                Line
              </button>
              <button
                onClick={() => setChartType('area')}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                  chartType === 'area'
                    ? 'bg-purple-500 text-white shadow-sm'
                    : 'text-gray-600 hover:text-purple-600 hover:bg-purple-50'
                }`}
              >
                Area
              </button>
            </div>
          </div>
        </div>

        {/* Chart Section */}
        <div className="p-8">
          {isLoading ? (
            <div className="h-96 flex items-center justify-center">
              <div className="text-center">
                <div className="relative">
                  <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-500 rounded-full animate-spin mx-auto mb-4"></div>
                  <div className="w-12 h-12 border-4 border-purple-200 border-t-purple-500 rounded-full animate-spin absolute top-2 left-1/2 transform -translate-x-1/2 opacity-60"></div>
                </div>
                <p className="text-gray-600 font-medium">Loading your portfolio data...</p>
                <p className="text-sm text-gray-500 mt-1">Analyzing market trends</p>
              </div>
            </div>
          ) : chartData.length > 0 ? (
            <div className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                {chartType === 'area' ? (
                  <AreaChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                    <defs>
                      <linearGradient id="colorGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3}/>
                        <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.05}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" opacity={0.6} />
                    <XAxis 
                      dataKey="x"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: '#6b7280', fontWeight: 500 }}
                      tickFormatter={(value) => {
                        const dataPoint = chartData[value];
                        if (dataPoint && dataPoint.date) {
                          return new Date(dataPoint.date).toLocaleDateString('en-US', { 
                            month: 'short', 
                            day: 'numeric' 
                          });
                        }
                        return value;
                      }}
                    />
                    <YAxis 
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: '#6b7280', fontWeight: 500 }}
                      tickFormatter={(value) => {
                        if (value >= 1000000) {
                          return `$${(value / 1000000).toFixed(1)}M`;
                        } else if (value >= 1000) {
                          return `$${(value / 1000).toFixed(0)}K`;
                        }
                        return `$${value}`;
                      }}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Area 
                      type="monotone" 
                      dataKey="y" 
                      stroke="#3b82f6" 
                      strokeWidth={3}
                      fill="url(#colorGradient)"
                      dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                      activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 3, fill: '#fff', shadow: true }}
                    />
                  </AreaChart>
                ) : (
                  <LineChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" opacity={0.6} />
                    <XAxis 
                      dataKey="x"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: '#6b7280', fontWeight: 500 }}
                      tickFormatter={(value) => {
                        const dataPoint = chartData[value];
                        if (dataPoint && dataPoint.date) {
                          return new Date(dataPoint.date).toLocaleDateString('en-US', { 
                            month: 'short', 
                            day: 'numeric' 
                          });
                        }
                        return value;
                      }}
                    />
                    <YAxis 
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: '#6b7280', fontWeight: 500 }}
                      tickFormatter={(value) => {
                        if (value >= 1000000) {
                          return `$${(value / 1000000).toFixed(1)}M`;
                        } else if (value >= 1000) {
                          return `$${(value / 1000).toFixed(0)}K`;
                        }
                        return `$${value}`;
                      }}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Line 
                      type="monotone" 
                      dataKey="y" 
                      stroke="#3b82f6" 
                      strokeWidth={3}
                      dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                      activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 3, fill: '#fff' }}
                    />
                  </LineChart>
                )}
              </ResponsiveContainer>
            </div>
          ) : (
            <div className="h-96 flex items-center justify-center">
              <div className="text-center max-w-md">
                <div className="w-24 h-24 mx-auto mb-6 rounded-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                  <span className="text-4xl">📊</span>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No Data Available</h3>
                <p className="text-gray-600 mb-4">
                  {selectedHolding === 'all' 
                    ? 'No portfolio data found for the selected time period'
                    : `No data available for ${selectedHolding} in the selected time period`
                  }
                </p>
                <div className="bg-blue-50 rounded-xl p-4">
                  <p className="text-sm text-blue-700">
                    💡 Try selecting a different time period or ensure your investment data is up to date.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer Info */}
        {chartData.length > 0 && (
          <div className="bg-gray-50/50 px-8 py-4 border-t border-gray-200/50">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <div className="flex items-center space-x-6">
                <span className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span>{selectedHolding === 'all' ? 'Portfolio Value' : `${selectedHolding} Value`}</span>
                </span>
                <span>{chartData.length} data points</span>
              </div>
              <div className="text-right">
                <span>Last updated: {new Date().toLocaleDateString()}</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default InvestmentsChart;