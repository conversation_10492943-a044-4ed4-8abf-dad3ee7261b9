import { ofType } from 'redux-observable';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { from, of } from 'rxjs';
import { axiosInstance } from '../api/axiosConfig';
import { getCurrentUserId } from '../../web/src/utils/AuthUtil';

import {
  // Sync actions
  syncInvestmentDataRequest,
  syncInvestmentDataSuccess,
  
  // Fetch actions
  fetchUserInvestmentsRequest,
  fetchUserInvestmentsSuccess,
  fetchDailyInvestmentStocksRequest,
  fetchDailyInvestmentStocksSuccess,
  fetchPortfolioSummaryRequest,
  fetchPortfolioSummarySuccess,
  fetchInvestmentByTickerRequest,
  fetchInvestmentByTickerSuccess,
  
  // Stock history actions
  fetchAllStocksHistoryRequest,
  fetchAllStocksHistorySuccess,
  fetchStockHistoryRequest,
  fetchStockHistorySuccess,
  
  // Common failure action
  investmentActionFailure
} from '../redux/investmentsSlice';

// Helper function to get interval days based on months
const getIntervalDays = (months) => {
  const intervalMapping = {
    1: 3,   // 1 month -> 3 days interval
    3: 10,  // 3 months -> 10 days interval
    6: 20,  // 6 months -> 20 days interval
    12: 41  // 12 months -> 41 days interval
  };
  return intervalMapping[months] || 3; // Default to 3 if not found
};

// Epic for syncing investment data
export const syncInvestmentDataEpic = (action$) =>
  action$.pipe(
    ofType(syncInvestmentDataRequest.type),
    mergeMap((action) => {
      const userId = getCurrentUserId();
      
      if (!userId || userId === '0') {
        console.error('User not authenticated for sync investment data');
        return of(investmentActionFailure('User not authenticated'));
      }

      return from(axiosInstance.post(`/pennypal/api/investments/sync/${userId}`)).pipe(
        map((response) => {
          console.log("Sync investment API Response:", response.data);
          if (response.data.success) {
            return syncInvestmentDataSuccess({
              message: response.data.message,
              userId: response.data.userId,
              timestamp: response.data.timestamp
            });
          } else {
            return investmentActionFailure(response.data.error || 'Sync failed');
          }
        }),
        catchError((error) => {
          console.error('Sync investment data failed:', error);
          const errorMsg = error.response?.data?.error || error.response?.data?.message || 'Failed to sync investment data';
          return of(investmentActionFailure(errorMsg));
        })
      );
    })
  );

// Epic for fetching user investments
export const fetchUserInvestmentsEpic = (action$) =>
  action$.pipe(
    ofType(fetchUserInvestmentsRequest.type),
    mergeMap(() => {
      const userId = getCurrentUserId();

      if (!userId || userId === '0') {
        console.error('User not authenticated for fetching investments');
        return of(investmentActionFailure('User not authenticated'));
      }

      return from(axiosInstance.get(`/pennypal/api/investments/user/${userId}`)).pipe(
        map((response) => {
          console.log('User investments API response:', response.data);
          if (response.data.success) {
            return fetchUserInvestmentsSuccess({
              investments: response.data.investments || [],
              count: response.data.count || 0,
              userId: response.data.userId
            });
          } else {
            return investmentActionFailure(response.data.error || 'Failed to fetch investments');
          }
        }),
        catchError((error) => {
          console.error('Fetch user investments failed:', error);
          const errorMsg = error.response?.data?.error || error.response?.data?.message || 'Failed to fetch user investments';
          return of(investmentActionFailure(errorMsg));
        })
      );
    })
  );

// Epic for fetching daily investment stocks
export const fetchDailyInvestmentStocksEpic = (action$) =>
  action$.pipe(
    ofType(fetchDailyInvestmentStocksRequest.type),
    mergeMap((action) => {
      const userId = getCurrentUserId();
      const { date, ticker } = action.payload || {};
      
      if (!userId || userId === '0') {
        console.error('User not authenticated for daily investment stocks');
        return of(investmentActionFailure('User not authenticated'));
      }

      const params = {};
      if (date) params.date = date;
      if (ticker) params.ticker = ticker;

      return from(axiosInstance.get(`/pennypal/api/investments/daily/${userId}`, { params })).pipe(
        map((response) => {
          console.log("Daily investment stocks API Response:", response.data);
          if (response.data.success) {
            return fetchDailyInvestmentStocksSuccess({
              dailyStocks: response.data.dailyStocks || [],
              count: response.data.count || 0,
              filters: response.data.filters || {},
              userId: response.data.userId
            });
          } else {
            return investmentActionFailure(response.data.error || 'Failed to fetch daily stocks');
          }
        }),
        catchError((error) => {
          console.error('Fetch daily investment stocks failed:', error);
          const errorMsg = error.response?.data?.error || error.response?.data?.message || 'Failed to fetch daily investment stocks';
          return of(investmentActionFailure(errorMsg));
        })
      );
    })
  );

// Epic for fetching portfolio summary
export const fetchPortfolioSummaryEpic = (action$) =>
  action$.pipe(
    ofType(fetchPortfolioSummaryRequest.type),
    mergeMap((action) => {
      const userId = getCurrentUserId();
      
      if (!userId || userId === '0') {
        console.error('User not authenticated for portfolio summary');
        return of(investmentActionFailure('User not authenticated'));
      }

      return from(axiosInstance.get(`/pennypal/api/investments/portfolio/${userId}`)).pipe(
        map((response) => {
          console.log("Portfolio summary API Response:", response.data);
          if (response.data.success) {
            return fetchPortfolioSummarySuccess(response.data);
          } else {
            return investmentActionFailure(response.data.error || 'Failed to fetch portfolio summary');
          }
        }),
        catchError((error) => {
          console.error('Fetch portfolio summary failed:', error);
          const errorMsg = error.response?.data?.error || error.response?.data?.message || 'Failed to fetch portfolio summary';
          return of(investmentActionFailure(errorMsg));
        })
      );
    })
  );

// Epic for fetching investment by ticker
export const fetchInvestmentByTickerEpic = (action$) =>
  action$.pipe(
    ofType(fetchInvestmentByTickerRequest.type),
    mergeMap((action) => {
      const userId = getCurrentUserId();
      const { ticker } = action.payload || {};
      
      if (!userId || userId === '0') {
        console.error('User not authenticated for investment by ticker');
        return of(investmentActionFailure('User not authenticated'));
      }

      if (!ticker) {
        console.error('Ticker is required for fetching investment by ticker');
        return of(investmentActionFailure('Ticker is required'));
      }

      return from(axiosInstance.get(`/pennypal/api/investments/ticker/${userId}`, { 
        params: { ticker } 
      })).pipe(
        map((response) => {
          console.log("Investment by ticker API Response:", response.data);
          if (response.data.success) {
            return fetchInvestmentByTickerSuccess({
              investment: response.data.investment || null,
              userId: response.data.userId
            });
          } else {
            return investmentActionFailure(response.data.error || 'Failed to fetch investment by ticker');
          }
        }),
        catchError((error) => {
          console.error('Fetch investment by ticker failed:', error);
          const errorMsg = error.response?.data?.error || error.response?.data?.message || 'Failed to fetch investment by ticker';
          return of(investmentActionFailure(errorMsg));
        })
      );
    })
  );

// Epic for fetching all stocks aggregated data
export const fetchAllStocksHistoryEpic = (action$) =>
  action$.pipe(
    ofType(fetchAllStocksHistoryRequest.type),
    mergeMap((action) => {
      const userId = getCurrentUserId();
      const { months = 1 } = action.payload || {}; // Default to 1 month
      
      if (!userId || userId === '0') {
        console.error('User not authenticated for all stocks history');
        return of(investmentActionFailure('User not authenticated'));
      }

      const intervalDays = getIntervalDays(months);
      const params = {
        userId: parseInt(userId),
        months: months,
        intervalDays: intervalDays
      };

      console.log('Fetching all stocks history with params:', params);

      return from(axiosInstance.get('/pennypal/api/investments/aggregated/all', { params })).pipe(
        map((response) => {
          console.log("All stocks aggregated data API Response:", response.data);
          if (response.data) {
            return fetchAllStocksHistorySuccess({
              data: response.data,
              months: months,
              intervalDays: intervalDays
            });
          } else {
            return investmentActionFailure('Failed to fetch all stocks history');
          }
        }),
        catchError((error) => {
          console.error('Fetch all stocks history failed:', error);
          const errorMsg = error.response?.data?.error || error.response?.data?.message || 'Failed to fetch all stocks aggregated data';
          return of(investmentActionFailure(errorMsg));
        })
      );
    })
  );

// Epic for fetching single stock aggregated data
export const fetchStockHistoryEpic = (action$) =>
  action$.pipe(
    ofType(fetchStockHistoryRequest.type),
    mergeMap((action) => {
      const userId = getCurrentUserId();
      const { ticker, months = 1 } = action.payload || {}; // Default to 1 month
      
      if (!userId || userId === '0') {
        console.error('User not authenticated for stock history');
        return of(investmentActionFailure('User not authenticated'));
      }

      if (!ticker) {
        console.error('Ticker is required for fetching stock history');
        return of(investmentActionFailure('Ticker is required'));
      }

      const intervalDays = getIntervalDays(months);
      const params = {
        userId: parseInt(userId),
        ticker: ticker,
        months: months,
        intervalDays: intervalDays
      };

      console.log('Fetching stock history with params:', params);

      return from(axiosInstance.get('/pennypal/api/investments/aggregated', { params })).pipe(
        map((response) => {
          console.log(`Stock aggregated data API Response for ${ticker}:`, response.data);
          if (response.data) {
            return fetchStockHistorySuccess({
              data: response.data,
              ticker: ticker,
              months: months,
              intervalDays: intervalDays
            });
          } else {
            return investmentActionFailure(`Failed to fetch stock history for ${ticker}`);
          }
        }),
        catchError((error) => {
          console.error(`Fetch stock history failed for ${ticker}:`, error);
          const errorMsg = error.response?.data?.error || error.response?.data?.message || `Failed to fetch stock aggregated data for ${ticker}`;
          return of(investmentActionFailure(errorMsg));
        })
      );
    })
  );

// Combine all investment epics
export const investmentsEpics = [
  syncInvestmentDataEpic,
  fetchUserInvestmentsEpic,
  fetchDailyInvestmentStocksEpic,
  fetchPortfolioSummaryEpic,
  fetchInvestmentByTickerEpic,
  fetchAllStocksHistoryEpic,
  fetchStockHistoryEpic,
];

export default investmentsEpics;