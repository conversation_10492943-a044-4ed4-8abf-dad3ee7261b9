import React from "react";

import { useDispatch, useSelector } from "react-redux";

import { useState, useRef, useEffect } from "react";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import PictureAsPdf from "@mui/icons-material/PictureAsPdf";

import {
  AddCircleOutline,
  VisibilityOff,
  FilterList,
  Upload,
  Receipt,
  Download,
} from "@mui/icons-material";

import {
  fetchTransactionsStart,
  setSelectedTransaction,
  setOpenModal,
  toggleCardVisibility,
  setSearchName,
  setStartDate,
  setEndDate,
  setSearchDate,
  setSelectedDateRange,
  toggleDateFilter,
  setSortOrder,
  toggleSelectTransaction,
  toggleSelectAll,
  toggleAddTransactionModal,
  updateNewTransaction,
  resetNewTransaction,
  applyFilters,
  addTransactionRequest,
  fetchTransactionSummaryStart,
  setCustomSearchText,
  selectAllTransactions,
  deselectAllTransactions,
  setPage,
} from "@pp-logic/redux/transactionSlice";

import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Button,
  IconButton,
  InputAdornment,
  FormControl,
  InputLabel,
  LinearProgress,
  CircularProgress,
  TextField,
  Table,
  TableHead,
  Tooltip,
  Select,
  Grid2,
  Paper,
  ListSubheader,
  TableBody,
  TableRow,
  TableCell,
  Tabs,
  Tab,
  MenuItem,
  Radio,
  Box,
  Typography,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import AccountBalanceWalletIcon from "@mui/icons-material/AccountBalanceWallet";

const TransactionControls = ({
  selectedTransactions,
  isProcessing,
  setReceiptUploadModal,
}) => {
  const dispatch = useDispatch();
  // Refs
  const dateFilterRef = useRef(null);
  const customText = useSelector(
    (state) => state.transactions.customSearchText
  );
  const dateFilterOpen = useSelector(
    (state) => state.transactions.dateFilterOpen
  );
  const selectedDateRange = useSelector(
    (state) => state.transactions.selectedDateRange
  );

  const exportDropdownRef = useRef(null);
  //dropdown for export data
  const [exportDropdownOpen, setExportDropdownOpen] = useState(false);

  // Receipts state
  const {
    errorMessage,
    jsonResponse,
    currentTab,
    receiptUploadModal,
    showError,
    blinkError,
    isReceiptModalOpen,
    selectedReceipt,
    // selectedFile,
    fileMetadata,
    // selectedTransaction,
    uploadPopupWidth,
    editingField,
    editedValue,
    editedItemIndex,
    selectedDate,
    uploadProgress,
    isUploading,
    //isProcessing,
    isPopupVisible,

    //  receiptNewTransaction,
    receiptTransactionIds,
  } = useSelector((state) => state.receipts);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        exportDropdownRef.current &&
        !exportDropdownRef.current.contains(event.target)
      ) {
        setExportDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Open receipt modal
  const toggleReceiptModal = () => {
    dispatch(setReceiptUploadModal(true));
    dispatch(setJsonResponse(null));
    dispatch(setErrorMessage("")); // Clear error message
    dispatch(setShowError(false));
  };
  const selectDateRange = (range) => {
    dispatch(setSelectedDateRange(range));
    logEvent("DateFilter", "SelectRange", { range });

    const today = new Date();
    let start = null;
    let end = null;

    switch (range) {
      case "last7days":
        start = new Date(today);
        start.setDate(today.getDate() - 6); // Include today
        end = today;
        break;
      case "currentMonth":
        start = new Date(today.getFullYear(), today.getMonth(), 1);
        end = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        break;
      case "lastMonth":
        start = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        end = new Date(today.getFullYear(), today.getMonth(), 0);
        break;
      case "custom":
        setCustomFilterOpen(true); // Open the input field
        dispatch(setSelectedDateRange("custom")); // ✅ Important!
        // dispatch(applyFilters());
        return;

      case "all":
        dispatch(setSearchDate({ start: "", end: "" }));
        dispatch(applyFilters());
        dispatch(toggleDateFilter());
        return;
    }

    if (start && end) {
      dispatch(
        setSearchDate({
          start: start.toISOString().split("T")[0],
          end: end.toISOString().split("T")[0],
        })
      );
      dispatch(applyFilters());
    }

    dispatch(toggleDateFilter());
  };

  const handleOpenAddTransaction = () => {
    dispatch(
      setSelectedTransaction({
        date: new Date().toISOString().split("T")[0],
        name: "",
        subcategory: "",
        bank: "",
        amount: "",
        tax: "",
        notes: "",
        tag: "",
        hideFromBudget: false,
        hidden: false,
      })
    );
    dispatch(setIsMatchingTransactionAdd(false));
    dispatch(setOpenModal(true));
  };

  const exportToCSV = () => {
    const headers = [
      "Transaction Date",
      "Description",
      "Amount",
      "Category",
      "Account Name",
    ];

    const rows = transactions.map((tx) => [
      tx.transactionDate || "",
      tx.description || "",
      tx.transactionAmount || "",
      tx.category || "",
      tx.accountName || "",
    ]);
    const csvContent =
      "data:text/csv;charset=utf-8," +
      [headers, ...rows].map((e) => e.join(",")).join("\n");

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "transactions.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const exportToPDF = () => {
    const doc = new jsPDF({ orientation: "landscape" });

    if (!transactions.length) return;

    const headers = [
      ["Transaction Date", "Description", "Amount", "Category", "Account Name"],
    ];

    const data = transactions.map((tx) => [
      tx.transactionDate || "",
      tx.description || "",
      tx.transactionAmount || "",
      tx.category || "",
      tx.accountName || "",
    ]);

    autoTable(doc, {
      head: headers,
      body: data,
      styles: {
        fontSize: 7,
        cellPadding: 2,
        overflow: "linebreak",
        cellWidth: "wrap",
      },
      headStyles: {
        fillColor: [139, 195, 74],
        fontSize: 7,
      },
      startY: 10,
      margin: { left: 5, right: 5 },
      theme: "striped",
    });

    doc.save("transactions.pdf");
  };

  return (
    <div className="flex-1 p-4 overflow-visible relative">
      {/* Search and Button Container - Fixed positioning */}
      <div className="flex items-center justify-between gap-4 mb-4 w-full max-w-7xl mx-auto">
        {" "}
        {/* Left side - Search */}
        <div className="flex-shrink-0 w-60">
          <input
            type="text"
            placeholder="Search by description (e.g., KFC, Spotify)"
            className="border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 w-60"
            value={customText}
            onChange={(e) => {
              dispatch(setCustomSearchText(e.target.value));
              dispatch(setSelectedDateRange("custom"));
              dispatch(applyFilters());
            }}
          />
        </div>
        {/* Add Transaction Button */}
        <div className="flex flex-wrap items-center gap-2 flex-shrink-0">
          <Tooltip title="Add New Transaction" placement="top">
            <button
              className="bg-[#8bc34a] hover:bg-[#7cb342] text-black p-2 rounded flex items-center"
              onClick={handleOpenAddTransaction}
            >
              <AddCircleOutline fontSize="small" />
            </button>
          </Tooltip>

          {/* Hide Selected Button */}
          <Tooltip title="Hide Selected Transactions" placement="top">
            <button
              className="bg-[#8bc34a] hover:bg-[#7cb342] text-black p-2 rounded flex items-center disabled:opacity-50"
              onClick={() => {
                if (selectedTransactions.length === 0) return;
                console.log("Selected transactions:", selectedTransactions);
                if (
                  window.confirm(
                    "Are you sure you want to hide the selected transactions?"
                  )
                ) {
                  dispatch({
                    type: "transactions/hideTransactions",
                    payload: selectedTransactions,
                  });
                }
              }}
              disabled={selectedTransactions.length === 0 || isProcessing}
            >
              {isProcessing ? (
                <CircularProgress size={20} className="mr-2" />
              ) : (
                <VisibilityOff fontSize="small" />
              )}
            </button>
          </Tooltip>

          {/* Hide from Budget Button */}
          <Tooltip
            title="Hide Selected Transactions from Budget"
            placement="top"
          >
            <button
              className="bg-[#8bc34a] hover:bg-[#7cb342] text-black p-2 rounded flex items-center disabled:opacity-50"
              onClick={() => {
                if (selectedTransactions.length === 0) return;
                console.log(
                  "Selected transactions to hide from budget:",
                  selectedTransactions
                );
                if (
                  window.confirm(
                    "Are you sure you want to hide the selected transactions from budget?"
                  )
                ) {
                  dispatch({
                    type: "transactions/hideFromBudget",
                    payload: selectedTransactions,
                  });
                }
              }}
              disabled={selectedTransactions.length === 0 || isProcessing}
            >
              {isProcessing ? (
                <CircularProgress size={20} className="mr-2" />
              ) : (
                <AccountBalanceWalletIcon fontSize="small" />
              )}
            </button>
          </Tooltip>

          {/* Filter By Button */}
          <div className="relative z-50">
            <Tooltip title="Filter Transactions" placement="top">
              <div
                className="bg-[#8bc34a] hover:bg-[#7cb342] text-black p-2 rounded flex items-center cursor-pointer relative"
                onClick={() => dispatch(toggleDateFilter())}
              >
                <FilterList fontSize="small" />

                {/* Date Filter Dropdown */}
                {dateFilterOpen && (
                  <div
                    ref={dateFilterRef}
                    className="absolute top-full right-0 bg-white shadow-xl border border-gray-200 rounded-xl overflow-hidden z-50 min-w-48 animate-in slide-in-from-top-2 duration-200 mt-1"
                  >
                    <div className="py-2">
                      <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-b border-gray-100">
                        Date Range
                      </div>

                      <button
                        onClick={() => selectDateRange("all")}
                        className="w-full px-4 py-3 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 text-left transition-all duration-150 flex items-center gap-3 group"
                      >
                        <div className="w-2 h-2 rounded-full bg-gray-300 group-hover:bg-blue-500 transition-colors duration-150"></div>
                        <span className="font-medium">All</span>
                      </button>

                      <button
                        onClick={() => selectDateRange("last7days")}
                        className="w-full px-4 py-3 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 text-left transition-all duration-150 flex items-center gap-3 group"
                      >
                        <div className="w-2 h-2 rounded-full bg-gray-300 group-hover:bg-green-500 transition-colors duration-150"></div>
                        <span className="font-medium">Last 7 Days</span>
                      </button>

                      <button
                        onClick={() => selectDateRange("currentMonth")}
                        className="w-full px-4 py-3 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-700 text-left transition-all duration-150 flex items-center gap-3 group"
                      >
                        <div className="w-2 h-2 rounded-full bg-gray-300 group-hover:bg-purple-500 transition-colors duration-150"></div>
                        <span className="font-medium">Current Month</span>
                      </button>

                      <button
                        onClick={() => selectDateRange("lastMonth")}
                        className="w-full px-4 py-3 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-700 text-left transition-all duration-150 flex items-center gap-3 group"
                      >
                        <div className="w-2 h-2 rounded-full bg-gray-300 group-hover:bg-orange-500 transition-colors duration-150"></div>
                        <span className="font-medium">Last Month</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </Tooltip>
          </div>

          {/* Upload Receipt Button */}
          <Tooltip title="Upload Receipt" placement="top">
            <button
              className="bg-[#8bc34a] hover:bg-[#7cb342] text-black p-2 rounded flex items-center"
              onClick={toggleReceiptModal}
            >
              <Receipt fontSize="small" />
            </button>
          </Tooltip>

          {/* Export Data Button with Dropdown */}
          <div className="relative z-50">
            <Tooltip title="Export Transactions" placement="top">
              <button
                className="bg-[#8bc34a] hover:bg-[#7cb342] text-black p-2 rounded flex items-center"
                onClick={() => setExportDropdownOpen(!exportDropdownOpen)}
              >
                <Download fontSize="small" />
              </button>
            </Tooltip>

            {/* Export Options Dropdown */}
            {exportDropdownOpen && (
              <div
                ref={exportDropdownRef}
                className="absolute top-full right-0 bg-white shadow-xl border border-gray-200 rounded-xl overflow-hidden z-50 min-w-40 animate-in slide-in-from-top-2 duration-200 mt-1"
              >
                <div className="py-2">
                  <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-b border-gray-100">
                    Export Format
                  </div>

                  {/* CSV Export Option */}
                  <button
                    onClick={() => {
                      exportToCSV();
                      setExportDropdownOpen(false);
                    }}
                    className="w-full px-4 py-3 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 text-left transition-all duration-150 flex items-center gap-3 group"
                  >
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-200 transition-colors duration-150">
                      <svg
                        className="w-4 h-4 text-green-600"
                        fill="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                      </svg>
                    </div>
                    <div>
                      <div className="font-medium">Export as CSV</div>
                      <div className="text-xs text-gray-500">
                        Spreadsheet format
                      </div>
                    </div>
                  </button>

                  {/* PDF Export Option */}
                  <button
                    onClick={() => {
                      exportToPDF();
                      setExportDropdownOpen(false);
                    }}
                    className="w-full px-4 py-3 text-sm text-gray-700 hover:bg-red-50 hover:text-red-700 text-left transition-all duration-150 flex items-center gap-3 group"
                  >
                    <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center group-hover:bg-red-200 transition-colors duration-150">
                      <PictureAsPdf className="text-red-600" fontSize="small" />
                    </div>
                    <div>
                      <div className="font-medium">Export as PDF</div>
                      <div className="text-xs text-gray-500">
                        Document format
                      </div>
                    </div>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransactionControls;
