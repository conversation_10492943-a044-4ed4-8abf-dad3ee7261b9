import '@testing-library/jest-dom'

// Global test utilities for React components
global.testUtils = {
  // Mock window.matchMedia for responsive components
  mockMatchMedia: (query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: () => {},
    removeListener: () => {},
    addEventListener: () => {},
    removeEventListener: () => {},
    dispatchEvent: () => {},
  }),

  // Mock IntersectionObserver
  mockIntersectionObserver: class {
    observe() {}
    unobserve() {}
    disconnect() {}
  }
}

// Setup global mocks
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: global.testUtils.mockMatchMedia,
})

Object.defineProperty(global, 'IntersectionObserver', {
  writable: true,
  value: global.testUtils.mockIntersectionObserver,
})

// Mock localStorage
Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: vi.fn(() => null),
    setItem: vi.fn(() => null),
    removeItem: vi.fn(() => null),
    clear: vi.fn(() => null),
  },
  writable: true,
})

// Mock sessionStorage
Object.defineProperty(window, 'sessionStorage', {
  value: {
    getItem: vi.fn(() => null),
    setItem: vi.fn(() => null),
    removeItem: vi.fn(() => null),
    clear: vi.fn(() => null),
  },
  writable: true,
})

console.log('✅ React tests setup complete')