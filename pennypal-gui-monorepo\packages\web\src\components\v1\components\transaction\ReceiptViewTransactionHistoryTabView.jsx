import React from "react";

const ReceiptViewTransactionHistoryTabView = () => {
  return (
    <DialogContent
      sx={{ padding: "16px", height: "calc(80vh - 150px)", overflowY: "auto" }}
    >
      <Typography variant="h6" sx={{ mb: 2 }}>
        Matching Transactions
      </Typography>

      <Box sx={{ flex: 1, overflow: "auto", mb: 2 }}>
        <Paper sx={{ overflow: "auto" }}>
          <Table>
            <TableHead>
              <TableRow sx={{ backgroundColor: "#8BC34A" }}>
                <TableCell
                  sx={{ color: "#333", fontWeight: "bold" }}
                ></TableCell>
                <TableCell sx={{ color: "#333", fontWeight: "bold" }}>
                  Date
                </TableCell>
                <TableCell sx={{ color: "#333", fontWeight: "bold" }}>
                  Transaction Name
                </TableCell>
                <TableCell sx={{ color: "#333", fontWeight: "bold" }}>
                  Category
                </TableCell>
                <TableCell sx={{ color: "#333", fontWeight: "bold" }}>
                  Account
                </TableCell>
                <TableCell sx={{ color: "#333", fontWeight: "bold" }}>
                  Amount
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {jsonResponse?.matchingTransactions?.filter((tx) => tx).length >
              0 ? (
                jsonResponse.matchingTransactions
                  .filter((tx) => tx) // Filter out any null/undefined
                  .map((transaction, index) => (
                    <TableRow
                      key={index}
                      sx={{
                        backgroundColor:
                          selectedTransaction === transaction.transactionId
                            ? "#f0f7f0"
                            : "transparent",
                        "&:hover": { backgroundColor: "#f5f5f5" },
                      }}
                    >
                      <TableCell>
                        <Radio
                          checked={
                            selectedTransaction === transaction.transactionId
                          }
                          onChange={() =>
                            dispatch(
                              setSelectedTransaction(transaction.transactionId)
                            )
                          }
                          color="primary"
                          sx={{
                            color: "#8BC34A",
                            "&.Mui-checked": { color: "#8BC34A" },
                          }}
                        />
                      </TableCell>
                      <TableCell>
                        {transaction.transactionDate
                          ? new Date(
                              transaction.transactionDate
                            ).toLocaleDateString()
                          : "-"}
                      </TableCell>
                      <TableCell>{transaction.description || "-"}</TableCell>
                      <TableCell>{transaction.category || "-"}</TableCell>
                      <TableCell>
                        {transaction.account ||
                          accounts.find(
                            (a) => a.accountId === transaction.accountId
                          )?.accountName ||
                          "-"}
                      </TableCell>
                      <TableCell>
                        ${transaction.transactionAmount?.toFixed(2) || "0.00"}
                      </TableCell>
                    </TableRow>
                  ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={6}
                    sx={{ textAlign: "center", padding: "16px" }}
                  >
                    No matching transactions found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </Paper>
      </Box>

      <Box sx={{ mt: "auto", marginBottom: "40px" }}>
        <Button
          onClick={handleShowPopup}
          color="primary"
          variant="contained"
          sx={{
            fontFamily: "Roboto, sans-serif",
            mt: 2,
            backgroundColor: "#8BC34A",
            "&:hover": { backgroundColor: "#7CB342" },
            width: "100%",
          }}
          fullWidth
        >
          Add Transaction
        </Button>
      </Box>
    </DialogContent>
  );
};

export default ReceiptViewTransactionHistoryTabView;
