import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  loading: false,
  error: null,
  summary: null
};

const budgetDashboardSlice = createSlice({
  name: 'budgetDashboard',
  initialState,
  reducers: {
    fetchBudgetDashboardData: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchBudgetDashboardDataSuccess: (state, action) => {
      state.loading = false;
      state.summary = action.payload.data;
    },
    fetchBudgetDashboardDataFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload.error;
    },
    setBudgetSummary: (state, action) => {
      state.summary = action.payload;
      state.loading = false;
      state.error = null;
    },
    setError: (state, action) => {
      state.error = action.payload;
      state.loading = false;
    },
    resetBudgetDashboard: () => initialState
  }
});

export const {
  fetchBudgetDashboardData,
  fetchBudgetDashboardDataSuccess,
  fetchBudgetDashboardDataFailure,
  setBudgetSummary,
  setError,
  resetBudgetDashboard
} = budgetDashboardSlice.actions;

export default budgetDashboardSlice.reducer;
