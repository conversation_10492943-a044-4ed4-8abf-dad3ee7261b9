import { combineEpics, ofType } from 'redux-observable';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { from, of } from 'rxjs';
import {
  inviteFamilyMemberRequest,
  inviteFamilyMemberSuccess,
  inviteFamilyMemberFailure,
  validateInviteLinkRequest,
  validateInviteLinkSuccess,
  validateInviteLinkFailure,
  completeSignupRequest,
  completeSignupSuccess,
  completeSignupFailure,
  getFamilyMembersRequest,
  getFamilyMembersSuccess,
  getFamilyMembersFailure,
  revokeFamilyMemberRequest,
  revokeFamilyMemberSuccess,
  revokeFamilyMemberFailure
} from '../redux/memberSlice';
import { axiosInstance } from '../api/axiosConfig';
import { getCurrentUserId } from '../utils/authUtils'; // Import the auth utility

// Epic for inviting a family member
const inviteFamilyMemberEpic = (action$) => action$.pipe(
  ofType(inviteFamilyMemberRequest.type),
  mergeMap(action => {
    const currentUserId = getCurrentUserId();
    
    // Optional: Add currentUserId to payload if needed by your API
    const payload = {
      ...action.payload,
      // invitedBy: currentUserId // Uncomment if your API needs this
    };

    return from(
      axiosInstance.post('/pennypal/api/membership/invite', payload)
    ).pipe(
      map(response => inviteFamilyMemberSuccess(response.data)),
      catchError(error => {
        console.error('Error inviting family member:', error);
        return of(inviteFamilyMemberFailure(
          error.response?.data?.message || 'Failed to send invite'
        ));
      })
    );
  })
);

// Epic for validating an invite link
const validateInviteLinkEpic = (action$) => action$.pipe(
  ofType(validateInviteLinkRequest.type),
  mergeMap(action => {
    const currentUserId = getCurrentUserId();
    
    return from(
      axiosInstance.get(`/pennypal/api/membership/validate-invite?token=${action.payload}`)
    ).pipe(
      map(response => validateInviteLinkSuccess({
        ...response.data,
        // currentUserId // Add if needed for frontend state management
      })),
      catchError(error => {
        console.error('Error validating invite link:', error);
        return of(validateInviteLinkFailure(
          error.response?.data?.message || 'Invalid or expired invite link'
        ));
      })
    );
  })
);

// Epic for completing a signup process
const completeSignupEpic = (action$) => action$.pipe(
  ofType(completeSignupRequest.type),
  mergeMap(action => {
    const currentUserId = getCurrentUserId();
    
    // Add currentUserId to signup details if needed
    const signupDetails = {
      ...action.payload.signupDetails,
      // completedBy: currentUserId // Uncomment if your API needs this
    };

    return from(
      axiosInstance.post(
        `/pennypal/api/membership/complete-signup?token=${action.payload.token}`,
        signupDetails
      )
    ).pipe(
      map(response => completeSignupSuccess(response.data)),
      catchError(error => {
        console.error('Error completing signup:', error);
        return of(completeSignupFailure(
          error.response?.data?.message || 'Failed to complete signup'
        ));
      })
    );
  })
);

// Epic for fetching family members
const getFamilyMembersEpic = (action$) => action$.pipe(
  ofType(getFamilyMembersRequest.type),
  mergeMap(() => {
    const currentUserId = getCurrentUserId();
    
    // Optional: Add currentUserId as query param if your API needs it
    const endpoint = '/pennypal/api/membership/family-members';
    // const endpoint = `/pennypal/api/membership/family-members?userId=${currentUserId}`;

    return from(
      axiosInstance.get(endpoint)
    ).pipe(
      map(response => getFamilyMembersSuccess({
        ...response.data,
        currentUserId // Add currentUserId to help with frontend logic
      })),
      catchError(error => {
        console.error('Error fetching family members:', error);
        return of(getFamilyMembersFailure(
          error.response?.data?.message || 'Failed to fetch family members'
        ));
      })
    );
  })
);

// Epic for revoking a family member's access
const revokeFamilyMemberEpic = (action$) => action$.pipe(
  ofType(revokeFamilyMemberRequest.type),
  mergeMap(action => {
    const currentUserId = getCurrentUserId();
    
    return from(
      axiosInstance.delete(`/pennypal/api/membership/revoke/${action.payload}`)
    ).pipe(
      map(response => revokeFamilyMemberSuccess({
        ...response.data,
        relationshipId: action.payload,
        revokedBy: currentUserId // Track who performed the revocation
      })),
      catchError(error => {
        console.error('Error revoking family member access:', error);
        return of(revokeFamilyMemberFailure(
          error.response?.data?.message || 'Failed to revoke family member access'
        ));
      })
    );
  })
);

// Combine all epics
const membershipEpics = combineEpics(
  inviteFamilyMemberEpic,
  validateInviteLinkEpic,
  completeSignupEpic,
  getFamilyMembersEpic,
  revokeFamilyMemberEpic
);

export default membershipEpics;