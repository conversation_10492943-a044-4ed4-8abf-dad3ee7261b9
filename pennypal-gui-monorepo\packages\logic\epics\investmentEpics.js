import { ofType } from 'redux-observable';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { from, of } from 'rxjs';
import { axiosInstance } from '../api/axiosConfig';

import {
  fetchInvestmentsRequest,
  fetchPerformanceRequest,
  fetchInvestmentAccountsRequest,
  fetchPortfolioDiversityRequest,
  fetchPortfolioOptimizationRequest,
  // syncPlaidInvestmentsRequest,
  fetchInvestmentsSuccess,
  fetchPerformanceSuccess,
  fetchInvestmentAccountsSuccess,
  fetchPortfolioDiversitySuccess,
  fetchPortfolioOptimizationSuccess,
  // syncPlaidInvestmentsSuccess,
  investmentActionFailure
} from '../redux/investmentSlice';

// Epic for fetching user investments
// export const fetchSync = (action$) =>
//   action$.pipe(
//     ofType(fetchInvestmentsRequest.type),
//     mergeMap((action) => {
//       const userId = action.payload;
//       return from(axiosInstance.get(`/pennypal/api/mock/investments/user/${userId}`)).pipe(
//         map((response) =>   fetchInvestmentsSuccess(response.data)),
 
//         catchError((error) => 
//           of(investmentActionFailure(error.response?.data?.message || 'Failed to fetch investments'))
//         )
//       );
//     })
//   );

// Epic for fetching investment performance metrics
export const fetchPerformanceEpic = (action$) =>
  action$.pipe(
    ofType(fetchPerformanceRequest.type),
    mergeMap((action) => {
      const userId = action.payload;
      return from(axiosInstance.get(`/pennypal/api/mock/investments/performance/${userId}`)).pipe(
        map((response) => fetchPerformanceSuccess(response.data)),
        catchError((error) => 
          of(investmentActionFailure(error.response?.data?.message || 'Failed to fetch performance metrics'))
        )
      );
    })
  );

// Epic for fetching investment accounts
export const fetchInvestmentAccountsEpic = (action$) =>
  action$.pipe(
    ofType(fetchInvestmentAccountsRequest.type),
    mergeMap((action) => {
      const userId = action.payload;
      return from(axiosInstance.get(`/pennypal/api/mock/investments/accounts/${userId}`)).pipe(
        map((response) => fetchInvestmentAccountsSuccess(response.data)),
        catchError((error) => 
          of(investmentActionFailure(error.response?.data?.message || 'Failed to fetch investment accounts'))
        )
      );
    })
  );

// Epic for fetching portfolio diversity
export const fetchPortfolioDiversityEpic = (action$) =>
  action$.pipe(
    ofType(fetchPortfolioDiversityRequest.type),
    mergeMap((action) => {
      const userId = action.payload;
      return from(axiosInstance.get(`/pennypal/api/mock/investments/diversity/${userId}`)).pipe(
        map((response) => fetchPortfolioDiversitySuccess(response.data)),
        catchError((error) => 
          of(investmentActionFailure(error.response?.data?.message || 'Failed to fetch portfolio diversity'))
        )
      );
    })
  );

// Epic for fetching portfolio optimization
export const fetchPortfolioOptimizationEpic = (action$) =>
  action$.pipe(
    ofType(fetchPortfolioOptimizationRequest.type),
    mergeMap((action) => {
      const userId = action.payload;
      return from(axiosInstance.get(`/pennypal/api/mock/investments/optimization/${userId}`)).pipe(
        map((response) => fetchPortfolioOptimizationSuccess(response.data)),
        catchError((error) => 
          of(investmentActionFailure(error.response?.data?.message || 'Failed to fetch portfolio optimization'))
        )
      );
    })
  );

// Epic for syncing investments from Plaid
export const fetchInvestmentsEpic = (action$) =>
  action$.pipe(
    ofType(fetchInvestmentsRequest.type),
    mergeMap((action) => {
      const userId = action.payload;
      return from(axiosInstance.post(`/pennypal/api/mock/investments/fetch/${userId}`)).pipe(
        map((response) => {  console.log("Raw investment API Response:", response);
          console.log("investment data structure:", response.data);
          return fetchInvestmentsSuccess(response.data)}),
        catchError((error) => 
          of(investmentActionFailure(error.response?.data?.message || 'Failed to fetch investments from Plaid'))
        )
      );
    })
  );

// Combine all investment epics
export const investmentEpics = [
  fetchInvestmentsEpic,
  fetchPerformanceEpic,
  fetchInvestmentAccountsEpic,
  fetchPortfolioDiversityEpic,
  fetchPortfolioOptimizationEpic,

];
export default investmentEpics;