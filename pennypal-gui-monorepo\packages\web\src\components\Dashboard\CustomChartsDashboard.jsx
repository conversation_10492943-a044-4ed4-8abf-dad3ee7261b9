import React, { useState, useEffect, useRef } from 'react';
import { DndContext, useSensor, useSensors, PointerSensor } from '@dnd-kit/core';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { SortableContext, rectSortingStrategy } from '@dnd-kit/sortable';
import ChatbotVisualizer from '../v1/SideNav/ChatbotVisualizer';
import { Download, Pencil, Loader2, Trash2 } from 'lucide-react';
import * as XLSX from 'xlsx';
import html2canvas from 'html2canvas';
import { axiosInstance } from 'logic/api/axiosConfig';
import { getCurrentUserId } from '../../utils/AuthUtil';
import { themeClasses } from '../../utils/tailwindUtils'; // Adjust path as neededimport PennyLogoDrawing from '../path/to/PennyLogoDrawing'; // Adjust path as needed
import PaymentLoader from '../load/PaymentLoader'; // Adjust path as needed


// Color constants
const COLORS = {
  shadow: 'rgba(127, 224, 41, 0.15)'
};

// DraggableChart component
const DraggableChart = ({ id, children, darkMode }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 10 : 1,
    height: '100%',
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      <div
        className={`rounded-xl shadow-lg p-5 ml-5 mr-5 border-l-4 h-full cursor-move mb-4 ${
          darkMode ? 'bg-gray-800 border-gray-600 text-gray-100' : 'bg-white border-[#7fe029] text-black'
        }`}
        style={{
          boxShadow: `0 4px 12px ${darkMode ? 'rgba(0,0,0,0.2)' : COLORS.shadow}`,
        }}
      >
        {children}
      </div>
    </div>
  );
};

// CustomChartsSection component
const CustomChartsSection = ({ darkMode }) => {
  const userId = getCurrentUserId();
  const [customCharts, setCustomCharts] = useState([]);
  const [editingChartId, setEditingChartId] = useState(null);
  const [editingTitle, setEditingTitle] = useState('');
  const [savingChartId, setSavingChartId] = useState(null);
  const [deletingChartId, setDeletingChartId] = useState(null);
  const [loadingCharts, setLoadingCharts] = useState(false);
  const [error, setError] = useState(null);

  const chartRefs = useRef({});

  // Load saved chart positions or use defaults
  const [chartPositions, setChartPositions] = useState(() => {
    const savedLayout = localStorage.getItem('customChartsLayout');
    console.log('savedLayout', savedLayout);
    return savedLayout ? JSON.parse(savedLayout) : [];
  });

  // Save chart positions to localStorage
  useEffect(() => {
    localStorage.setItem('customChartsLayout', JSON.stringify(chartPositions));
  }, [chartPositions]);

  // Fetch custom charts
  useEffect(() => {
    if (!userId) return;

    const fetchCustomCharts = async () => {
      setLoadingCharts(true);
      setError(null);
      try {
        const response = await axiosInstance.get(`pennypal/api/v1/charts/user/${userId}`);
        
        const charts = response.data.map(chart => ({
          ...chart,
          data: JSON.parse(chart.data),
        }));

        setCustomCharts(charts);

        // Initialize chartRefs for each chart
        charts.forEach(chart => {
          chartRefs.current[chart.id] = React.createRef();
        });

        // Sync chartPositions with any new charts
        setChartPositions(prevPositions => {
          const existingIds = new Set(prevPositions.map(p => p.id));
          const newCharts = charts.filter(chart => !existingIds.has(chart.id));

          const newPositions = newCharts.map((chart, index) => ({
            id: chart.id,
            position: `chart-${prevPositions.length + index}`,
          }));

          const updated = [...prevPositions, ...newPositions];
          localStorage.setItem('customChartsLayout', JSON.stringify(updated));
          return updated;
        });
      } catch (err) {
        setCustomCharts([]);
        setError('Failed to fetch charts');
      } finally {
        setLoadingCharts(false);
      }
    };

    fetchCustomCharts();
  }, [userId]);

  // Start editing chart title
  const startEditingTitle = (chart) => {
    setEditingChartId(chart.id);
    setEditingTitle(chart.title || '');
  };

  // Save edited chart title
  const saveTitleEdit = async (chartId) => {
    if (!editingTitle.trim()) return;

    if (editingTitle === customCharts.find(chart => chart.id === chartId).title) {
      setEditingChartId(null);
      setEditingTitle('');
      return;
    }

    try {
      setSavingChartId(chartId);
      const updatedCharts = customCharts.map(chart =>
        chart.id === chartId ? { ...chart, title: editingTitle } : chart
      );
      setCustomCharts(updatedCharts);

      await axiosInstance.put(`pennypal/api/v1/charts/update/${chartId}`, {
        title: editingTitle,
      });
    } catch (error) {
      console.error('Failed to update chart title:', error);
      alert('Failed to update chart title');
    } finally {
      setEditingChartId(null);
      setEditingTitle('');
      setSavingChartId(null);
    }
  };

  // Delete chart
  const handleDeleteChart = async (chartId) => {
    try {
      setDeletingChartId(chartId);
      await axiosInstance.delete(`pennypal/api/v1/charts/delete/${chartId}`);
      setCustomCharts(prevCharts => prevCharts.filter(chart => chart.id !== chartId));
    } catch (error) {
      console.error('Failed to delete chart:', error);
      alert('Failed to delete chart');
    } finally {
      setDeletingChartId(null);
    }
  };

  // Fix colors for download
  const fixColors = (element) => {
    const allElements = element.querySelectorAll('*');
    allElements.forEach((el) => {
      const color = getComputedStyle(el).color;
      const bgColor = getComputedStyle(el).backgroundColor;
      const fill = getComputedStyle(el).fill;
      const stroke = getComputedStyle(el).stroke;

      if (color.includes('oklch')) {
        el.style.color = themeClasses.tableCell(darkMode).split(' ')[0].replace('text-', '#');
      }
      if (bgColor.includes('oklch')) {
        el.style.backgroundColor = themeClasses.chartContainer(darkMode).split(' ')[0].replace('bg-', '#');
      }
      if (fill.includes('oklch')) {
        el.style.fill = themeClasses.svgText(darkMode).replace('fill-', '#');
      }
      if (stroke.includes('oklch')) {
        el.style.stroke = themeClasses.svgStroke(darkMode).replace('stroke-', '#');
      }
      if (el.style.color?.includes('oklch')) {
        el.style.color = themeClasses.tableCell(darkMode).split(' ')[0].replace('text-', '#');
      }
      if (el.style.backgroundColor?.includes('oklch')) {
        el.style.backgroundColor = themeClasses.chartContainer(darkMode).split(' ')[0].replace('bg-', '#');
      }
      if (el.style.fill?.includes('oklch')) {
        el.style.fill = themeClasses.svgText(darkMode).replace('fill-', '#');
      }
      if (el.style.stroke?.includes('oklch')) {
        el.style.stroke = themeClasses.svgStroke(darkMode).replace('stroke-', '#');
      }
    });
  };

  // Download chart
  const downloadChart = async (chartId) => {
    const chart = customCharts.find(c => c.id === chartId);
    const visualRef = chartRefs.current[chartId];

    if (!visualRef?.current) return;

    if (chart.type === 'table') {
      const worksheet = XLSX.utils.json_to_sheet(chart.data);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
      XLSX.writeFile(workbook, `${chart.title || 'table'}.xlsx`);
    } else if (visualRef.current) {
      const clone = visualRef.current.cloneNode(true);
      document.body.appendChild(clone);
      clone.style.position = 'absolute';
      clone.style.left = '-9999px';
      clone.style.pointerEvents = 'none';

      fixColors(clone);

      try {
        const canvas = await html2canvas(clone, {
          scale: 2,
          logging: false,
          useCORS: true,
        });
        const link = document.createElement('a');
        link.download = `${chart.title || 'chart'}.png`;
        link.href = canvas.toDataURL('image/png');
        link.click();
      } catch (error) {
        console.error('Error generating canvas:', error);
      } finally {
        document.body.removeChild(clone);
      }
    }
  };

  // Configure sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  // Handle drag end event
  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      setChartPositions((positions) => {
        const activeIndex = positions.findIndex(pos => pos.id === active.id);
        const overIndex = positions.findIndex(pos => pos.id === over.id);

        const newPositions = [...positions];
        const activePos = { ...newPositions[activeIndex] };
        const overPos = { ...newPositions[overIndex] };

        const tempPosition = activePos.position;
        activePos.position = overPos.position;
        overPos.position = tempPosition;

        newPositions[activeIndex] = overPos;
        newPositions[overIndex] = activePos;

        localStorage.setItem('customChartsLayout', JSON.stringify(newPositions));
        return newPositions;
      });
    }
  };

  // Reset layout to default
  const resetLayout = () => {
    const newPositions = customCharts.map((chart, index) => ({
      id: chart.id,
      position: `chart-${index}`
    }));
    setChartPositions(newPositions);
  };

  // Get chart for position
  const getChartForPosition = (position) => {
    const pos = chartPositions.find(pos => pos.position === position);
    return pos ? customCharts.find(chart => chart.id === pos.id) : null;
  };

  if (loadingCharts) {
    return (
      <div className={`min-h-screen w-full p-5 flex flex-col justify-center items-center ${themeClasses.container(darkMode)}`}>
        <PaymentLoader />
        {/* <p className={`mt-2 text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          Loading charts...
        </p> */}
      </div>
    );
  }

  if (error) {
    return (
      <div className={`w-full max-w-4xl mx-auto p-4 ${themeClasses.container(darkMode)}`}>
        <div className={`border px-4 py-3 rounded relative ${themeClasses.error(darkMode)}`} role="alert">
          <strong className={`font-bold ${darkMode ? 'text-red-400' : 'text-red-700'}`}>Error:</strong>
          <span className={`block sm:inline ${darkMode ? 'text-red-300' : 'text-red-700'}`}> {error}</span>
        </div>
      </div>
    );
  }

  return (
    <section className="">
      <div className="flex justify-between items-center p-5 mb-6">
        <h1 className={`text-2xl `}>Custom AI Charts</h1>
        {/* <button
          className={`py-2 px-4 rounded flex items-center text-sm transition-colors duration-300 ${
            darkMode
              ? 'bg-gray-700 hover:bg-gray-600 text-white'
              : 'bg-[#8bc34a] hover:bg-[#6ec122] text-white'
          }`}
          onClick={resetLayout}
        >
          Reset Charts Layout
        </button> */}
      </div>

      {!loadingCharts && customCharts.length === 0 && (
        <p className={`text-sm ${themeClasses.noChartsText(darkMode)}`}>No charts found.</p>
      )}

      <DndContext sensors={sensors} onDragEnd={handleDragEnd}>
        <SortableContext items={chartPositions.map(pos => pos.id)} strategy={rectSortingStrategy}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {chartPositions.map((pos, index) => {
              const chart = getChartForPosition(pos.position);
              if (!chart) return null;

              const isEditing = editingChartId === chart.id;

              return (
                <DraggableChart key={chart.id} id={chart.id} darkMode={darkMode}>
                  <div className="relative">
                    <div className="absolute right-4 z-10 flex gap-2 items-center">
                      <button
                        onClick={() => {
                          if (window.confirm('Are you sure you want to delete this chart?')) {
                            handleDeleteChart(chart.id);
                          }
                        }}
                        className={`text-xs px-3 py-1 rounded ${
                          deletingChartId === chart.id
                            ? themeClasses.actionButtonDisabled(darkMode)
                            : themeClasses.deleteButton(darkMode)
                        }`}
                        disabled={deletingChartId === chart.id}
                      >
                        {deletingChartId === chart.id ? (
                          <svg className="animate-spin h-4 w-4 mx-auto" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8v4l3-3-3-3v4a8 8 0 000 16v-4l-3 3 3 3v-4a8 8 0 01-8-8z"
                            />
                          </svg>
                        ) : (
                          <Trash2 className="h-4 w-4" />
                        )}
                      </button>
                      <button
                        onClick={() => downloadChart(chart.id)}
                        className={`px-2 py-2 rounded ${themeClasses.downloadButton(darkMode)}`}
                        title="Download Chart"
                      >
                        <Download className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="mb-2 flex items-center gap-2">
                      {isEditing ? (
                        <div className="flex items-center gap-2">
                          <input
                            type="text"
                            value={editingTitle}
                            autoFocus
                            onChange={(e) => setEditingTitle(e.target.value)}
                            onBlur={() => saveTitleEdit(chart.id)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                e.preventDefault();
                                saveTitleEdit(chart.id);
                              }
                            }}
                            className={`text-base font-semibold rounded-md px-1 py-1 border focus:outline-none ${themeClasses.inputField(darkMode)} ${themeClasses.inputFocusRing(darkMode)}`}
                          />
                          {savingChartId === chart.id && (
                            <Loader2 className="animate-spin w-4 h-4 text-blue-500" />
                          )}
                        </div>
                      ) : (
                        <div className="flex items-center gap-1">
                          <h3 className={`text-lg font-semibold ${themeClasses.chartTitle(darkMode)}`}>{chart.title || 'Placeholder Title'}</h3>
                          <button
                            onClick={() => startEditingTitle(chart)}
                            className={`${themeClasses.editButton(darkMode)}`}
                            disabled={savingChartId === chart.id}
                          >
                            {savingChartId === chart.id ? (
                              <Loader2 className="animate-spin w-4 h-4 text-blue-500" />
                            ) : (
                              <Pencil className="w-4 h-4" />
                            )}
                          </button>
                        </div>
                      )}
                    </div>

                    <div ref={chartRefs.current[chart.id]} id={`chart-${chart.id}`}>
                      <ChatbotVisualizer
                        visualization={chart}
                        userId={userId}
                        hideTitle={true}
                        hideSaveButton={true}
                        hideDownloadButton={true}
                      />
                    </div>
                  </div>
                </DraggableChart>
              );
            })}
          </div>
        </SortableContext>
      </DndContext>
    </section>
  );
};

export default CustomChartsSection;