import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  setBudgetData,
  changeMonth,
  setToToday,
  toggleAllTables,
  toggleCategory,
  setExpandedCategories,
  toggleZeroBudget,
  formatCurrency,
  getProgress,
  calculateTotals,
} from "../../../../logic/redux/budgetSlice";
import BudgetPopupComponent from "./BudgetPopupComponent";
import EditBudgetPopupComponent from "./EditBudgetPopupComponent";
import PaymentLoader from "../load/PaymentLoader";
import {
  FaWallet,
  FaDollarSign,
  FaCreditCard,
  FaBuilding,
  FaChevronDown,
  FaChevronUp,
  FaArrowCircleRight,
  FaArrowCircleLeft,
  FaPlus,
  FaMinus,
  FaTrashAlt,
  FaQuestionCircle,
  FaMoneyBillWave,
  // APT-125 fix for correct icons not showing
  FaBusinessTime,
  FaCar,
  FaChild,
  FaFilm,
  FaGraduationCap,
  FaHeart,
  FaHome,
  FaHospital,
  FaPlane,
  FaShoppingCart,
  FaUtensils,
  /** End of APT-125 fix */
} from "react-icons/fa";
import {
  UilShoppingBasket,
  UilCoffee,
  UilAirplay,
  UilMoneyBill,
  UilWallet,
  UilDollarSign,
  UilCreditCard,
  UilBuilding,
} from "@iconscout/react-unicons";

const iconMapping = {
  FaMoneyBillWave,
  FaWallet,
  FaDollarSign,
  FaCreditCard,
  FaBuilding,
  FaQuestionCircle,
  // APT-125 fix for correct icons not showing
  FaBusinessTime,
  FaCar,
  FaChild,
  FaFilm,
  FaGraduationCap,
  FaHeart,
  FaHome,
  FaHospital,
  FaPlane,
  FaShoppingCart,
  FaUtensils,
  /** End of APT-125 fix */
  UilShoppingBasket,
  UilCoffee,
  UilAirplay,
  UilMoneyBill,
  UilWallet,
  UilDollarSign,
  UilCreditCard,
  UilBuilding,
};

const iconColorMapping = {
  FaMoneyBillWave: "#4caf50",
  FaWallet: "#3f51b5",
  FaDollarSign: "#4caf50",
  FaCreditCard: "#009688",
  FaBuilding: "#ff9800",
  FaQuestionCircle: "#607d8b",
  UilShoppingBasket: "#795548",
  UilCoffee: "#6f4e37",
  UilAirplay: "#009688",
  UilMoneyBill: "#4caf50",
  FaChevronDown: "#607d8b",
  FaChevronUp: "#607d8b",
  FaArrowCircleRight: "#009688",
  FaArrowCircleLeft: "#009688",
  FaPlus: "#8bc34a",
  FaMinus: "#f44336",
  FaTrashAlt: "#f44336",
};

const ProgressBar = ({ spent, budget }) => {
  const percentage = getProgress(spent, budget);
  let colorClass = "bg-emerald-500";
  if (percentage > 100) colorClass = "bg-red-500";
  else if (percentage > 75) colorClass = "bg-amber-500";

  return (
    <div className="mt-1 mb-3 w-full">
      <div className="flex justify-between text-xs text-gray-500">
        <span>
          ${formatCurrency(spent)} / ${formatCurrency(budget)}
        </span>
        <span>{percentage}%</span>
      </div>
      <div className="h-1 w-full bg-gray-200 rounded-full">
        <div
          className={`h-1 rounded-full ${colorClass}`}
          style={{ width: `${Math.min(percentage, 100)}%` }}
        />
      </div>
    </div>
  );
};

const Budget6 = ({ darkMode }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const {
    currentMonth,
    currentYear,
    budgetData,
    loading,
    expandedCategories,
    showZeroBudget,
    showAllTables,
  } = useSelector((state) => state.budget);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [isEditBudgetPopupOpen, setIsEditBudgetPopupOpen] = useState(false);
  const [selectedBudgetItem, setSelectedBudgetItem] = useState(null);
  const [clickTimeout, setClickTimeout] = useState(null);

  useEffect(() => {
    dispatch(setToToday());
  }, [dispatch]);

  if (loading) {
    return (
      <div
        className={`min-h-screen w-full p-5 flex flex-col items-center justify-center ${
          darkMode ? "bg-gray-800 text-gray-100" : "bg-white text-gray-900"
        }`}
      >
        <PaymentLoader darkMode={darkMode} cycleDuration={8} />
        <p className={`mt-4 ${darkMode ? "text-gray-300" : "text-gray-600"}`}>
          Loading budget data...
        </p>
      </div>
    );
  }

  const calculateCategory1Actual = () => {
    const category1 = budgetData.find((cat) => cat.categoryName === "Income");
    if (!category1) return 0;
    return category1.subcategories.reduce(
      (total, sub) => total + (sub.actual || 0),
      // APT-80 fix for incorrect income amount in summary card - removed category level add
      0
    );
  };

  const category1Actual = calculateCategory1Actual();
  const { totalBudget, totalActual, totalRemaining } = calculateTotals(budgetData);

  const handleCategoryClick = (categoryId) => {
    navigate(`/dashboard/category-details/${categoryId}`);
  };

  const handleSubcategoryClick = (categoryId, subCategoryId) => {
    if (clickTimeout) {
      clearTimeout(clickTimeout);
    }

    const timeout = setTimeout(() => {
      navigate(`/dashboard/category-details/${categoryId}/${subCategoryId}`);
    }, 250);

    setClickTimeout(timeout);
  };

  const handleSubcategoryDoubleClick = (categoryId, subcategory) => {
    if (clickTimeout) {
      clearTimeout(clickTimeout);
      setClickTimeout(null);
    }

    const category = budgetData.find((cat) => cat.categoryId === categoryId);
    setSelectedBudgetItem({
      id: subcategory.id,
      categoryId,
      categoryName: category?.categoryName || "Unknown",
      subcategoryId: subcategory.subcategoryId,
      compositeKey: subcategory.compositeKey,
      subcategoryName: subcategory.subcategoryName,
      customSubCategoryName: subcategory.customSubCategoryName,
      allocated: subcategory.allocated,
      rollover: subcategory.rollover,
      isExcluded: subcategory.isExcluded || false,
      icon: subcategory.icon,
    });
    setIsEditBudgetPopupOpen(true);
  };

  const handlePopupToggle = () => setIsPopupOpen(!isPopupOpen);

  const handleSaveBudget = (budgetItem) => {
    dispatch({ type: "budget/addBudgetItem", payload: budgetItem });
    setIsPopupOpen(false);
  };

  const handleSaveEditBudget = (updatedBudget) => {
    dispatch({ type: "budget/saveBudget", payload: updatedBudget });
    setIsEditBudgetPopupOpen(false);
  };

  const handleCloseSubcategory = (categoryId, subcategoryId) => {
    const category = budgetData.find((cat) => cat.categoryId === categoryId);
    if (!category) return;
    const subcategory = category.subcategories.find(
      (sub) => sub.compositeKey === subcategoryId
    );
    if (!subcategory) return;
    const budgetId = subcategory.id;

    dispatch({
      type: "budget/deleteSubcategoryBudget",
      payload: { budgetId, categoryId, subcategoryId },
    });
  };

  // APT-82 fix for expand/collapse not working
  const handleToggleAllTables = () => {
    dispatch(toggleAllTables());
    // Update expandedCategories for all categories
    const newExpandedState = {};
    budgetData.forEach((category) => {
      newExpandedState[category.categoryId] = !showAllTables;
    });
    dispatch(setExpandedCategories(newExpandedState));
  };
  // End of APT-82 fix

  const monthNames = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  return (
    <div
      className={`p-5 min-h-screen w-full font-roboto ${
        darkMode ? "bg-gray-900 text-gray-100" : "bg-white text-gray-900"
      }`}
    >
      <div className="justify-between items-center mb-2">
        <h1 className="text-2xl">Budget</h1>
        <div className="flex justify-end pt-12">
          <span
            className={`cursor-pointer mr-2.5 text-lg ${
              darkMode ? "text-gray-100" : "text-gray-900"
            }`}
            onClick={() => dispatch(setToToday())}
          >
            Today
          </span>
          <span
            className={`cursor-pointer mx-2 text-2xl mt-1 ${
              darkMode ? "text-gray-100" : "text-gray-900"
            }`}
            onClick={() => dispatch(changeMonth(-1))}
          >
            <FaArrowCircleLeft />
          </span>
          <span className="mx-2 text-lg">
            {monthNames[currentMonth]} {currentYear}
          </span>
          <span
            className={`cursor-pointer mx-2 text-2xl mt-1 ${
              darkMode ? "text-gray-100" : "text-gray-900"
            }`}
            onClick={() => dispatch(changeMonth(1))}
          >
            <FaArrowCircleRight />
          </span>
        </div>
      </div>

      <div className="flex flex-wrap justify-between mb-1">
        {["Income", "Budget", "Actual", "Remaining"].map((title, index) => (
          <div
            key={title}
            className={`flex flex-col justify-center border shadow rounded-2xl p-5 w-full sm:w-1/2 md:w-[24%] relative mb-4 ${
              darkMode
                ? "bg-gray-800 text-gray-100 border-gray-800"
                : "bg-white border-gray-300"
            }`}
          >
            <strong
              className={`w-full text-left text-xl ${
                darkMode ? "text-gray-100" : "text-gray-900"
              }`}
            >
              {title}
              <div
                className={`absolute top-6 right-4 bg-${
                  ["dc6266", "b383c6", "20d7cb", "bda431"][index]
                } rounded-full p-1`}
              >
                {[
                  UilWallet,
                  UilDollarSign,
                  UilCreditCard,
                  UilBuilding,
                ][index]({ size: 24, className: "text-lg text-white" })}
              </div>
            </strong>
            <span
              className={`w-full text-center text-xl font-bold mt-8 ${
                darkMode ? "text-gray-100" : ""
              } ${
                title === "Remaining" && totalRemaining < 0
                  ? "text-red-500"
                  : title === "Remaining"
                  ? "text-green-500"
                  : ""
              }`}
            >
              $
              {formatCurrency(
                [category1Actual, totalBudget, totalActual, totalRemaining][index]
              )}
            </span>
          </div>
        ))}
      </div>

      <div className="flex justify-between items-center mt-2">
        <button
          // APT-82 fix for expand/collapse not working - only added onClick={handleToggleAllTables} line
          onClick={handleToggleAllTables}
          className={`bg-transparent border-0 cursor-pointer flex justify-center items-center p-[10px] hover:opacity-80 transition ${
            darkMode ? "text-gray-100" : "text-gray-900"
          }`}
        >
          {showAllTables ? (
            <FaChevronDown className="text-[20px]" />
          ) : (
            <FaChevronUp className="text-[20px]" />
          )}
        </button>
        <button
          onClick={handlePopupToggle}
          className={`border-0 cursor-pointer py-[8px] px-[16px] hover:opacity-80 transition ${
            darkMode ? "text-gray-100" : "text-gray-900"
          }`}
        >
          <FaPlus className="text-[20px]" />
        </button>
      </div>

      {isPopupOpen && (
        <BudgetPopupComponent
          onSave={handleSaveBudget}
          onClose={handlePopupToggle}
          darkMode={darkMode}
        />
      )}

      {isEditBudgetPopupOpen && selectedBudgetItem && (
        <EditBudgetPopupComponent
          budgetItem={selectedBudgetItem}
          onSave={handleSaveEditBudget}
          onClose={() => setIsEditBudgetPopupOpen(false)}
          darkMode={darkMode}
        />
      )}

      <div className={`mt-5 ${darkMode ? "text-gray-100" : "bg-white text-gray-900"}`}>
        <div
          className={`flex font-bold p-4 mt-5 ${
            darkMode ? "bg-gray-800 border-gray-800" : "bg-[#c5e1a5] border-gray-300"
          } border rounded-xl shadow`}
        >
          {["Category", "Budget", "Actual", "Remaining"].map((header) => (
            <span
              key={header}
              className={`flex-1 ${header === "Category" ? "text-left pl-3" : "text-center"}`}
            >
              {header}
            </span>
          ))}
        </div>
        {budgetData.length > 0 ? (
          budgetData.map((category) => {
            const nonZeroSubs = category.subcategories.filter(
              (sub) => sub.allocated !== 0
            );
            const zeroSubs = category.subcategories.filter(
              (sub) => sub.allocated === 0
            );
            const isExpanded = expandedCategories[category.categoryId] ?? showAllTables;
            const IconComponent = iconMapping[category.icon] || FaQuestionCircle;

            const categoryBudgetTotal = category.subcategories.reduce(
              (acc, sub) => acc + (sub.allocated || 0),
              0
            );
            const categoryActualTotal = category.subcategories.reduce(
              (acc, sub) => acc + (sub.actual || 0),
              0
            );
            const categoryRemainingTotal = category.subcategories.reduce(
              (acc, sub) => acc + (sub.remaining || 0),
              0
            );

            return (
              <React.Fragment key={category.categoryId}>
                <div
                  className={`flex font-bold p-2.5 mt-5 ${
                    darkMode
                      ? "bg-gray-800 border-gray-800"
                      : "bg-white border-gray-300"
                  // APT-82 fix for expand/collapse not working
                  } rounded-t-xl border-t border-l border-r shadow-[0_-4px_6px_0_rgba(0,0,0,0.1),0_6px_8px_-4px_rgba(0,0,0,0.1)]`}
                >
                  <div className="flex items-center flex-1">
                    <button
                      onClick={() => dispatch(toggleCategory(category.categoryId))}
                      className={`bg-transparent border-0 cursor-pointer flex justify-center items-center p-[10px] hover:opacity-80 transition ${
                        darkMode ? "text-gray-100" : "text-gray-900"
                      }`}
                    >
                      {isExpanded ? (
                        <FaChevronDown className="text-[20px]" />
                      ) : (
                        <FaChevronUp className="text-[20px]" />
                      )}
                    </button>
                    <strong
                      className="ml-2.5 pt-1.5 flex items-center cursor-pointer"
                      onClick={() => handleCategoryClick(category.categoryId)}
                    >
                      <IconComponent
                        className="mr-2"
                        style={{ color: iconColorMapping[category.icon] || "#000" }}
                      />
                      {category.categoryName || "Unknown"}
                    </strong>
                  </div>
                  {/* End of APT-82 fix */}
                  {["Budget", "Actual", "Remaining"].map((type, index) => (
                    <span
                      key={type}
                      className={`flex-1 text-center ${
                        type === "Remaining" && categoryRemainingTotal < 0
                          ? "text-red-500"
                          : type === "Remaining"
                          ? "text-green-500"
                          : ""
                      }`}
                    >
                      $
                      {formatCurrency(
                        [
                          categoryBudgetTotal,
                          categoryActualTotal,
                          categoryRemainingTotal,
                        ][index]
                      )}
                    </span>
                  ))}
                </div>
                {isExpanded && (
                  <div
                    className={`border-l border-r border-b px-5 pt-1 ${
                      darkMode
                        ? "bg-gray-800 border-x-gray-800 border-y-gray-800"
                        : "bg-white border-x-gray-300 border-y-white"
                    }`}
                  >
                    <ProgressBar
                      spent={categoryActualTotal}
                      budget={categoryBudgetTotal}
                    />
                  </div>
                )}
                <div
                  className={`border-b border-l border-r shadow-lg rounded-b-xl ${
                    darkMode
                      ? "bg-gray-800 text-gray-100 border-gray-800"
                      : "border-gray-300 bg-white"
                  }`}
                >
                  {isExpanded && category.subcategories.length > 0 && (
                    <>
                      {nonZeroSubs.map((sub) => {
                        const SubIconComponent = iconMapping[sub.icon] || FaQuestionCircle;
                        return (
                          <React.Fragment key={sub.compositeKey}>
                            <div
                              className={`flex justify-between p-5 transition-colors duration-300 group ${
                                darkMode
                                  ? "text-gray-100 hover:bg-[#484a4a]"
                                  : "text-gray-900 hover:bg-[#e0f7fa]"
                              }`}
                              tabIndex="0"
                              onClick={() => handleSubcategoryClick(category.categoryId, sub.subcategoryId)}
                              onDoubleClick={() => handleSubcategoryDoubleClick(category.categoryId, sub)}
                            >
                              <div className="flex-1 text-left flex items-center cursor-pointer">
                                <SubIconComponent
                                  className="mr-2"
                                  style={{ color: iconColorMapping[sub.icon] || "#000" }}
                                />
                                {sub.subcategoryName || sub.customSubCategoryName || "N/A"}
                              </div>
                              <div className="flex-1 text-center">
                                ${formatCurrency(sub.allocated ?? 0)}
                              </div>
                              <div className="flex-1 text-center">
                                ${formatCurrency(sub.actual || 0)}
                              </div>
                              <div
                                className={`flex-1 text-center ${
                                  sub.remaining < 0 ? "text-red-500" : "text-green-500"
                                }`}
                              >
                                ${formatCurrency(sub.remaining || 0)}
                              </div>
                              {sub.allocated !== 0 && (
                                <div
                                  className="text-center cursor-pointer hidden group-hover:block"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleCloseSubcategory(category.categoryId, sub.compositeKey);
                                  }}
                                >
                                  <FaTrashAlt />
                                </div>
                              )}
                            </div>
                            <div className="px-5 pt-1">
                              <ProgressBar spent={sub.actual || 0} budget={sub.allocated || 0} />
                            </div>
                          </React.Fragment>
                        );
                      })}
                      {zeroSubs.length > 0 && !showZeroBudget[category.categoryId] && (
                        <div
                          className="flex justify-center items-center text-blue-500 italic cursor-pointer"
                          onClick={() => dispatch(toggleZeroBudget(category.categoryId))}
                        >
                          <FaPlus className="mr-2 mb-5" />
                        </div>
                      )}
                      {zeroSubs.length > 0 && showZeroBudget[category.categoryId] && (
                        <>
                          {zeroSubs.map((sub) => {
                            const SubIconComponent = iconMapping[sub.icon] || FaQuestionCircle;
                            return (
                              <React.Fragment key={sub.compositeKey}>
                                <div
                                  className={`flex justify-between p-5 transition-colors duration-300 group ${
                                    darkMode
                                      ? "text-gray-100 hover:bg-[#484a4a]"
                                      : "text-gray-900 hover:bg-[#e0f7fa]"
                                  }`}
                                  tabIndex="0"
                                  onClick={() => handleSubcategoryClick(category.categoryId, sub.subcategoryId)}
                                  onDoubleClick={() => handleSubcategoryDoubleClick(category.categoryId, sub)}
                                >
                                  <div className="flex-1 text-left flex items-center cursor-pointer">
                                    <SubIconComponent
                                      className="mr-2"
                                      style={{ color: iconColorMapping[sub.icon] || "#000" }}
                                    />
                                    {sub.subcategoryName || sub.customSubCategoryName || "N/A"}
                                  </div>
                                  <div className="flex-1 text-center">
                                    ${formatCurrency(sub.allocated ?? 0)}
                                  </div>
                                  <div className="flex-1 text-center">
                                    ${formatCurrency(sub.actual || 0)}
                                  </div>
                                  <div
                                    className={`flex-1 text-center ${
                                      sub.remaining < 0 ? "text-red-500" : "text-green-500"
                                    }`}
                                  >
                                    ${formatCurrency(sub.remaining || 0)}
                                  </div>
                                  {sub.allocated !== 0 && (
                                    <div
                                      className="text-center cursor-pointer hidden group-hover:block"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleCloseSubcategory(category.categoryId, sub.compositeKey);
                                      }}
                                    >
                                      <FaTrashAlt />
                                    </div>
                                  )}
                                </div>
                                <div className="ml-8">
                                  <ProgressBar
                                    spent={sub.actual || 0}
                                    budget={sub.allocated || 0}
                                  />
                                </div>
                              </React.Fragment>
                            );
                          })}
                          <div
                            className="flex justify-center items-center text-blue-500 italic cursor-pointer"
                            onClick={() => dispatch(toggleZeroBudget(category.categoryId))}
                          >
                            <FaMinus className="mr-2" />
                          </div>
                        </>
                      )}
                    </>
                  )}
                </div>
              </React.Fragment>
            );
          })
        ) : (
          <p>No budget data available.</p>
        )}
      </div>
    </div>
  );
};

export default Budget6;