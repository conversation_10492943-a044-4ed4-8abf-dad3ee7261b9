// import React, { useState, useEffect } from "react";
// import { useSelector, useDispatch } from "react-redux";
// import { 
//   FaTimes, 
//   FaQuestionCircle,
//   FaMoneyBillWave,
//   FaCar,
//   FaHome,
//   FaUtensils,
//   FaPlane,
//   FaShoppingCart,
//   FaChild,
//   FaHeart,
//   FaBusinessTime,
//   FaHospital,
//   FaGraduationCap,
//   FaFilm,
//   FaWallet,
//   FaDollarSign,
//   FaCreditCard,
//   FaBuilding,
//   FaEllipsisH,
//   FaSpinner,
//   FaCheck
// } from "react-icons/fa";

// const BudgetPopupComponent = ({ onSave, onClose }) => {
//   const dispatch = useDispatch();
//   const { currentMonth, currentYear, loading, error, networkError } = useSelector((state) => state.budget);
  
//   // Get categories and subcategories from Redux store
//   const categories = useSelector((state) => state.budget.categories);
//   const allSubcategories = useSelector((state) => state.budget.subcategories);

//   console.log("********************************START********************");
//   console.log("Categories:", categories);
//   console.log("All Subcategories:", allSubcategories);
//   console.log("********************************END********************");

//   // Icon mapping object
//   const iconMap = {
//     "FaMoneyBillWave": FaMoneyBillWave,
//     "FaCar": FaCar,
//     "FaHome": FaHome,
//     "FaUtensils": FaUtensils,
//     "FaPlane": FaPlane,
//     "FaShoppingCart": FaShoppingCart,
//     "FaChild": FaChild,
//     "FaHeart": FaHeart,
//     "FaBusinessTime": FaBusinessTime,
//     "FaHospital": FaHospital,
//     "FaGraduationCap": FaGraduationCap,
//     "FaFilm": FaFilm,
//     "FaWallet": FaWallet,
//     "FaDollarSign": FaDollarSign,
//     "FaCreditCard": FaCreditCard,
//     "FaBuilding": FaBuilding,
//     "FaMiscellaneous": FaEllipsisH
//   };

//   // State declarations
//   const [selectedCategory, setSelectedCategory] = useState("");
//   const [selectedSubcategory, setSelectedSubcategory] = useState("");
//   const [subcategoryInputValue, setSubcategoryInputValue] = useState("");
//   const [newBudgetAmount, setNewBudgetAmount] = useState(0);
//   const [isRollover, setIsRollover] = useState(false);
//   const [isExcluded, setIsExcluded] = useState(false);
//   const [isEditingBudget, setIsEditingBudget] = useState(false);
//   const [subcategories, setSubcategories] = useState([]);
//   const [isSubmitting, setIsSubmitting] = useState(false);
//   const [showSuccess, setShowSuccess] = useState(false);

//   // Dropdown toggles
//   const [categoryDropdownOpen, setCategoryDropdownOpen] = useState(false);
//   const [subcategoryDropdownOpen, setSubcategoryDropdownOpen] = useState(false);

//   // Icon dropdown for new subcategory
//   const [iconDropdownOpen, setIconDropdownOpen] = useState(false);
//   const [selectedIconForNewSub, setSelectedIconForNewSub] = useState("FaMiscellaneous");
  
//   const availableIcons = [
//     "FaMoneyBillWave", "FaCar", "FaHome", "FaUtensils", "FaPlane", 
//     "FaShoppingCart", "FaChild", "FaHeart", "FaBusinessTime", 
//     "FaHospital", "FaGraduationCap", "FaFilm", "FaWallet", 
//     "FaDollarSign", "FaCreditCard", "FaBuilding", "FaMiscellaneous"
//   ];

//   // Helper function to render icon
//   const renderIcon = (iconKey, className = "") => {
//     const IconComponent = iconMap[iconKey] || iconMap["FaMiscellaneous"];
//     return <IconComponent className={className} />;
//   };

//   useEffect(() => {
//     // Only dispatch actions to fetch data if not already loaded
//     if (!categories || categories.length === 0) {
//       dispatch({ type: 'budget/fetchCategories' });
//     }
//     if (!allSubcategories || allSubcategories.length === 0) {
//       dispatch({ type: 'budget/fetchSubcategories' });
//     }
//   }, [dispatch, categories, allSubcategories]);

//   // Filter subcategories based on the selected category's id
//   useEffect(() => {
//     console.log("Filtering subcategories...");
//     console.log("Selected Category:", selectedCategory);
//     console.log("All Subcategories:", allSubcategories);
    
//     if (selectedCategory && allSubcategories && allSubcategories.length > 0) {
//       // Filter subcategories by categoryId
//       const filteredSubs = allSubcategories.filter((sub) => {
//         // Check different possible structures for category reference
//         const categoryId = sub.categoryId || sub.category?.id || sub.category_id;
//         console.log("Subcategory:", sub.subCategory || sub.name, "CategoryId:", categoryId, "Selected:", selectedCategory);
//         return categoryId && categoryId.toString() === selectedCategory.toString();
//       });
      
//       console.log("Filtered subcategories:", filteredSubs);
//       setSubcategories(filteredSubs);
//     } else {
//       setSubcategories([]);
//     }
//   }, [selectedCategory, allSubcategories]);

//   // Monitor for successful budget creation
//   useEffect(() => {
//     if (!loading && !error && !networkError && isSubmitting) {
//       setShowSuccess(true);
//       setIsSubmitting(false);
      
//       // Auto close after success
//       setTimeout(() => {
//         onClose();
//       }, 1500);
//     }
//   }, [loading, error, networkError, isSubmitting, onClose]);

//   // Slider change handler
//   const handleSliderChange = (e) => {
//     setNewBudgetAmount(parseInt(e.target.value, 10) || 0);
//   };

//   // Handle subcategory selection from dropdown
//   const handleSubcategorySelect = (subcategory) => {
//     setSelectedSubcategory(subcategory.id);
//     setSubcategoryInputValue(subcategory.subCategory || subcategory.name || subcategory.subcategory_name);
//     setSelectedIconForNewSub(subcategory.iconKey || subcategory.icon_key || "FaMiscellaneous");
//     setSubcategoryDropdownOpen(false);
//   };

//   // Handle subcategory input change
//   const handleSubcategoryInputChange = (e) => {
//     const value = e.target.value;
//     setSubcategoryInputValue(value);
    
//     // Show dropdown when user types
//     if (!subcategoryDropdownOpen) {
//       setSubcategoryDropdownOpen(true);
//     }
    
//     // Check if the typed value matches any existing subcategory
//     const matchingSubcategory = subcategories.find(
//       sub => {
//         const subName = sub.subCategory || sub.name || sub.subcategory_name || "";
//         return subName.toLowerCase() === value.toLowerCase();
//       }
//     );
    
//     if (matchingSubcategory) {
//       setSelectedSubcategory(matchingSubcategory.id);
//       setSelectedIconForNewSub(matchingSubcategory.iconKey || matchingSubcategory.icon_key || "FaMiscellaneous");
//     } else {
//       // This will be a new custom subcategory
//       setSelectedSubcategory(null);
//     }
//   };

//   // Handle subcategory dropdown toggle
//   const handleSubcategoryDropdownToggle = () => {
//     setSubcategoryDropdownOpen(!subcategoryDropdownOpen);
//   };

//   // Save the budget 
// const handleAddToBudget = () => {
//   if (!selectedCategory || !subcategoryInputValue.trim()) {
//     alert("Please select a category and enter a subcategory.");
//     return;
//   }

//   if (newBudgetAmount < 0) {
//     alert("Budget amount cannot be negative.");
//     return;
//   }

//   const catObj = categories.find(
//     (c) => c.id.toString() === selectedCategory.toString()
//   );

//   if (!catObj) {
//     alert("Selected category not found.");
//     return;
//   }

//   // Check if this is an existing subcategory or a new custom one
//   const existingSubcategory = subcategories.find(
//     sub => {
//       const subName = sub.subCategory || sub.name || sub.subcategory_name || "";
//       return subName.toLowerCase() === subcategoryInputValue.toLowerCase();
//     }
//   );

//   // Build the budget object - make sure categoryId matches what backend expects
//   const budgetItem = {
//     categoryId: catObj.id, // Make sure this is the correct category ID
//     subCategoryId: existingSubcategory ? existingSubcategory.id : null,
//     customSubCategory: subcategoryInputValue.trim(),
//     allocated: newBudgetAmount,
//     isRollover,
//     isExcluded,
//     icon: selectedIconForNewSub
//   };

//   console.log("Budget item to save:", budgetItem);
//   console.log("Category object:", catObj);
//   console.log("Is new custom subcategory:", !existingSubcategory);
  
//   setIsSubmitting(true);
//   setShowSuccess(false);
  
//   // Dispatch the action to create budget
//   dispatch({ type: 'budget/addBudgetItem', payload: budgetItem });
// };

//   // Filter subcategories for dropdown display based on input
//   const filteredSubcategoriesForDropdown = subcategories.filter(sub => {
//     const subName = sub.subCategory || sub.name || sub.subcategory_name || "";
//     return subName.toLowerCase().includes(subcategoryInputValue.toLowerCase());
//   });

//   // Close dropdowns when clicking outside
//   const handleClickOutside = (e) => {
//     if (!e.target.closest('.dropdown-container')) {
//       setSubcategoryDropdownOpen(false);
//     }
//     if (!e.target.closest('.category-dropdown-container')) {
//       setCategoryDropdownOpen(false);
//     }
//     if (!e.target.closest('.icon-dropdown-container')) {
//       setIconDropdownOpen(false);
//     }
//   };

//   useEffect(() => {
//     document.addEventListener('click', handleClickOutside);
//     return () => {
//       document.removeEventListener('click', handleClickOutside);
//     };
//   }, []);

//   // Show success message
//   if (showSuccess) {
//     return (
//       <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center popup-overlay z-50">
//         <div className="bg-white rounded-lg shadow-2xl w-full max-w-md p-8 text-center">
//           <div className="text-green-500 text-6xl mb-4">
//             <FaCheck />
//           </div>
//           <h3 className="text-xl font-bold mb-2 text-gray-800">Success!</h3>
//           <p className="text-gray-600">Budget item has been created successfully.</p>
//         </div>
//       </div>
//     );
//   }

//   return (
//     <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center popup-overlay z-50">
//       <div className="bg-white rounded-lg shadow-2xl w-full max-w-md max-h-screen overflow-hidden relative popup-container">
//         {/* Header */}
//         <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 relative">
//           <h3 className="text-xl font-bold m-0 popup-title">
//             Budget Details
//           </h3>
//           <div 
//             className="absolute top-4 right-4 cursor-pointer text-white text-xl hover:text-gray-200 transition-colors" 
//             onClick={onClose}
//             disabled={isSubmitting}
//           >
//             <FaTimes />
//           </div>
//         </div>

//         {/* Error Display */}
//         {(error || networkError) && (
//           <div className="bg-red-50 border-l-4 border-red-500 p-4 mx-4 mt-4">
//             <div className="text-red-700 text-sm">
//               {error || networkError}
//             </div>
//           </div>
//         )}

//         {/* Content */}
//         <div className="p-6 relative overflow-y-auto max-h-96">
//           {/* Category */}
//           <div className="mb-6 relative category-dropdown-container">
//             <label className="block text-sm font-medium mb-2 text-gray-700">Category</label>
//             <div
//               onClick={() => !isSubmitting && setCategoryDropdownOpen(!categoryDropdownOpen)}
//               className={`w-full p-3 border border-gray-300 rounded-md text-sm flex items-center justify-between min-h-10 ${!isSubmitting ? 'cursor-pointer hover:border-green-400' : 'cursor-not-allowed opacity-50'} transition-colors focus:outline-none focus:ring-2 focus:ring-green-500`}
//             >
//               {selectedCategory ? (
//                 (() => {
//                   const cat = categories.find(
//                     (c) => c.id.toString() === selectedCategory.toString()
//                   );
//                   if (cat) {
//                     return cat.category || cat.name || cat.category_name;
//                   }
//                   return "Select a Category";
//                 })()
//               ) : (
//                 <span className="text-gray-500">Select a Category</span>
//               )}
//               <span className={`arrow transition-transform ${categoryDropdownOpen ? 'rotate-180' : ''}`}>&#9662;</span>
//             </div>
//             {categoryDropdownOpen && !isSubmitting && (
//               <div className="absolute top-full left-0 w-full mt-1 border border-gray-300 rounded-md bg-white shadow-lg z-50 max-h-36 overflow-y-auto">
//                 {categories && categories.length > 0 ? (
//                   categories.map((cat) => {
//                     return (
//                       <div
//                         key={cat.id}
//                         className="flex items-center w-full min-h-10 p-3 text-sm cursor-pointer border-0 rounded-none bg-transparent hover:bg-green-50 transition-colors"
//                         onClick={() => {
//                           setSelectedCategory(cat.id);
//                           setCategoryDropdownOpen(false);
//                           // Clear subcategory values when category changes
//                           setSelectedSubcategory(null);
//                           setSubcategoryInputValue("");
//                           setSelectedIconForNewSub("FaMiscellaneous");
//                           // Show subcategory dropdown after category selection
//                           setTimeout(() => setSubcategoryDropdownOpen(true), 100);
//                         }}
//                       >
//                         {cat.category || cat.name || cat.category_name}
//                       </div>
//                     );
//                   })
//                 ) : (
//                   <div className="flex items-center w-full min-h-10 p-3 text-sm cursor-pointer border-0 rounded-none bg-transparent text-gray-500">
//                     <em>No categories available</em>
//                   </div>
//                 )}
//               </div>
//             )}
//           </div>

//           {/* Editable Subcategory Dropdown */}
//           {selectedCategory && (
//             <div className="flex items-end gap-3 mb-6 relative">
//               <div className="flex-1 relative dropdown-container">
//                 <label className="block text-sm font-medium mb-2 text-gray-700">Subcategory</label>
//                 <div className="relative flex items-center">
//                   <input
//                     type="text"
//                     value={subcategoryInputValue}
//                     onChange={handleSubcategoryInputChange}
//                     onFocus={() => !isSubmitting && setSubcategoryDropdownOpen(true)}
//                     placeholder="Select or type subcategory name"
//                     disabled={isSubmitting}
//                     className={`w-full p-3 pr-8 border border-gray-300 rounded-md text-sm min-h-10 outline-none ${!isSubmitting ? 'hover:border-green-400 focus:border-green-500 focus:ring-2 focus:ring-green-200' : 'cursor-not-allowed opacity-50'} transition-colors`}
//                   />
//                   <span 
//                     className={`absolute right-3 ${!isSubmitting ? 'cursor-pointer' : 'cursor-not-allowed'} text-gray-600 text-xs transition-transform ${subcategoryDropdownOpen ? 'rotate-180' : ''}`}
//                     onClick={() => !isSubmitting && handleSubcategoryDropdownToggle()}
//                   >
//                     &#9662;
//                   </span>
//                 </div>
//                 {subcategoryDropdownOpen && !isSubmitting && (
//                   <div className="absolute top-full left-0 w-full mt-1 border border-gray-300 rounded-md bg-white shadow-lg z-40 max-h-36 overflow-y-auto">
//                     {filteredSubcategoriesForDropdown.length > 0 ? (
//                       filteredSubcategoriesForDropdown.map((sub) => (
//                         <div
//                           key={sub.id}
//                           className="flex items-center w-full min-h-10 p-3 text-sm cursor-pointer border-0 rounded-none bg-transparent hover:bg-green-50 transition-colors"
//                           onClick={() => handleSubcategorySelect(sub)}
//                         >
//                           <span className="mr-2">
//                             {renderIcon(sub.iconKey || sub.icon_key || "FaMiscellaneous", "text-green-600")}
//                           </span>
//                           {sub.subCategory || sub.name || sub.subcategory_name}
//                         </div>
//                       ))
//                     ) : subcategoryInputValue.trim() ? (
//                       <div className="flex items-center w-full min-h-10 p-3 text-sm cursor-pointer border-0 rounded-none bg-transparent text-green-600">
//                         <span className="mr-2">
//                           {renderIcon(selectedIconForNewSub)}
//                         </span>
//                         <em>Create new: "{subcategoryInputValue}"</em>
//                       </div>
//                     ) : (
//                       <div className="flex items-center w-full min-h-10 p-3 text-sm cursor-pointer border-0 rounded-none bg-transparent text-gray-500">
//                         <em>No subcategories found for this category</em>
//                       </div>
//                     )}
//                   </div>
//                 )}
//               </div>
              
//               <div className="icon-dropdown-container">
//                 <label className="block text-sm font-medium mb-2 text-gray-700">Icon</label>
//                 <div
//                   className={`${!isSubmitting ? 'cursor-pointer hover:border-green-400' : 'cursor-not-allowed opacity-50'} border border-gray-300 rounded-md w-12 h-10 flex items-center justify-center relative transition-colors bg-white`}
//                   onClick={() => !isSubmitting && setIconDropdownOpen(!iconDropdownOpen)}
//                 >
//                   {renderIcon(selectedIconForNewSub, "text-green-600 text-lg")}
//                   {iconDropdownOpen && !isSubmitting && (
//                     <div className="absolute top-full right-0 bg-white border border-gray-300 rounded-md shadow-lg p-2 z-50 grid grid-cols-4 gap-2 max-h-48 overflow-y-auto min-w-[200px]">
//                       {availableIcons.map((iconKey) => {
//                         return (
//                           <div
//                             key={iconKey}
//                             onClick={(e) => {
//                               e.stopPropagation();
//                               setSelectedIconForNewSub(iconKey);
//                               setIconDropdownOpen(false);
//                             }}
//                             className={`cursor-pointer p-2 text-lg rounded hover:bg-green-50 transition-colors flex items-center justify-center ${
//                               selectedIconForNewSub === iconKey ? 'bg-green-100 text-green-600' : 'text-gray-600'
//                             }`}
//                           >
//                             {renderIcon(iconKey)}
//                           </div>
//                         );
//                       })}
//                     </div>
//                   )}
//                 </div>
//               </div>
//             </div>
//           )}

//           {/* Budget Amount Slider */}
//           <div className="mb-6 flex flex-col">
//             <div className="flex items-center justify-between mb-3">
//               <label className="block text-sm font-medium text-gray-700">Budget Amount</label>
//               {isEditingBudget ? (
//                 <input
//                   type="number"
//                   value={newBudgetAmount}
//                   onChange={(e) =>
//                     setNewBudgetAmount(Number(e.target.value) || 0)
//                   }
//                   onBlur={() => setIsEditingBudget(false)}
//                   onKeyDown={(e) => e.key === 'Enter' && setIsEditingBudget(false)}
//                   disabled={isSubmitting}
//                   className={`text-right border border-gray-300 rounded p-2 w-28 font-medium ${!isSubmitting ? 'cursor-pointer focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200' : 'cursor-not-allowed opacity-50'}`}
//                   autoFocus
//                 />
//               ) : (
//                 <span
//                   className={`${!isSubmitting ? 'cursor-pointer hover:text-green-700 hover:bg-green-50' : 'cursor-not-allowed opacity-50'} font-medium text-green-600 transition-colors px-2 py-1 rounded`}
//                   onClick={() => !isSubmitting && setIsEditingBudget(true)}
//                 >
//                   ${newBudgetAmount.toLocaleString()}
//                 </span>
//               )}
//             </div>
//             <div className="relative">
//               <input
//                 type="range"
//                 min="0"
//                 max="10000"
//                 step="100"
//                 value={newBudgetAmount}
//                 onChange={handleSliderChange}
//                 disabled={isSubmitting}
//                 className={`w-full h-2 bg-gray-200 rounded-lg appearance-none outline-none slider ${isSubmitting ? 'cursor-not-allowed opacity-50' : ''}`}
//                 style={{
//                   background: `linear-gradient(to right, #10b981 0%, #10b981 ${(newBudgetAmount / 10000) * 100}%, #e5e7eb ${(newBudgetAmount / 10000) * 100}%, #e5e7eb 100%)`
//                 }}
//               />
//               <div className="flex justify-between text-xs text-gray-500 mt-1">
//                 <span>$0</span>
//                 <span>$10,000</span>
//               </div>
//             </div>
//           </div>

//           {/* Rollover & Exclusion Toggles */}
//           <div className="mb-4 flex items-center justify-between py-2">
//             <label className="block text-sm font-medium text-gray-700">Make this a rollover budget</label>
//             <div className="relative inline-block w-12 h-6">
//               <input
//                 type="checkbox"
//                 checked={isRollover}
//                 onChange={() => !isSubmitting && setIsRollover(!isRollover)}
//                 disabled={isSubmitting}
//                 className="opacity-0 w-0 h-0"
//               />
//               <span
//                 className={`absolute cursor-pointer top-0 left-0 right-0 bottom-0 transition-all duration-300 rounded-full ${
//                   isRollover ? 'bg-green-500' : 'bg-gray-300'
//                 } hover:${isRollover ? 'bg-green-600' : 'bg-gray-400'}`}
//                 onClick={() => setIsRollover(!isRollover)}
//               >
//                 <span
//                   className={`absolute h-5 w-5 left-0.5 bottom-0.5 bg-white transition-all duration-300 rounded-full transform shadow-sm ${
//                     isRollover ? 'translate-x-6' : 'translate-x-0'
//                   }`}
//                 />
//               </span>
//             </div>
//           </div>

//           <div className="mb-4 flex items-center justify-between py-2">
//             <label className="block text-sm font-medium text-gray-700">Exclude from budget</label>
//             <div className="relative inline-block w-12 h-6">
//               <input
//                 type="checkbox"
//                 checked={isExcluded}
//                 onChange={() => setIsExcluded(!isExcluded)}
//                 className="opacity-0 w-0 h-0"
//               />
//               <span
//                 className={`absolute cursor-pointer top-0 left-0 right-0 bottom-0 transition-all duration-300 rounded-full ${
//                   isExcluded ? 'bg-green-500' : 'bg-gray-300'
//                 } hover:${isExcluded ? 'bg-green-600' : 'bg-gray-400'}`}
//                 onClick={() => setIsExcluded(!isExcluded)}
//               >
//                 <span
//                   className={`absolute h-5 w-5 left-0.5 bottom-0.5 bg-white transition-all duration-300 rounded-full transform shadow-sm ${
//                     isExcluded ? 'translate-x-6' : 'translate-x-0'
//                   }`}
//                 />
//               </span>
//             </div>
//           </div>
//         </div>

//         {/* Footer */}
//         <div className="p-6 border-t border-gray-200 bg-gray-50">
//           <button 
//             className="w-full p-3 border-0 rounded-md bg-gradient-to-r from-green-500 to-green-600 text-white cursor-pointer text-center font-medium hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-[1.02]"
//             onClick={handleAddToBudget}
//           >
//             Save Budget
//           </button>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default BudgetPopupComponent;
import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { 
  FaTimes, 
  FaQuestionCircle,
  FaMoneyBillWave,
  FaCar,
  FaHome,
  FaUtensils,
  FaPlane,
  FaShoppingCart,
  FaChild,
  FaHeart,
  FaBusinessTime,
  FaHospital,
  FaGraduationCap,
  FaFilm,
  FaWallet,
  FaDollarSign,
  FaCreditCard,
  FaBuilding,
  FaEllipsisH,
  FaSpinner,
  FaCheck
} from "react-icons/fa";

const BudgetPopupComponent = ({ onSave, onClose }) => {
  const dispatch = useDispatch();
  const { currentMonth, currentYear, loading, error, networkError } = useSelector((state) => state.budget);
  
  // Get categories and subcategories from Redux store
  const categories = useSelector((state) => state.budget.categories || []);
  const allSubcategories = useSelector((state) => state.budget.subcategories || []);

  console.log("********************************START********************");
  console.log("Categories:", categories);
  console.log("All Subcategories:", allSubcategories);
  console.log("********************************END********************");

  // Icon mapping object
  const iconMap = {
    "FaMoneyBillWave": FaMoneyBillWave,
    "FaCar": FaCar,
    "FaHome": FaHome,
    "FaUtensils": FaUtensils,
    "FaPlane": FaPlane,
    "FaShoppingCart": FaShoppingCart,
    "FaChild": FaChild,
    "FaHeart": FaHeart,
    "FaBusinessTime": FaBusinessTime,
    "FaHospital": FaHospital,
    "FaGraduationCap": FaGraduationCap,
    "FaFilm": FaFilm,
    "FaWallet": FaWallet,
    "FaDollarSign": FaDollarSign,
    "FaCreditCard": FaCreditCard,
    "FaBuilding": FaBuilding,
    "FaMiscellaneous": FaEllipsisH
  };

  // State declarations
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedSubcategory, setSelectedSubcategory] = useState("");
  const [subcategoryInputValue, setSubcategoryInputValue] = useState("");
  const [newBudgetAmount, setNewBudgetAmount] = useState(0);
  const [isRollover, setIsRollover] = useState(false);
  const [isExcluded, setIsExcluded] = useState(false);
  const [isEditingBudget, setIsEditingBudget] = useState(false);
  const [subcategories, setSubcategories] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // Dropdown toggles
  const [categoryDropdownOpen, setCategoryDropdownOpen] = useState(false);
  const [subcategoryDropdownOpen, setSubcategoryDropdownOpen] = useState(false);

  // Icon dropdown for new subcategory
  const [iconDropdownOpen, setIconDropdownOpen] = useState(false);
  const [selectedIconForNewSub, setSelectedIconForNewSub] = useState("FaMiscellaneous");
  
  const availableIcons = [
    "FaMoneyBillWave", "FaCar", "FaHome", "FaUtensils", "FaPlane", 
    "FaShoppingCart", "FaChild", "FaHeart", "FaBusinessTime", 
    "FaHospital", "FaGraduationCap", "FaFilm", "FaWallet", 
    "FaDollarSign", "FaCreditCard", "FaBuilding", "FaMiscellaneous"
  ];

  // Helper function to render icon
  const renderIcon = (iconKey, className = "") => {
    const IconComponent = iconMap[iconKey] || iconMap["FaMiscellaneous"];
    return <IconComponent className={className} />;
  };

  useEffect(() => {
    // Fetch categories and subcategories if not already loaded
    if (!categories || categories.length === 0) {
      dispatch({ type: 'budget/fetchCategories' });
    }
    if (!allSubcategories || allSubcategories.length === 0) {
      dispatch({ type: 'budget/fetchSubcategories' });
    }
  }, [dispatch, categories, allSubcategories]);

  // Filter subcategories based on the selected category's id
  useEffect(() => {
    console.log("Filtering subcategories...");
    console.log("Selected Category:", selectedCategory);
    console.log("All Subcategories:", allSubcategories);
    
    if (selectedCategory && allSubcategories && allSubcategories.length > 0) {
      const filteredSubs = allSubcategories.filter((sub) => {
        const categoryId = sub.categoryId || sub.category?.id;
        return categoryId && categoryId.toString() === selectedCategory.toString();
      });
      
      console.log("Filtered subcategories:", filteredSubs);
      setSubcategories(filteredSubs);
    } else {
      setSubcategories([]);
    }
  }, [selectedCategory, allSubcategories]);

  // Monitor for successful budget creation
  useEffect(() => {
    if (!loading && !error && !networkError && isSubmitting) {
      setShowSuccess(true);
      setIsSubmitting(false);
      
      setTimeout(() => {
        onClose();
      }, 1500);
    }
  }, [loading, error, networkError, isSubmitting, onClose]);

  // Slider change handler
  const handleSliderChange = (e) => {
    setNewBudgetAmount(parseInt(e.target.value, 10) || 0);
  };

  // Handle subcategory selection from dropdown
  const handleSubcategorySelect = (subcategory) => {
    setSelectedSubcategory(subcategory.id);
    setSubcategoryInputValue(subcategory.subCategory || subcategory.name || "");
    setSelectedIconForNewSub(subcategory.iconKey || "FaMiscellaneous");
    setSubcategoryDropdownOpen(false);
  };

  // Handle subcategory input change
  const handleSubcategoryInputChange = (e) => {
    const value = e.target.value;
    setSubcategoryInputValue(value);
    
    if (!subcategoryDropdownOpen) {
      setSubcategoryDropdownOpen(true);
    }
    
    const matchingSubcategory = subcategories.find(
      sub => (sub.subCategory || sub.name || "").toLowerCase() === value.toLowerCase()
    );
    
    if (matchingSubcategory) {
      setSelectedSubcategory(matchingSubcategory.id);
      setSelectedIconForNewSub(matchingSubcategory.iconKey || "FaMiscellaneous");
    } else {
      setSelectedSubcategory(null);
    }
  };

  // Handle subcategory dropdown toggle
  const handleSubcategoryDropdownToggle = () => {
    setSubcategoryDropdownOpen(!subcategoryDropdownOpen);
  };

  // Save the budget 
  const handleAddToBudget = () => {
    if (!selectedCategory || !subcategoryInputValue.trim()) {
      alert("Please select a category and enter a subcategory.");
      return;
    }

    if (newBudgetAmount < 0) {
      alert("Budget amount cannot be negative.");
      return;
    }

    const catObj = categories.find(
      (c) => c.id.toString() === selectedCategory.toString()
    );

    if (!catObj) {
      alert("Selected category not found.");
      return;
    }

    const existingSubcategory = subcategories.find(
      sub => (sub.subCategory || sub.name || "").toLowerCase() === subcategoryInputValue.toLowerCase()
    );

    const budgetItem = {
      categoryId: catObj.id,
      subCategoryId: existingSubcategory ? existingSubcategory.id : null,
      customSubCategory: existingSubcategory ? null : subcategoryInputValue.trim(),
      allocated: newBudgetAmount,
      isRollover,
      isExcluded,
      icon: selectedIconForNewSub
    };

    setIsSubmitting(true);
    setShowSuccess(false);
    
    dispatch({ type: 'budget/addBudgetItem', payload: budgetItem });
  };

  // Filter subcategories for dropdown display based on input
  const filteredSubcategoriesForDropdown = subcategories.filter(sub => {
    const subName = sub.subCategory || sub.name || "";
    return subName.toLowerCase().includes(subcategoryInputValue.toLowerCase());
  });

  // Close dropdowns when clicking outside
  const handleClickOutside = (e) => {
    if (!e.target.closest('.dropdown-container')) {
      setSubcategoryDropdownOpen(false);
    }
    if (!e.target.closest('.category-dropdown-container')) {
      setCategoryDropdownOpen(false);
    }
    if (!e.target.closest('.icon-dropdown-container')) {
      setIconDropdownOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  // Show success message
  if (showSuccess) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center popup-overlay z-50">
        <div className="bg-white rounded-lg shadow-2xl w-full max-w-md p-8 text-center">
          <div className="text-green-500 text-6xl mb-4">
            <FaCheck />
          </div>
          <h3 className="text-xl font-bold mb-2 text-gray-800">Success!</h3>
          <p className="text-gray-600">Budget item has been created successfully.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center popup-overlay z-50">
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-md max-h-screen overflow-hidden relative popup-container">
        {/* Header */}
        <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 relative">
          <h3 className="text-xl font-bold m-0 popup-title">
            Budget Details
          </h3>
          <div 
            className="absolute top-4 right-4 cursor-pointer text-white text-xl hover:text-gray-200 transition-colors" 
            onClick={onClose}
            disabled={isSubmitting}
          >
            <FaTimes />
          </div>
        </div>

        {/* Error Display */}
        {(error || networkError) && (
          <div className="bg-red-50 border-l-4 border-red-500 p-4 mx-4 mt-4">
            <div className="text-red-700 text-sm">
              {error || networkError}
            </div>
          </div>
        )}

        {/* Content */}
        <div className="p-6 relative overflow-y-auto max-h-96">
          {/* Category */}
          <div className="mb-6 relative category-dropdown-container">
            <label className="block text-sm font-medium mb-2 text-gray-700">Category</label>
            <div
              onClick={() => !isSubmitting && setCategoryDropdownOpen(!categoryDropdownOpen)}
              className={`w-full p-3 border border-gray-300 rounded-md text-sm flex items-center justify-between min-h-10 ${!isSubmitting ? 'cursor-pointer hover:border-green-400' : 'cursor-not-allowed opacity-50'} transition-colors focus:outline-none focus:ring-2 focus:ring-green-500`}
            >
              {selectedCategory ? (
                (() => {
                  const cat = categories.find(
                    (c) => c.id.toString() === selectedCategory.toString()
                  );
                  if (cat) {
                    return (
                      <div className="flex items-center">
                        {renderIcon(cat.categoryIconKey || "FaMiscellaneous", "text-green-600 mr-2")}
                        {cat.category || cat.name || "Select a Category"}
                      </div>
                    );
                  }
                  return "Select a Category";
                })()
              ) : (
                <span className="text-gray-500">Select a Category</span>
              )}
              <span className={`arrow transition-transform ${categoryDropdownOpen ? 'rotate-180' : ''}`}>▾</span>
            </div>
            {categoryDropdownOpen && !isSubmitting && (
              <div className="absolute top-full left-0 w-full mt-1 border border-gray-300 rounded-md bg-white shadow-lg z-50 max-h-36 overflow-y-auto">
                {categories && categories.length > 0 ? (
                  categories.map((cat) => (
                    <div
                      key={cat.id}
                      className="flex items-center w-full min-h-10 p-3 text-sm cursor-pointer border-0 rounded-none bg-transparent hover:bg-green-50 transition-colors"
                      onClick={() => {
                        setSelectedCategory(cat.id);
                        setCategoryDropdownOpen(false);
                        setSelectedSubcategory(null);
                        setSubcategoryInputValue("");
                        setSelectedIconForNewSub("FaMiscellaneous");
                        setTimeout(() => setSubcategoryDropdownOpen(true), 100);
                      }}
                    >
                      {renderIcon(cat.categoryIconKey || "FaMiscellaneous", "text-green-600 mr-2")}
                      {cat.category || cat.name || ""}
                    </div>
                  ))
                ) : (
                  <div className="flex items-center w-full min-h-10 p-3 text-sm cursor-pointer border-0 rounded-none bg-transparent text-gray-500">
                    <em>No categories available</em>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Editable Subcategory Dropdown */}
          {selectedCategory && (
            <div className="flex items-end gap-3 mb-6 relative">
              <div className="flex-1 relative dropdown-container">
                <label className="block text-sm font-medium mb-2 text-gray-700">Subcategory</label>
                <div className="relative flex items-center">
                  <input
                    type="text"
                    value={subcategoryInputValue}
                    onChange={handleSubcategoryInputChange}
                    onFocus={() => !isSubmitting && setSubcategoryDropdownOpen(true)}
                    placeholder="Select or type subcategory name"
                    disabled={isSubmitting}
                    className={`w-full p-3 pr-8 border border-gray-300 rounded-md text-sm min-h-10 outline-none ${!isSubmitting ? 'hover:border-green-400 focus:border-green-500 focus:ring-2 focus:ring-green-200' : 'cursor-not-allowed opacity-50'} transition-colors`}
                  />
                  <span 
                    className={`absolute right-3 ${!isSubmitting ? 'cursor-pointer' : 'cursor-not-allowed'} text-gray-600 text-xs transition-transform ${subcategoryDropdownOpen ? 'rotate-180' : ''}`}
                    onClick={() => !isSubmitting && handleSubcategoryDropdownToggle()}
                  >
                    ▾
                  </span>
                </div>
                {subcategoryDropdownOpen && !isSubmitting && (
                  <div className="absolute top-full left-0 w-full mt-1 border border-gray-300 rounded-md bg-white shadow-lg z-40 max-h-36 overflow-y-auto">
                    {filteredSubcategoriesForDropdown.length > 0 ? (
                      filteredSubcategoriesForDropdown.map((sub) => (
                        <div
                          key={sub.id}
                          className="flex items-center w-full min-h-10 p-3 text-sm cursor-pointer border-0 rounded-none bg-transparent hover:bg-green-50 transition-colors"
                          onClick={() => handleSubcategorySelect(sub)}
                        >
                          <span className="mr-2">
                            {renderIcon(sub.iconKey || "FaMiscellaneous", "text-green-600")}
                          </span>
                          {sub.subCategory || sub.name || ""}
                        </div>
                      ))
                    ) : subcategoryInputValue.trim() ? (
                      <div className="flex items-center w-full min-h-10 p-3 text-sm cursor-pointer border-0 rounded-none bg-transparent text-green-600">
                        <span className="mr-2">
                          {renderIcon(selectedIconForNewSub)}
                        </span>
                        <em>Create new: "{subcategoryInputValue}"</em>
                      </div>
                    ) : (
                      <div className="flex items-center w-full min-h-10 p-3 text-sm cursor-pointer border-0 rounded-none bg-transparent text-gray-500">
                        <em>No subcategories found for this category</em>
                      </div>
                    )}
                  </div>
                )}
              </div>
              
              <div className="icon-dropdown-container">
                <label className="block text-sm font-medium mb-2 text-gray-700">Icon</label>
                <div
                  className={`${!isSubmitting ? 'cursor-pointer hover:border-green-400' : 'cursor-not-allowed opacity-50'} border border-gray-300 rounded-md w-12 h-10 flex items-center justify-center relative transition-colors bg-white`}
                  onClick={() => !isSubmitting && setIconDropdownOpen(!iconDropdownOpen)}
                >
                  {renderIcon(selectedIconForNewSub, "text-green-600 text-lg")}
                  {iconDropdownOpen && !isSubmitting && (
                    <div className="absolute top-full right-0 bg-white border border-gray-300 rounded-md shadow-lg p-2 z-50 grid grid-cols-4 gap-2 max-h-48 overflow-y-auto min-w-[200px]">
                      {availableIcons.map((iconKey) => (
                        <div
                          key={iconKey}
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedIconForNewSub(iconKey);
                            setIconDropdownOpen(false);
                          }}
                          className={`cursor-pointer p-2 text-lg rounded hover:bg-green-50 transition-colors flex items-center justify-center ${
                            selectedIconForNewSub === iconKey ? 'bg-green-100 text-green-600' : 'text-gray-600'
                          }`}
                        >
                          {renderIcon(iconKey)}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Budget Amount Slider */}
          <div className="mb-6 flex flex-col">
            <div className="flex items-center justify-between mb-3">
              <label className="block text-sm font-medium text-gray-700">Budget Amount</label>
              {isEditingBudget ? (
                <input
                  type="number"
                  value={newBudgetAmount}
                  onChange={(e) =>
                    setNewBudgetAmount(Number(e.target.value) || 0)
                  }
                  onBlur={() => setIsEditingBudget(false)}
                  onKeyDown={(e) => e.key === 'Enter' && setIsEditingBudget(false)}
                  disabled={isSubmitting}
                  className={`text-right border border-gray-300 rounded p-2 w-28 font-medium ${!isSubmitting ? 'cursor-pointer focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200' : 'cursor-not-allowed opacity-50'}`}
                  autoFocus
                />
              ) : (
                <span
                  className={`${!isSubmitting ? 'cursor-pointer hover:text-green-700 hover:bg-green-50' : 'cursor-not-allowed opacity-50'} font-medium text-green-600 transition-colors px-2 py-1 rounded`}
                  onClick={() => !isSubmitting && setIsEditingBudget(true)}
                >
                  ${newBudgetAmount.toLocaleString()}
                </span>
              )}
            </div>
            <div className="relative">
              <input
                type="range"
                min="0"
                max="10000"
                step="100"
                value={newBudgetAmount}
                onChange={handleSliderChange}
                disabled={isSubmitting}
                className={`w-full h-2 bg-gray-200 rounded-lg appearance-none outline-none slider ${isSubmitting ? 'cursor-not-allowed opacity-50' : ''}`}
                style={{
                  background: `linear-gradient(to right, #10b981 0%, #10b981 ${(newBudgetAmount / 10000) * 100}%, #e5e7eb ${(newBudgetAmount / 10000) * 100}%, #e5e7eb 100%)`
                }}
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>$0</span>
                <span>$10,000</span>
              </div>
            </div>
          </div>

          {/* Rollover & Exclusion Toggles */}
          <div className="mb-4 flex items-center justify-between py-2">
            <label className="block text-sm font-medium text-gray-700">Make this a rollover budget</label>
            <div className="relative inline-block w-12 h-6">
              <input
                type="checkbox"
                checked={isRollover}
                onChange={() => !isSubmitting && setIsRollover(!isRollover)}
                disabled={isSubmitting}
                className="opacity-0 w-0 h-0"
              />
              <span
                className={`absolute cursor-pointer top-0 left-0 right-0 bottom-0 transition-all duration-300 rounded-full ${
                  isRollover ? 'bg-green-500' : 'bg-gray-300'
                } hover:${isRollover ? 'bg-green-600' : 'bg-gray-400'}`}
                onClick={() => setIsRollover(!isRollover)}
              >
                <span
                  className={`absolute h-5 w-5 left-0.5 bottom-0.5 bg-white transition-all duration-300 rounded-full transform shadow-sm ${
                    isRollover ? 'translate-x-6' : 'translate-x-0'
                  }`}
                />
              </span>
            </div>
          </div>

          <div className="mb-4 flex items-center justify-between py-2">
            <label className="block text-sm font-medium text-gray-700">Exclude from budget</label>
            <div className="relative inline-block w-12 h-6">
              <input
                type="checkbox"
                checked={isExcluded}
                onChange={() => setIsExcluded(!isExcluded)}
                className="opacity-0 w-0 h-0"
              />
              <span
                className={`absolute cursor-pointer top-0 left-0 right-0 bottom-0 transition-all duration-300 rounded-full ${
                  isExcluded ? 'bg-green-500' : 'bg-gray-300'
                } hover:${isExcluded ? 'bg-green-600' : 'bg-gray-400'}`}
                onClick={() => setIsExcluded(!isExcluded)}
              >
                <span
                  className={`absolute h-5 w-5 left-0.5 bottom-0.5 bg-white transition-all duration-300 rounded-full transform shadow-sm ${
                    isExcluded ? 'translate-x-6' : 'translate-x-0'
                  }`}
                />
              </span>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <button 
            className="w-full p-3 border-0 rounded-md bg-gradient-to-r from-green-500 to-green-600 text-white cursor-pointer text-center font-medium hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-[1.02]"
            onClick={handleAddToBudget}
          >
            Save Budget
          </button>
        </div>
      </div>
    </div>
  );
};

export default BudgetPopupComponent;