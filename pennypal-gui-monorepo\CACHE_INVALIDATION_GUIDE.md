# Cache Invalidation Guide

## 🎯 Overview

The caching system automatically invalidates and refreshes data when changes occur. This guide explains how cache invalidation works and how to use it effectively.

## 🔄 Automatic Cache Invalidation

### Transaction-Related Actions
The cache automatically invalidates when these actions occur:

**Transaction Operations (Validated):**
- `transactions/addTransactionSuccess` ✅
- `transactions/updateTransactionSuccess` ✅
- `transactions/deleteTransactionSuccess` ✅
- `transactions/hideFromBudgetSuccess` ✅

**Split Transaction Operations (Validated):**
- `splitTransaction/submitSplitSuccess` ✅

**Recurring Transaction Operations (Validated):**
- `recurring/setupRecurringContributionSuccess` ✅
- `recurring/updateRecurringContributionSuccess` ✅
- `recurring/deleteRecurringContributionSuccess` ✅
- `recurring/triggerContributionSuccess` ✅

### Account Sync Actions
Cache invalidates when accounts are synced:

**Account Sync Operations (Validated):**
- `accounts/refreshAllAccounts/fulfilled` ✅
- `accounts/syncAccount/fulfilled` ✅
- `accounts/exchangePublicToken/fulfilled` ✅

### Budget Operations
Cache invalidates when budget data changes:

**Budget Operations (Validated):**
- `budget/addBudget` ✅
- `budget/updateBudget` ✅
- `budget/deleteSubcategory` ✅
- `budget/saveBudget` ✅
- `budget/addBudgetItem` ✅

### Receipt Operations
Cache invalidates when receipt data changes:

**Receipt Operations (Validated):**
- `receipts/uploadReceiptSuccess` ✅
- `receipts/saveReceiptSuccess` ✅
- `receipts/addNewTransaction` ✅

### Chatbot Operations
Cache invalidates when new chatbot queries are made:

**Chatbot Operations (To be implemented):**
- `chatbot/querySuccess` ⚠️
- `chatbot/newQuery` ⚠️

## 🛠️ Manual Cache Invalidation

### Using Cache Utils in Components

```javascript
import { useCacheInvalidation } from '../../../../logic/utils/cacheUtils';

const MyComponent = () => {
  const {
    invalidateTransactions,
    invalidateBudget,
    invalidateAllTransactionData,
    invalidateReceipts,
    invalidateChatbot,
    clearAllCache
  } = useCacheInvalidation();

  const handleTransactionUpdate = async () => {
    // Update transaction via API
    await updateTransaction(transactionData);
    
    // Manually invalidate cache if automatic invalidation doesn't trigger
    invalidateTransactions();
  };

  const handleAccountSync = async () => {
    // Sync accounts
    await syncAccounts();
    
    // Invalidate all transaction-related data
    invalidateAllTransactionData();
  };
};
```

### Direct Dispatch Method

```javascript
import { useDispatch } from 'react-redux';
import { 
  invalidateTransactionCache,
  invalidateBudgetCache,
  invalidateAllTransactionRelatedCache 
} from '../../../../logic/redux/cacheSlice';

const MyComponent = () => {
  const dispatch = useDispatch();

  const handleDataChange = () => {
    // Invalidate specific cache
    dispatch(invalidateTransactionCache());
    
    // Or invalidate all transaction-related cache
    dispatch(invalidateAllTransactionRelatedCache());
  };
};
```

## 📋 Cache Invalidation Types

### 1. `invalidateTransactionCache()`
**When to use:** Individual transaction operations
**What it clears:** 
- User transactions (`api/v1/transaction/transactions/user/{userId}`)

### 2. `invalidateRecurringTransactionCache()`
**When to use:** Recurring transaction operations
**What it clears:**
- Recurring transactions (`api/v1/transaction/recurring/fetch2/{userId}`)
- Future recurring transactions (`api/v1/transaction/recurring/fetch_future/{userId}`)

### 3. `invalidateBudgetCache()`
**When to use:** Budget operations only
**What it clears:**
- Budget summary (`api/v1/budget/summary_by_month/{userId}/{year}/{month}`)
- Budget data (`api/v1/budget/user/{userId}/month`)

### 4. `invalidateAllTransactionRelatedCache()`
**When to use:** Major operations affecting both transactions and budgets
**What it clears:**
- All transaction data
- All recurring transaction data
- All budget data
- Transaction summary (`api/v1/transaction/summary/user/{userId}`)
- Hidden transactions (`api/v1/transaction/hidden/user/{userId}`)
- Reconcile data (`api/v1/reconcile/all`)

### 5. `invalidateReceiptCache()`
**When to use:** Receipt operations
**What it clears:**
- Receipt transaction IDs (`api/receipts/getReceiptTransactionIds`)
- Receipt items (`api/v1/receipt-items/all`)
- Receipt summary (`api/v1/receipt/summary`)
- User receipts (`api/receipts/user/{userId}`)

### 6. `invalidateChatbotCache()`
**When to use:** New chatbot queries
**What it clears:**
- Chatbot history (`api/v1/chatbot/history/{userId}`)

### 7. `clearCache()`
**When to use:** User logout, major app reset, troubleshooting
**What it clears:** Everything (nuclear option)

## 🔄 Automatic Refetch

After cache invalidation, the system automatically refetches the invalidated data:

1. **Cache Invalidated** → Triggers refetch epic
2. **Refetch Epic** → Dispatches appropriate fetch actions
3. **Fetch Epics** → Make API calls and update cache
4. **Components** → Automatically receive fresh data

## 🎯 Best Practices

### 1. Let Automatic Invalidation Handle Most Cases
The system automatically invalidates cache for most operations. Only use manual invalidation when:
- Custom API calls that don't trigger automatic invalidation
- Third-party integrations
- Troubleshooting data inconsistencies

### 2. Choose the Right Invalidation Level
```javascript
// ✅ Good: Specific invalidation for transaction changes
dispatch(invalidateTransactionCache());

// ❌ Avoid: Over-invalidation for small changes
dispatch(clearCache()); // Too broad for single transaction update
```

### 3. Handle Edge Cases
```javascript
const handleCustomOperation = async () => {
  try {
    await customApiCall();
    // Success: invalidate relevant cache
    invalidateTransactions();
  } catch (error) {
    // Error: don't invalidate cache
    console.error('Operation failed:', error);
  }
};
```

### 4. Use in Error Recovery
```javascript
const handleDataInconsistency = () => {
  // If data seems inconsistent, clear cache to force fresh fetch
  clearAllCache();
};
```

## 🚨 Common Scenarios

### Scenario 1: Adding a New Transaction
```javascript
// Automatic: Action 'transactions/addTransactionSuccess' triggers invalidation
// Manual: Not needed, handled automatically
```

### Scenario 2: Syncing Bank Accounts
```javascript
// Automatic: Action 'plaid/syncAccountsSuccess' triggers invalidation
// Manual: Use invalidateAllTransactionData() if custom sync
```

### Scenario 3: Updating Budget
```javascript
// Automatic: Action 'budget/updateBudget' triggers invalidation
// Manual: Use invalidateBudget() for custom budget operations
```

### Scenario 4: Uploading Receipt
```javascript
// Automatic: Action 'receipts/uploadReceiptSuccess' triggers invalidation
// Manual: Use invalidateReceipts() for custom receipt operations
```

### Scenario 5: New Chatbot Query
```javascript
// Automatic: Action 'chatbot/querySuccess' triggers invalidation
// Manual: Use invalidateChatbot() for custom chatbot operations
```

### Scenario 6: User Logout
```javascript
const handleLogout = () => {
  // Clear all cache when user logs out
  clearAllCache();
  // Proceed with logout logic
};
```

## 🔍 Debugging Cache Issues

### Check Cache State
```javascript
// In component or browser console
const cacheState = store.getState().cache;
console.log('Cache state:', cacheState);
```

### Force Cache Refresh
```javascript
// Clear cache and trigger fresh fetch
clearAllCache();
```

### Monitor Cache Invalidation
Look for console messages:
- `🗑️ Invalidating [type] cache`
- `🔄 [Action] data changed, invalidating cache`
- `🔄 Cache invalidated, triggering refetch`

## ⚡ Performance Considerations

1. **Selective Invalidation**: Use specific invalidation methods rather than clearing entire cache
2. **Batch Operations**: Group related operations to minimize cache invalidations
3. **User Experience**: Cache invalidation + refetch happens automatically, maintaining smooth UX
4. **Network Efficiency**: Only invalidated data is refetched, not entire cache

The cache invalidation system ensures data consistency while maintaining optimal performance!
